package strategyer

import (
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/strategyer/base"
)

type Prefetch_QueryStragtegy struct {
	base.BaseStrategy
}

func (this *Prefetch_QueryStragtegy) Build(bName string, ctx *gdp.WebContext, dataInput []byte, dataOutput *[]byte) Strategyer {
	strategyerObj := &Prefetch_QueryStragtegy{}
	strategyerObj.Ctx = ctx
	strategyerObj.Name = bName
	strategyerObj.DataInput = dataInput
	strategyerObj.DataOutput = dataOutput
	return strategyerObj
}

func (this *Prefetch_QueryStragtegy) BName() string {
	return "Prefetch_QueryStragtegy"
}

// 返回true，表示命中策略，需要被过滤
func (this *Prefetch_QueryStragtegy) Execute(dataInput []byte) bool {
	query := (*this.GetRequest())["query"]

	// 如果 query 是这几种情况之一，表示命中策略，本次请求需要被过滤
	return this.isDeleteReq(query) || this.isChineseAndPinyin(query) || this.isNoChinese(query) ||
		this.atLabelIsFirst(query) || this.isSingleWord(query) || this.isPinyin(query)
}

func (this *Prefetch_QueryStragtegy) atLabelIsFirst(query string) bool {
	return strings.Index(query, "@") == 0
}

func (this *Prefetch_QueryStragtegy) isSingleWord(query string) bool {
	querylen := utf8.RuneCountInString(query)
	return querylen <= 1
}

// 简单判断是否全部是字母
func (this *Prefetch_QueryStragtegy) isPinyin(query string) bool {
	querySimple := strings.ReplaceAll(query, " ", "")
	match, _ := regexp.MatchString("^[A-Za-z]+$", querySimple)
	return match
}

// 中文，英文大小写，标点符号，包含2类及以上
func (this *Prefetch_QueryStragtegy) isChineseAndPinyin(query string) bool {
	count := 0
	if match, _ := regexp.MatchString(`[A-Za-z]+`, query); match {
		count++
	}
	for _, v := range query {
		if unicode.Is(unicode.Han, v) {
			count++
			break
		}
	}
	for _, v := range query {
		if unicode.IsPunct(v) {
			count++
			break
		}
	}

	if count >= 2 {
		return true
	}

	return false
}

// 判断是否不包含中文
func (this *Prefetch_QueryStragtegy) isNoChinese(query string) bool {
	hasChinese := false
	for _, v := range query {
		if unicode.Is(unicode.Han, v) {
			hasChinese = true
			break
		}
	}

	if !hasChinese {
		return true
	}

	return false
}

// 是否为回删时发起的请求
func (this *Prefetch_QueryStragtegy) isDeleteReq(query string) bool {
	isdelete, ok := (*this.GetRequest())["isdelete"]
	if ok && isdelete == "1" {
		return true
	}

	return false
}

func init() {
	Register(&Prefetch_QueryStragtegy{})
}
