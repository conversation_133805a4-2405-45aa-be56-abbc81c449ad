package strategyer

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
)

func TestIsFilterQuery(t *testing.T) {
	type expCase struct {
		query              string
		isChineseAndPinyin bool
		isNoChinese        bool
	}
	queryList := []expCase{
		{
			query:              "今天tianqi",
			isChineseAndPinyin: true,
			isNoChinese:        false,
		},
		{
			query:              "tianqi好，",
			isChineseAndPinyin: true,
			isNoChinese:        false,
		},
		{
			query:              "好 A",
			isChineseAndPinyin: true,
			isNoChinese:        false,
		},
		{
			query:              "好,..",
			isChineseAndPinyin: true,
			isNoChinese:        false,
		},
		{
			query:              "好看",
			isChineseAndPinyin: false,
			isNoChinese:        false,
		},
		{
			query:              "1 ;;a1",
			isChineseAndPinyin: true,
			isNoChinese:        true,
		},
		{
			query:              "A北京",
			isChineseAndPinyin: true,
			isNoChinese:        false,
		},
		{
			query:              "Aasfb",
			isChineseAndPinyin: false,
			isNoChinese:        true,
		},
		{
			query:              "hao123",
			isChineseAndPinyin: false,
			isNoChinese:        true,
		},
	}

	obj := Prefetch_QueryStragtegy{}
	obj.Ctx = createGetWebContext()

	for _, v := range queryList {
		assert.Equal(t, obj.isChineseAndPinyin(v.query), v.isChineseAndPinyin)
		assert.Equal(t, obj.isNoChinese(v.query), v.isNoChinese)
	}
}

func createGetWebContext() *gdp.WebContext {
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	bodyio := bytes.NewBuffer([]byte{})
	c.Request, _ = http.NewRequest("GET", `/suggest?query=be`, bodyio)

	// 构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1234", "127.0.0.1"),
	}

	return wc
}
