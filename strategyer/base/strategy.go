package base

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

//startegy base定义
type BaseStrategy struct {
	Name    string
	Ctx     *gdp.WebContext
	DataInput []byte
	DataOutput *[]byte
}

func (this *BaseStrategy) SName() string {
	return this.Name
}

//获取策略执行后的输出
func (this *BaseStrategy) GetOutputByteData() []byte {
	return *this.DataOutput
}

//设置策略执行的输入数据
func (this *BaseStrategy) setInputByteData(dataInput []byte) {
	this.DataInput = dataInput
}

func (this *BaseStrategy) GetRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := this.Ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (this *BaseStrategy) GetAdaption() *map[string]string {
	var adaption map[string]string
	if val, exists := this.Ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	return &adaption
}

