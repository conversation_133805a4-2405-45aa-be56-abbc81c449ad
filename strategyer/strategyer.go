package strategyer

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"log"
)

// stragetyer 接口定义
type Strategyer interface {
	SName() string
	Execute([]byte) bool
	GetOutputByteData() []byte
}

type Builder interface {
	BName() string
	Build(bName string, ctx *gdp.WebContext, dataInput []byte, dataOutput *[]byte) Strategyer
}

// strategy初始化
func NewStrategyer(strageyName string, ctx *gdp.WebContext, dataInput []byte, dataOutput *[]byte) Strategyer {
	builder, ok := builders[strageyName]
	if !ok {
		ctx.WarnF("strategy: unknown adapter %s (forgotten register?)", strageyName)
		return nil
	}
	return builder.Build(strageyName, ctx, dataInput, dataOutput)
}

var builders = make(map[string]Builder)

// strategy注册函数
func Register(b Builder) {
	if _, ok := builders[b.BName()]; ok {
		panic("strategy: Register called twice for builder " + b.BName())
	}
	log.Printf("strategy:%s register builder", b.BName())
	builders[b.BName()] = b
}
