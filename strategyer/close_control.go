package strategyer

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/strategyer/base"
)

type PrefetchClose struct {
	base.BaseStrategy
}

func (this *PrefetchClose) Build(bName string, ctx *gdp.WebContext, dataInput []byte, dataOutput *[]byte) Strategyer {
	strategyerObj := &PrefetchClose{}
	strategyerObj.Ctx = ctx
	strategyerObj.Name = bName
	strategyerObj.DataInput = dataInput
	strategyerObj.DataOutput = dataOutput
	return strategyerObj
}

func (this *PrefetchClose) BName() string {
	return "Prefetch_Close"
}

// 返回true，表示命中策略，需要被过滤
func (this *PrefetchClose) Execute(dataInput []byte) bool {
	return true
}

func init() {
	Register(&PrefetchClose{})
}
