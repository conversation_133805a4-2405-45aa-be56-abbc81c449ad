package strategyer
import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/strategyer/base"
)
type Prefetch_NetworkStragtegy struct {
	base.BaseStrategy
}

func (this* Prefetch_NetworkStragtegy) Build(bName string, ctx *gdp.WebContext, dataInput []byte, dataOutput *[]byte) Strategyer {
	strategyerObj := &Prefetch_NetworkStragtegy{}
	strategyerObj.Ctx = ctx
	strategyerObj.Name = bName
	strategyerObj.DataInput = dataInput
	strategyerObj.DataOutput = dataOutput
	return strategyerObj
}
func (this* Prefetch_NetworkStragtegy) BName() string {
	return "Prefetch_NetworkStragtegy"
}

func (this* Prefetch_NetworkStragtegy) Execute(dataInput []byte) bool {
	network_type := (*this.GetRequest())["network_type"]
	this.Ctx.AddNotice("networkType", network_type)
	prefetch_network_enabled_arr := [...]string{"wifi", "4g", "5g"}
	enabled := false
	for _, networkItems := range prefetch_network_enabled_arr {
		if network_type == networkItems {
			enabled = true
			break
		}
	}
	return !enabled
}

func init() {
	Register(&Prefetch_NetworkStragtegy{})
}