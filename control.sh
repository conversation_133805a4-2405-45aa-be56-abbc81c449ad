#!/bin/bash
# AbsPath: absolute path of control script
# AppRoot: absolute path of app root
# AppName: same as the dir name
AbsPath=$(cd $(dirname "$BASH_SOURCE") && pwd)
AppRoot=$(dirname "$AbsPath")
AppName="go-suggest"
AppPort="8087"

# app
App="${AppRoot}/bin/${AppName}"
PortConf="${AppRoot}/conf/port.conf"

LOG_DIR="${AppRoot}/log/"
CONTROL_LOG="control.log"

# supervise
Supervise="${AppRoot}/bin/supervise/bin/supervise"
SuperviseDir="${AppRoot}/supervise_status/${AppName}"

if uname -m | grep -q 'arm\|aarch64'; then
    echo "This is an ARM environment."
    mv ${AppRoot}/bin/supervise/bin/supervise.arm ${AppRoot}/bin/supervise/bin/supervise
fi

function Run() {
 GOGC=300 GOTRACEBACK=crash ${App} --port=${AppPort}  &>${LOG_DIR}/${CONTROL_LOG} </dev/null
 return 0
}

# start
# if app is running, nothing to do.
function ControlStart() {
    mkdir -p "${SuperviseDir}"
    ${Supervise} -p ${SuperviseDir} -f "bash ${AppRoot}/bin/control.sh run" &
    return 0
}

# 获取进程树的所有 PID (BFS 遍历)
get_list_of_child_pid() {
    local parent_pid=$1
    local pid_queue="$parent_pid"
    local result_pid_list=""

    while [ -n "$pid_queue" ]; do
        # 拆分 pid_queue 成数组风格（空格分隔）
        set -- $pid_queue
        local current_pid=$1

        # 从 $2 开始拼接剩下的队列
        shift
        pid_queue="$*"
        result_pid_list="$result_pid_list $current_pid"
        
        # 获取直接子进程 PID
        local sub_pids
        sub_pids=$(ps -o pid= --ppid "$current_pid" 2>/dev/null | sort | uniq)
        
        if [ -n "$sub_pids" ]; then
            for pid in $sub_pids; do
                pid_queue="$pid_queue $pid"
            done
        fi
    done
    echo "$result_pid_list"
}

# stop
# /bin/ps only list matrix container process
function ControlStop() {
    # kill supervise
    parent_pid=`/bin/ps -ef | grep supervise | grep "supervise_status/${AppName}" | grep -v grep | awk '{print $2}'`

    if [[ -z "$parent_pid" ]]; then
        echo "No supervise process found, maybe already stopped"
        # 兜底处理
        /bin/ps -ef | grep "${AppName} --port=${AppPort}" | grep -v grep | awk '{print $2}' | xargs kill -9 &>/dev/null
        /bin/ps -ef | grep "bash bin/control.sh start" | grep -v grep | awk '{print $2}' | xargs kill -9 &>/dev/null
        return 0
    fi
    # 获取所有 PID (包括父进程)
    all_pids=($(get_list_of_child_pid "$parent_pid"))
    child_pids=""
    
    # 分离父进程和子进程
    for pid in "${all_pids[@]}"; do
        if [ "$pid" != "$parent_pid" ]; then
            child_pids="$child_pids $pid"
        fi
    done

    echo "Terminating process tree:"
    echo "  Parent PID: ${parent_pid} (SIGKILL)"
    echo "  Child PIDs: ${child_pids} (SIGINT then SIGKILL if needed)"

    # 1. 立即终止父进程
    kill -9 "$parent_pid" > /dev/null 2>&1

    # 2. 向所有子进程发送 SIGINT (优雅终止)
    if [ -n "$child_pids" ]; then
        kill -2 $child_pids > /dev/null 2>&1
        
        # 3. 等待 12 秒
        echo "Waiting 12 seconds for graceful termination..."
        sleep 12
        
        # 4. 检查并强制终止剩余进程
        remaining_pids=""
        for pid in $child_pids; do
            if ps -p "$pid" > /dev/null 2>&1; then
                remaining_pids="$remaining_pids $pid"
            fi
        done
        
        if [ -n "$remaining_pids" ]; then
            echo "Force killing remaining child processes: $remaining_pids"
            kill -9 $remaining_pids > /dev/null 2>&1
        else
            echo "All child processes exited gracefully"
        fi
    else
        echo "No child processes found"
    fi
    
    echo "Process tree terminated successfully"
}
# restart
function ControlRestart() {
 ControlStop
 ControlStart
}
# usage
function Usage() {
 echo "Usage: $(basename "$0") [start|stop|restart]"
}
# init
function Init() {
 if [[ ! -x ${App} ]]; then
 echo "app cmd not exist: ${App}"
 return 1
 fi
if [[ ! -f "${PortConf}" ]]; then
 echo "port conf not exist: ${PortConf}, use default port 8087"
else
    # replace port template value
    port=$(grep "LISTEN_PORT" "${PortConf}" | awk '{print $NF}')
    AppPort=$port
fi
 return 0
}

function main() {
 Init || exit 1
action=$1
 case "X${action}" in
 Xstart)
 ControlStart || ( echo 'retry...' && ControlRestart )
 ;;
 Xstop)
 ControlStop
 ;;
 Xrestart)
 ControlRestart
 ;;
 Xrun)
 Run
 ;;
 *)
 Usage
 ;;
 esac
}
if [ $# != 1 ] ; then
 Usage
 exit 1
fi
cd "${AppRoot}" || exit 1
main "$@"