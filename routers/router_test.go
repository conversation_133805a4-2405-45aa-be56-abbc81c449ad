package routers

import (
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

// createTestWebContext 创建测试用的 WebContext
func createTestWebContext() *gdp.WebContext {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	return gdp.NewWebContext(c)
}

func TestHandleIntelligentMode(t *testing.T) {
	tests := []struct {
		name               string
		intelligentHeader  string
		bdVersion          string
		expectedMode       string
		shouldHaveAdaption bool
		description        string
	}{
		{
			name:               "Header为1时设置intelligent_mode为1",
			intelligentHeader:  "1",
			bdVersion:          "15.18",
			expectedMode:       "1",
			shouldHaveAdaption: true,
			description:        "当user-intelligent-mode header为1时，应该设置intelligent_mode为1",
		},
		{
			name:               "Header为0时设置intelligent_mode为0",
			intelligentHeader:  "0",
			bdVersion:          "15.18",
			expectedMode:       "0",
			shouldHaveAdaption: true,
			description:        "当user-intelligent-mode header为0时，应该设置intelligent_mode为0",
		},
		{
			name:               "Header为空时设置intelligent_mode为0",
			intelligentHeader:  "",
			bdVersion:          "15.18",
			expectedMode:       "0",
			shouldHaveAdaption: true,
			description:        "当user-intelligent-mode header为空时，应该设置intelligent_mode为0",
		},
		{
			name:               "版本低于15.17时设置intelligent_mode为1",
			intelligentHeader:  "0",
			bdVersion:          "15.16",
			expectedMode:       "1",
			shouldHaveAdaption: true,
			description:        "当版本低于15.17时，应该覆盖之前的设置，将intelligent_mode设置为1",
		},
		{
			name:               "版本等于15.17时不覆盖设置",
			intelligentHeader:  "0",
			bdVersion:          "15.17",
			expectedMode:       "0",
			shouldHaveAdaption: true,
			description:        "当版本等于15.17时，不应该覆盖之前的设置",
		},
		{
			name:               "版本高于15.17时不覆盖设置",
			intelligentHeader:  "0",
			bdVersion:          "15.18",
			expectedMode:       "0",
			shouldHaveAdaption: true,
			description:        "当版本高于15.17时，不应该覆盖之前的设置",
		},
		{
			name:               "无适配参数时仍设置header值",
			intelligentHeader:  "1",
			bdVersion:          "",
			expectedMode:       "1",
			shouldHaveAdaption: false,
			description:        "当没有适配参数时，仍应该根据header设置intelligent_mode",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			ctx := createTestWebContext()

			// 设置表单数据，确保能正确设置值
			ctx.Request.Form = url.Values{}

			// 模拟 RequestWare 中间件的行为，预先设置 requestParams
			testRequestParams := make(map[string]string)
			ctx.Set(constant.REQPARAMS, testRequestParams)

			// 设置请求头
			if tt.intelligentHeader != "" {
				ctx.Request.Header.Set("user-intelligent-mode", tt.intelligentHeader)
			}

			// 设置适配参数
			if tt.shouldHaveAdaption && tt.bdVersion != "" {
				adaption := map[string]string{
					constant.BDVERSION: tt.bdVersion,
				}
				ctx.Set(constant.ADAPARAMS, adaption)
			}

			// 调用被测试的函数
			handleIntelligentMode(ctx)

			// 验证结果 - 从 requestParams 中获取
			var requestParams map[string]string
			if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
				requestParams, _ = val.(map[string]string)
			}

			actualMode := ""
			if requestParams != nil {
				actualMode = requestParams["intelligent_mode"]
			}

			if actualMode != tt.expectedMode {
				t.Errorf("%s: expected intelligent_mode=%s, got=%s",
					tt.description, tt.expectedMode, actualMode)
			}
		})
	}
}

func TestCompatibleMiddleware(t *testing.T) {
	tests := []struct {
		name               string
		compatible         string
		intelligentHeader  string
		bdVersion          string
		expectedMode       string
		shouldHaveAdaption bool
		description        string
	}{
		{
			name:               "Compatible为1时应该处理intelligent_mode和设置路由参数",
			compatible:         "1",
			intelligentHeader:  "1",
			bdVersion:          "15.18",
			expectedMode:       "1",
			shouldHaveAdaption: true,
			description:        "当compatible=1时，应该处理intelligent_mode并设置路由参数",
		},
		{
			name:               "Compatible为1且版本低于15.17时覆盖intelligent_mode",
			compatible:         "1",
			intelligentHeader:  "0",
			bdVersion:          "15.16",
			expectedMode:       "1",
			shouldHaveAdaption: true,
			description:        "当compatible=1且版本低于15.17时，应该覆盖intelligent_mode为1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			ctx := createTestWebContext()

			// 设置表单数据
			ctx.Request.Form = url.Values{}
			ctx.Request.Form.Set("compatible", tt.compatible)

			// 模拟 RequestWare 中间件的行为，预先设置 requestParams
			testRequestParams := make(map[string]string)
			ctx.Set(constant.REQPARAMS, testRequestParams)

			// 设置请求头
			if tt.intelligentHeader != "" {
				ctx.Request.Header.Set("user-intelligent-mode", tt.intelligentHeader)
			}

			// 设置适配参数
			if tt.shouldHaveAdaption && tt.bdVersion != "" {
				adaption := map[string]string{
					constant.BDVERSION: tt.bdVersion,
				}
				ctx.Set(constant.ADAPARAMS, adaption)
			}

			// 调用被测试的中间件（注意：这会调用controllers.Suggest，但在测试环境中不会有问题）
			CompatibleMiddleware(ctx)

			// 验证 intelligent_mode 设置 - 从 requestParams 中获取
			var requestParams map[string]string
			if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
				requestParams, _ = val.(map[string]string)
			}

			actualMode := ""
			if requestParams != nil {
				actualMode = requestParams["intelligent_mode"]
			}

			if actualMode != tt.expectedMode {
				t.Errorf("%s: expected intelligent_mode=%s, got=%s",
					tt.description, tt.expectedMode, actualMode)
			}

			// 如果compatible=1，验证其他参数是否正确设置
			if tt.compatible == "1" {
				if ctx.Request.FormValue("ctl") != "his" {
					t.Errorf("%s: expected ctl=his, got=%s",
						tt.description, ctx.Request.FormValue("ctl"))
				}
				if ctx.Request.FormValue("action") != "universal" {
					t.Errorf("%s: expected action=universal, got=%s",
						tt.description, ctx.Request.FormValue("action"))
				}
			}
		})
	}
}

func TestAddIntelligentNotice(t *testing.T) {
	tests := []struct {
		name             string
		intelligentMode  string
		hasRequestParams bool
		description      string
	}{
		{
			name:             "智能模式开启时调用函数",
			intelligentMode:  "1",
			hasRequestParams: true,
			description:      "当intelligent_mode为1时，函数应正常执行",
		},
		{
			name:             "智能模式关闭时调用函数",
			intelligentMode:  "0",
			hasRequestParams: true,
			description:      "当intelligent_mode为0时，函数应正常执行",
		},
		{
			name:             "智能模式为空时调用函数",
			intelligentMode:  "",
			hasRequestParams: true,
			description:      "当intelligent_mode为空时，函数应正常执行",
		},
		{
			name:             "智能模式为其他值时调用函数",
			intelligentMode:  "2",
			hasRequestParams: true,
			description:      "当intelligent_mode为其他值时，函数应正常执行",
		},
		{
			name:             "没有requestParams时调用函数",
			intelligentMode:  "",
			hasRequestParams: false,
			description:      "当没有requestParams时，函数应正常执行",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			ctx := createTestWebContext()

			// 设置 requestParams（如果需要）
			if tt.hasRequestParams {
				testRequestParams := make(map[string]string)
				if tt.intelligentMode != "" {
					testRequestParams["intelligent_mode"] = tt.intelligentMode
				}
				ctx.Set(constant.REQPARAMS, testRequestParams)
			}

			// 调用被测试的函数 - 验证不会 panic
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("%s: 函数执行时发生 panic: %v", tt.description, r)
				}
			}()

			addIntelligentNotice(ctx)

			// 验证函数成功执行（没有 panic）
			// 注意：由于 AddNotice 是内部日志方法，无法直接验证其调用结果
			// 这里主要验证函数的逻辑分支能够正常执行而不会出错
		})
	}
}
