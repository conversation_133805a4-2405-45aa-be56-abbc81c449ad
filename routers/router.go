package routers

import (
	"strings"

	"github.com/gin-gonic/gin"
	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"

	controllerV2 "icode.baidu.com/baidu/searchbox/go-suggest/api/handler"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/middleware"
	"icode.baidu.com/baidu/searchbox/go-suggest/controllers"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/middlewares"
)

// Init  web路由初始化
func Init(s *gdp.WebServer) {
	s.Use(middlewares.GinHandler2WebHandler(gin.Recovery()))
	s.Use(middlewares.RequestWare) // 优先注册RequestWare，读取sdel body参数

	gdp.HandlerMap["cookie"] = func(ctx *gdp.WebContext) string {
		cookies := ctx.Request.Cookies()
		vs := make([]string, 0, len(cookies))
		for _, c := range cookies {
			// 安全红线 http://security.baidu.com/ssp/web/#/require/redLine?id=34&from=page
			// 如下字段不可以输出到日志
			switch c.Name {
			case "BDUSS",
				"BDUSS_BFESS",
				"UUAP_P_TOKEN",
				"SECURE_UUAP_P_TOKEN":
				continue

			// 屏蔽大字段
			case "fnizebra_b",
				"GCONF_PARAMS",
				"BDSFRCVID_BFESS",
				"H_BDCLCKID_SF_BFESS",
				"FC_MODEL",
				"SCONF_PARAMS",
				"COOKIE_SESSION",
				"BA_HECTOR",
				"H_WISE_SIDS",
				"STDCJUVF",
				"BMAP_SECKEY",
				"SECKEY_ABVK",
				"H_BDCLCKID_SF",
				"BDPASSGATE":
				continue
			}

			vs = append(vs, c.String())
		}

		return strings.Join(vs, ";")
	}
	// 日志输出
	s.Use(gdp.ShoubaiTowerLogware)

	s.Use(middlewares.GinHandler2WebHandler(gin.Recovery()))
	s.Use(middlewares.AdaptionWare) // 添加结构化参数中间件(api/middleware)
	s.Use(middlewares.LogWare)
	s.Use(middlewares.Sample)
	s.Any("/", controllers.Index)
	s.Any("/sug", controllers.SugRewrite)
	s.Any("/suggest", controllers.Suggest)
	// 返回一个固定版本
	s.GET("/suggest/api/version", func(c *gdp.WebContext) { c.String(200, "%s", "3") })
	s.Any("/suggest/api/his/universal", middleware.StructuredParamsWare, CompatibleMiddleware, controllerV2.HisUniversal)
	s.GET("/suggest/api/his/aitool/feed", middleware.StructuredParamsWare, middleware.ValidateTokenMiddleware, controllerV2.FileSearchSTS)
	s.GET("/suggest/api/his/aitool/sts", middleware.StructuredParamsWare, middleware.ValidateTokenMiddleware, controllerV2.FileSearchSTS)
	s.POST("/suggest/api/his/aitool/sug", middleware.StructuredParamsWare, middleware.ValidateTokenMiddleware, controllerV2.FileSearchSug)
	s.POST("/suggest/api/his/aitool/validate", middleware.StructuredParamsWare, middleware.ValidateTokenMiddleware, controllerV2.FileSearchValidate)
	// 手百交互式搜索框架
	s.Any("/assistant/cmd", controllers.Assistant)

	// search-forecast-service
	s.Any("/rec", controllers.Sfs)

	s.Any("/private/pprof", controllers.Pprof)
}

// compatibleMiddleware 检查compatible参数，如果为1则路由到controllers.Suggest
func CompatibleMiddleware(ctx *gdp.WebContext) {
	if ctx.Request.FormValue("compatible") == "1" {
		// 处理 intelligent_mode 参数设置
		handleIntelligentMode(ctx)

		ctx.Request.Form.Set("ctl", "his")
		ctx.Request.Form.Set("action", "universal")
		controllers.Suggest(ctx)
		addIntelligentNotice(ctx)
		// 不调用 ctx.Next()，直接中断后续中间件和控制器的执行
		ctx.Abort()
	}
	ctx.Next()
}

func addIntelligentNotice(ctx *gdp.WebContext) {
	var requestParams map[string]string
	if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
		requestParams, _ = val.(map[string]string)
	}
	if requestParams["intelligent_mode"] == "1" {
		ctx.AddNotice("api", "his-universal-compatible-intelligent")
	} else {
		ctx.AddNotice("api", "his-universal-compatible")
	}
}

// handleIntelligentMode 处理 intelligent_mode 参数的设置逻辑
func handleIntelligentMode(ctx *gdp.WebContext) {
	// 获取 requestParams
	var requestParams map[string]string
	if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
		requestParams, _ = val.(map[string]string)
	}

	// 获取适配参数
	var adaption map[string]string
	if val, exists := ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}

	// 第一个逻辑：基于 header 和 compatible=1 设置 intelligent_mode
	intelligentHeader := ctx.Request.Header.Get("user-intelligent-mode")
	if intelligentHeader == "1" {
		requestParams["intelligent_mode"] = "1"
	} else {
		requestParams["intelligent_mode"] = "0"
	}

	// 第二个逻辑：对于低版本的兼容处理
	if len(adaption) > 0 && version.Compare(adaption[constant.BDVERSION], "15.17", "<") {
		requestParams["intelligent_mode"] = "1"
	}

	// 添加通知日志
	ctx.AddNotice("intelligent_mode", requestParams["intelligent_mode"])
}
