package main

import (
	"context"
	"flag"
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/conf"
	"icode.baidu.com/baidu/gdp/gdp/metrics"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/sample"
	gosug_modules "icode.baidu.com/baidu/searchbox/go-suggest/modules"
	"icode.baidu.com/baidu/searchbox/go-suggest/modules/mod_prison"
	"icode.baidu.com/baidu/searchbox/go-suggest/routers"
)

var port = flag.String("port", "8087", "listen port")

func main() {
	flag.Parse()
	app := gdp.App{}

	// 使用命令行参数指定端口，所以不直接使用Init
	appConf := "app.toml"
	config := &gdp.AppConfig{}
	if err := conf.ReadFile(appConf, config); err != nil {
		panic(fmt.Sprintf("Can't load config file %s: %s", appConf, err.Error()))
	}
	config.HTTPListen = "0.0.0.0:" + *port
	// 初始化App
	Init()
	app.InitWithConfig(config)
	// app.Init()
	confLoad()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	background.InitBackgroundTask(ctx)
	app.WebServer().RegisterRouter(routers.Init)
	(&metrics.HTTPServer{}).UseIt(app.WebServer())
	// 优先使用bfe的logid
	gdp.UseBFELogID = true

	// 设置ral的读写超时进行merge
	// ghttp.RalRWTimeoutMerge = true
	app.RunWebGraceServer()
}

func Init() {
	// 为兼容sdel接口ios content-type 错误，gdp.DefaultWebServerMiddlewares置为空，router.go调整注册顺序
	gdp.DefaultWebServerMiddlewares = []gdp.WebHandlerFunc{}
	gosug_modules.ModPrison = mod_prison.NewModulePrison()
	moduleInitErr := gosug_modules.ModPrison.Init("conf")
	if moduleInitErr != nil {
		panic(fmt.Sprintf("module prison init error: %s", moduleInitErr.Error()))
	}
}

func confLoad() {
	// 配置加载
	common.ConfLoaderExecute()
	sample.DegradeSwitchConfInit()
}
