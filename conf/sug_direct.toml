# 直达sug配置说明
# https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/76YVMWYfJM/7fnymOz3cf/40FE9mEJzU_tla


# 笔记sug直达
[note]
name_color = "#000"

tag = "笔记"
tagColor = "blue"
ubcSource = "note"
# 笔记icon
img = "https://search-operate.cdn.bcebos.com/36d6b1a41657bbe27a80b77473bf93b3.png"
imgDark = "https://search-operate.cdn.bcebos.com/614a94ff7e07052d4e1763aace8c4fd0.png"
imgNight = "https://search-operate.cdn.bcebos.com/63e548f5469c7fcc15a9b4479b2d0c9c.png"
# 跳转icon
btnIcon = "https://search-operate.cdn.bcebos.com/b7285257fcc2b4d8e0d7080147baee06.png"
btnIconDark = "https://search-operate.cdn.bcebos.com/70b80f0824c87cde93b92f78ee5af7f5.png"
btnIconNight = "https://search-operate.cdn.bcebos.com/dd9dbe2da1b4cca40b24d5feacdee597.png"

# 笔记icon，低版本黑色
imgOld = "https://search-operate.cdn.bcebos.com/752dcff6bc2bf566dea94d48837c165a.png"
imgDarkOld = "https://search-operate.cdn.bcebos.com/4016cd20d6185df23612863b3487373f.png"
imgNightOld = "https://search-operate.cdn.bcebos.com/ff1f51cd05ad919b5fb06c568faddfd5.png"
# 跳转icon，低版本黑色
btnIconOld = "https://search-operate.cdn.bcebos.com/fde57c2b0282c5cf03e5e548829b00c3.png"
btnIconDarkOld = "https://search-operate.cdn.bcebos.com/b04877d87c3fcfa5abb784105c3770bb.png"
btnIconNightOld = "https://search-operate.cdn.bcebos.com/a9fcb0d853a683bb13c51df7ae09303c.png"

# sug焕新
# 笔记icon
imgNew = "https://search-operate.cdn.bcebos.com/9937765b2d648faa4ae6b7dd42aa2372.png"
imgDarkNew = "https://search-operate.cdn.bcebos.com/9329133d09bf26adaf6d193a07596359.png"
imgNightNew = "https://search-operate.cdn.bcebos.com/ca9e06c3d9781f3e9db67f1d44a25935.png"
# 跳转icon
btnIconNew = "https://search-operate.cdn.bcebos.com/0b4aa45f2fea89005a2d4ac7f76b3687.png"
btnIconDarkNew = "https://search-operate.cdn.bcebos.com/9d34f138199b73e92a7f28e332abf484.png"
btnIconNightNew = "https://search-operate.cdn.bcebos.com/893d8b715b632f185d3fb5af1a3a7023.png"

[note.tag_style_list]
    tag_type = 1
    text = "笔记"
    text_color = "#FF3366FF"
    border_color = "#663366FF"
    bg_color = "#00FFFFFF"
    dark_text_color = "#FF3366FF"
    dark_border_color = "#803366FF"
    dark_bg_color = "#00000000"
    night_text_color = "#FF263678"
    night_border_color = "#80263678"
    night_bg_color = "#00000000"
# 焕新标签
[note.tag_style_new]
    tag_type = 1
    text = "笔记"
    text_color = "#FF4E6EF2"
    border_color = "#003366FF"
    bg_color = "#FFEDF1FC"
    dark_text_color = "#FF4E6EF2"
    dark_border_color = "#003366FF"
    dark_bg_color = "#1A4E6EF2"
    night_text_color = "#FF263678"
    night_border_color = "#00263678"
    night_bg_color = "#1A4E6EF2"


# 热
[hot]
btnKuang = 1
name_color = "#000"

[hot.tag_style]
    tag_type = 1
    text = "热"
    text_color = "#FFFFFFFF"
    border_color = "#7FFF6600"
    bg_color = "#FFFF6600"
    dark_text_color = "#FFFFFFFF"
    dark_border_color = "#7FFF6600"
    dark_bg_color = "#FFFF6600"
    night_text_color = "#80FFFFFF"
    night_border_color = "#803300"
    night_bg_color = "#803300"


# 新
[new]
btnKuang = 1
name_color = "#000"

[new.tag_style]
    tag_type = 1
    text = "新"
    text_color = "#FFFFFFFF"
    border_color = "#7FFF4267"
    bg_color = "#FFFF4267"
    dark_text_color = "#FFFFFFFF"
    dark_border_color = "#7FFF4267"
    dark_bg_color = "#FFFF4267"
    night_text_color = "#80FFFFFF"
    night_border_color = "#751919"
    night_bg_color = "#751919"


# 高考游戏直达
[24gaokao_game]
flag = "24gaokao_game"
his=1

brief = "最高得168元，新人立领2元"
btnKuang = 1
img = "https://search-operate.cdn.bcebos.com/5e5de3886410789a6bd5da6a25228e3f.png"
query="高考找红包"
tag = "小游戏"
tagColor="blue"
info = "" # 副标题右侧
ubcSource="24gaokao_game"

[[24gaokao_game.name]]
color="#000" # sug内容的颜色
text="高考找红包" # sug的内容


# 答题游戏直达
[question_game]
flag = "question_game"
his=1

brief = "答题提现88元"
btnKuang = 1
img = "https://gips1.baidu.com/it/u=1975601156,3471231197&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f119_120"
query="答题红包"
tag = "小游戏"
tagColor="blue"
info = "" # 副标题右侧
ubcSource="question_game"

[[question_game.name]]
color="#000" # sug内容的颜色
text="答题红包" # sug的内容


#品专sug直达
[pinzhuan]
name_color = "#000"

tag = "品牌推荐"
tagColor = "blue"
ubcSource = "pinzhuan"

[pinzhuan.tag_style_list]
    tag_type = 1
    text = "品牌推荐"
    text_color = "#FF3366FF"
    border_color = "#663366FF"
    bg_color = "#00FFFFFF"
    dark_text_color = "#FF3366FF"
    dark_border_color = "#803366FF"
    dark_bg_color = "#00000000"
    night_text_color = "#FF263678"
    night_border_color = "#80263678"
    night_bg_color = "#00000000"


# 电商sug
[shop]
btnKuang = 1 # 是否有上框按钮
name_color = "#000"

[shop.tag_style_list]
    tag_type = 1
    text = "优选"
    text_color = "#FFFD503E"
    border_color = "#66FD503E"
    bg_color = "#00FFFFFF"
    dark_text_color = "#FFFD503E"
    dark_border_color = "#80FD503E"
    dark_bg_color = "#00000000"
    night_text_color = "#FFFD503E"
    night_border_color = "#80FD503E"
    night_bg_color = "#00000000"
# 蓝色
[shop.tag_style_list_blue]
    tag_type = 1
    text = "优选"
    text_color = "#FF3366FF"
    border_color = "#663366FF"
    bg_color = "#00FFFFFF"
    dark_text_color = "#FF3366FF"
    dark_border_color = "#803366FF"
    dark_bg_color = "#00000000"
    night_text_color = "#FF263678"
    night_border_color = "#80263678"
    night_bg_color = "#00000000"


# AI助手sug直达
[aichat]
name_color = "#000"

tag = "AI助手"
tagColor = "blue"
# AI助手icon
img = "https://ai-chat.bj.bcebos.com/im_logo/normal_icon.png"
imgDark = "https://ai-chat.bj.bcebos.com/im_logo/ios_black_icon.png"
imgNight = "https://ai-chat.bj.bcebos.com/im_logo/android_black_icon.png"
# 跳转icon
btnIcon = "https://search-operate.cdn.bcebos.com/b7285257fcc2b4d8e0d7080147baee06.png"
btnIconDark = "https://search-operate.cdn.bcebos.com/70b80f0824c87cde93b92f78ee5af7f5.png"
btnIconNight = "https://search-operate.cdn.bcebos.com/dd9dbe2da1b4cca40b24d5feacdee597.png"

[aichat.tag_style]
    tag_type = 1
    text = "AI助手"
    text_color = "#FF6E4BFA"
    border_color = "#00FFFFFF"
    bg_color = "#176E4BFA"
    dark_text_color = "#FF6E4BFA"
    dark_border_color = "#00000000"
    dark_bg_color = "#176E4BFA"
    night_text_color = "#FF6E4BFA"
    night_border_color = "#00000000"
    night_bg_color = "#176E4BFA"


# 其他通用sug焕新标签tag_style配置
[other]
# 蓝色
[other.blue]
    tag_type = 1
    text_color = "#FF4E6EF2"
    border_color = "#003366FF"
    bg_color = "#FFEDF1FC"
    dark_text_color = "#FF4E6EF2"
    dark_border_color = "#003366FF"
    dark_bg_color = "#1A4E6EF2"
    night_text_color = "#FF263678"
    night_border_color = "#00263678"
    night_bg_color = "#1A4E6EF2"


#各业务sug图/标签干预 vec_str_raw字段获取
#具体格式参考这里：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/MYFIkxNl2o/iQgghcKAMO/MddaexZjGdHBpb#anchor-9091aac0-50df-11f0-8a70-ede36633b777

[sugConf]
# sugpk优先级，数字越大优先级越低
[sugConf.pklist]
    insert_interv = 1   # 运营干预
	hot_flow = 2        # 新热
    pinzhuan = 3        # 品专
	shop = 4            # 电商
	health = 5          # 医疗top sug

#默认配置（版本限制）
[sugConf.default]
[sugConf.default.versionControl.a0]
    max = ""
    min = "11.26.0.0"
[sugConf.default.versionControl.i0]
    max = ""
    min = "11.26.0.0"

#垂类自定义
#医疗的特殊配置,覆盖默认配置
[sugConf.health]
    flag = "health"
[sugConf.health.value]
    ubcSource = "health"
    btnKuang = 1
