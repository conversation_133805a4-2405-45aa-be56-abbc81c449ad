# Auto Generate By "gdp tov2" At 2021-02-23 16:50:52

# logger's config
# http://gdp.baidu-int.com/gdp2/docs/examples/12_log/

# 日志文件名称
FileName="{gdp.LogDir}/background/background.log"

# 日志切分规则,可选参数，默认为1hour
# 可选值和切分的文件后缀如下：
# 1hour -> .2020072714
# no 不切分
# 1day -> .20200727
# 1min -> .202007271452
# 5min -> .202007271450
# 10min -> .202007271450
# 30min -> .202007271430
# 若上述默认规则不满足，也可以自定义，详见 baidu/gdp/extension ：writer
RotateRule="1hour"

# 日志文件保留个数，可选参数
# 默认48个，若为-1，日志文件将不清理
MaxFileNum=60

# 日志异步队列大小，可选参数
# 默认值 4096，若为-1，则队列大小为0
BufferSize=4096

# 写磁盘的缓存大小，即 buffer 达到或接近此大小时，writer 会将 buffer 的数据写入磁盘（22年8月新增可配置）
# 当为 0 时使用默认值 4096，对于大多数磁盘，4k 可能是一个比较合适的值
# 对于文本日志，一般是按行写入磁盘的（尾部是\n）,所以每次 Flush 的时候，buffer的大小总是 不大于此值的
Alignment = 256

# 日志编码的对象池名称，可选参数
# 默认为 default_text（普通文本编码）
# 可选值：default_json，支持自定义
EncoderPool="default_text"

# 日志进入待写队列超时时间，毫秒
# 默认为0，不超时，若出现落盘慢的时候，调用写日志的地方会出现同步等待
WriterTimeout=50

# 日志分发规则
# 规则1：notice 和 trace 类型的日志，输出到默认的日志文件（{gdp.LogDir}/service/service.log）
[[Dispatch]]
FileSuffix=""
Levels=["NOTICE","TRACE"]

# 规则2：WARNING 和 ERROR 、FATAL类型的日志，输出到wf日志文件（{gdp.LogDir}/service/service.log.wf）
# 同一个日志等级，可以出现在多个规则中，如也可以将WARNING配置到上述规则1
[[Dispatch]]
FileSuffix=".wf"
Levels=["WARNING","ERROR","FATAL"]