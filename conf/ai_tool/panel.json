{"aiWriteV1": {"title": "AI写作", "titleUrl": "https://gips2.baidu.com/it/u=1910290038,2236141289&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f162_48", "searchBtnTxt": "创作", "hint": "帮我写一篇高中作文，文体是议论文，字数是800字", "items": [{"title": "类型", "template": "wordList", "category": "分类", "foldChild": 1, "data": [{"name": "作文", "prompt": "帮我写一篇小学作文，文体是议论文，字数是200字", "items": [{"title": "要求", "template": "wordList", "category": "文体", "editRules": [{"matchRegex": "(文体是)(事物|事理|叙事|抒情|哲理)?(议论?文?|记叙?文?|说明?文?|散文?)?", "replaceIndex": 3}], "data": [{"name": "议论文", "isSelect": 1, "prompt": "帮我写一篇小学作文，文体是议论文，字数是200字", "items": [{"title": "", "template": "wordList", "category": "教育阶段", "clause": "帮我写一篇%s作文，", "editRules": [{"matchRegex": "(写一篇)(小学?|初中?|高中?)?(作文)", "replaceIndex": 2}], "data": [{"name": "小学", "isSelect": 1}, {"name": "初中"}, {"name": "高中"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "200字"}, {"name": "300字"}, {"name": "500字"}, {"name": "800字", "isSelect": 1}]}]}, {"name": "记叙文", "prompt": "帮我写一篇小学作文，文体是记叙文，类型是记人，字数是200字", "items": [{"title": "", "template": "wordList", "category": "类型", "clause": "类型是%s，", "editRules": [{"matchRegex": "(类型是)(记人?|叙事?|写景?|状物?)?", "replaceIndex": 2}], "data": [{"name": "记人", "isSelect": 1}, {"name": "叙事"}, {"name": "写景"}, {"name": "状物"}]}, {"title": "", "template": "wordList", "category": "教育阶段", "clause": "帮我写一篇%s作文，", "editRules": [{"matchRegex": "(写一篇)(小学?|初中?|高中?)?(作文)", "replaceIndex": 2}], "data": [{"name": "小学", "isSelect": 1}, {"name": "初中"}, {"name": "高中"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "200字", "isSelect": 1}, {"name": "300字"}, {"name": "500字"}, {"name": "800字"}]}]}, {"name": "说明文", "prompt": "帮我写一篇小学作文，文体是事物说明文，说明顺序是空间，字数是200字", "items": [{"title": "", "template": "wordList", "category": "说明类型", "clause": "文体是%s说明文，", "editRules": [{"matchRegex": "(文体是)(事物|事理|事)?(说明?文?)?", "replaceIndex": 2}], "data": [{"name": "事物", "isSelect": 1}, {"name": "事理"}]}, {"title": "", "template": "wordList", "category": "说明顺序", "clause": "说明顺序是%s，", "editRules": [{"matchRegex": "(说明顺序是)(空间?|时间?|逻辑?)?", "replaceIndex": 2}], "data": [{"name": "空间", "isSelect": 1}, {"name": "时间"}, {"name": "逻辑"}]}, {"title": "", "template": "wordList", "category": "教育阶段", "clause": "帮我写一篇%s作文，", "editRules": [{"matchRegex": "(写一篇)(小学?|初中?|高中?)?(作文)", "replaceIndex": 2}], "data": [{"name": "小学", "isSelect": 1}, {"name": "初中"}, {"name": "高中"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "200字", "isSelect": 1}, {"name": "300字"}, {"name": "500字"}, {"name": "800字"}]}]}, {"name": "散文", "prompt": "帮我写一篇小学作文，文体是叙事散文，字数是200字", "items": [{"title": "", "template": "wordList", "category": "散文类型", "clause": "文体是%s散文，", "editRules": [{"matchRegex": "(文体是)(叙事?|抒情?|哲理?)?(散文?)?", "replaceIndex": 2}], "data": [{"name": "叙事", "isSelect": 1}, {"name": "抒情"}, {"name": "哲理"}]}, {"title": "", "template": "wordList", "category": "教育阶段", "clause": "帮我写一篇%s作文，", "editRules": [{"matchRegex": "(写一篇)(小学?|初中?|高中?)?(作文)", "replaceIndex": 2}], "data": [{"name": "小学", "isSelect": 1}, {"name": "初中"}, {"name": "高中"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "200字", "isSelect": 1}, {"name": "300字"}, {"name": "500字"}, {"name": "800字"}]}]}]}]}, {"name": "朋友圈文案", "prompt": "帮我写一篇朋友圈文案，内容关于旅游打卡，字数是40字", "items": [{"title": "要求", "template": "wordList", "category": "类型", "clause": "内容关于%s，", "editRules": [{"matchRegex": "(内容关于)(旅游?打?卡?|美食?分?享?|情感?抒?发?|家庭?记?录?|校园?生?活?|微商?推?广?)?", "replaceIndex": 2}], "data": [{"name": "旅游打卡", "isSelect": 1}, {"name": "美食分享"}, {"name": "情感抒发"}, {"name": "家庭记录"}, {"name": "校园生活"}, {"name": "微商推广"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "40字", "isSelect": 1}, {"name": "80字"}, {"name": "100字"}, {"name": "200字"}]}]}, {"name": "小红书文案", "prompt": "帮我写一篇小红书文案，内容关于美食攻略，字数是100字", "items": [{"title": "要求", "template": "wordList", "category": "类型", "clause": "内容关于%s，", "editRules": [{"matchRegex": "(内容关于)(美食?攻?略?|生活?分?享?|探店?种?草?|旅游?攻?略?|读书?心?得?)?", "replaceIndex": 2}], "data": [{"name": "美食攻略", "isSelect": 1}, {"name": "生活分享"}, {"name": "探店种草"}, {"name": "旅游攻略"}, {"name": "读书心得"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "100字", "isSelect": 1}, {"name": "200字"}, {"name": "300字"}, {"name": "500字"}]}]}, {"name": "祝福语", "prompt": "帮我写一段给老师的祝福语，字数是50字", "items": [{"title": "要求", "template": "wordList", "category": "祝福对象", "clause": "帮我写一段给%s的祝福语，", "editRules": [{"matchRegex": "(帮我写一段给)(同学|同事|同|老师?|领导?|长辈?|客户?|朋友?|情侣?)?(的祝?福?语?)?", "replaceIndex": 2}], "data": [{"name": "老师", "isSelect": 1}, {"name": "同学"}, {"name": "同事"}, {"name": "领导"}, {"name": "长辈"}, {"name": "客户"}, {"name": "朋友"}, {"name": "情侣"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "50字", "isSelect": 1}, {"name": "100字"}, {"name": "200字"}, {"name": "400字"}]}]}, {"name": "评语", "prompt": "帮我写一段中文评语，字数是50字", "items": [{"title": "要求", "template": "wordList", "category": "语言", "clause": "帮我写一段%s评语，", "editRules": [{"matchRegex": "(帮我写一段)(中文?|英文?)?(评语?)?", "replaceIndex": 2}], "data": [{"name": "中文", "isSelect": 1}, {"name": "英文"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "50字", "isSelect": 1}, {"name": "100字"}, {"name": "200字"}, {"name": "500字"}]}]}, {"name": "日记", "prompt": "帮我写一篇日记，字数是100字", "items": [{"title": "要求", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "100字", "isSelect": 1}, {"name": "200字"}, {"name": "500字"}, {"name": "1000字"}]}]}, {"name": "汇报总结", "prompt": "帮我写一篇汇报总结，内容关于转正述职，字数是1000字", "items": [{"title": "要求", "template": "wordList", "category": "类型", "clause": "内容关于%s，", "editRules": [{"matchRegex": "(内容关于)(转正?述?职?|竞聘?述?职?|课题?汇?报?)?", "replaceIndex": 2}], "data": [{"name": "转正述职", "isSelect": 1}, {"name": "竞聘述职"}, {"name": "课题汇报"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "1000字", "isSelect": 1}, {"name": "2000字"}, {"name": "4000字"}, {"name": "10000字"}]}]}, {"name": "邮件", "prompt": "帮我写一封中文邮件，字数是100字", "items": [{"title": "要求", "template": "wordList", "category": "语言", "clause": "帮我写一封%s邮件，", "editRules": [{"matchRegex": "(帮我写一封)(中文?|英文?)?(邮件?)?", "replaceIndex": 2}], "data": [{"name": "中文", "isSelect": 1}, {"name": "英文"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "100字", "isSelect": 1}, {"name": "200字"}, {"name": "400字"}, {"name": "1000字"}]}]}, {"name": "诗歌", "prompt": "帮我写一首诗歌，体裁是通用诗句，字数是50字", "items": [{"title": "要求", "template": "wordList", "category": "类型", "clause": "体裁是%s，", "editRules": [{"matchRegex": "(体裁是)(五言律诗|五言绝句|五言绝|五言律|五言?律?诗?|五言?绝?句?|通用?诗?句?|七言律诗|七言绝句|七言绝|七言律|七言?律?诗?|七言?绝?句?|藏头?诗?|现代?诗?)?", "replaceIndex": 2}], "data": [{"name": "通用诗句", "isSelect": 1}, {"name": "五言律诗"}, {"name": "五言绝句"}, {"name": "七言律诗"}, {"name": "七言绝句"}, {"name": "藏头诗"}, {"name": "现代诗"}]}, {"title": "", "template": "wordList", "category": "字数", "clause": "字数是%s，", "editRules": [{"matchRegex": "(字数是|字数为|字数)(\\d+字?)?", "replaceIndex": 2}], "data": [{"name": "50字", "isSelect": 1}, {"name": "100字"}, {"name": "200字"}, {"name": "300字"}, {"name": "400字"}]}]}, {"name": "计划", "prompt": "帮我制定一个工作计划，目标是", "items": [{"title": "要求", "template": "wordList", "category": "字数", "clause": "帮我制定一个%s，目标是", "editRules": [{"matchRegex": "(制定一个)(工作?计?划?|学习?计?划?|健身?计?划?|理财?计?划?)?", "replaceIndex": 2}], "data": [{"name": "工作计划", "isSelect": 1}, {"name": "学习计划"}, {"name": "健身计划"}, {"name": "理财计划"}]}]}]}]}, "aiDrawV1": {"hint": "帮我设计一个头像，主体是情侣，形象为戴墨镜，带着微笑的表情，背景是海滩，风格是卡通", "title": "AI画图", "titleUrl": "https://gips3.baidu.com/it/u=2273662168,325343441&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f162_48", "searchBtnTxt": "创作", "items": [{"title": "一键同款", "template": "imageList", "category": "同款", "mutexSelectCategory": ["分类"], "data": [{"name": "打工头像", "prompt": "画一幅图：萌萌的Q版华妃娘娘坐在办公电脑前，头戴镶金边墨镜，樱桃小嘴高高撅起，手里捧着一杯冒热气的焦糖玛奇朵，远处打过来一束阳光，画面平静祥和", "url": "https://gips3.baidu.com/it/u=3217072956,400795086&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "名画恶搞", "prompt": "画一幅图：对维米尔(Vermeer)的“戴珍珠耳环的女孩”的迷人而有趣的黄化玄凤鹦鹉模仿。一只可爱的带腮红黄化玄凤鹦鹉，睁大而富有表现力的眼睛，戴着蓝色和黄色的头巾，戴着闪亮的耳环。背景黑暗而简单，突出了玄凤的脸。风格:古典油画风格，具有戏剧性的灯光和柔和的纹理，但带有喜剧玄凤的风格", "url": "https://gips3.baidu.com/it/u=3400764118,322353205&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "宠物自拍", "prompt": "画一幅图：请画一张及其平凡无奇的自拍照，没有明确的主体和构图，类似于随手拍的快照。照片略带运动模糊，呈现出轻微的曝光过度。角度奇怪，构图杂乱，呈现出慵懒感。主角是两只兔子，背景是夜晚的巴黎铁塔，兔子可以长大嘴巴表示欢乐欢呼", "url": "https://gips1.baidu.com/it/u=3232887215,2249616030&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "搞怪CP", "prompt": "画一幅图：孙悟空和林黛玉在古色古香的贾府大院内相拥，孙悟空身着鎏金锁子甲，黛玉一袭月白纱裙站在桥上，红色的衣袍被风吹起，参考红楼梦和西游记造型，背景有在飘落粉红花瓣的树木。工笔重彩与奇幻元素融合，8K超写实渲染，景深层次分明", "url": "https://gips2.baidu.com/it/u=2852711377,2203205498&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "情侣头像", "prompt": "画一幅图：一张上下两宫格的卡通风格头像插画。上方画面：一位穿着黄白拼接连帽卫衣的男孩，棕色短发蓬松自然。面带灿烂笑容，脸颊微红，他左手拿着一个橙色橘子。下方画面：一位穿着黄白拼接连帽卫衣扎着双麻花辫的女孩，头发为棕色，辫子末端用绿色发圈绑住。面带灿烂笑容，脸颊微红，她右手拿着一个橙色的橘子", "url": "https://gips0.baidu.com/it/u=649688356,3482453839&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "复古像素", "prompt": "画一幅图：像素风格插画，一个日式动漫风留长发的女孩子穿着黑色系kitty吊带坐在桌子前的电竞椅上横屏拿着iPad玩着游戏，桌子上放着一台台式电脑和机械键盘鼠标，打开的饮料零食，电脑上放着动漫。背景有一个温馨可爱的床，主机设备等多装饰物，女孩带着耳机，窗外繁星点点可以看到城市轮廓，天色朦胧昏暗月光照着，旁边有一只布偶猫，整体氛围孤寂略显忧郁", "url": "https://gips0.baidu.com/it/u=2146874149,3243437885&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "婚礼插画", "prompt": "画一幅图：动漫插画风格，极简风格，抽象派风格，高美感，线条型设计，可爱卡通人物，新娘穿着一件白色的婚纱，手持一束白色的花束，头戴白色面纱。新郎则身着一套黑色西装，搭配领结。两人站在画面中央，面向前方，脸上带着甜蜜的笑容", "url": "https://gips3.baidu.com/it/u=1119273440,1079784301&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f886_1182"}, {"name": "3D玩偶", "prompt": "画一幅画：3D卡通玩偶穿着白色带粉色耳朵的毛绒帽衫，帽子上的粉色按钮和小熊耳饰增添了可爱感。粉色的发型和大眼睛为她的造型增色不少，整件服装有卡通人物风格，脚穿白色和红色的运动鞋，鞋底设计独特", "url": "https://gips2.baidu.com/it/u=432603246,243539449&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f912_1216"}, {"name": "动漫奇幻", "prompt": "画一幅画：三只羊驼戴着彩色护目镜，披着紫色和粉色的飞行披风，飞行在天空中，它们飞过一堆五彩斑斓的枕头，周围是淡蓝色的云朵和盛开的花朵", "url": "https://gips2.baidu.com/it/u=2376513158,1644049053&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f912_1216"}, {"name": "印象画作", "prompt": "画一幅画：画面中女子的衣裙和周围背景融为一体，色彩斑斓的细节让人联想到印象派画作，整体氛围温暖而梦幻", "url": "https://gips2.baidu.com/it/u=2580797906,981688231&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f912_1216"}, {"name": "街头霓虹", "prompt": "画一幅画：画面中的人物面部和身体被霓虹线条覆盖，勾画出细致的骨骼结构，穿着黑色皮夹克，脖部挂着链条，眼镜是黑色的，带有浓烈的街头艺术感", "url": "https://gips2.baidu.com/it/u=3679745515,676738355&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f912_1216"}, {"name": "暗黑魔幻", "prompt": "画一幅画：画面中，女性的面容冷峻，背景模糊，而她手中的剑上反射出令人不寒而栗的鬼面。剑柄华丽精致，显示出其珍贵和威慑力。她的眼睛直视前方，仿佛正在准备迎接一场决定性的战斗", "url": "https://gips2.baidu.com/it/u=422324939,2476278469&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f912_1216"}]}, {"title": "一键创作", "template": "wordList", "category": "分类", "mutexSelectCategory": ["同款"], "data": [{"name": "头像", "prompt": "帮我设计一个头像，主体是男生，形象为戴墨镜，带着微笑的表情，背景是海滩，风格是卡通", "items": [{"title": "要求", "template": "wordList", "category": "主体", "clause": "主体是%s，", "editRules": [{"matchRegex": "(主体是)(一对?情?侣?|男生?|女生?|猫咪?|狗狗?)?", "replaceIndex": 2}], "data": [{"name": "男生", "isSelect": 1}, {"name": "女生"}, {"name": "猫咪"}, {"name": "狗狗"}, {"name": "一对情侣"}]}, {"title": "", "template": "wordList", "category": "形象", "clause": "形象为%s，", "editRules": [{"matchRegex": "(形象为)(穿皮衣|穿正装|穿正|穿皮|穿|戴墨?镜?|浪漫?卷?发?|短发?|听音?乐?)?", "replaceIndex": 2}], "data": [{"name": "戴墨镜", "isSelect": 1}, {"name": "浪漫卷发"}, {"name": "穿皮衣"}, {"name": "短发"}, {"name": "穿正装"}, {"name": "听音乐"}]}, {"title": "", "template": "wordList", "category": "姿态", "clause": "带着%s的表情，", "editRules": [{"matchRegex": "(带着)(微笑?|卖萌?|邪魅?一?笑?|委屈?|忧郁?|睥睨?天?下?|贱兮?兮?)?(的表情)", "replaceIndex": 2}], "data": [{"name": "微笑", "isSelect": 1}, {"name": "卖萌"}, {"name": "委屈"}, {"name": "忧郁"}, {"name": "邪魅一笑"}, {"name": "睥睨天下"}, {"name": "贱兮兮"}]}, {"title": "", "template": "wordList", "category": "背景", "clause": "背景是%s，", "editRules": [{"matchRegex": "(背景是)(海滩?|未来?城?市?|渐变?|霓虹?光?斑?|田野?|草坪?|樱花?树?|铁塔?|星空?|校园?|蝴蝶?飞?舞?)?", "replaceIndex": 2}], "data": [{"name": "海滩", "isSelect": 1}, {"name": "田野"}, {"name": "樱花树"}, {"name": "铁塔"}, {"name": "未来城市"}, {"name": "渐变"}, {"name": "霓虹光斑"}, {"name": "草坪"}, {"name": "星空"}, {"name": "校园"}, {"name": "蝴蝶飞舞"}]}, {"title": "", "template": "wordList", "category": "风格", "clause": "风格是%s，", "editRules": [{"matchRegex": "(风格是)(卡通?|手绘?|Q版?|写真?|3D?玩?偶?|复古?像?素?|粘土?)?", "replaceIndex": 2}], "data": [{"name": "卡通", "isSelect": 1}, {"name": "写真"}, {"name": "复古像素"}, {"name": "3D玩偶"}, {"name": "手绘"}, {"name": "Q版"}, {"name": "粘土"}]}]}, {"name": "人像", "prompt": "帮我创作一张人像，形象为机甲战士在仰望，背景是海滩，风格是赛博朋克", "items": [{"title": "要求", "template": "wordList", "category": "形象", "clause": "形象为%s，", "editRules": [{"matchRegex": "(形象为)(少女|少年|少|小男孩|小女孩|小男?|小女?|机甲?战?士?|帅哥?|御姐?)?", "replaceIndex": 2}], "data": [{"name": "机甲战士", "isSelect": 1}, {"name": "少女"}, {"name": "少年"}, {"name": "御姐"}, {"name": "帅哥"}, {"name": "小男孩"}, {"name": "小女孩"}]}, {"title": "", "template": "wordList", "category": "姿态", "clause": "在%s，", "editRules": [{"matchRegex": "(在)(仰望?|RA?P?|飞行?|闻花?香?|晒太?阳?|办公?|做饭?|运动?|玩音?乐?|拍照?|沉思?|打篮?球?|微笑?)?", "replaceIndex": 2}], "data": [{"name": "仰望", "isSelect": 1}, {"name": "玩音乐"}, {"name": "拍照"}, {"name": "微笑"}, {"name": "RAP"}, {"name": "飞行"}, {"name": "闻花香"}, {"name": "晒太阳"}, {"name": "沉思"}, {"name": "打篮球"}, {"name": "办公"}, {"name": "做饭"}, {"name": "运动"}]}, {"title": "", "template": "wordList", "category": "背景", "clause": "背景是%s，", "editRules": [{"matchRegex": "(背景是)(未来?城?市?|海滩?|渐变?|霓虹?光?斑?|田野?|草坪?|樱花?树?|铁塔?|星空?|校园?|蝴蝶?飞?舞?)?", "replaceIndex": 2}], "data": [{"name": "海滩", "isSelect": 1}, {"name": "田野"}, {"name": "樱花树"}, {"name": "铁塔"}, {"name": "未来城市"}, {"name": "渐变"}, {"name": "霓虹光斑"}, {"name": "草坪"}, {"name": "星空"}, {"name": "校园"}, {"name": "蝴蝶飞舞"}]}, {"title": "", "template": "wordList", "category": "风格", "clause": "风格是%s，", "editRules": [{"matchRegex": "(风格是)(赛博?朋?克?|写实?照?片?|复古?胶?片?|艺术?摄?影?|多重?曝?光?)?", "replaceIndex": 2}], "data": [{"name": "赛博朋克", "isSelect": 1}, {"name": "写实照片"}, {"name": "复古胶片"}, {"name": "多重曝光"}, {"name": "艺术摄影"}]}]}, {"name": "手抄报", "prompt": "帮我画一幅手抄报，主题是学雷锋", "items": [{"title": "要求", "template": "wordList", "category": "主题", "clause": "帮我画一幅手抄报，主题是%s，", "editRules": [{"matchRegex": "(主题是)(学雷?锋?|诗歌?赏?析?|读书?|爱国?|安全?教?育?|传统?文?化?|保护?环?境?|英语?学?习?|科技?创?新?)?", "replaceIndex": 2}], "data": [{"name": "学雷锋", "isSelect": 1}, {"name": "诗歌赏析"}, {"name": "读书"}, {"name": "爱国"}, {"name": "安全教育"}, {"name": "传统文化"}, {"name": "保护环境"}, {"name": "英语学习"}, {"name": "科技创新"}]}]}, {"name": "儿童画", "prompt": "帮我画一幅儿童画，主题是四季风景，画风是水彩画", "items": [{"title": "要求", "template": "wordList", "category": "主题", "clause": "主题是%s，", "editRules": [{"matchRegex": "(主题是)(四季?风?景?|学校?生?活?|家务?劳?动?|环保?|公益?|野生?动?物?|传统?节?日?|秋收?|我的?祖?国?)?", "replaceIndex": 2}], "data": [{"name": "四季风景", "isSelect": 1}, {"name": "学校生活"}, {"name": "家务劳动"}, {"name": "环保"}, {"name": "公益"}, {"name": "野生动物"}, {"name": "传统节日"}, {"name": "秋收"}]}, {"title": "", "template": "wordList", "category": "风格", "clause": "画风是%s画，", "editRules": [{"matchRegex": "(画风是)(水彩?|蜡笔?|简笔?|素描?)?画?", "replaceIndex": 2}], "data": [{"name": "水彩", "isSelect": 1}, {"name": "蜡笔"}, {"name": "简笔"}, {"name": "素描"}]}]}]}]}, "aiTourV0": {"title": "AI出游", "titleUrl": "https://gips3.baidu.com/it/u=2452666211,1369383154&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f162_48", "searchBtnTxt": "规划", "hint": "帮我制定一个亲子出游计划，我想去漂流玩水，天数为1天，人均预算不限", "items": [{"title": "类型", "template": "wordList", "category": "分类", "foldChild": 1, "data": [{"name": "旅行盲盒", "prompt": "帮我制定一个亲子出游计划，我想去漂流玩水，天数为1天，人均预算不限，我的出发地是", "items": [{"name": "要求", "template": "wordList", "category": "偏好", "clause": "我想去%s，", "editRules": [{"matchRegex": "(我想去)(漂流?玩?水?|避暑?胜?地?|草原?观?光?|海岛?海?滨?|小众?目?的?地?|户外?徒?步?|郊野?露?营?|境外?免?签?地?|山川?湖?泊?|古城?古?镇?|大西?北?|5A?景?点?|文化?古?迹?|主题?乐?园?)?", "replaceIndex": 2}], "data": [{"name": "漂流玩水", "isSelect": 1}, {"name": "避暑胜地"}, {"name": "草原观光"}, {"name": "海岛海滨"}, {"name": "小众目的地"}, {"name": "户外徒步"}, {"name": "郊野露营"}, {"name": "境外免签地"}, {"name": "山川湖泊"}, {"name": "古城古镇"}, {"name": "大西北"}, {"name": "5A景点"}, {"name": "文化古迹"}, {"name": "主题乐园"}]}, {"title": "", "template": "wordList", "category": "画像", "clause": "制定一个%s出游计划", "editRules": [{"matchRegex": "(制定一个)(亲子?|情侣?|朋友?|一个?人?|带老?人?)?(出游?计?划?)?", "replaceIndex": 2}], "data": [{"name": "亲子", "isSelect": 1}, {"name": "情侣"}, {"name": "朋友"}, {"name": "一个人"}, {"name": "带老人"}]}, {"name": "", "template": "wordList", "category": "天数", "clause": "天数为%s，", "editRules": [{"matchRegex": "(天数为)(\\d+天|\\d+天?|2天?|3天?|4天?|5天?|6天?|7天?)?", "replaceIndex": 2}], "data": [{"name": "1天", "isSelect": 1}, {"name": "3天"}, {"name": "5天"}, {"name": "7天"}]}, {"name": "", "template": "wordList", "category": "预算", "clause": "人均预算%s，", "editRules": [{"matchRegex": "(人均预算)(不限?|\\d+十?百?千?万?元?|一?二?三?四?五?六?七?八?九?十?百?千?万?元?)?", "replaceIndex": 2}], "data": [{"name": "不限", "isSelect": 1}, {"name": "1000元"}, {"name": "3000元"}, {"name": "5000元"}, {"name": "7000元"}, {"name": "1万元"}, {"name": "2万元"}]}]}, {"name": "周边短途", "prompt": "帮我制定一个周边短途出游计划，出游偏好为户外徒步，目的地不限，天数为1天", "items": [{"name": "要求", "template": "wordList", "category": "偏好", "clause": "出游偏好为%s，", "editRules": [{"matchRegex": "(出游偏好为)(户外?徒?步?|郊野?露?营?|悠闲?度?假?|特种?兵?打?卡?|文化?探?寻?|城市?漫?游?|美食?之?旅?)?", "replaceIndex": 2}], "data": [{"name": "户外徒步", "isSelect": 1}, {"name": "郊野露营"}, {"name": "悠闲度假"}, {"name": "特种兵打卡"}, {"name": "文化探寻"}, {"name": "城市漫游"}, {"name": "美食之旅"}]}, {"name": "", "template": "wordList", "category": "目的地", "clause": "目的地%s，", "editRules": [{"matchRegex": "(目的地)(不限?|北京?|上海?|广州?|深圳?|昆明?|西安?|成都?|杭州?|南京?|重庆?|青岛?|长沙?|武汉?)?", "replaceIndex": 2}], "data": [{"name": "不限", "isSelect": 1}, {"name": "北京"}, {"name": "上海"}, {"name": "广州"}, {"name": "深圳"}, {"name": "昆明"}, {"name": "西安"}, {"name": "成都"}, {"name": "杭州"}, {"name": "南京"}, {"name": "重庆"}, {"name": "青岛"}, {"name": "长沙"}, {"name": "武汉"}]}, {"name": "", "template": "wordList", "category": "天数", "clause": "天数为%s，", "editRules": [{"matchRegex": "(天数为)(\\d+天|\\d+天?|2天?|3天?|4天?|5天?|6天?|7天?)?", "replaceIndex": 2}], "data": [{"name": "1天", "isSelect": 1}, {"name": "2天"}, {"name": "3天"}]}]}, {"name": "国内长途", "prompt": "帮我制定一个国内长途出游计划，出游偏好为悠闲度假，目的地不限，天数为4天，人均预算不限", "items": [{"name": "要求", "template": "wordList", "category": "偏好", "clause": "出游偏好为%s，", "editRules": [{"matchRegex": "(出游偏好为)(悠闲?度?假?|特种?兵?打?卡?|文化?探?寻?|城市?漫?游?|接触?自?然?|美食?之?旅?)?", "replaceIndex": 2}], "data": [{"name": "悠闲度假", "isSelect": 1}, {"name": "特种兵打卡"}, {"name": "文化探寻"}, {"name": "城市漫游"}, {"name": "接触自然"}, {"name": "美食之旅"}]}, {"name": "", "template": "wordList", "category": "目的地", "clause": "目的地%s，", "editRules": [{"matchRegex": "(目的地)(不限?|北京?|上海?|广东?|江苏?|浙江?|云南?|新疆?|东北?|内蒙?古?|海南?|四川?|西藏?|青海?|甘肃?)?", "replaceIndex": 2}], "data": [{"name": "不限", "isSelect": 1}, {"name": "北京"}, {"name": "上海"}, {"name": "广东"}, {"name": "江苏"}, {"name": "浙江"}, {"name": "云南"}, {"name": "新疆"}, {"name": "东北"}, {"name": "内蒙古"}, {"name": "海南"}, {"name": "四川"}, {"name": "西藏"}, {"name": "青海"}, {"name": "甘肃"}]}, {"name": "", "template": "wordList", "category": "天数", "clause": "天数为%s，", "editRules": [{"matchRegex": "(天数为)(\\d+天|\\d+天?|2天?|3天?|4天?|5天?|6天?|7天?)?", "replaceIndex": 2}], "data": [{"name": "4天", "isSelect": 1}, {"name": "5天"}, {"name": "6天"}, {"name": "7天"}]}, {"name": "", "template": "wordList", "category": "预算", "clause": "人均预算%s，", "editRules": [{"matchRegex": "(人均预算)(不限?|\\d+十?百?千?万?元?|一?二?三?四?五?六?七?八?九?十?百?千?万?元?)?", "replaceIndex": 2}], "data": [{"name": "不限", "isSelect": 1}, {"name": "3000元"}, {"name": "5000元"}, {"name": "7000元"}, {"name": "1万元"}, {"name": "2万元"}]}]}, {"name": "境外游", "prompt": "帮我制定一个境外出游计划，介绍出入境要求，目的地免签地，出游偏好为悠闲度假，天数为3天，人均预算不限", "items": [{"name": "", "template": "wordList", "category": "目的地", "clause": "目的地%s，", "editRules": [{"matchRegex": "(目的地)(中国香港|中国澳门|中国香|中国澳|中国?|澳大?利?亚?|免签?地?|日本?|韩国?|东南?亚?|美国?|英国?)?", "replaceIndex": 2}], "data": [{"name": "免签地", "isSelect": 1}, {"name": "中国香港"}, {"name": "中国澳门"}, {"name": "日本"}, {"name": "韩国"}, {"name": "东南亚"}, {"name": "美国"}, {"name": "澳大利亚"}, {"name": "英国"}]}, {"name": "要求", "template": "wordList", "category": "偏好", "clause": "出游偏好为%s，", "editRules": [{"matchRegex": "(出游偏好为)(悠闲?度?假?|特种?兵?打?卡?|文化?探?寻?|城市?漫?游?|接触?自?然?|美食?之?旅?)?", "replaceIndex": 2}], "data": [{"name": "悠闲度假", "isSelect": 1}, {"name": "特种兵打卡"}, {"name": "文化探寻"}, {"name": "城市漫游"}, {"name": "接触自然"}, {"name": "美食之旅"}]}, {"name": "", "template": "wordList", "category": "天数", "clause": "天数为%s，", "editRules": [{"matchRegex": "(天数为)(\\d+天|\\d+天?|3天?|4天?|5天?|6天?|7天?)?", "replaceIndex": 2}], "data": [{"name": "3天", "isSelect": 1}, {"name": "4天"}, {"name": "5天"}, {"name": "6天"}, {"name": "7天"}]}, {"name": "", "template": "wordList", "category": "预算", "clause": "人均预算%s，", "editRules": [{"matchRegex": "(人均预算)(不限?|\\d+十?百?千?万?元?|一?二?三?四?五?六?七?八?九?十?百?千?万?元?)?", "replaceIndex": 2}], "data": [{"name": "不限", "isSelect": 1}, {"name": "3000元"}, {"name": "5000元"}, {"name": "7000元"}, {"name": "1万元"}, {"name": "2万元"}]}]}]}]}, "transV0": {"title": "AI翻译", "titleUrl": "https://gips1.baidu.com/it/u=561362217,2312616309&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f162_48", "searchBtnTxt": "翻译", "hint": "请输入你要翻译的内容，如passion", "searchHint": "passion", "linkOption": 0, "attachment": {"image": 1, "file": 1}, "camera": {"show": 1, "schema": "baiduboxapp://imageSearch/imagesearch?params=%7B%22from%22%3A%22hongmengaifanyi%22%2C%22imageSearch_type%22%3A%22TRANSLATE%22%2C%22imageSearch_subtype%22%3A%22TRANSLATE%22%7D"}, "voice": {"show": 0}, "items": [{"title": "", "template": "translateOptions", "data": [{"name": "来源语言", "category": "来源语言", "highlightSelect": 0, "persistent": 1, "k": "from", "clause": "", "subMenu": [{"name": "自动检测", "v": "auto", "isSelect": 1}, {"name": "中文", "v": "zh"}, {"name": "英语", "v": "en"}, {"name": "日语", "v": "jp"}, {"name": "韩语", "v": "kor"}, {"name": "葡萄牙语", "v": "pt"}, {"name": "法语", "v": "fra"}, {"name": "西班牙语", "v": "spa"}, {"name": "德语", "v": "de"}, {"name": "意大利语", "v": "it"}, {"name": "俄语", "v": "ru"}]}, {"name": "目标语言", "category": "目标语言", "clause": "", "highlightSelect": 0, "persistent": 1, "k": "to", "subMenu": [{"name": "中文", "v": "zh", "isSelect": 1}, {"name": "英语", "v": "en"}, {"name": "日语", "v": "jp"}, {"name": "韩语", "v": "kor"}, {"name": "葡萄牙语", "v": "pt"}, {"name": "法语", "v": "fra"}, {"name": "西班牙语", "v": "spa"}, {"name": "德语", "v": "de"}, {"name": "意大利语", "v": "it"}, {"name": "俄语", "v": "ru"}]}]}]}}