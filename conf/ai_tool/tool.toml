[tools]

[tools.aiWriteV0]
schema = "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fchat.baidu.com%2Fsearch%3Fpd%3Dcsaitab%26word%3D%25E7%2599%25BE%25E5%25BA%25A6AI%25E5%258A%25A9%25E6%2589%258B%26sa%3D%26extParams%3Dword%253D%2525E7%252599%2525BE%2525E5%2525BA%2525A6AI%2525E5%25258A%2525A9%2525E6%252589%25258B%2526searchBoxPanel%253Dai_write%2526selectedId%253DERINE-3.5%2526fullFeature%253Dtrue%26cs_ext_params%3Dword%253D%2525E7%252599%2525BE%2525E5%2525BA%2525A6AI%2525E5%25258A%2525A9%2525E6%252589%25258B%2526searchBoxPanel%253Dai_write%2526selectedId%253DERINE-3.5%2526fullFeature%253Dtrue%26enterType%3Dhis_write%26isShowHello%3D1"
icon = "https://gips1.baidu.com/it/u=3858750662,4172614868&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
iconDark = "https://gips1.baidu.com/it/u=49253036,2444796483&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
iconNight = "https://gips0.baidu.com/it/u=3557248075,365143793&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
text = "AI写作"
type = "aiWrite"
back = 1
panelVer = 1

[tools.aiWriteV1]
schema = ""
icon = "https://gips3.baidu.com/it/u=2885346907,4237275847&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips3.baidu.com/it/u=2686113405,1236958519&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips0.baidu.com/it/u=1649003509,4164116081&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI写作"
type = "aiWrite"
back = 1
panelVer = 3

[tools.aiDrawV0]
schema = "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fchat.baidu.com%2Fsearch%3Fpd%3Dcsaitab%26word%3D%25E7%2599%25BE%25E5%25BA%25A6AI%25E5%258A%25A9%25E6%2589%258B%26sa%3D%26extParams%3Dword%253D%2525E7%252599%2525BE%2525E5%2525BA%2525A6AI%2525E5%25258A%2525A9%2525E6%252589%25258B%2526searchBoxPanel%253Dai_paint%2526selectedId%253DERINE-3.5%2526fullFeature%253Dtrue%26cs_ext_params%3Dword%253D%2525E7%252599%2525BE%2525E5%2525BA%2525A6AI%2525E5%25258A%2525A9%2525E6%252589%25258B%2526searchBoxPanel%253Dai_paint%2526selectedId%253DERINE-3.5%2526fullFeature%253Dtrue%26enterType%3Dhis_pic%26isShowHello%3D1"
icon = "https://gips2.baidu.com/it/u=1027313035,4027161117&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
iconDark = "https://gips3.baidu.com/it/u=2606757869,3455542299&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
iconNight = "https://gips0.baidu.com/it/u=2254141098,313957922&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
text = "AI画图"
type = "aiDraw"
back = 1
panelVer = 1

[tools.aiDrawV1]
schema = ""
icon = "https://gips1.baidu.com/it/u=59634705,584860811&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips0.baidu.com/it/u=2897775369,530446978&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips3.baidu.com/it/u=971600155,1842986481&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI画图"
type = "aiDraw"
back = 1
panelVer = 4

[tools.aiQuesV0]
schema = "baiduboxapp://imageSearch/imagesearch?&params=%7B%22imageSearch_type%22%3A%22QUESTION%22%2C%22imageSearch_subtype%22%3A%22QUESTION%22%2C%22client_type%22%3A%22BDBOX%22%2C%22from%22%3A%22his_searchnew%22%7D"
icon = "https://gips3.baidu.com/it/u=2035948442,1327753236&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
iconDark = "https://gips1.baidu.com/it/u=3028029351,1893428368&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
iconNight = "https://gips0.baidu.com/it/u=3485187497,1672736763&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f54_55"
text = "AI解题"
type = "aiQues"
back = 1
panelVer = 1

[tools.aiQuesV1]
schema = "baiduboxapp://imageSearch/imagesearch?&params=%7B%22imageSearch_type%22%3A%22QUESTION%22%2C%22imageSearch_subtype%22%3A%22QUESTION%22%2C%22client_type%22%3A%22BDBOX%22%2C%22from%22%3A%22his_searchnew%22%7D"
icon = "https://gips0.baidu.com/it/u=77615205,1104613208&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips3.baidu.com/it/u=916132780,1829742082&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips2.baidu.com/it/u=87177905,535440972&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI解题"
type = "aiQues"
back = 1
panelVer = 2

[tools.scanV0]
schema = "baiduboxapp://imageSearch/imagesearch?&params=%7B%22imageSearch_type%22%3A%22CHARS%22%2C%22imageSearch_subtype%22%3A%22CHARS%22%2C%22client_type%22%3A%22BDBOX%22%2C%22from%22%3A%22his_searchnew%22%7D"
icon = "https://gips2.baidu.com/it/u=1693782365,1536814763&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f55_55"
iconDark = "https://gips3.baidu.com/it/u=587262883,1491221220&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f55_55"
iconNight = "https://gips1.baidu.com/it/u=1060077492,2798624887&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f55_55"
text = "扫描王"
type = "scan"
back = 1
panelVer = 1

[tools.aiTourV0]
schema = ""
icon = "https://gips1.baidu.com/it/u=1279414728,3262110472&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips3.baidu.com/it/u=2422196158,2691297648&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips1.baidu.com/it/u=450579818,1741450455&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI出游"
type = "aiTour"
back = 1
panelVer = 5


[tools.aiChatV0]
schema = "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fyuexia.baidu.com%2Famuse%2Ftheater%2F%3Ffromsource%3Daisearch%26hasBackBtn%3D1%26SA%3Daib_aichat&notShowLandingTopBar=1&needSafeArea=1&bottomBarType=5"
icon = "https://gips1.baidu.com/it/u=331264059,302617976&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips2.baidu.com/it/u=2638337546,2632354860&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips2.baidu.com/it/u=3532106095,1360151958&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI聊伴"
type = "aiChat"
back = 1
panelVer = 1

[tools.picArtV0]
schema = "baiduboxapp://imageSearch/imagesearch?params=%7B%22from%22%3A%22aib_picart%22%2C%22imageSearch_type%22%3A%22AI_CREATION%22%7D"
icon = "https://gips1.baidu.com/it/u=3772816053,710626346&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips2.baidu.com/it/u=2015833219,1419836389&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips0.baidu.com/it/u=3424192040,2824401589&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI修图"
type = "picArt"
back = 1
panelVer = 1

[tools.musicV0]
schema = "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dai%25E9%259F%25B3%25E4%25B9%2590%26sa%3Daib_music&newbrowser=1&forbidautorotate=1&notShowLandingTopBar=1&bottomBarType=4"
icon = "https://gips1.baidu.com/it/u=2234309142,3173411287&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips3.baidu.com/it/u=4027666866,1875575561&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips3.baidu.com/it/u=335961137,2119853243&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI音乐"
type = "music"
back = 1
panelVer = 1

[tools.callV0]
schema = ""
icon = "https://gips2.baidu.com/it/u=952701137,2111489262&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48"
iconDark = "https://gips2.baidu.com/it/u=1476924199,3453484266&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48"
iconNight = "https://gips3.baidu.com/it/u=3269690855,1077631341&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48"
text = "视频通话"
type = "aiCall"
login = 1
back = 1
panelVer = 1

[tools.videoV0]
schema = "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dai%25E8%25A7%2586%25E9%25A2%2591%26sid%3D661632%26sa%3Daib_aivideo"
icon = "https://gips2.baidu.com/it/u=1654704112,1756971650&fm=3028&app=3028&f=JPEG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips2.baidu.com/it/u=4040968088,856755663&fm=3028&app=3028&f=JPEG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips0.baidu.com/it/u=767387371,4064628804&fm=3028&app=3028&f=JPEG&fmt=auto&q=75&size=f48_48"
text = "生成视频"
type = "aiVideo"
login = 1
back = 1
panelVer = 1

[tools.transV0]
schema = ""
icon = "https://gips3.baidu.com/it/u=2442736423,3675248092&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconDark = "https://gips1.baidu.com/it/u=2264096317,2309176034&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
iconNight = "https://gips2.baidu.com/it/u=2122816205,3223251387&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_48"
text = "AI翻译"
type = "trans"
back = 1
panelVer = 2

# 新增工具版本请务必升序排列数组
[toolVer]
[[toolVer.i0]]
appVer = "15.3" # 手百版本最低对应的版本号，如果下一个数组是 15.15，客户端版本是 15.13则会使用该项对应的 tools
tools = ["aiWriteV1", "aiDrawV1",  "aiTourV0", "aiQuesV1"] #对应的工具列表，请注意顺序很重要，端上会按照该顺序展示工具
incognitoTools =["aiWriteV1", "aiDrawV1",  "aiTourV0", "aiQuesV1"]
# 15.18-20 AI解题、AI聊伴、修图出片、AI写作、AI画图、AI出游、AI音乐
[[toolVer.i0]]
appVer = "15.18" # 手百版本最低对应的版本号，如果下一个数组是 15.15，客户端版本是 15.13则会使用该项对应的 tools
tools = ["videoV0","aiQuesV1", "aiChatV0",  "picArtV0", "musicV0", "aiWriteV1", "aiDrawV1", "aiTourV0"] #对应的工具列表，请注意顺序很重要，端上会按照该顺序展示工具
incognitoTools = ["videoV0","aiQuesV1", "aiChatV0",  "picArtV0", "musicV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]

[[toolVer.i0]]
appVer = "15.20" # 手百版本最低对应的版本号，如果下一个数组是 15.15，客户端版本是 15.13则会使用该项对应的 tools
tools = ["callV0" ,"videoV0" ,"aiQuesV1", "aiChatV0", "musicV0", "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"] #对应的工具列表，请注意顺序很重要，端上会按照该顺序展示工具
incognitoTools = ["callV0" ,"videoV0" ,"aiQuesV1", "aiChatV0","musicV0" , "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]

[[toolVer.i0]]
appVer = "15.25" # 增加AI翻译工具，兜底第三位展示
tools = ["callV0", "videoV0", "transV0", "aiQuesV1", "aiChatV0", "musicV0", "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"] #对应的工具列表，请注意顺序很重要，端上会按照该顺序展示工具
incognitoTools = ["callV0", "videoV0", "transV0", "aiQuesV1", "aiChatV0", "musicV0", "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]

[[toolVer.a0]]
appVer = "15.3"
tools = ["aiWriteV1", "aiDrawV1",  "aiTourV0", "aiQuesV1"]
incognitoTools = ["aiWriteV1", "aiDrawV1",  "aiTourV0", "aiQuesV1"]

[[toolVer.a0]]
appVer = "15.18"
tools = ["videoV0","aiQuesV1", "aiChatV0",  "picArtV0", "musicV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]
incognitoTools = ["videoV0","aiQuesV1", "aiChatV0",  "picArtV0", "musicV0","aiWriteV1", "aiDrawV1", "aiTourV0"]

[[toolVer.a0]]
appVer = "15.20"
tools = ["callV0", "videoV0","aiQuesV1", "aiChatV0", "musicV0" , "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]
incognitoTools = ["callV0", "videoV0","aiQuesV1", "aiChatV0", "musicV0", "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]

[[toolVer.a0]]
appVer = "15.25"
tools = ["callV0", "videoV0", "transV0", "aiQuesV1", "aiChatV0", "musicV0", "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]
incognitoTools = ["callV0", "videoV0", "transV0", "aiQuesV1", "aiChatV0", "musicV0", "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]

[[toolVer.h0]]
appVer = "15.3"
tools = ["aiWriteV1", "aiDrawV1",  "aiTourV0", "aiQuesV1"]
incognitoTools = ["aiWriteV1", "aiDrawV1",  "aiTourV0", "aiQuesV1"]

[[toolVer.h0]]
appVer = "15.24"
tools =  ["aiQuesV1", "aiChatV0",  "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]
incognitoTools = ["aiQuesV1", "aiChatV0",  "picArtV0", "aiWriteV1", "aiDrawV1", "aiTourV0"]



[[toolVer.default]]
appVer = "1.0"
tools = ["aiWriteV0", "aiDrawV0", "aiQuesV0", "scanV0"]
incognitoTools = ["aiWriteV0", "aiDrawV0", "aiQuesV0", "scanV0"]


