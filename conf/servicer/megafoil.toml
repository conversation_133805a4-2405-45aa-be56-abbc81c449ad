# Service的名字，必选
Name = "megafoil"

## 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 连接超时
ConnTimeOut = 50
# 写数据超时
WriteTimeOut = 150
# 读数据超时
ReadTimeOut = 100
# 请求失败后的重试次数：总请求次数 = Retry + 1
Retry = 1

# HTTP 协议专有
[Headers]

# 资源使用策略，非必选，默认使用 RoundRobin
[Strategy]
#RoundRobin-依次轮询
#Random-随机
#LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"
[Resource.BNS]
BNSName = "group.smartbns-from_product=default%group.flow-rec-data-access-wise.www.all"
PortKey = "main"

# 资源定位：手动配置 - 使用 Host、端口
# 当没有IDC能匹配的时候，会使用 default
#[Resource.Manual]
#[[Resource.Manual.default]]
#Host = "************"
#Port = 8010