# Auto Generate

# 下游服务配置
# http://gdp.baidu-int.com/gdp2/docs/examples/30_servicer/

# 下游服务的名字，必填，必须和文件名保持一致
Name = "afd_server"

# 自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选

# 连接超时，单位毫秒
ConnTimeOut = 50

# 写数据超时，单位毫秒
WriteTimeOut = 50

# 读数据超时，单位毫秒
ReadTimeOut = 150

# 请求失败后的重试次数：总请求次数 = Retry + 1，默认0
Retry = 1

# 资源使用/负载均衡策略，非必选，默认使用 RoundRobin
# 可选 RoundRobin:依次轮询 Random:随机 LocalityAware ：la加权轮询，需要策略配置
[Strategy]
Name="Random"

# 以下为资源定位配置,必须有,且只有一项

# 资源定位：使用 BNS
[Resource.BNS]
# 支持普通的bns、smartbns、meshbns
BNSName = "group.smartbns-from_product=wisens-other%group.flow-afdrouter.feedafd.all"
#[Resource.Manual]
#[[Resource.Manual.default]]
#Host = "***********"  # proxy 的 host
#Port = 8044        # proxy 的 port
