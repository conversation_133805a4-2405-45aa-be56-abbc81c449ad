# Service的名字，必选，需自定义修改
Name = "redis_mircochannel"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 100
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 200
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 200
Retry = 1
[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
[Resource.BNS]
BNSName = "group.bdrp-microchannel-subnum-proxy.redis.all"

#[Resource.Manual]
#[[Resource.Manual.default]]
#Host = "mygo.bcc-gzbh.baidu.com"
#Port = 8379

[Redis]
# DB 可选
DB = 0
# PoolSizePerIP 每个实例pool连接容量，可选
PoolSizePerIP = 10
# MinIdleConnsPerIP 每个实例pool初始连接个数，可选
MinIdleConnsPerIP = 5
#CmdArgsLogLen打印cmd命令长度:0不打印,-1全部打印
CmdArgsLogLen = -1