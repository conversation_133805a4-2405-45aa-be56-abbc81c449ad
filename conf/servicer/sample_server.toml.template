# Auto Generate By "gdp tov2" At 2021-02-23 16:50:52

# 下游服务配置
# http://gdp.baidu-int.com/gdp2/docs/examples/30_servicer/
# 原配置文件路径：conf/ral/services/sug_his_rec.toml

# 下游服务的名字，必填，必须和文件名保持一致
Name = "sample_server"

# 以下自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用了Manual，则ConnTimeOut、WriteTimeOut、ReadTimeOut、Retry 需要配置
# 使用其他方式，如BNS，上述则ConnTimeOut等参数默认会从 BNS config 中读取，
# 若配置了则会导致 BNS config 中的值不生效

# 可选，连接超时，单位毫秒，默认5000
ConnTimeOut = 100

# 可选，写数据超时，单位毫秒，默认5000ms
WriteTimeOut = 150

# 可选，读数据超时，单位毫秒，默认5000
ReadTimeOut = 2000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1。，默认0
Retry = 2

# 资源使用/负载均衡策略，非必选，默认使用 RoundRobin
# 可选 RoundRobin:依次轮询 Random:随机 LocalityAware ：la加权轮询，需要策略配置
[Strategy]
Name="RoundRobin"

# 以下为资源定位配置,必须有,且只有一项

# 资源定位：使用 BNS
[Resource.BNS]
BNSName = "<$sample_servers$>"
UsePort = "main"

# 资源定位：手动配置 - 使用IP、端口
#[Resource.Manual]
#[[Resource.Manual.default]]
#Host = "*************"
#Port = 7000