his = [
    "atadd",
    "list",
    "sdel",
    "tcate",
    "setpri",
    "ispri",
    "rec",
    "board",
    "universal",
    "discover",
    "float",
]

error = "index"

[sug]
#action未缺省但是key不在名单里 使用Union作为默认值
__default = "union"
#主SUG action缺省key时使用sug服务
sug = "sug"
######六合TAB 共用一个pageservice
open = [
    "app",
    "video",
    "wenku",
    "baipin",
    "image_content",
    "notes",
    "userlist",
    "topic_search_new",
]

[chillin_sug_route]
chilladv = 'adv/setfold'
chillapi_noveloff = 'api/noveloff'
chillspreset = 'spreset/getlist'
chillsug = 'sug/suggest'