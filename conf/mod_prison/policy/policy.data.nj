{"config": {"wise": [{"Name": "wise_yqna_z<PERSON>s_ios", "Cond": "req_query_key_in(\"premode\") && !req_ua_regmatch(\"Android\")", "accessSignConf": {"url": false, "path": false, "query": [], "header": [], "Cookie": []}, "action": {"cmd": "FINISH", "params": []}, "checkPeriod": 1, "stayPeriod": 1, "threshold": 14, "accessDictSize": 10000, "prisonDictSize": 10000}, {"Name": "wise_yqna_z<PERSON>s_android", "Cond": "req_query_key_in(\"premode\") && req_ua_regmatch(\"Android\")", "accessSignConf": {"url": false, "path": false, "query": [], "header": [], "Cookie": []}, "action": {"cmd": "FINISH", "params": []}, "checkPeriod": 1, "stayPeriod": 1, "threshold": 23, "accessDictSize": 10000, "prisonDictSize": 10000}]}, "version": "1.0"}