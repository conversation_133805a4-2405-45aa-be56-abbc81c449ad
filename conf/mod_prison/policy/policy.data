{"config": {"wiseoff": [{"Name": "wise_yqna_z<PERSON>s_ios", "Cond": "req_query_key_in(\"premode\") && !req_ua_regmatch(\"Android\")", "accessSignConf": {"url": false, "path": false, "query": [], "header": [], "Cookie": []}, "action": {"cmd": "FINISH", "params": []}, "checkPeriod": 1, "stayPeriod": 10, "threshold": 5, "accessDictSize": 100000, "prisonDictSize": 100000}, {"Name": "wise_yqna_z<PERSON>s_android", "Cond": "req_query_key_in(\"premode\") && req_ua_regmatch(\"Android\")", "accessSignConf": {"url": false, "path": false, "query": [], "header": [], "Cookie": []}, "action": {"cmd": "FINISH", "params": []}, "checkPeriod": 1, "stayPeriod": 10, "threshold": 5, "accessDictSize": 100000, "prisonDictSize": 100000}]}, "version": "1.0"}