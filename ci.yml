Global:
  version: 2.0

Default:
  profile: [x86_64_agent, aarch64_agent]
  adjustArtifacts:
    enable: true
    storeStrategy: SEPARATE
    artifactsProfile: [x86_64_agent]

Profiles:
  - profile:
    name: x86_64_agent
    mode: AGENT
    environment:
      cluster: DECK_CENTOS6U3_K3
    build:
      command: make -f Makefile
    artifacts:
      release: true
      platform:
        arch: X86_64   # (必填) 产出对应架构，可选值为X86_64、AARCH64、SW_64
        os: LINUX      # (必填) 产出对应操作系统，可选值为LINUX、WINDOWS、DARWIN

  - profile:
    name: aarch64_agent
    mode: AGENT
    environment:
      cluster: INF_ARM_AMPERE_7U6
    build:
      command: make -f Makefile
    artifacts:
      release: true
      platform:
        arch: AARCH64   # (必填) 产出对应架构，可选值为X86_64、AARCH64、SW_64
        os: LINUX      # (必填) 产出对应操作系统，可选值为LINUX、WINDOWS、DARWIN

