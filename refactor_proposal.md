# FileSearch Handler 重构建议

## 问题分析

当前的 `FileSearchValidate` 和 `FileSearchSug` handler 承担了过多的业务逻辑，违反了单一职责原则。主要问题包括：

### 1. Handler层职责过重
- **FileSearchValidate**: 166行代码，包含复杂的文件验证、风控检查、数据转换等逻辑
- **FileSearchSug**: 包含词条选择、响应构造等业务逻辑
- Handler应该只负责HTTP请求/响应处理，不应包含复杂业务逻辑

### 2. 代码重复和耦合度高
- 多个service的直接调用和协调
- 大量的数据转换逻辑散布在handler中
- 错误处理逻辑重复

## 重构方案

### 1. 创建专门的FileSearchBusinessService

将复杂的业务逻辑从handler移动到新的service层：

```go
// api/service/filesearch_business.go
type FileSearchBusinessService interface {
    // 文件验证业务逻辑
    ValidateFiles(req *FileSearchValidateRequest, sessionToken string, uid string) (*FileSearchValidateResponse, error)
    
    // 文件搜索建议业务逻辑  
    GenerateFileSuggestions(req *FileSearchSugRequest, uid string) (*FileSearchSugResponse, error)
}
```

### 2. Handler层简化

Handler只负责：
- 请求参数验证和解析
- 调用business service
- 返回响应

```go
func FileSearchValidate(ctx *gdp.WebContext) {
    // 1. 参数验证
    reqParams := entity.GetRequestParams(ctx)
    if reqParams == nil {
        ctx.JSON(200, responseInvalidRequestBody(ctx))
        return
    }
    
    var req FileSearchValidateRequest
    if err := ctx.BindJSON(&req); err != nil {
        ctx.JSON(200, responseInvalidRequestBody(ctx))
        return
    }
    
    // 2. 调用business service
    businessService := service.NewFileSearchBusinessService(ctx)
    response, err := businessService.ValidateFiles(&req, ctx.Query("stk"), reqParams.UID)
    if err != nil {
        ctx.JSON(200, responseInternalError(ctx))
        return
    }
    
    // 3. 返回响应
    ctx.JSON(200, response)
}
```

### 3. 业务逻辑分层

#### FileSearchBusinessService 负责：
- 文件验证流程编排
- 多个service的协调调用
- 业务规则实现
- 数据转换和映射

#### 现有Service保持职责单一：
- **BosService**: 文件存储相关操作
- **RiskService**: 风控检查
- **FileSearchService**: Redis数据操作
- **AiToolService**: AI工具相关

### 4. 具体重构步骤

#### 步骤1: 创建FileSearchBusinessService
```go
type fileSearchBusinessServiceImpl struct {
    ctx               *gdp.WebContext
    bosService        BosService
    riskService       RiskService
    fileSearchService FileSearchService
    aiToolService     AiToolService
}

func (s *fileSearchBusinessServiceImpl) ValidateFiles(req *FileSearchValidateRequest, sessionToken string, uid string) (*FileSearchValidateResponse, error) {
    // 1. 文件路径验证
    validatedFiles, riskRequestFiles := s.validateFilePaths(req.File, uid)
    
    // 2. 风控检查
    riskResult, err := s.riskService.CheckRisk(riskRequestFiles)
    if err != nil {
        return s.buildErrorResponse(503, "failed"), nil
    }
    
    // 3. 处理风控结果
    fileInfos := s.processRiskResults(riskRequestFiles, riskResult, &validatedFiles)
    
    // 4. 存储文件信息到Redis
    err = s.fileSearchService.SetFileInfo(fileInfos, sessionToken)
    if err != nil {
        s.handleRedisError(&validatedFiles)
    }
    
    // 5. 构造响应
    return s.buildValidateResponse(validatedFiles), nil
}
```

#### 步骤2: 提取辅助方法
```go
func (s *fileSearchBusinessServiceImpl) validateFilePaths(files []FileItem, uid string) ([]FileValidationResult, []service.RiskRequestFile) {
    // 文件路径验证逻辑
}

func (s *fileSearchBusinessServiceImpl) processRiskResults(riskFiles []service.RiskRequestFile, riskResult service.RiskResult, validatedFiles *[]FileValidationResult) []service.FileSearchInfo {
    // 风控结果处理逻辑
}

func (s *fileSearchBusinessServiceImpl) buildValidateResponse(validatedFiles []FileValidationResult) *FileSearchValidateResponse {
    // 响应构造逻辑
}
```

#### 步骤3: 重构FileSearchSug
```go
func (s *fileSearchBusinessServiceImpl) GenerateFileSuggestions(req *FileSearchSugRequest, uid string) (*FileSearchSugResponse, error) {
    // 1. 确定建议词条和默认框
    sugWords, defaultBox := s.determineSuggestionWords(req.File)
    
    // 2. 生成AI工具信息（如果需要）
    fileTool := s.generateFileSearchTool(req.File, uid)
    
    // 3. 随机选择词条
    selectedWords := s.selectRandomWords(sugWords, 10)
    
    // 4. 构造建议项
    sugItems := s.buildSuggestionItems(selectedWords)
    
    // 5. 构造响应
    return s.buildSugResponse(req.Word, defaultBox, fileTool, sugItems), nil
}
```

### 5. 优势

1. **职责清晰**: Handler只处理HTTP层，Business Service处理业务逻辑
2. **可测试性**: 业务逻辑可以独立测试
3. **可维护性**: 业务逻辑集中，易于修改和扩展
4. **可复用性**: Business Service可以被其他handler复用
5. **错误处理**: 统一的错误处理和日志记录

### 6. 迁移策略

1. 先创建FileSearchBusinessService接口和实现
2. 逐步将handler中的业务逻辑移动到business service
3. 简化handler代码
4. 添加单元测试
5. 验证功能正确性后删除旧代码

这样的重构将使代码结构更清晰，职责更明确，同时提高代码的可测试性和可维护性。
