#!/bin/shell
basepath=$(cd `dirname $0`; pwd)
instance_path="${basepath}/.."

# phpui_log.sh
#bash ${basepath}/backuplog.sh -D 01 -S 2 -P ${instance_path}/log/service -F service.log,service.log.wf -T h -B ${instance_path}/log/log_bak/ -X 1;
#bash ${basepath}/backuplog.sh -D 01 -S 2 -P ${instance_path}/log/ral -F ral.log,ral.log.wf,ral-worker.log,ral-worker.log.wf -T h -B ${instance_path}/log/log_bak/ -X 1
#bash ${basepath}/backuplog.sh -D 01 -S 2 -P ${instance_path}/log/index -F index.log,index.log.wf -T h -B ${instance_path}/log/log_bak/ -X 1

