// Copyright (c) 2019 The BFE Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package mod_prison

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"path/filepath"
	"time"

	"github.com/baidu/go-lib/log"
	"github.com/baidu/go-lib/web-monitor/metrics"
	"github.com/bfenetworks/bfe/bfe_basic"
	"github.com/bfenetworks/bfe/bfe_http"
	"github.com/bfenetworks/bfe/bfe_module"
	"icode.baidu.com/baidu/gdp/gdp"
)

var (
	openDebug = false
)

const (
	ModPrison        = "mod_prison"
	ReqCtxPrisonInfo = "mod_prison.prison_info"
)

var (
	ErrPrison = errors.New("PRISON") // deny by mod_prison
)

type ModulePrisonState struct {
	AllChecked *metrics.Counter // count of checked requests
	AllPrison  *metrics.Counter // count of blocked requests
}

type PrisonInfo struct {
	PrisonType string    // type of prison, mod_prison
	PrisonName string    // name of prison rule
	FreeTime   time.Time // free time
	IsExpired  bool      // is expired
	Action     string    // action
}

type ModulePrison struct {
	name            string            // name of module
	state           ModulePrisonState // module state
	metrics         metrics.Metrics
	productConfPath string            // path for prodct rule
	productTable    *productRuleTable // product rule table
	ctx             *gdp.WebContext
}

func NewModulePrison() *ModulePrison {
	m := new(ModulePrison)
	m.name = ModPrison
	m.metrics.Init(&m.state, ModPrison, 0)
	m.productTable = newProductRuleTable()
	return m
}

func (m *ModulePrison) SetCtx(ctx *gdp.WebContext) {
	m.ctx = ctx
}

func (m *ModulePrison) Name() string {
	return m.name
}

func (m *ModulePrison) PrisonHandler(product string, req *http.Request) (
	int, *bfe_http.Response) {
	//http request => bfe_basic.Request
	bfe_http_req := bfe_http.Request{}
	bfe_http_req.RequestURI = req.RequestURI
	bfe_http_req.URL = req.URL
	bfe_http_req.RemoteAddr = req.RemoteAddr
	bfe_http_req.Host = req.Host
	bfe_http_req.Header = bfe_http.Header(req.Header)
	bfe_http_req.PostForm = req.PostForm
	bfe_http_req.Method = req.Method

	bfe_req := bfe_basic.NewRequest(&bfe_http_req, nil, nil, nil, nil)
	bfe_req.Session = &bfe_basic.Session{}
	// process global prison rules
	ret, res := m.processProductRules(bfe_req, bfe_basic.GlobalProduct)
	//m.ctx.AddNotice("cachedKeys", bfe_req.CachedQuery())
	if ret != bfe_module.BfeHandlerGoOn {
		return ret, res
	}

	// process product prison rules
	ret, res = m.processProductRules(bfe_req, product)
	return ret, res
}

func (m *ModulePrison) processProductRules(req *bfe_basic.Request, product string) (int, *bfe_http.Response) {
	rules, ok := m.productTable.getRules(product)
	if !ok {
		//m.ctx.WarningF("product[%s] without prison rules, pass", product)
		return bfe_module.BfeHandlerGoOn, nil
	}
	return m.processRules(req, rules)
}

func (m *ModulePrison) processRules(req *bfe_basic.Request, rules *prisonRules) (int, *bfe_http.Response) {
	for _, rule := range rules.ruleList {
		if !rule.cond.Match(req) {
			continue
		}
		if rule.ctx == nil {
			rule.SetCtx(m.ctx)
		}
		m.state.AllChecked.Inc(1)
		m.ctx.AddNotice("beforeRecord", rule.GetAccessCount(req))
		rule.recordAndCheck(req)

		if rule.GetAccessCount(req) >= rule.threshold {
			return bfe_module.BfeHandlerFinish, nil
		}
		/*if !rule.recordAndCheck(req) {
			continue
		}
		m.ctx.AddNotice("afterRecord", rule.GetAccessCount(req))
		m.ctx.AddNotice("rule.action.cmd", rule.action.Cmd)
		m.state.AllPrison.Inc(1)
		switch rule.action.Cmd {
		case action.ActionClose:
			req.ErrCode = ErrPrison
			return bfe_module.BfeHandlerClose, nil
		case action.ActionFinish:
			req.ErrCode = ErrPrison
			return bfe_module.BfeHandlerFinish, nil
		default:
			rule.action.Do(req)
		}*/
	}

	return bfe_module.BfeHandlerGoOn, nil
}

func (m *ModulePrison) loadProductRuleTable(query url.Values) (string, error) {
	// get reload file path
	path := query.Get("path")
	if path == "" {
		path = m.productConfPath // use default
	}

	// load and update rules
	productConf, err := productRuleConfLoad(path)
	if err != nil {
		return "", fmt.Errorf("%s: load product rule err %s", m.name, err.Error())
	}
	if err = m.productTable.load(productConf); err != nil {
		return "", fmt.Errorf("%s: load prison err %s", m.name, err.Error())
	}

	version := *productConf.Version
	_, fileName := filepath.Split(path)
	return fmt.Sprintf("%s=%s", fileName, version), nil
}

func (m *ModulePrison) Init(cr string) error {
	// load module config
	confPath := bfe_module.ModConfPath(cr, m.name)
	conf, err := ConfLoad(confPath, cr)
	if err != nil {
		return log.Logger.Warn(fmt.Errorf("%s.Init():load conf err %s", m.name, err.Error()))
	}
	m.productConfPath = conf.Basic.ProductRulePath
	openDebug = conf.Log.OpenDebug

	// load product rule table
	if _, err := m.loadProductRuleTable(nil); err != nil {
		return log.Logger.Warn(fmt.Errorf("%s.Init():loadProductRuleTable(): %s", m.name, err.Error()))
	}
	return nil
}
