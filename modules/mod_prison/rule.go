// Copyright (c) 2019 The BFE Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package mod_prison

import (
	"fmt"
	"regexp"
	"time"

	"github.com/baidu/go-lib/log"
	"github.com/baidu/go-lib/lru_cache"
	"github.com/bfenetworks/bfe/bfe_basic"
	"github.com/bfenetworks/bfe/bfe_basic/action"
	"github.com/bfenetworks/bfe/bfe_basic/condition"
	"icode.baidu.com/baidu/gdp/gdp"
)

type prisonRule struct {
	name           string              // rule name
	cond           condition.Condition // condition parsed
	condStr        string              // condition string
	action         action.Action       // action
	accessSigner   AccessSigner        // access signer
	checkPeriodNs  int64               // check period in nanoSeconds
	stayPeriodNs   int64               // stay period in nanoSeconds
	threshold      int32               // threshold period
	accessDict     *lru_cache.LRUCache // dict store access info
	prisonDict     *lru_cache.LRUCache // dict store prison info
	accessDictSize int                 // access dict size
	prisonDictSize int                 // prison dict size
	ctx            *gdp.WebContext
}

func (r *prisonRule) GetAccessCount(req *bfe_basic.Request) int32 {
	// get sign
	sign, err := r.accessSigner.Sign(r.condStr, req)
	if err != nil {
		return 0
	}
	value, ok := r.accessDict.Get(sign)
	if !ok {
		return 0
	}

	f := value.(*AccessCounter)
	count := f.GetCount()
	r.ctx.AddNotice("count", count)
	return count

}

func (r *prisonRule) SetCtx(ctx *gdp.WebContext) {
	r.ctx = ctx
}

func newPrisonRule(ruleConf PrisonRuleConf) (*prisonRule, error) {
	// build condition
	cond, err := condition.Build(*ruleConf.Cond)
	if err != nil {
		return nil, fmt.Errorf("build condition err: condStr[%s], err[%s]", *ruleConf.Cond, err)
	}

	regExp, _ := regexp.Compile(ruleConf.AccessSignConf.UrlRegexp)

	// create new rule
	rule := new(prisonRule)
	rule.cond = cond
	rule.condStr = *ruleConf.Cond
	rule.action = *ruleConf.Action
	rule.name = *ruleConf.Name
	rule.accessSigner = AccessSigner{
		AccessSignConf: *ruleConf.AccessSignConf,
		UrlReg:         regExp,
	}
	rule.checkPeriodNs = *ruleConf.CheckPeriod * 1e9
	rule.stayPeriodNs = *ruleConf.StayPeriod * 1e9
	rule.threshold = *ruleConf.Threshold
	rule.accessDictSize = *ruleConf.AccessDictSize
	rule.prisonDictSize = *ruleConf.PrisonDictSize

	return rule, nil
}

func (r *prisonRule) GetName() string {
	return r.name
}

func (r *prisonRule) GetCondCstr() string {
	return r.condStr
}

func (r *prisonRule) initDict(oldRule *prisonRule) {
	if oldRule == nil {
		// if oldRule is nil, create new dict
		r.accessDict = lru_cache.NewLRUCache(r.accessDictSize)
		r.prisonDict = lru_cache.NewLRUCache(r.prisonDictSize)
	} else {
		// use old dict instead
		r.accessDict = oldRule.accessDict
		r.prisonDict = oldRule.prisonDict

		// resize dict
		r.accessDict.EnlargeCapacity(r.accessDictSize)
		r.prisonDict.EnlargeCapacity(r.prisonDictSize)
	}
}

func (r *prisonRule) recordAndCheck(req *bfe_basic.Request) bool {
	if openDebug {
		log.Logger.Debug("begin process rule %s", r.name)
	}

	// get sign
	sign, err := r.accessSigner.Sign(r.condStr, req)
	if err != nil {
		r.ctx.AddNotice("signerr", err.Error())
		return false
	}
	// check whether the access should be denied directyly
	/*var deny bool
	deny = r.shouldDeny(sign, req)
	r.ctx.AddNotice("shouldDeny1", deny)
	if deny {
		return deny
	}*/

	// record and check
	deny := r.recordAccess(sign)
	//deny = r.shouldDeny(sign, req)
	//r.ctx.AddNotice("shouldDeny2", deny)
	return deny
}

func (r *prisonRule) recordAccess(sign AccessSign) bool {
	var f *AccessCounter

	// check access dict
	value, ok := r.accessDict.Get(sign)
	if !ok {
		f = NewAccessCounter()
		r.accessDict.Add(sign, f)
	} else {
		f = value.(*AccessCounter)
	}
	// check threshod
	if block, _ := f.IncAndCheck(r.checkPeriodNs, r.threshold); block {
		// should block the access, update prisonDict and accessDict
		//freeTimeNs := r.stayPeriodNs + restTimeNs + time.Now().UnixNano()
		//r.prisonDict.Add(sign, freeTimeNs)
		//r.accessDict.Del(sign)
		return block
	}
	return true
}

func (r *prisonRule) shouldDeny(sign AccessSign, req *bfe_basic.Request) bool {
	// find prison record for this sign
	freeTimeNs, ok := r.prisonDict.Get(sign)
	if !ok {
		return false
	}

	r.ctx.AddNotice("freeTimes1", fmt.Sprintf("%s", time.Unix(int64(freeTimeNs.(int64))/1e9, 0)))
	r.ctx.AddNotice("timesNow", fmt.Sprintf("%s", time.Unix(time.Now().UnixNano()/1e9, 0)))
	// check prison time
	if time.Now().UnixNano() < freeTimeNs.(int64) {
		prisonInfo := &PrisonInfo{
			PrisonType: ModPrison,
			PrisonName: r.name,
			FreeTime:   time.Unix(0, freeTimeNs.(int64)),
			IsExpired:  false,
			Action:     r.action.Cmd,
		}
		req.SetContext(ReqCtxPrisonInfo, prisonInfo)
		return true
	}

	// remove prison record if expired
	r.prisonDict.Del(sign)
	r.ctx.AddNotice("signExpired", sign)
	// set prisoninfo
	prisonInfo := &PrisonInfo{
		PrisonType: ModPrison,
		PrisonName: r.name,
		FreeTime:   time.Unix(0, freeTimeNs.(int64)),
		IsExpired:  true,
		Action:     r.action.Cmd,
	}
	req.SetContext(ReqCtxPrisonInfo, prisonInfo)

	return false
}
