# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# init GO & GOD path
# 云端编译使用
MACHINE := $(shell uname -m)
ifeq ($(MACHINE), aarch64)
	GOROOT  := /home/<USER>/go/1.16.3
else
	GOROOT  := $(GO_1_16_HOME)
endif

GO := $(GOROOT)/bin/go
GOPATH := $(shell $(GO) env GOPATH)

#编译使用本地时候使用
#export GOROOT := $(HOMEDIR)/../../../baidu/go-env/go1-15-linux-amd64/
#GO := $(GOROOT)/bin/go
#export GOPATH := $(HOMEDIR)/../../../../../


export PATH := $(GODPATH)/bin:$(GOPATH)/bin:$(GOROOT)/bin:$(PATH)
export GDP_ROOT_PATH := $(HOMEDIR)

# init command params
GOMOD   := $(GO) mod
GOBUILD := $(GO) build
GOTEST  := $(GO) test
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")

# UT & COV result definition
UTFILE := $(HOMEDIR)/ut_report/unittest.txt # summary of the test results
COVFILE := $(HOMEDIR)/ut_report/coverage.out # coverage profile
COVFUNC := $(HOMEDIR)/ut_report/coverage.txt # coverage profile information for each function
COVHTML := $(HOMEDIR)/ut_report/coverage.html # HTML representation of coverage profile
# make, make all
all: prepare compile package
report:  prepare compile test package
# make prepare, download dependencies
prepare: prepare-dep
prepare-dep:
	export LANG=en_US.UTF-8
	git config --global http.sslVerify false

set-env:
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*
ifeq ($(MACHINE), aarch64)
	$(GO) env -w CC=/opt/compiler/gcc-10/bin/gcc
else
	$(GO) env -w CC=/opt/compiler/gcc-8.2/bin/gcc
endif

# make compile, go build
compile: build
build:set-env
	$(GOMOD) download
	$(GOBUILD) -o $(HOMEDIR)/go-suggest

# make test, test your code
test: test-cover
test-cover:set-env
	mkdir -p $(HOMEDIR)/ut_report
	$(GO) test -gcflags=-l -cover -coverprofile=$(COVFILE) $(GOPKGS) | tee $(UTFILE)
	$(GO) tool cover -o $(COVFUNC) -func=$(COVFILE)
	$(GO) tool cover -o $(COVHTML) -html=$(COVFILE)
	test -z `grep "^FAIL$$" $(UTFILE)`
	if [ -d "ut_report"  ]; then cp -r ut_report ${OUTDIR}/; fi
	test -z `grep "^FAIL$$" $(UTFILE)`

# make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)/bin
	mv go-suggest  $(OUTDIR)/bin
	cp control.sh $(OUTDIR)/bin/
	chmod +x $(OUTDIR)/bin/control.sh
	mv supervise $(OUTDIR)/bin/
	if [ -d "conf" ]; then cp -r conf ${OUTDIR}/; fi
	if [ -d "spec" ]; then cp -r spec ${OUTDIR}/; fi
	if [ -d "noahdes" ]; then cp -r noahdes ${OUTDIR}/; fi
	if [ -d "crontab" ]; then cp -r crontab ${OUTDIR}/; fi
	mkdir -p $(OUTDIR)/log
# make clean
clean:
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/go-suggest
	rm -rf $(GOPATH)/pkg/darwin_amd64
# avoid filename conflict and speed up build 
.PHONY: all prepare compile test package clean build
