package middlewares

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

var observerRequestConf = []string{
	constant.V,
	constant.QUERY,
	constant.SERVICE,
	constant.FROM,
	constant.CFROM,
	constant.NETWORK,
	constant.CIP,
	constant.CTL,
	constant.ACTION,
	constant.TYPEID,
	constant.CEN,
	constant.CTV,
	constant.VERSION,
	constant.UA,
	constant.UT,
	constant.ST,
	constant.UID,
	constant.PUID,
	constant.PQ,
	constant.PT,
	constant.BDID,
	constant.VOI,
	//constant.SID,
	constant.OSBRANCH,

	//手百公共参数，简单搜索项目依赖，新增读取
	constant.OSNAME,
	constant.OriLid,
	constant.ChatSearchTag,
	constant.InteractiveTag,

	constant.FV,
	constant.DATA,
	constant.LID,
	constant.SUGID,
	constant.DERVICETIME,
	constant.SETPARAMS,
	constant.PWD,
	constant.SUGMODE,
	constant.SCENE,
	constant.HOT_LAUNCH,
	constant.OFrom,

	//预取参数
	constant.PRERENDERE,
	constant.ISID,
	constant.PU,
	constant.ANTCT,
	constant.TN,
	constant.WISECSOR,
	constant.CLIST,
	constant.EVENTTYPE,
	constant.EVENTCHANNEL,
	constant.RSVSUG4,
	constant.RSGPQ,
	constant.OQ,
	constant.RQ,
	constant.SA,
	constant.T_SAMP,
	constant.CKSIZE,
	constant.PREMODE,

	// 兼容混合协议
	constant.CSMIXED,

	//双清单参数
	constant.PSV,
	constant.PNW,
	constant.MPV,
	constant.APPNAME,
	constant.BRACHNAME,
	//先知搜索用户行为特征
	constant.PREINPUT_NUM,
	constant.DELETE_NUM,
	constant.PRETLIST,
	constant.ISDELETE,
	constant.MAX_RETURN_NUM,

	constant.SpanUpgrade,
	constant.Tbundle,
	constant.Tversion,
}

// Print 一个简单的使用gin 中间件的示例
func RequestWare(ctx *gdp.WebContext) {
	request := ctx.Request
	requestParams := make(map[string]string, 30)
	dealGP(ctx, request, &requestParams)
	dealCen(request, &requestParams)

	dealCookie(request, &requestParams)
	dealHarmonyCuid(ctx, &requestParams)
	dealAndroidUid(ctx, &requestParams)

	dealPassport(ctx, request, &requestParams)

	uid, ok := requestParams[constant.SESSION_UID]
	if !ok {
		uid = "0"
	}

	// 适配鸿蒙主版->安卓主版 osbranch参数
	if requestParams["osbranch"] == "h0" {
		changeOsBranch("h0", "a0", requestParams, request)
		// 主板
		ctx.AddNotice("is_harmony", "1")
	}

	// 适配鸿蒙极速版->安卓极速版 osbranch参数
	if requestParams["osbranch"] == "h2" {
		changeOsBranch("h2", "a2", requestParams, request)
		// 极速版
		ctx.AddNotice("is_harmony", "2")
	}

	if requestParams["realOsbranch"] == "" {
		requestParams["realOsbranch"] = requestParams["osbranch"]
	}

	ctx.AddNotice("session_uid", uid)
	ctx.Set(constant.REQPARAMS, requestParams)
	ctx.Next()
}
func changeOsBranch(orOsbranch string, newOsbranch string, requestParams map[string]string, request *http.Request) {
	requestParams["osbranch"] = newOsbranch
	requestParams["realOsbranch"] = orOsbranch
	// 回写，有人直接从http取osbranch
	request.Form.Set("osbranch", newOsbranch)
}

func checkHarmonyCuid(cuidParts []string) bool {
	// 无｜分隔
	if len(cuidParts) != 2 {
		return false
	}
	imei := cuidParts[1]
	if len(imei) == 0 {
		return false
	}
	// 1896D8DDBEF36A7BF85893D2F3BD04B|H004DI5DWI, |后接H
	if imei[0] != 'H' {
		return false
	}
	return true
}

func dealHarmonyCuid(ctx *gdp.WebContext, requestParams *map[string]string) {
	// MappingBody mapping 响应体
	type MappingBody struct {
		DeviceID string `json:"deviceid"`
		Source   string `json:"source"`
	}

	// MappingRespBody mapping 响应
	type MappingRespBody struct {
		Errno int    `json:"err_no"`
		Cuid  string `json:"cuid,omitempty"`
	}

	// HTTPResp 响应
	type HTTPResp struct {
		Head ral.HTTPHead
		Body MappingRespBody
		Raw  []byte
	}

	request := ctx.Request
	cuid := request.FormValue("uid")
	cuidParts := strings.Split(cuid, "|")
	// 非鸿蒙格式cuid, 不处理。
	if !checkHarmonyCuid(cuidParts) {
		return
	}
	deviceID := cuidParts[0]

	source := "searchFront"
	body := MappingBody{
		DeviceID: deviceID,
		Source:   source,
	}

	header := map[string][]string{
		"Log-Id": {ctx.GetLogID()},
	}

	httpReq := ral.HTTPRequest{
		Header:    header,
		Method:    "POST",
		Path:      "UserPrivacyMappingService/Query",
		Body:      body,
		Converter: ral.JSONConverter,
		Ctx:       ctx,
	}

	resp := HTTPResp{}
	if err := ral.Ral("harmony_cuid_mapping", httpReq, &resp, ral.JSONConverter); err != nil {
		ctx.AddNotice("cuidRalErr", err.Error())
	}
	// 查询成功
	if resp.Body.Errno == 1 {
		request.Form.Set("uid", resp.Body.Cuid)
		(*requestParams)["uid"] = resp.Body.Cuid
		ctx.AddNotice("cuid_rp_harmony", "1")
	}
	(*requestParams)["huid"] = cuid
}

/*
*
处理network参数
*/
func dealIosNetwork(request *http.Request, requestParams *map[string]string) {
	osbranch := request.FormValue("osbranch")
	network := request.FormValue("network")
	p_nw := request.FormValue("p_nw")
	(*requestParams)["network_type"] = "unkown"

	if osbranch != "i0" && osbranch != "i3" {
		return
	}
	if network != "" {
		networkParts := strings.Split(network, "_")
		if len(networkParts) != 2 {
			return
		}
		if networkParts[0] == "1" {
			(*requestParams)["network_type"] = "wifi"
			return
		}
		switch networkParts[1] {
		case "13":
			(*requestParams)["network_type"] = "4g"
		case "19", "20":
			(*requestParams)["network_type"] = "5g"
		default:
			(*requestParams)["network_type"] = "unkown"
		}
	}

	if p_nw != "" {
		if (*requestParams)["network_type"] == "unkown" {
			switch request.FormValue("p_nw") {
			case "13":
				(*requestParams)["network_type"] = "4g"
			case "19", "20":
				(*requestParams)["network_type"] = "5g"
			default:
				(*requestParams)["network_type"] = "unkown"
			}
		}
	}

}

/**
 * _dealCen
 * @desc 处理加密参数
 */
func dealCen(request *http.Request, requestParams *map[string]string) {
	cen := request.FormValue("cen")
	ctv := request.FormValue("ctv")

	if cen == "" || ctv == "" {
		return
	}

	targetParams := map[string]string{
		"cen": cen,
		"ctv": ctv,
	}
	parseParams(targetParams, requestParams)
	cenRes, cenokRes := (*requestParams)["cen"]
	ctvRes, ctvokRes := (*requestParams)["ctv"]
	if cenokRes && cenRes != "" && ctvokRes && ctvRes != "" {
		uriDecoder(request, requestParams, cenRes, ctvRes)
	}
}

/**
 * 处理安卓uid
 */
func dealAndroidUid(ctx *gdp.WebContext, requestParams *map[string]string) {
	request := ctx.Request
	uid := request.FormValue("uid")

	uid = appendIMEI(uid, requestParams, ctx)
	(*requestParams)["uid"] = string(uid)
}

/**
 * 更新安卓cuid
 */
func appendIMEI(cuid string, requestParams *map[string]string, ctx *gdp.WebContext) string {
	cuidParts := strings.Split(cuid, "|")
	if len(cuidParts) != 2 || cuidParts[1] != "O" {
		return cuid
	}
	(*requestParams)["cuid_mapping"] = "1"
	imei := device2imei(cuidParts[0], ctx)

	if imei == "" {
		return cuid
	}

	return cuidParts[0] + "|" + imei
}

/**
 * 请求获取imei
 */
func device2imei(device string, ctx *gdp.WebContext) string {
	type MappingBody struct {
		DeviceID string `json:"deviceid"`
		Source   string `json:"source"`
	}

	// MappingRespBody mapping 响应
	type MappingRespBody struct {
		Errno int    `json:"err_no"`
		Exist bool   `json:"exist,omitempty"`
		IMEI  string `json:"imei,omitempty"`
	}

	// HTTPResp 响应
	type HTTPResp struct {
		Head ral.HTTPHead
		Body MappingRespBody
		Raw  []byte
	}
	source := "bdapp_suggest"
	body := MappingBody{
		DeviceID: device,
		Source:   source,
	}

	header := map[string][]string{
		"Log-Id": {ctx.GetLogID()},
	}

	httpReq := ral.HTTPRequest{
		Header:    header,
		Method:    "POST",
		Path:      "MappingService/Mapping",
		Body:      body,
		Converter: ral.JSONConverter,
		Ctx:       ctx,
	}

	resp := HTTPResp{}
	err := ral.Ral("bdbox_cuid_mapping", httpReq, &resp, ral.JSONConverter)
	if err != nil {
		ctx.AddNotice("cuidRalErr", err)
		return "0"
	}
	if resp.Body.Errno != 0 {
		ctx.AddNotice("cuidRalRetErr", resp.Body.Errno)
		return "0"
	}
	if !resp.Body.Exist {
		ctx.AddNotice("cuidRalNotExist", "1")
		return "0"
	}

	ctx.AddNotice("cuidRalSuccess", "1")
	return resp.Body.IMEI
}

/**
 * _dealGP
 * @desc 处理get、post参数
 */
func dealGP(ctx *gdp.WebContext, request *http.Request, requestParams *map[string]string) {
	// 临时存储参数
	allParams := make(map[string]string, 30)

	//ios把sdel的content-type错传为application/x-www-form-urlencoded
	//把router.go中RequestWare提到gdp默认中间件ShoubaiTowerLogware之前(因为gdp默认的中间件ShoubaiTowerLogware会调用ParseForm，ParseForm会校验application/x-www-form-urlencoded类型的参数，sdel的参数(经过gzencode)校验不合法，被过滤掉
	//因此在读取sdel body参数时，不能调用ParseForm
	if strings.Contains(request.URL.RawQuery, "action=sdel") {
		ctx.TimerStart("readRequestBody")
		bodyBytes, err := ioutil.ReadAll(request.Body)
		if err != nil {
			ctx.AddNotice("readBodyErr", err)
		}
		ctx.AddNotice("readRequestBodyCost", fmt.Sprintf("%.2f", ctx.TimerSince("readRequestBody").Seconds()*1000))
		request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
		// 只在his-sdel接口中使用
		allParams["sdel"] = string(bodyBytes)
		ctx.AddNotice("body_size", len(bodyBytes))
	}

	// 经过ParseForm后，从url和form中获取参数
	request.ParseForm()
	for _, key := range observerRequestConf {
		if key == "cen" || key == "ctv" {
			continue //_dealCen加解密参数时已处理过 此处跳过
		}
		realValue := request.Form.Get(key)
		if realValue != "" {
			allParams[key] = realValue
		}
	}
	dealIosNetwork(request, requestParams)
	// 从allParams中筛选参数，填充进requestParams
	parseParams(allParams, requestParams)
}

/**
 * _dealCookie
 * @desc 处理cookie信息
 */
func dealCookie(request *http.Request, requestParams *map[string]string) {
	SST, err := request.Cookie("SST")
	if err != nil || SST.Value == "" {
		(*requestParams)[constant.SST] = "0" // 默认不屏蔽
	} else {
		(*requestParams)[constant.SST] = SST.Value
	}

	BAIDUCUID, err := request.Cookie("BAIDUCUID")
	if err != nil || BAIDUCUID.Value == "" {
		return
	}

	//从cookie中解cuid
	cookieCuid, err := base64.Deocde(BAIDUCUID.Value, 0)
	if err != nil {
		return
	}

	(*requestParams)["_uid"] = string(cookieCuid)
}

/**
 * 根据cen和ctv 对部分全局变量(如uri) 以及表单参数(如ua、ut、uid)进行解密
 * @important 此方法会直接修改上下文Http全局
 */
func uriDecoder(request *http.Request, requestParams *map[string]string, cen string, ctv string) {
	_uri := request.RequestURI
	if cen == "" || _uri == "" {
		return
	}

	//根据cen切分加密参数
	targets := strings.Split(cen, "_") //[]string
	if len(targets) < 1 {
		return //不需要切分 理论上Split至少返回[1]个结果
	}

	for _, targetKey := range targets {
		if targetKey == "" {
			continue
		}

		targetValue := request.FormValue(targetKey)
		if targetValue == "" {
			continue
		}

		targetUrlDecoder, _ := url.QueryUnescape(targetValue) //先解码一次
		targetB64Decoder, decodeErr := _b64_decode(targetUrlDecoder)

		if decodeErr != nil {
			continue //解码失败 跳过
		}

		request.Form.Set(targetKey, targetB64Decoder)             //反写回表单
		addB64EncodeParams(requestParams, targetKey, targetValue) //用_{大写KEY}保留原始值
	}
}

// 将原始值保留 拼接服务端链接用
func addB64EncodeParams(request *map[string]string, k string, v string) {
	rawk := "_" + strings.ToUpper(k)
	(*request)[rawk] = v
}

/**
 * 此方法会根据配置request.conf，把筛选后的参数填充进requestParams
 * 若request.conf中未配置参数key，则会被过滤
 */
func parseParams(array map[string]string, requestParams *map[string]string) {
	for key, value := range array {
		paramName := strings.ToLower(key)
		if key != "query" {
			value = strings.TrimSpace(value)
		}

		// 荣耀白牌，修改osbranch
		if key == "osbranch" && value == "a10" {
			(*requestParams)[constant.RongyaoBaipai] = "1"
			value = "a0"
		}
		if value != "" {
			(*requestParams)[paramName] = value
			continue
		}

		if key == "network" {
			(*requestParams)[paramName] = "0"
			continue
		}
		if key == "typeid" {
			(*requestParams)[paramName] = "0"
			continue
		}
		if key == "version" {
			(*requestParams)[paramName] = "3"
			continue
		}
	}
}

/**
 * 字符串b64解密
 */
func _b64_decode(encodedStr string) (string, error) {

	decodedStr := ""
	decodeRet, err := base64.Deocde(encodedStr, 0)
	if err != nil {
		return decodedStr, err
	}
	return string(decodeRet), err
}
