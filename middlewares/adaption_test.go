package middlewares

import (
	"net/http"
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestAdaptionWare(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: AdaptionWare ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	requestParams := &map[string]string{}
	(*requestParams)["network"] = "0"
	(*requestParams)["typeid"] = "1"
	ctx.Set(constant.REQPARAMS, *requestParams)

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"TestAdaptionWare",
			args{
				ctx,
			},
		},
	}

	for _, tt := range tests {
		AdaptionWare(tt.args.ctx)
	}
	ag.Run()
}

func Test_dealUa(t *testing.T) {
	type args struct {
		adaption *map[string]string
		request  *http.Request
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealUa ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got := dealUa(tt.args.adaption, tt.args.request)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_dealUa, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func Test_dealOua(t *testing.T) {
	type args struct {
		adaption *map[string]string
		request  *http.Request
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealOua ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/suggest?query=be&cfrom=1099a&from=1099a&network=1_0&osbranch=i0&osname=baiduboxapp&puid=_a2K8jun28e6A&service=bdbox&ua=750_1334_iphone_11.3.6.10_0&uid=D21DBA569DE01513BC23847AF4C5A42E79D82E372OHFAOKEQMR&ut=iPhone8%2C1_11.3&zid=Y-KKDc8Yxi1YpH87_ea1q4kNX-dP0ZqQV_fDqPEQ1L6KtISkxgbT03ZhuHzRGtj5sqai5JIOURiBbj_no65zMWg&bdid=E7101D946E3509D10F03306AC256BAF4%3AFG%3D1&wise_csor=2&v=3`, nil)
	adaption := map[string]string{}

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //bool
	}{
		{
			"Test_dealOua",
			args{
				&adaption,
				req,
			},
			Want{
				true,
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := dealOua(tt.args.adaption, tt.args.request)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_dealOua, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func Test_dealUid(t *testing.T) {
	type args struct {
		adaption  *map[string]string
		request   *http.Request
		reqParams map[string]string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealUid ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got := dealUid(tt.args.adaption, tt.args.request, tt.args.reqParams)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_dealUid, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func Test_dealUt(t *testing.T) {
	type args struct {
		adaption *map[string]string
		request  *http.Request
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealUt ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got := dealUt(tt.args.adaption, tt.args.request)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_dealUt, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func TestUaMod(t *testing.T) {
	type args struct {
		adaption *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: uaMod ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
		{
			"Test_uaMod",
			args{
				&map[string]string{
					"platform": "harmony",
				},
			},
		},
	}

	for _, tt := range tests {
		uaMod(tt.args.adaption)
	}
	ag.Run()
}

func Test_hdRec(t *testing.T) {
	type args struct {
		adaption *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: hdRec ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		hdRec(tt.args.adaption)
	}
	ag.Run()
}

func Test_iOShdRec(t *testing.T) {
	type args struct {
		device_mode string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: iOShdRec ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //int64
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got := iOShdRec(tt.args.device_mode)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_iOShdRec, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(int64))
	}
	ag.Run()
}

func Test_round(t *testing.T) {
	type args struct {
		f        float64
		position int
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: round ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //float64
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got := round(tt.args.f, tt.args.position)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_round, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(float64))
	}
	ag.Run()
}
