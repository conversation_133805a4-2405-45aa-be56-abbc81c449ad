package middlewares

import (
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestLogWare(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: LogWare ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		LogWare(tt.args.ctx)
	}
	ag.Run()
}

func Test_logFinish(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: logFinish ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		logFinish(tt.args.ctx)
	}
	ag.Run()
}

func Test_logTemplates(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: logTemplates ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"Test_logTemplates",
			args{
				ctx,
			},
		},
	}

	for _, tt := range tests {
		logTemplates(tt.args.ctx)
	}
	ag.Run()
}
