package middlewares

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/logger"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/golang-lib/passport"
)

// LogWare 注册log组件
func LogWare(ctx *gdp.WebContext) {
	ctx.Next()

	logFinish(ctx)
	addIndexLog(ctx)
}

func logFinish(ctx *gdp.WebContext) {

	logTemplates(ctx)
}

var logTemplate = []string{constant.UID, constant.UT, constant.UA, constant.UNAME, constant.UATYPE, constant.CUID}

// 为保证log中字段顺序，需要按照模板来打印字段值
func logTemplates(ctx *gdp.WebContext) {
	ckbduss, errbd := ctx.Cookie("BDUSS")
	user, err := passport.Decode(ckbduss)
	if err != nil || errbd != nil {
		user.Id = 0
		user.Name = ""
	}
	var reqParams map[string]string
	if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	var adaption map[string]string
	if val, exists := ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	if reqParams == nil || adaption == nil {
		return
	}
	request := ctx.Request
	for _, key := range logTemplate {
		value := reqParams[key]
		//TODO
		if key == "uaType" {
			value = adaption["platform"]
		}
		if key == "ua" || key == "ut" {
			value = request.FormValue(key)
		}
		if key == "uname" {
			value = user.Name
		}
		if key == "cuid" {
			value = reqParams["uid"]
			if value == "" {
				value = reqParams["_uid"]
			}
			stdCtx := ctx.StdContext()
			logit.AddMetaFields(stdCtx, logit.String("cuid", value))
		}
		if key == "uid" {
			value := user.Id
			ctx.AddNotice(key, value)
			continue
		}
		ctx.AddNotice(key, value)
	}
}

// 增加index日志文件
func addIndexLog(ctx *gdp.WebContext) {

	indexLog := logger.GetWriter("index")
	logMsg := &logger.Message{
		Separator: "[]",
	}
	var indexFields = []string{"logid", "api", "cuid"}
	errLog := "1"
	if ctx.DealSucc {
		errLog = "0"
	}
	stdCtx := ctx.StdContext()
	for _, key := range indexFields {
		v := logit.FindMetaField(stdCtx, key)
		if v == nil {
			continue
		}
		logMsg.AddField(key, v.Value())
	}

	logMsg.AddField("err", errLog)
	indexLog.CoupLogd(logger.NOTICE, 2, logMsg)
}
