package middlewares

import (
	"bytes"
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestRequestWare(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: RequestWare ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// 73CA15249C60AEBBE36825E593D6FEF9
	ctx := createGetWebContext()
	ctx.Request.Form = url.Values{}
	ctx.Request.Form.Set("osbranch", "h0")
	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"TestRequestWare",
			args{
				ctx,
			},
		},
	}

	ctx.Request.Header.Del("Cookie")

	for _, tt := range tests {
		RequestWare(tt.args.ctx)
	}
	ag.Run()
}

func TestDealCen(t *testing.T) {
	type args struct {
		request       *http.Request
		requestParams *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealCen ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/suggest?query=be&cfrom=1099a&from=1099a&network=1_0&osbranch=i0&osname=baiduboxapp&puid=_a2K8jun28e6A&service=bdbox&ua=750_1334_iphone_11.3.6.10_0&uid=D21DBA569DE01513BC23847AF4C5A42E79D82E372OHFAOKEQMR&ut=iPhone8%2C1_11.3&zid=Y-KKDc8Yxi1YpH87_ea1q4kNX-dP0ZqQV_fDqPEQ1L6KtISkxgbT03ZhuHzRGtj5sqai5JIOURiBbj_no65zMWg&bdid=E7101D946E3509D10F03306AC256BAF4%3AFG%3D1&cen=test&ctv=test&action=sdel&wise_csor=2&v=3`, nil)
	requestParams := map[string]string{}

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"Test_dealCen",
			args{
				req,
				&requestParams,
			},
		},
	}

	for _, tt := range tests {
		dealCen(tt.args.request, tt.args.requestParams)
	}
	ag.Run()
}

func TestDealGP(t *testing.T) {
	type args struct {
		request       *http.Request
		requestParams *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealGP ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}
	ctx := createGetWebContext()

	for _, tt := range tests {
		dealGP(ctx, tt.args.request, tt.args.requestParams)
	}
	ag.Run()
}

func TestDealCookie(t *testing.T) {
	type args struct {
		request       *http.Request
		requestParams *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _dealCookie ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		dealCookie(tt.args.request, tt.args.requestParams)
	}
	ag.Run()
}

func Test_uriDecoder(t *testing.T) {
	type args struct {
		request       *http.Request
		requestParams *map[string]string
		cen           string
		ctv           string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _uriDecoder ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/suggest?query=be&cfrom=1099a&from=1099a&network=1_0&osbranch=i0&osname=baiduboxapp&puid=_a2K8jun28e6A&service=bdbox&ua=750_1334_iphone_11.3.6.10_0&uid=D21DBA569DE01513BC23847AF4C5A42E79D82E372OHFAOKEQMR&ut=iPhone8%2C1_11.3&zid=Y-KKDc8Yxi1YpH87_ea1q4kNX-dP0ZqQV_fDqPEQ1L6KtISkxgbT03ZhuHzRGtj5sqai5JIOURiBbj_no65zMWg&bdid=E7101D946E3509D10F03306AC256BAF4%3AFG%3D1&cen=test_test_1&_uri=test&wise_csor=2&v=3`, nil)
	requestParams := map[string]string{}

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"Test_uriDecoder",
			args{
				req,
				&requestParams,
				"test_test_test",
				"test",
			},
		},
	}

	for _, tt := range tests {
		uriDecoder(tt.args.request, tt.args.requestParams, tt.args.cen, tt.args.ctv)
	}
	ag.Run()
}

func TestAddB64EncodeParams(t *testing.T) {
	type args struct {
		request *map[string]string
		k       string
		v       string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _addB64EncodeParams ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		addB64EncodeParams(tt.args.request, tt.args.k, tt.args.v)
	}
	ag.Run()
}

func TestParseParams(t *testing.T) {
	type args struct {
		array         map[string]string
		requestParams *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _parseParams ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		parseParams(tt.args.array, tt.args.requestParams)
	}
	ag.Run()
}

func Test_b64_decode(t *testing.T) {
	type args struct {
		encodedStr string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: _b64_decode ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //string
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got, err := _b64_decode(tt.args.encodedStr)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of Test_b64_decode, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_b64_decode, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

var (
	httpRspBody1 *httptest.ResponseRecorder
)

func createGetWebContext() *gdp.WebContext {
	httpRspBody1 = httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	bodyBytes := []byte{1}
	bodyio := bytes.NewBuffer(bodyBytes)
	//构造Request                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           //获取Gin提供的Context，WebContext包括gin.Context
	req, _ := http.NewRequest("GET", `/suggest?query=be&cfrom=1099a&from=1099a&network=1_0&osbranch=i0&osname=baiduboxapp&puid=_a2K8jun28e6A&service=bdbox&ua=750_1334_iphone_11.3.6.10_0&uid=D21DBA569DE01513BC23847AF4C5A42E79D82E372OHFAOKEQMR&ut=iPhone8%2C1_11.3&zid=Y-KKDc8Yxi1YpH87_ea1q4kNX-dP0ZqQV_fDqPEQ1L6KtISkxgbT03ZhuHzRGtj5sqai5JIOURiBbj_no65zMWg&bdid=E7101D946E3509D10F03306AC256BAF4%3AFG%3D1&wise_csor=2&v=3`, bodyio)

	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "*******"),
	}
	return wc
}

func TestDealHarmonyCuid(t *testing.T) {
	type args struct {
		ctx           *gdp.WebContext
		requestParams *map[string]string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: dealHarmonyCuid ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	ctx.Request.Form = url.Values{}
	ctx.Request.Form.Set("uid", "73CA15249C60AEBBE36825E593D6FEF9|H004DI5DWI")
	requestParams := make(map[string]string)

	dealHarmonyCuid(ctx, &requestParams)
	ag.Run()
}
