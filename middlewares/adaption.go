package middlewares

import (
	"net/http"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func AdaptionWare(ctx *gdp.WebContext) {

	adaption := make(map[string]string, 30)
	var reqParams map[string]string
	if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}

	// 先解ua
	// dealUa或dealOua返回true，会执行else
	// 都解析失败，则不执行else
	if !dealUa(&adaption, ctx.Request) && !dealOua(&adaption, ctx.Request) {

	} else {
		uaMod(&adaption)

		//再解其他参数
		dealUid(&adaption, ctx.Request, reqParams)
		dealUt(&adaption, ctx.Request)

		//高清设备识别
		hdRec(&adaption)
	}

	// 荣耀白牌适配 bd_version
	if reqParams[constant.RongyaoBaipai] == "1" {
		adaption["bd_version"] = reqParams["fv"]
	}

	ctx.Set(constant.ADAPARAMS, adaption)
	ctx.Next()
}

/**
 * # 屏宽/屏高/平台/app版本/屏幕密度
 * [ua]
 * pattern = ^(\d+)_(\d+)_(\w+)_([\d\.]+)_(\w+)$
 * params  = resolution_width,resolution_height,platform,bd_version,device_dDpi *
 */
func dealUa(adaption *map[string]string, request *http.Request) bool {
	uaValue := request.FormValue("ua")
	if uaValue == "" {
		return false
	}

	uaSplit := strings.Split(uaValue, "_")
	if len(uaSplit) != 5 {
		return false
	}

	//屏宽 必须数字
	if _, err1 := strconv.ParseUint(uaSplit[0], 10, 32); err1 != nil {
		return false
	}

	//屏高 必须数字
	if _, err2 := strconv.ParseUint(uaSplit[1], 10, 32); err2 != nil {
		return false
	}

	//版本号 至少两位 最多4位
	versionSplit := strings.Split(uaSplit[3], ".")
	if len(versionSplit) < 2 || len(versionSplit) > 4 {
		return false
	}

	(*adaption)["resolution_width"] = uaSplit[0]
	(*adaption)["resolution_height"] = uaSplit[1]
	(*adaption)["platform"] = uaSplit[2]
	(*adaption)["bd_version"] = uaSplit[3]
	(*adaption)["device_dDpi"] = uaSplit[4]
	return true
}

/**
 * # 屏宽/屏高/平台/app版本/屏幕密度
 * [ua]
 * pattern = (\d+)_(\d+)_(\w+)_([\d\.]+)_(\w+)$
 * params  = resolution_width,resolution_height,platform,bd_version,device_dDpi *
 */
func dealOua(adaption *map[string]string, request *http.Request) bool {
	uaValue := request.FormValue("ua")
	if uaValue == "" {
		return false
	}

	uaSplitTemp := strings.Split(uaValue, "_")
	if len(uaSplitTemp) < 5 {
		return false
	}

	uaSplit := uaSplitTemp[len(uaSplitTemp)-5:]

	//屏宽 必须数字
	if _, err1 := strconv.ParseUint(uaSplit[0], 10, 32); err1 != nil {
		return false
	}

	//屏高 必须数字
	if _, err2 := strconv.ParseUint(uaSplit[1], 10, 32); err2 != nil {
		return false
	}

	//版本号 至少两位 最多4位
	versionSplit := strings.Split(uaSplit[3], ".")
	if len(versionSplit) < 2 || len(versionSplit) > 4 {
		return false
	}

	(*adaption)["resolution_width"] = uaSplit[0]
	(*adaption)["resolution_height"] = uaSplit[1]
	(*adaption)["platform"] = uaSplit[2]
	(*adaption)["bd_version"] = uaSplit[3]
	(*adaption)["device_dDpi"] = uaSplit[4]
	return true
}

/**
 * # uid部分 分平台紧缩uid
 * [uid]
 * # 平台间有差异
 * osdependent = 1
 * # 不需要细粒度解析 只校验整个字段
 * pattern_android = ^(\w{32})\|(\w+)$
 * pattern_iphone = ^(\w{32})|(\w{51})$
 * params_android = device_id,device_rimei
 * params_iphone = device_id
 */
func dealUid(adaption *map[string]string, request *http.Request, reqParams map[string]string) bool {
	(*adaption)["device_id"] = ""
	(*adaption)["device_rimei"] = ""

	uidValue := request.FormValue("uid")

	//若参数丢失 从cookie再取一次
	if uidValue == "" {
		cookieUidValue, cok := reqParams["_uid"]
		if cok && cookieUidValue != "" {
			uidValue = cookieUidValue
		}
	}
	//获取两次均失败时 跳过
	if uidValue == "" {
		return false
	}

	//平台之间有差异
	platform, pok := (*adaption)["platform"]
	if !pok || platform == "" {
		return false
	}

	if platform == "android" {
		s := strings.Split(uidValue, "|")
		if len(s[0]) != 32 {
			return false
		}
		(*adaption)["device_id"] = s[0] //只认32位
		if len(s) >= 2 {
			(*adaption)["device_rimei"] = s[1] //不论多长
		}
		return true
	}

	if platform == "iphone" {
		s := strings.Split(uidValue, "|")
		if len(s[0]) != 32 {
			return false
		}
		(*adaption)["device_id"] = s[0] //只认32位 第一部分
		return true
	}
	return false
}

/**
 * # ut部分 机型/系统版本/sdk版本/厂商
 * [ut]
 * # 平台间有差异
 * osdependent = 1
 * pattern_android = ^(.*?)_(.*)_(\d+)_(.*)$
 * pattern_iphone = ^(.*?)_(.*)$
 * params_android = device_model,os_version,sdk_version,device_vendor
 * params_iphone = device_model,os_version
 */
func dealUt(adaption *map[string]string, request *http.Request) bool {
	(*adaption)["device_model"] = ""
	(*adaption)["os_version"] = ""
	(*adaption)["sdk_version"] = ""
	(*adaption)["device_vendor"] = ""

	utValue := request.FormValue("ut")
	if utValue == "" {
		return false
	}

	//平台之间有差异
	platform, pok := (*adaption)["platform"]
	if !pok || platform == "" {
		return false
	}

	utValues := strings.Split(utValue, "_")
	if platform == "android" {
		if len(utValues) != 4 {
			return false
		}
		if _, err := strconv.ParseUint(utValues[2], 10, 32); err != nil {
			return false
		}
		(*adaption)["device_model"] = utValues[0]
		(*adaption)["os_version"] = utValues[1]
		(*adaption)["sdk_version"] = utValues[2]
		(*adaption)["device_vendor"] = utValues[3]
		return true
	}

	if platform == "iphone" {
		if len(utValues) != 2 {
			return false
		}
		(*adaption)["device_model"] = utValues[0]
		(*adaption)["os_version"] = utValues[1]
		return true
	}

	return false
}

/**
 * ua修正函数 由于迭代中旧版ua参数不适配新框架 此处进行其他适配解析
 */
func uaMod(adaption *map[string]string) {
	platform := (*adaption)["platform"]
	uaAll := []string{"iphone", "android", "wphone", "symbian", "win", "uwp", "harmony"}
	find := false
	for _, uaStr := range uaAll {
		if uaStr == platform {
			find = true
		}
	}
	if !find {
		platform = "unknow" //ua修复 未知ua
	}

	//兼容鸿蒙为Android
	if platform == "harmony" {
		platform = "android"
	}

	//记录真实平台 兼容过老版本设备 方便记录
	(*adaption)["realPlatform"] = (*adaption)["platform"]
	(*adaption)["platform"] = platform

	//此处最好将各个平台分开来写 不要柔和
	isAndroid := "0"
	if platform == "android" {
		isAndroid = "1"
	}
	(*adaption)["isAndroid"] = isAndroid

	isiOS := "0"
	if platform == "iphone" {
		isiOS = "1"
	}
	(*adaption)["isiOS"] = isiOS

	isUwp := "0"
	if platform == "uwp" {
		isUwp = "1"
	}
	(*adaption)["isUwp"] = isUwp

	//wphone与android兼容 逻辑其实都一样
	if platform == "wphone" {
		(*adaption)["platform"] = "android"
		(*adaption)["isAndroid"] = "1"
	}
}

/**
 * 高清设备识别
 */
func hdRec(adaption *map[string]string) {
	isAndroid := (*adaption)["isAndroid"]
	isiOS := (*adaption)["isiOS"]

	//修复平台号和屏幕密度的版本间差异
	device_dDpi := (*adaption)["device_dDpi"]
	device_model := (*adaption)["device_model"]
	if device_dDpi == "" {
		device_model = "0"
	}
	if device_model == "" {
		device_model = "unknown"
	}

	//device_dDpi
	if device_dDpi == "0" {
		if isiOS == "1" {
			//iOS平台
			device_dDpi = strconv.FormatInt(iOShdRec(device_model), 10)
		} else if isAndroid != "1" {
			device_dDpi = "240" //非iOS Android设置为高分
		} else {
			device_dDpi = "160" //默认按照中分 mdpi
		}
	}
	(*adaption)["device_dDpi"] = device_dDpi

	//device_dpi
	fdpi, errfd := strconv.ParseFloat(device_dDpi, 10)
	if errfd != nil {
		(*adaption)["device_dpis"] = "mdpi"
		return
	}
	device_dpi := round(fdpi/160, 2)
	//屏幕密度(float, eg: 1.0) 区分屏幕密度DPI(int, eg: 240)
	(*adaption)["device_dpi"] = strconv.FormatInt(int64(device_dpi), 10)

	//屏幕是否高清 1.5为界
	var device_hd int64
	if device_dpi > 1.5 {
		device_hd = 1
	} else {
		device_hd = 0
	}
	(*adaption)["device_hd"] = strconv.FormatInt(device_hd, 10)

	//device_dip
	width, rok := (*adaption)["resolution_width"]
	if !rok {
		width = "0"
	}
	fwidth, errfw := strconv.ParseFloat(width, 64)
	idDpi, erriD := strconv.ParseFloat(device_dDpi, 64)
	if errfw != nil || erriD != nil {
		(*adaption)["device_dpis"] = "mdpi"
		return
	}
	device_dip := int64(round(fwidth*160.0/idDpi, 0))
	if device_dip == 0 {
		device_dip = 320
	}
	if isiOS == "1" && strings.Index(device_model, "iPad") != -1 {
		device_dip = 320
	}
	//密度无关像素(int, eg:320 360)
	(*adaption)["device_dip"] = strconv.FormatInt(device_dip, 10)

	//device_dpis
	_dpis := map[string]string{
		"0.75": "ldpi",
		"1":    "mdpi",
		"1.5":  "hdpi",
		"2":    "xhdpi",
		"3":    "xhdpi", //xxhdpi目前按照xhdpi处理
	}
	dpis, dpisMapOk := _dpis[(*adaption)["device_dpi"]]
	if !dpisMapOk {
		dpis = "mdpi"
	}
	(*adaption)["device_dpis"] = dpis
}

/**
 * iOS判断高清设备
 * 由于conf不支持特殊标点 此处不写配置
 */
func iOShdRec(device_mode string) int64 {
	//机型全匹配
	hdDevices := map[string]string{
		"iPhone3,1":      "iPhone4 GSM",
		"iPhone3,2":      "iPhone4 Verzon",
		"iPhone3,3":      "iPhone4",
		"iPhone4,1":      "iPhone4S",
		"iPhone4,2":      "iPhone4S",
		"iPod4,1":        "iPod 4G",
		"iPad3,1":        "The New Pad WiFi",
		"iPad3,2":        "The New Pad GSM",
		"iPad3,3":        "The New Pad CDMA",
		"iPhone3,i386":   "iPhone Simulator",
		"iPhone3,x86_64": "iPhone Simulator",
		"iPad3,i386":     "iPad Simulator",
		"iPad3,x86_64":   "iPad Simulator",
		//iPhone5逗号后的数字代表定制版 每个手机有几种
		"iPhone5,2": "iPhone5",  //iPhone5
		"iPhone5,3": "iPhone5c", //iPhone5c
		"iPhone5,4": "iPhone5c", //iPhone5c
		"iPhone6,2": "iPhone5s", //iPhone5s
		"iPhone7,2": "Iphone6",  //iPhone6
		"iPhone8,1": "Iphone6s", //iPhone6s
	}
	if _, find := hdDevices[device_mode]; find {
		return 320
	}

	//用正则再匹配一次
	hdDevicesReg := map[string]string{
		"iPhone5": "iPhone5",
		"iPhone6": "iPhone5",
	}
	for key, _ := range hdDevicesReg {
		if strings.Index(device_mode, key) == 0 {
			return 320
		}
	}

	shdDevices := map[string]string{
		"iPhone7,1": "Iphone6 Plus",
		"iPhone8,2": "Iphone6s Plus", //iPhone6s plus
	}
	if _, find := shdDevices[device_mode]; find {
		return 480
	}

	return 160
}

/**
 * 按位取四舍五入 php round的实现
 */
func round(f float64, position int) float64 {
	var pow float64 = 1
	for i := 0; i < position; i++ {
		pow *= 10
	}
	return float64(int((f*pow)+0.5)) / pow
}
