/*
 * @Author: houming01 <EMAIL>
 * @Date: 2024-12-06 17:58:34
 * @LastEditors: houming01 <EMAIL>
 * @LastEditTime: 2024-12-09 15:12:20
 * @FilePath: /go-suggest/middlewares/sample.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package middlewares

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/sample"
)

func Sample(ctx *gdp.WebContext) {
	ctl := ctx.Context.Request.FormValue("ctl")
	action := ctx.Context.Request.FormValue("action")

	joinedStr := fmt.Sprintf("%s_%s", ctl, action)
	if _, ok := common.SampleWhiteMap[joinedStr]; ok {
		sample.SetSampleHWiseSids(ctx)
	}

	ctx.Next()
}
