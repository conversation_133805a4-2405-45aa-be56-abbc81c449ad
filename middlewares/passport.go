package middlewares

import (
	"fmt"
	"net/http"
	"strconv"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/passport"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func dealPassport(ctx *gdp.WebContext, request *http.Request, requestParams *map[string]string) {
	bduss, err := request.Cookie("BDUSS")
	if err != nil {
		ctx.AddNotice("passport", fmt.Sprintf("get bduss fail: %s", err.Error()))
		return
	}

	res, err := requestSession(ctx, request, bduss)
	if err != nil {
		ctx.Warning(fmt.Sprintf("request session error: %s", err.Error()))
		return
	}

	// 校验登录状态失败
	if res.Status != 0 || res.Uid == 0 {
		ctx.Warning(fmt.Sprintf("session response error: %d", res.Status))
		return
	}

	// 保存到 request中
	(*requestParams)[constant.SESSION_UID] = strconv.FormatInt(res.Uid, 10)
	ctx.AddNotice("passport", "ok")
}

func requestSession(ctx *gdp.WebContext, request *http.Request, bduss *http.Cookie) (*passport.RecvSession, error) {
	tokenConf := passport.NewAuthTokenParametersByConf()
	authToken := passport.NewAuthToken(tokenConf)

	req := passport.SendSession{
		Bduss:     bduss.Value,
		AuthToken: authToken,
	}
	req.SetContext(ctx)

	resp, err := passport.GetSession(&req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
