#!/bin/bash

# 获取当前目录名
current_dir=$(basename "$(pwd)")

# 检查当前目录名是否为 go-suggest
if [ "$current_dir" != "go-suggest" ]; then
  echo "错误：当前目录不是 go-suggest"
  exit 1
fi

# 确保 conf 和 conf/servicer 目录存在
mkdir -p conf/servicer

# 询问用户是否需要手动设置配置
read -p "是否需要手动设置配置？(y/n): " need_manual

if [[ "$need_manual" == "y" || "$need_manual" == "Y" ]]; then
  # 验证IP地址格式
  validate_ip() {
    local ip=$1
    local stat=1
    
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
      OIFS=$IFS
      IFS='.'
      ip_array=($ip)
      IFS=$OIFS
      
      if [[ ${ip_array[0]} -le 255 && ${ip_array[1]} -le 255 && ${ip_array[2]} -le 255 && ${ip_array[3]} -le 255 ]]; then
        stat=0
      fi
    fi
    
    return $stat
  }

  # 验证端口号
  validate_port() {
    local port=$1
    
    if [[ $port =~ ^[0-9]+$ && $port -ge 1 && $port -le 65535 ]]; then
      return 0
    else
      return 1
    fi
  }

  # 交互式询问 IP 地址和端口
  while true; do
    read -p "请输入 BFF IP 地址: " ip_address
    
    if validate_ip "$ip_address"; then
      break
    else
      echo "错误：IP 地址格式不正确，请重新输入"
    fi
  done

  while true; do
    read -p "请输入 BFF 端口号: " port_number
    
    if validate_port "$port_number"; then
      break
    else
      echo "错误：端口号必须是 1-65535 之间的数字，请重新输入"
    fi
  done

  # 询问用户填写 sid
  read -p "请输入 sid: " sid

  # 创建 wisenginx_bfe.toml 文件
  cat > conf/servicer/wisenginx_bfe.toml << EOF
# Auto Generate By "gdp tov2" At 2021-02-23 16:50:52

# 下游服务配置
# http://gdp.baidu-int.com/gdp2/docs/examples/30_servicer/
# 原配置文件路径：conf/ral/services/wisenginx_bfe.toml

# 下游服务的名字，必填，必须和文件名保持一致
Name = "wisenginx_bfe"

# 以下自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用了Manual，则ConnTimeOut、WriteTimeOut、ReadTimeOut、Retry 需要配置
# 使用其他方式，如BNS，上述则ConnTimeOut等参数默认会从 BNS config 中读取，
# 若配置了则会导致 BNS config 中的值不生效

# 可选，连接超时，单位毫秒，默认5000
ConnTimeOut = 100

# 可选，写数据超时，单位毫秒，默认5000ms
WriteTimeOut = 150

# 可选，读数据超时，单位毫秒，默认5000
ReadTimeOut = 150

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1。，默认0
Retry = 2

# 资源使用/负载均衡策略，非必选，默认使用 RoundRobin
# 可选 RoundRobin:依次轮询 Random:随机 LocalityAware ：la加权轮询，需要策略配置
[Strategy]
Name="Random"

# 以下为资源定位配置,必须有,且只有一项

## 资源定位：使用 BNS
#[Resource.BNS]
## 支持普通的bns、smartbns、meshbns
#BNSName = "group.bfe-yq.NWISE.all"
## PortKey 用于指定使用那个端口，可选，默认为'main',请找bns提供方确认该使用那个端口

[Resource.Manual]
[[Resource.Manual.default]]
Host = "${ip_address}"
Port = ${port_number}
#PortKey = "main"
EOF
else
  echo "未进行任何配置修改，保持现有配置文件不变"
fi
  # 创建指定的文件
  touch conf/aigc_discover.toml
  touch conf/aigc_agent.toml
  touch conf/super_pinzhuan.toml
  touch conf/dump.toml
  touch conf/normal_pinzhuan.toml
  touch conf/shop_sug.toml

  # 创建 sample.toml 文件并写入 sid 配置
  cat > conf/sample.toml << EOF
[http_tag]
"${sid}" = "1"
EOF

  # 创建 sample_switch.toml 文件
  touch conf/sample_switch.toml

  echo "已成功在 go-suggest/conf/ 目录下创建所有配置文件"
  echo "BFF IP地址 ${ip_address} 和端口 ${port_number} 已写入 conf/servicer/wisenginx_bfe.toml"
  echo "SID ${sid} 已写入 conf/sample.toml"

