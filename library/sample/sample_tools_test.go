/*
 * @Author: houming01 <EMAIL>
 * @Date: 2024-12-03 15:41:45
 * @LastEditors: houming01 <EMAIL>
 * @LastEditTime: 2024-12-20 16:12:23
 * @FilePath: /go-suggest/library/sample/sample_tools_test.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package sample

import (
	"fmt"
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"gopkg.in/go-playground/assert.v1"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func createGetWebContext() *gdp.WebContext {
	httpRspBody1 := httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	req, _ := http.NewRequest("GET",
		"/suggest?ctl=sug&query=sug&v=3&voi=0&wise_csor=3&lid=15625789587437782785&"+
			"service=bdbox&uid=gi2kig8zvujjaHtH_uS3a0OH-a_Aa2a20uvg8lu_vuKaLiqlB&"+
			"from=757b&ua=_a-qi4ujvfg4NE65I5me6NN0v8oNu2I4_hvDhSdqNqqqB&"+
			"ut=09STI0tc2iyqPXiDzuD58gIVQMlykSO6A&osname=baiduboxapp&"+
			"osbranch=a0&pkgname=com.baidu.searchbox&network=1_0&cfrom=757b&"+
			"ctv=2&cen=uid_ua_ut&typeid=0&puid=_u-oi_aq-ieNdqqqB&"+
			"zid=9BE28306D28D2E866398511E0D675B075987B4E95332AF1B834DA4A197B8DE&"+
			"pq=%E5%8F%82%E6%95%B0&pt=1562567664", nil)

	req.Header.Set("User-Agent",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 15_6_1 like Mac OS X) "+
			"AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 SP-engine/3.8.0 "+
			"main/1.0 baiduboxapp/********** (Baidu; P2 15.6.1) NABar/1.0 "+
			"themeUA=Theme/default")
	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	req.AddCookie(&http.Cookie{
		Name:  "BAIDUCUID",
		Value: "jOvNalu52ugwiSa9gOBgulaJv8Y5uHiP_u2vu_PWSi0EO2iuga2ji0ieQR0qfSPCb6HmA",
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		Context:    c,
		LogContext: gdp.NewLogContext("1", "*******"),
	}
	wc.Set(constant.REQPARAMS, map[string]string{"uid": "895AC73F2CEE599EF74192E2727D38B7C8123106AFIQDHAKSCH"})
	return wc
}

func TestBuildSampleReq(t *testing.T) {
	ctx := createGetWebContext()

	req := NewSampleInfo(ctx)

	// 测试 BuildSampleReq 函数
	err := req.BuildSampleReq(ctx)
	if err != nil {
		t.Errorf("BuildSampleReq() returned unexpected error: %v", err)
	}

	// 校验结果
	if req.Cuid == "" {
		t.Errorf("BuildSampleReq() did not set Cuid correctly, got: %v", req.Cuid)
	}
	if req.Extra.Browser == "" || req.Extra.BrowserVersion == "" {
		t.Errorf("BuildSampleReq() did not set Browser or BrowserVersion correctly, got: %+v", req.Extra)
	}
}

func TestMakeRequest(t *testing.T) {
	// 调用现有的 createGetWebContext() 函数
	ctx := createGetWebContext()

	// 创建并初始化请求对象
	req := NewSampleInfo(ctx)
	err := req.BuildSampleReq(ctx)
	if err != nil {
		t.Fatalf("BuildSampleReq() returned unexpected error: %v", err)
	}
	_ = req.MakeRequest(ctx)
}

func mockRandomNumberGenerator(result int) func(n int) int {
	return func(n int) int {
		return result
	}
}

func TestSetSampleHWiseSids(t *testing.T) {
	ctx := createGetWebContext()
	DegradeSwitchConf.Switch = 0
	common.SampleConfMem.SampleSwitch = true
	randomNumberGenerator = func(_ int) int {
		return 1
	}
	SetSampleHWiseSids(ctx)
	hWiseSids := ctx.GetString(constant.HWiseSIDs)
	fmt.Println(hWiseSids)
	assert.Equal(t, hWiseSids, "")
}

func TestCheckIsSearchNew(t *testing.T) {
	ctx := createGetWebContext()
	ctx.Context.Request.AddCookie(&http.Cookie{
		Name:  "s_attr",
		Value: "",
	})
	assert.Equal(t, checkIsSearchNew(ctx), true)

	ctx = createGetWebContext()
	ctx.Context.Request.AddCookie(&http.Cookie{
		Name:  "s_attr",
		Value: "ut-0_at-0",
	})
	assert.Equal(t, checkIsSearchNew(ctx), true)

	ctx = createGetWebContext()
	ctx.Context.Request.AddCookie(&http.Cookie{
		Name:  "s_attr",
		Value: "ut-0_at-0",
	})
	assert.Equal(t, checkIsSearchNew(ctx), true)

	ctx = createGetWebContext()
	ctx.Context.Request.AddCookie(&http.Cookie{
		Name:  "s_attr",
		Value: "ut-0_at-20191201",
	})
	fmt.Println(ctx.Request.Cookies())
	assert.Equal(t, checkIsSearchNew(ctx), false)
}
