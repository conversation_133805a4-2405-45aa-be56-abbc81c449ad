package sample

import (
	"errors"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Req struct {
	Req        ghttp.Request
	Persisted  bool
	Method     string
	Path       string
	Cuid       string
	Baiduid    string
	Production string
	Logid      string
	Token      string
	Extra      ReqExtra
}

type ReqExtra struct {
	Pd             string
	Format         string
	Browser        string
	BrowserVersion string
	Os             string
	OsVersion      string
	UserAgent      string
	UserID         string
	Idc            string
	ClientIP       string
	Query          string
	URI            string
	QueryLang      string
	ForceHitSid    string
	CsWSids        string
	BrowserWSids   string
	DeployPlatform string
	TaskType       string
}

type Resp struct {
	Errno          int      `json:"errno"`
	ErrMsg         string   `json:"errmsg"`
	OrthotableSign int      `json:"hashid"`
	Exptinfo       ExptInfo `json:"exptinfo"`
}

type ExptInfo struct {
	SidJSONArray []SidJSONArrayItem `json:"search_zhengjiao,omitempty"`
}

// SidJSONArrayItem 请求抽样服务收到的sid集合
type SidJSONArrayItem struct {
	Sid string `json:"id,omitempty"`
}

type DegradeSwitch struct {
	Switch int `toml:"switch"`
}

var DegradeSwitchConf DegradeSwitch

func NewSampleInfo(ctx *gdp.WebContext) *Req {
	return &Req{
		Method:    "POST",
		Path:      "/FeedsampleHttpService/handle",
		Logid:     ctx.GetLogID(),
		Extra:     ReqExtra{},
		Persisted: randomNumberGenerator(100) == 1,
	}
}

func (s *Req) BuildSampleReq(ctx *gdp.WebContext) error {
	userAgent := ctx.Request.Header.Get("User-Agent")

	var version string

	if version = common.GetZbiosMainVersion(userAgent); version == "" {
		return errors.New("browser not support")
	}
	var browser string
	if common.IsZibosLite(userAgent) {
		browser = "lite_baidubox"
	} else {
		browser = "baidubox"
	}
	s.Extra.Browser = browser
	s.Extra.BrowserVersion = version

	var reqParams map[string]string
	if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}

	s.Extra.UserAgent = userAgent
	s.Extra.Pd = "ps"
	s.Extra.Format = "zbios"
	s.Extra.Os = common.GetUserOS(userAgent)

	if _, ok := reqParams[constant.SESSION_UID]; ok {
		s.Extra.UserID = reqParams[constant.SESSION_UID]
	} else {
		s.Extra.UserID = ""
	}
	s.Extra.Idc = env.IDC()
	s.Extra.ClientIP = ctx.ClientIP()
	s.Extra.Query = ""
	s.Extra.URI = ctx.Request.URL.RequestURI()
	s.Extra.QueryLang = "SIMP_CHINESE"
	s.Extra.DeployPlatform = "online"
	forceHitSid := ctx.Context.Request.FormValue("sid")
	if forceHitSid != "" {
		s.Extra.ForceHitSid = forceHitSid
	}
	csWSids, err := ctx.Cookie("CS_W_SIDS")

	if err != nil || csWSids == "" {
		s.Extra.CsWSids = ""
	} else {
		s.Extra.CsWSids = csWSids
	}
	s.Extra.TaskType = "gosug"
	s.Cuid = reqParams["uid"]
	if s.Cuid == "" {
		return errors.New("cuid is empty")
	}
	s.Baiduid = ""
	s.Production = "search"
	s.Logid = ctx.GetLogID()
	s.Token = "search_token"
	return nil
}

func (s *Req) MakeRequest(ctx *gdp.WebContext) (sidArray []string) {
	ralReq := &ghttp.RalRequest{
		Method: s.Method,
		Path:   s.Path,
		Header: ctx.Request.Header,
	}
	ralReq.Header.Del("Accept-Encoding")
	ralReq.Header.Del("Connection")
	ralReq.Header.Add("Log-Id", s.Logid)

	extraData := map[string]interface{}{
		"pd":              s.Extra.Pd,
		"format":          s.Extra.Format,
		"browser":         s.Extra.Browser,
		"browser_version": s.Extra.BrowserVersion,
		"os":              s.Extra.Os,
		"useragent":       s.Extra.UserAgent,
		"userid":          s.Extra.UserID,
		"idc":             s.Extra.Idc,
		"client_ip":       s.Extra.ClientIP,
		"query":           s.Extra.Query,
		"uri":             s.Extra.URI,
		"query_lang":      s.Extra.QueryLang,
		"deploy_platform": s.Extra.DeployPlatform,
		"task_type":       s.Extra.TaskType,
		"cs_w_sids":       s.Extra.CsWSids,
	}
	if s.Extra.ForceHitSid != "" {
		extraData["force_hit_sid"] = s.Extra.ForceHitSid
	}
	postData := map[string]interface{}{
		"cuid":       s.Cuid,
		"baiduid":    s.Baiduid,
		"production": s.Production,
		"logid":      s.Logid,
		"token":      s.Token,
		"extra":      extraData,
	}
	if err := ralReq.WithBody(postData, codec.JSONEncoder); err != nil {
		return
	}

	sampleResp := &Resp{}
	ralResp := &ghttp.RalResponse{
		Data:    sampleResp,
		Decoder: codec.JSONDecoder,
	}

	if s.Persisted {
		ctx.AddNotice("cuid", s.Cuid)
		ctx.AddNotice("useragent", s.Extra.UserAgent)
		ctx.AddNotice("userid", s.Extra.UserID)
	}

	ctx.TimerStart("sampleCost")
	err := ral.RAL(ctx, "sample_server", ralReq, ralResp)
	ctx.AddNotice("sampleCost", fmt.Sprintf("%.2f", ctx.TimerSince("sampleCost").Seconds()*1000))
	if err != nil {
		ctx.AddNotice("samplerErr", err.Error())
		return
	}
	ctx.AddNotice("sampleRet", ralResp.Data.(*Resp).Errno)
	if ralResp.Data.(*Resp).Errno != 0 {
		return
	}
	sidObj := ralResp.Data.(*Resp).Exptinfo.SidJSONArray

	for _, item := range sidObj {
		sidArray = append(sidArray, item.Sid)
	}
	return
}

var randomNumberGenerator = func(n int) int {
	return rand.Intn(n)
}

func DegradeSwitchConfInit() {
	filePath := filepath.Join(env.ConfDir(), "degrades/search_sample_switch.toml")
	if _, err := toml.DecodeFile(filePath, &DegradeSwitchConf); err != nil {
		fmt.Fprintf(os.Stdout, "can't load switch config file %s: %s", filePath, err.Error())
	}
}

func checkIsSearchNew(ctx *gdp.WebContext) bool {
	sAttr, err := ctx.Cookie("s_attr")
	//不存在或者解析出错时认为是需要请求抽样服务
	if err != nil || sAttr == "" {
		return true
	}
	now := time.Now()

	formattedDate := now.Format("20060102")

	re := regexp.MustCompile(`at-([^_]+)`)
	match := re.FindStringSubmatch(sAttr)
	if len(match) > 1 {
		return formattedDate == match[1] || match[1] == "0"
	}
	return false

}

func sampleCheck(ctx *gdp.WebContext) bool {
	// 降级开关
	if DegradeSwitchConf.Switch != 0 {
		ctx.AddNotice("SampleSwitchOff", "1")
		return false
	}
	// 配置派生开关
	if !common.SampleConfMem.SampleSwitch {
		return false
	}

	// 新户或未携带s_attr cookie时请求抽样服务,暂不生效
	if !checkIsSearchNew(ctx) {
		return false
	}

	return true
}

func SetSampleHWiseSids(ctx *gdp.WebContext) {
	if !sampleCheck(ctx) {
		return
	}

	sampleTool := NewSampleInfo(ctx)

	if err := sampleTool.BuildSampleReq(ctx); err != nil {
		return
	}
	sidArray := sampleTool.MakeRequest(ctx)

	if len(sidArray) == 0 {
		return
	}
	hWiseSids := strings.Join(sidArray, "_")
	if sampleTool.Persisted {
		ctx.AddNotice("HWiseSidsLen", len(sidArray))
		ctx.AddNotice("HWiseSids", hWiseSids)
	}
	ctx.Set(constant.HWiseSIDs, hWiseSids)
}

func init() {
	c := cron.New(
		cron.WithSeconds(),
	)
	_, err := c.AddFunc("*/1 * * * * *", func() {
		DegradeSwitchConfInit()
	})
	if err != nil {
		fmt.Fprintf(os.Stdout, "can't add func to cron: %s", err.Error())
	}
	c.Start()
}
