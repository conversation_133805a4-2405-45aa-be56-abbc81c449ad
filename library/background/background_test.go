package background

import (
	"context"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

func TestInitBackgroundTask(t *testing.T) {
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()
	common.RedisClient = testRedis.Client()

	InitBackgroundTask(context.Background())
	time.Sleep(1500 * time.Millisecond)
	t.Log("success")
}
