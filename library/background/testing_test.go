package background

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestMockRongYaoBlackList(t *testing.T) {
	ctx := context.Background()

	// 创建一个新的 MockRongYaoBlackList 实例
	blacklist := NewMockRongYaoBlackList()

	// 初始状态下，未添加任何数据，任意 key 均应返回 false
	assert.False(t, blacklist.IsAccurateMatch("key1"), "预期未添加精确匹配数据时 key1 不匹配")
	assert.False(t, blacklist.IsFuzzyMatch("hello world"), "预期未添加模糊匹配数据时不匹配任何输入")

	// 测试精确匹配
	blacklist.AddAccurate("key1")
	assert.True(t, blacklist.IsAccurateMatch("key1"), "预期添加 key1 后精确匹配成功")
	assert.False(t, blacklist.IsAccurateMatch("key2"), "预期未添加 key2 时精确匹配失败")

	// 测试模糊匹配
	blacklist.AddFuzzy("hello;world;test")
	assert.True(t, blacklist.IsFuzzyMatch("say hello world test to everyone"), "预期输入包含三个交叉词时模糊匹配成功")
	assert.False(t, blacklist.IsFuzzyMatch("say hello only"), "预期输入不包含所有交叉词时模糊匹配失败")

	// 测试 SetAccurateData 方法，修改为使用字符串数组
	newAccurateData := []string{"key2"}
	blacklist.SetAccurateData(newAccurateData)
	assert.False(t, blacklist.IsAccurateMatch("key1"), "预期重置精确匹配数据后 key1 不匹配")
	assert.True(t, blacklist.IsAccurateMatch("key2"), "预期重置精确匹配数据后 key2 匹配成功")

	// 测试 SetFuzzData 方法
	newFuzzData := []string{"world;hello;example"}
	blacklist.SetFuzzData(newFuzzData)
	assert.True(t, blacklist.IsFuzzyMatch("hello world example content"), "预期输入包含三个交叉词时模糊匹配成功")
	assert.False(t, blacklist.IsFuzzyMatch("hello world"), "预期输入不包含所有交叉词时模糊匹配失败")

	// 调用 StartBackgroundUpdate 方法（空实现），确保不发生异常或 panic
	blacklist.StartBackgroundUpdate(ctx, time.Second)
}

func TestMockCPageHighlightBlackList(t *testing.T) {
	ctx := context.Background()

	// 创建一个新的 MockCPageHighlightBlackList 实例
	blacklist := NewMockCPageHighlightBlackList()

	// 初始状态下，未添加任何数据，任意 host 或 URL 均应返回 false
	assert.False(t, blacklist.IsHostMatch("example.com"), "预期未添加 Host 黑名单数据时不匹配任何域名")
	assert.False(t, blacklist.IsURLMatch("https://example.com/page"), "预期未添加 URL 黑名单数据时不匹配任何 URL")

	// 测试 Host 匹配
	blacklist.AddHost("example.com")
	assert.True(t, blacklist.IsHostMatch("example.com"), "预期添加 example.com 后 Host 匹配成功")
	assert.False(t, blacklist.IsHostMatch("other.com"), "预期未添加 other.com 时 Host 匹配失败")

	// 测试 URL 匹配
	blacklist.AddURL("https://example.com/page")
	assert.True(t, blacklist.IsURLMatch("https://example.com/page"), "预期添加 URL 后 URL 匹配成功")
	assert.False(t, blacklist.IsURLMatch("https://example.com/other"), "预期未添加的 URL 匹配失败")

	// 测试 SetHostData 方法，修改为使用字符串数组
	newHostData := []string{"new-example.com"}
	blacklist.SetHostData(newHostData)
	assert.False(t, blacklist.IsHostMatch("example.com"), "预期重置 Host 数据后原有域名不匹配")
	assert.True(t, blacklist.IsHostMatch("new-example.com"), "预期重置 Host 数据后新域名匹配成功")

	// 测试 SetURLData 方法，修改为使用字符串数组
	newURLData := []string{"https://new-example.com/page"}
	blacklist.SetURLData(newURLData)
	assert.False(t, blacklist.IsURLMatch("https://example.com/page"), "预期重置 URL 数据后原有 URL 不匹配")
	assert.True(t, blacklist.IsURLMatch("https://new-example.com/page"), "预期重置 URL 数据后新 URL 匹配成功")

	// 调用 StartBackgroundUpdate 方法（空实现），确保不发生异常或 panic
	blacklist.StartBackgroundUpdate(ctx, time.Second)
}

func TestMockCPageClickRecBlackList(t *testing.T) {
	ctx := context.Background()

	// 创建一个新的 MockCPageClickRecBlackList 实例
	blacklist := NewMockCPageClickRecBlackList()

	// 初始状态下，未添加任何数据，任意 host 或 URL 均应返回 false
	assert.False(t, blacklist.IsHostMatch("example.com"), "预期未添加 Host 黑名单数据时不匹配任何域名")
	assert.False(t, blacklist.IsURLMatch("https://example.com/page"), "预期未添加 URL 黑名单数据时不匹配任何 URL")

	// 测试 Host 匹配
	blacklist.AddHost("example.com")
	assert.True(t, blacklist.IsHostMatch("example.com"), "预期添加 example.com 后 Host 匹配成功")
	assert.False(t, blacklist.IsHostMatch("other.com"), "预期未添加 other.com 时 Host 匹配失败")

	// 测试 URL 匹配
	blacklist.AddURL("https://example.com/page")
	assert.True(t, blacklist.IsURLMatch("https://example.com/page"), "预期添加 URL 后 URL 匹配成功")
	assert.False(t, blacklist.IsURLMatch("https://example.com/other"), "预期未添加的 URL 匹配失败")

	// 测试 SetHostData 方法
	newHostData := []string{"new-example.com"}
	blacklist.SetHostData(newHostData)
	assert.False(t, blacklist.IsHostMatch("example.com"), "预期重置 Host 数据后原有域名不匹配")
	assert.True(t, blacklist.IsHostMatch("new-example.com"), "预期重置 Host 数据后新域名匹配成功")

	// 测试 SetURLData 方法
	newURLData := []string{"https://new-example.com/page"}
	blacklist.SetURLData(newURLData)
	assert.False(t, blacklist.IsURLMatch("https://example.com/page"), "预期重置 URL 数据后原有 URL 不匹配")
	assert.True(t, blacklist.IsURLMatch("https://new-example.com/page"), "预期重置 URL 数据后新 URL 匹配成功")

	// 调用 StartBackgroundUpdate 方法（空实现），确保不发生异常或 panic
	blacklist.StartBackgroundUpdate(ctx, time.Second)
}

func TestSetupMockForTest(t *testing.T) {
	// 测试 SetupMockForTest 函数，现在接收三个返回值
	mockRongYao, mockCPage, mockClickRec := SetupMockForTest()

	// 验证全局变量已设置
	assert.Equal(t, RongYao, mockRongYao, "SetupMockForTest 应该正确设置 RongYao 全局变量")
	assert.Equal(t, CPageHighlight, mockCPage, "SetupMockForTest 应该正确设置 CPageHighlight 全局变量")
	assert.Equal(t, CPageClickRec, mockClickRec, "SetupMockForTest 应该正确设置 CPageClickRec 全局变量")

	// 测试一些基本功能确保它们能正常工作
	mockRongYao.AddAccurate("test")
	assert.True(t, RongYao.IsAccurateMatch("test"), "通过全局变量使用 mock 的功能应正常工作")

	mockCPage.AddHost("test.com")
	assert.True(t, CPageHighlight.IsHostMatch("test.com"), "通过全局变量使用 mock 的功能应正常工作")

	mockClickRec.AddHost("click.test.com")
	assert.True(t, CPageClickRec.IsHostMatch("click.test.com"), "通过全局变量使用 mock 的功能应正常工作")
}

func TestSetupMockRongYaoForTest(t *testing.T) {
	// 测试 SetupMockRongYaoForTest 函数
	mock := SetupMockRongYaoForTest()

	// 验证全局变量已设置
	assert.Equal(t, RongYao, mock, "SetupMockRongYaoForTest 应该正确设置 RongYao 全局变量")

	// 测试基本功能确保它能正常工作
	mock.AddAccurate("test")
	assert.True(t, RongYao.IsAccurateMatch("test"), "通过全局变量使用 mock 的功能应正常工作")
}

func TestSetupMockCPageHighlightForTest(t *testing.T) {
	// 测试 SetupMockCPageHighlightForTest 函数
	mock := SetupMockCPageHighlightForTest()

	// 验证全局变量已设置
	assert.Equal(t, CPageHighlight, mock, "SetupMockCPageHighlightForTest 应该正确设置 CPageHighlight 全局变量")

	// 测试基本功能确保它能正常工作
	mock.AddHost("test.com")
	assert.True(t, CPageHighlight.IsHostMatch("test.com"), "通过全局变量使用 mock 的功能应正常工作")
}

func TestSetupMockCPageClickRecForTest(t *testing.T) {
	// 测试 SetupMockCPageClickRecForTest 函数
	mock := SetupMockCPageClickRecForTest()

	// 验证全局变量已设置
	assert.Equal(t, CPageClickRec, mock, "SetupMockCPageClickRecForTest 应该正确设置 CPageClickRec 全局变量")

	// 测试基本功能确保它能正常工作
	mock.AddHost("click.test.com")
	assert.True(t, CPageClickRec.IsHostMatch("click.test.com"), "通过全局变量使用 mock 的功能应正常工作")
}
