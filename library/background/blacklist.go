package background

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/redis"
)

// 常量定义
const (
	// RongYao 相关常量
	RongYaoFuzzKey         = "go-suggest:rongyao-fuzz-black-list"
	RongYaoAccurateKey     = "go-suggest:rongyao-accurate-black-list"
	RongYaoFuzzKeySize     = 2000
	RongYaoAccurateKeySize = 20_0000

	// CPageHighlight 相关常量
	CPageHighlightHostKey     = "go-suggest:page-highlight-host-black-list"
	CPageHighlightURLKey      = "go-suggest:page-highlight-url-black-list"
	CPageHighlightHostKeySize = 2000
	CPageHighlightURLKeySize  = 20_0000

	// CPageClickRec 相关常量
	CPageClickRecHostKey     = "go-suggest:click-trigger-rec-host-black-list"
	CPageClickRecURLKey      = "go-suggest:click-trigger-rec-url-black-list"
	CPageClickRecHostKeySize = 2000
	CPageClickRecURLKeySize  = 20_0000
)

// BlackList 基础接口
type BlackList interface {
	StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration)
	LoadData(ctx context.Context) error
}

// RongYaoBlackListAPI 荣耀黑名单接口
type RongYaoBlackListAPI interface {
	BlackList
	IsAccurateMatch(key string) bool
	IsFuzzyMatch(key string) bool
}

// CPageHighlightBlackListAPI 落地页黑名单接口
type CPageHighlightBlackListAPI interface {
	BlackList
	IsHostMatch(host string) bool
	IsURLMatch(url string) bool
}

// CPageClickRecBlackListAPI 落地页点击推荐黑名单接口
type CPageClickRecBlackListAPI interface {
	BlackList
	IsHostMatch(host string) bool
	IsURLMatch(url string) bool
}

// BlackListStorage 黑名单存储接口
type BlackListStorage interface {
	// 添加一个条目到黑名单
	add(key string)
	// 检查一个条目是否匹配黑名单
	Match(key string) bool
	// 获取存储中的条目数量
	Size() int
	// 替换所有条目
	Replace(items []string)
}

// ExactMatchStorage 精确匹配存储实现
type ExactMatchStorage struct {
	items map[string]struct{}
}

// NewExactMatchStorage 创建新的精确匹配存储
func NewExactMatchStorage(initialSize int) BlackListStorage {
	if initialSize <= 0 {
		initialSize = 1000
	}
	return &ExactMatchStorage{
		items: make(map[string]struct{}, initialSize),
	}
}

// Add 实现添加方法
func (s *ExactMatchStorage) add(key string) {
	s.items[key] = struct{}{}
}

// Match 实现精确匹配方法
func (s *ExactMatchStorage) Match(key string) bool {
	_, exists := s.items[key]
	return exists
}

// Size 返回存储的条目数量
func (s *ExactMatchStorage) Size() int {
	return len(s.items)
}

// Replace 替换所有条目
func (s *ExactMatchStorage) Replace(items []string) {
	newItems := make(map[string]struct{}, len(items))
	s.items = newItems
	for _, item := range items {
		s.add(item)
	}
}

// FuzzyMatch3CrossStorage 模糊匹配3交叉存储实现
type FuzzyMatch3CrossStorage struct {
	items [][3]string
}

// NewFuzzyMatchStorage 创建新的模糊匹配存储
func NewFuzzyMatch3CrossStorage(initialSize int) BlackListStorage {
	if initialSize <= 0 {
		initialSize = 1000
	}
	return &FuzzyMatch3CrossStorage{
		items: make([][3]string, 0, initialSize),
	}
}

// Add 实现添加方法
func (s *FuzzyMatch3CrossStorage) add(key string) {
	parts := strings.Split(key, ";")
	// 最多取三个作为
	keyParts := [3]string{}
	for i := 0; i < 3 && i < len(parts); i++ {
		keyParts[i] = parts[i]
	}
	s.items = append(s.items, keyParts)
}

// Match 实现模糊匹配方法
func (s *FuzzyMatch3CrossStorage) Match(key string) bool {
	// 交叉匹配最多三个词
	for _, item := range s.items {
		if strings.Contains(key, item[0]) && strings.Contains(key, item[1]) && strings.Contains(key, item[2]) {
			return true
		}
	}
	return false
}

// Size 返回存储的条目数量
func (s *FuzzyMatch3CrossStorage) Size() int {
	return len(s.items)
}

// Replace 替换所有条目
func (s *FuzzyMatch3CrossStorage) Replace(items []string) {
	newItems := make([][3]string, 0, len(items))
	s.items = newItems
	for _, item := range items {
		s.add(item)
	}
}

// BaseBlackListManager 基础黑名单管理器
type BaseBlackListManager struct {
	client     redis.Client
	storage    BlackListStorage
	lock       sync.RWMutex
	keyPattern string
	currentKey string
	size       int
}

// RongYaoBlackListImpl 荣耀黑名单具体实现
type RongYaoBlackListImpl struct {
	fuzzMatch     *BaseBlackListManager
	accurateMatch *BaseBlackListManager
}

// CPageHighlightBlackListImpl 落地页黑名单具体实现
type CPageHighlightBlackListImpl struct {
	hostMatch *BaseBlackListManager
	urlMatch  *BaseBlackListManager
}

// CPageClickRecBlackListImpl 落地页黑名单具体实现
type CPageClickRecBlackListImpl struct {
	hostMatch *BaseBlackListManager
	urlMatch  *BaseBlackListManager
}

// 全局变量
var (
	RongYao        RongYaoBlackListAPI
	CPageHighlight CPageHighlightBlackListAPI
	CPageClickRec  CPageClickRecBlackListAPI
)

// NewBaseBlackListManager 创建基础黑名单管理器
func NewBaseBlackListManager(
	ctx context.Context,
	client redis.Client,
	keyPattern string,
	size int,
	storageCreator func(int) BlackListStorage,
) *BaseBlackListManager {
	manager := &BaseBlackListManager{
		client:     client,
		storage:    storageCreator(size),
		keyPattern: keyPattern,
		size:       size,
	}

	// 初始化时获取最新的 key 并加载数据
	manager.currentKey = manager.getLatestKey(ctx)
	if manager.currentKey != "" {
		if err := manager.loadData(ctx, manager.currentKey); err != nil {
			LoggerService.Error(ctx, keyPattern+"_black_list_err", logit.Error("redis error", err))
		}
	}

	return manager
}

// NewRongYaoBlackList 创建荣耀黑名单
func NewRongYaoBlackList(ctx context.Context, client redis.Client) RongYaoBlackListAPI {
	return &RongYaoBlackListImpl{
		fuzzMatch:     NewBaseBlackListManager(ctx, client, RongYaoFuzzKey, RongYaoFuzzKeySize, NewFuzzyMatch3CrossStorage),
		accurateMatch: NewBaseBlackListManager(ctx, client, RongYaoAccurateKey, RongYaoAccurateKeySize, NewExactMatchStorage),
	}
}

// NewCPageHighlightBlackList 创建落地页黑名单
func NewCPageHighlightBlackList(ctx context.Context, client redis.Client) CPageHighlightBlackListAPI {
	return &CPageHighlightBlackListImpl{
		hostMatch: NewBaseBlackListManager(ctx, client, CPageHighlightHostKey, CPageHighlightHostKeySize, NewExactMatchStorage),
		urlMatch:  NewBaseBlackListManager(ctx, client, CPageHighlightURLKey, CPageHighlightURLKeySize, NewExactMatchStorage),
	}
}

// NewCPageClickRecBlackList 创建落地页点击推荐黑名单
func NewCPageClickRecBlackList(ctx context.Context, client redis.Client) CPageClickRecBlackListAPI {
	return &CPageClickRecBlackListImpl{
		hostMatch: NewBaseBlackListManager(ctx, client, CPageClickRecHostKey, CPageClickRecHostKeySize, NewExactMatchStorage),
		urlMatch:  NewBaseBlackListManager(ctx, client, CPageClickRecURLKey, CPageClickRecURLKeySize, NewExactMatchStorage),
	}
}

// InitBlackListAsyncTask 初始化黑名单
func InitBlackListAsyncTask(ctx context.Context, redisClient redis.Client, updateInterval ...time.Duration) {
	RongYao = NewRongYaoBlackList(ctx, redisClient)
	CPageHighlight = NewCPageHighlightBlackList(ctx, redisClient)
	CPageClickRec = NewCPageClickRecBlackList(ctx, redisClient)

	// 启动后台更新
	interval := 60 * time.Second
	if len(updateInterval) > 0 {
		interval = updateInterval[0]
	}
	RongYao.StartBackgroundUpdate(ctx, interval)
	CPageHighlight.StartBackgroundUpdate(ctx, interval)
	CPageClickRec.StartBackgroundUpdate(ctx, interval)

	logit.AddNotice(ctx, logit.AutoField("black_list_init_success", 1))
}

// LoadData 实现 BlackList 接口的 LoadData 方法
func (r *RongYaoBlackListImpl) LoadData(ctx context.Context) error {
	// 添加空值检查
	if r == nil {
		return fmt.Errorf("RongYaoBlackListImpl is nil")
	}
	if err := r.fuzzMatch.loadData(ctx, r.fuzzMatch.currentKey); err != nil {
		return err
	}
	return r.accurateMatch.loadData(ctx, r.accurateMatch.currentKey)
}

// LoadData 实现 BlackList 接口的 LoadData 方法
func (c *CPageHighlightBlackListImpl) LoadData(ctx context.Context) error {
	// 添加空值检查
	if c == nil {
		return fmt.Errorf("CPageHighlightBlackListImpl is nil")
	}
	if err := c.hostMatch.loadData(ctx, c.hostMatch.currentKey); err != nil {
		return err
	}
	return c.urlMatch.loadData(ctx, c.urlMatch.currentKey)
}

// LoadData 实现 BlackList 接口的 LoadData 方法
func (c *CPageClickRecBlackListImpl) LoadData(ctx context.Context) error {
	// 添加空值检查
	if c == nil {
		return fmt.Errorf("CPageClickRecBlackListImpl is nil")
	}
	if err := c.hostMatch.loadData(ctx, c.hostMatch.currentKey); err != nil {
		return err
	}
	return c.urlMatch.loadData(ctx, c.urlMatch.currentKey)
}

// RongYao 的方法实现
func (r *RongYaoBlackListImpl) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	r.fuzzMatch.StartBackgroundUpdate(ctx, updateInterval)
	r.accurateMatch.StartBackgroundUpdate(ctx, updateInterval)
}

func (r *RongYaoBlackListImpl) IsFuzzyMatch(key string) bool {
	return r.fuzzMatch.fuzzyMatch(key)
}

func (r *RongYaoBlackListImpl) IsAccurateMatch(key string) bool {
	return r.accurateMatch.accurateMatch(key)
}

// CPageHighlight 的方法实现
func (c *CPageHighlightBlackListImpl) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	c.hostMatch.StartBackgroundUpdate(ctx, updateInterval)
	c.urlMatch.StartBackgroundUpdate(ctx, updateInterval)
}

func (c *CPageHighlightBlackListImpl) IsHostMatch(host string) bool {
	return c.hostMatch.accurateMatch(host)
}

func (c *CPageHighlightBlackListImpl) IsURLMatch(u string) bool {
	u, _ = url.QueryUnescape(u)
	return c.urlMatch.accurateMatch(u)
}

// CPageClickRec 的方法实现
func (c *CPageClickRecBlackListImpl) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	c.hostMatch.StartBackgroundUpdate(ctx, updateInterval)
	c.urlMatch.StartBackgroundUpdate(ctx, updateInterval)
}

func (c *CPageClickRecBlackListImpl) IsHostMatch(host string) bool {
	return c.hostMatch.accurateMatch(host)
}

func (c *CPageClickRecBlackListImpl) IsURLMatch(u string) bool {
	u, _ = url.QueryUnescape(u)
	return c.urlMatch.accurateMatch(u)
}

// BaseBlackListManager 的方法实现
func (m *BaseBlackListManager) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				LoggerService.Fatal(ctx, m.keyPattern+"_blacklist_panic", logit.AutoField("panic", r))
			}
		}()

		ticker := time.NewTicker(updateInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if latestKey := m.getLatestKey(ctx); latestKey != "" && latestKey != m.currentKey {
					if err := m.loadData(ctx, latestKey); err != nil {
						LoggerService.Error(ctx, m.keyPattern+"_black_list_load_data", logit.AutoField("err", err))
					} else {
						m.currentKey = latestKey
					}
				}
			}
		}
	}()
}

func (m *BaseBlackListManager) fuzzyMatch(key string) bool {
	m.lock.RLock()
	defer m.lock.RUnlock()
	return m.storage.Match(key)
}

func (m *BaseBlackListManager) accurateMatch(key string) bool {
	m.lock.RLock()
	defer m.lock.RUnlock()
	return m.storage.Match(key)
}

func (m *BaseBlackListManager) getLatestKey(ctx context.Context) string {
	var latestKey string
	var latestTimestamp string

	keys, err := m.client.SMembers(ctx, m.keyPattern).Result()
	if err != nil {
		LoggerService.Error(ctx, m.keyPattern+"_black_list_err", logit.Error("redis error", err))
		return ""
	}
	for _, key := range keys {
		timestamp := extractTimestamp(key)
		if timestamp > latestTimestamp {
			latestTimestamp = timestamp
			latestKey = key
		}
	}

	return latestKey
}

func (m *BaseBlackListManager) loadData(ctx context.Context, lastKey string) error {
	// 添加空值检查
	if m == nil {
		return fmt.Errorf("BaseBlackListManager is nil")
	}
	// 使用 KEYS 指令获取所有匹配的分片键
	keys, err := m.client.Keys(ctx, lastKey+":shard:*").Result()
	if err != nil {
		return fmt.Errorf("failed to retrieve keys from Redis: %w", err)
	}

	// 检查内存使用情况
	memUsageMB := 0.0
	for _, key := range keys {
		memUsageInterface, err := m.memoryUsage(ctx, key)
		if err != nil {
			return fmt.Errorf("failed to get set size from Redis: %w", err)
		}
		memUsage, _ := memUsageInterface.(int64)
		memUsageMB += float64(memUsage) / (1024 * 1024)
	}
	if memUsageMB > 50 {
		return fmt.Errorf("blacklist size over 50MB")
	}

	// 收集所有成员
	var allMembers []string
	for _, key := range keys {
		members, err := m.client.SMembers(ctx, key).Result()
		if err != nil {
			return fmt.Errorf("failed to get members from key %s: %w", key, err)
		}
		allMembers = append(allMembers, members...)
	}

	// 原子性地更新存储
	m.lock.Lock()
	defer m.lock.Unlock()
	m.storage.Replace(allMembers)
	return nil
}

func (m *BaseBlackListManager) memoryUsage(ctx context.Context, key string) (interface{}, error) {
	memUsageInterface, err := m.client.Do(ctx, "MEMORY", "USAGE", key).Result()
	return memUsageInterface, err
}

func extractTimestamp(key string) string {
	parts := strings.Split(key, ":")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return ""
}
