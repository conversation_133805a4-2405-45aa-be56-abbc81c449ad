package background

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/gdp/redis"
)

func TestRongYaoBlackList(t *testing.T) {
	// 启动 miniredis 测试实例
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()

	mini := testRedis.MiniRedis()
	LoggerService = logit.NewSimple(os.Stdout)

	// 准备测试数据
	testData := []struct {
		key   string
		value string
	}{
		// 模糊匹配数据 - 修改为符合分号分隔格式
		{"go-suggest:rongyao-fuzz-black-list", "go-suggest:rongyao-fuzz-black-list:1738846470"},
		{"go-suggest:rongyao-fuzz-black-list:1738846470:shard:0", "fuzz;word;test"},
		{"go-suggest:rongyao-fuzz-black-list:1738846470:shard:0", "sens;it;ive"},

		// 精确匹配数据
		{"go-suggest:rongyao-accurate-black-list", "go-suggest:rongyao-accurate-black-list:1738846470"},
		{"go-suggest:rongyao-accurate-black-list:1738846470:shard:0", "exact-match-1"},
		{"go-suggest:rongyao-accurate-black-list:1738846470:shard:0", "exact-match-2"},
	}

	// 写入测试数据
	for _, data := range testData {
		_, err := mini.SAdd(data.key, data.value)
		assert.NoError(t, err)
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Mock memoryUsage
	patch := gomonkey.NewPatches()
	defer patch.Reset()
	patch.ApplyFuncReturn((*BaseBlackListManager).memoryUsage, 1024*10, nil)

	// 初始化 RongYaoBlackList
	rongYao := NewRongYaoBlackList(ctx, testRedis.Client())
	rongYao.StartBackgroundUpdate(ctx, 1*time.Second)

	// 等待数据加载
	time.Sleep(150 * time.Millisecond)

	// 测试模糊匹配
	t.Run("FuzzyMatch", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected bool
			desc     string
		}{
			{"this contains fuzz and word and test here", true, "应该匹配包含的三个交叉词"},
			{"sensitive content with it", true, "应该匹配三个交叉词"},
			{"only fuzz here", false, "不应该匹配只有一个词的内容"},
			{"fuzz and word but no ", false, "不应该匹配只有两个词的内容"},
			{"normal content", false, "不应该匹配正常内容"},
		}

		for _, tc := range testCases {
			assert.Equal(t, tc.expected, rongYao.IsFuzzyMatch(tc.input), tc.desc)
		}
	})

	// 测试精确匹配
	t.Run("AccurateMatch", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected bool
			desc     string
		}{
			{"exact-match-1", true, "应该匹配精确词1"},
			{"exact-match-2", true, "应该匹配精确词2"},
			{"non-existent", false, "不应该匹配不存在的词"},
			{"partial-exact-match", false, "不应该部分匹配"},
		}

		for _, tc := range testCases {
			assert.Equal(t, tc.expected, rongYao.IsAccurateMatch(tc.input), tc.desc)
		}
	})

	// 测试更新机制
	t.Run("UpdateMechanism", func(t *testing.T) {
		// 添加新数据
		newData := []struct {
			key   string
			value string
		}{
			{"go-suggest:rongyao-fuzz-black-list", "go-suggest:rongyao-fuzz-black-list:1738847470"},
			{"go-suggest:rongyao-fuzz-black-list:1738847470:shard:0", "new;fuzz;word"},
			{"go-suggest:rongyao-accurate-black-list", "go-suggest:rongyao-accurate-black-list:1738847470"},
			{"go-suggest:rongyao-accurate-black-list:1738847470:shard:0", "new-exact-match"},
		}

		for _, data := range newData {
			_, err := mini.SAdd(data.key, data.value)
			assert.NoError(t, err)
		}

		// 等待更新
		time.Sleep(1500 * time.Millisecond)

		// 验证新数据
		assert.True(t, rongYao.IsFuzzyMatch("content with new and fuzz and word"), "应该匹配新的模糊词")
		assert.True(t, rongYao.IsAccurateMatch("new-exact-match"), "应该匹配新的精确词")
	})
}

func TestCPageHighlightBlackList(t *testing.T) {
	// 启动 miniredis 测试实例
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()

	mini := testRedis.MiniRedis()
	LoggerService = logit.NewSimple(os.Stdout)

	// 准备测试数据
	testData := []struct {
		key   string
		value string
	}{
		// Host 黑名单数据
		{"go-suggest:page-highlight-host-black-list", "go-suggest:page-highlight-host-black-list:1738846470"},
		{"go-suggest:page-highlight-host-black-list:1738846470:shard:0", "bad.example.com"},
		{"go-suggest:page-highlight-host-black-list:1738846470:shard:0", "spam.site.com"},

		// URL 黑名单数据
		{"go-suggest:page-highlight-url-black-list", "go-suggest:page-highlight-url-black-list:1738846470"},
		{"go-suggest:page-highlight-url-black-list:1738846470:shard:0", "https://example.com/spam"},
		{"go-suggest:page-highlight-url-black-list:1738846470:shard:0", "http://good.com/bad-page"},
	}

	// 写入测试数据
	for _, data := range testData {
		_, err := mini.SAdd(data.key, data.value)
		assert.NoError(t, err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	patch := gomonkey.NewPatches()
	defer patch.Reset()
	patch.ApplyFuncReturn((*BaseBlackListManager).memoryUsage, 1024*10, nil)

	// 初始化 CPageHighlightBlackList，使用较短的更新间隔方便测试
	updateInterval := 1 * time.Second
	cpage := NewCPageHighlightBlackList(ctx, testRedis.Client())
	cpage.StartBackgroundUpdate(ctx, updateInterval)

	// 等待数据加载
	time.Sleep(150 * time.Millisecond)

	// 测试 Host 匹配
	t.Run("HostMatch", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected bool
			desc     string
		}{
			{"bad.example.com", true, "应该匹配黑名单域名"},
			{"spam.site.com", true, "应该匹配黑名单域名"},
			{"good.example.com", false, "不应该匹配正常域名"},
		}

		for _, tc := range testCases {
			assert.Equal(t, tc.expected, cpage.IsHostMatch(tc.input), tc.desc)
		}
	})

	// 测试 URL 匹配
	t.Run("URLMatch", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected bool
			desc     string
		}{
			{"https://example.com/spam", true, "应该匹配黑名单URL"},
			{"http://good.com/bad-page", true, "应该匹配黑名单URL"},
			{"https://example.com/good", false, "不应该匹配正常URL"},
		}

		for _, tc := range testCases {
			assert.Equal(t, tc.expected, cpage.IsURLMatch(tc.input), tc.desc)
		}
	})

	// 测试更新机制
	t.Run("UpdateMechanism", func(t *testing.T) {
		// 添加新数据，时间戳更大
		newData := []struct {
			key   string
			value string
		}{
			{"go-suggest:page-highlight-host-black-list", "go-suggest:page-highlight-host-black-list:1738847470"},
			{"go-suggest:page-highlight-host-black-list:1738847470:shard:0", "new-bad-host.com"},
			{"go-suggest:page-highlight-url-black-list", "go-suggest:page-highlight-url-black-list:1738847470"},
			{"go-suggest:page-highlight-url-black-list:1738847470:shard:0", "https://new-bad-url.com/path"},
		}

		for _, data := range newData {
			_, err := mini.SAdd(data.key, data.value)
			assert.NoError(t, err)
		}

		// 等待黑名单更新（比更新间隔长一点以确保更新完成）
		time.Sleep(1500 * time.Millisecond)

		// 验证新数据是否生效
		assert.True(t, cpage.IsHostMatch("new-bad-host.com"), "应该匹配新添加的黑名单域名")
		assert.True(t, cpage.IsURLMatch("https://new-bad-url.com/path"), "应该匹配新添加的黑名单URL")
	})
}

func TestCPageClickRecBlackList(t *testing.T) {
	// 启动 miniredis 测试实例
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()

	mini := testRedis.MiniRedis()
	LoggerService = logit.NewSimple(os.Stdout)

	// 准备测试数据
	testData := []struct {
		key   string
		value string
	}{
		// Host 黑名单数据
		{"go-suggest:click-trigger-rec-host-black-list", "go-suggest:click-trigger-rec-host-black-list:1738846470"},
		{"go-suggest:click-trigger-rec-host-black-list:1738846470:shard:0", "bad.example.com"},
		{"go-suggest:click-trigger-rec-host-black-list:1738846470:shard:0", "spam.site.com"},

		// URL 黑名单数据
		{"go-suggest:click-trigger-rec-url-black-list", "go-suggest:click-trigger-rec-url-black-list:1738846470"},
		{"go-suggest:click-trigger-rec-url-black-list:1738846470:shard:0", "https://example.com/spam"},
		{"go-suggest:click-trigger-rec-url-black-list:1738846470:shard:0", "http://good.com/bad-page"},
	}

	// 写入测试数据
	for _, data := range testData {
		_, err := mini.SAdd(data.key, data.value)
		assert.NoError(t, err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	patch := gomonkey.NewPatches()
	defer patch.Reset()
	patch.ApplyFuncReturn((*BaseBlackListManager).memoryUsage, 1024*10, nil)

	// 初始化 CPageHighlightBlackList，使用较短的更新间隔方便测试
	updateInterval := 1 * time.Second
	cpage := NewCPageClickRecBlackList(ctx, testRedis.Client())
	cpage.StartBackgroundUpdate(ctx, updateInterval)

	// 等待数据加载
	time.Sleep(150 * time.Millisecond)

	// 测试 Host 匹配
	t.Run("HostMatch", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected bool
			desc     string
		}{
			{"bad.example.com", true, "应该匹配黑名单域名"},
			{"spam.site.com", true, "应该匹配黑名单域名"},
			{"good.example.com", false, "不应该匹配正常域名"},
		}

		for _, tc := range testCases {
			assert.Equal(t, tc.expected, cpage.IsHostMatch(tc.input), tc.desc)
		}
	})

	// 测试 URL 匹配
	t.Run("URLMatch", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected bool
			desc     string
		}{
			{"https://example.com/spam", true, "应该匹配黑名单URL"},
			{"http://good.com/bad-page", true, "应该匹配黑名单URL"},
			{"https://example.com/good", false, "不应该匹配正常URL"},
		}

		for _, tc := range testCases {
			assert.Equal(t, tc.expected, cpage.IsURLMatch(tc.input), tc.desc)
		}
	})

	// 测试更新机制
	t.Run("UpdateMechanism", func(t *testing.T) {
		// 添加新数据，时间戳更大
		newData := []struct {
			key   string
			value string
		}{
			{"go-suggest:click-trigger-rec-host-black-list", "go-suggest:click-trigger-rec-host-black-list:1738847470"},
			{"go-suggest:click-trigger-rec-host-black-list:1738847470:shard:0", "new-bad-host.com"},
			{"go-suggest:click-trigger-rec-url-black-list", "go-suggest:click-trigger-rec-url-black-list:1738847470"},
			{"go-suggest:click-trigger-rec-url-black-list:1738847470:shard:0", "https://new-bad-url.com/path"},
		}

		for _, data := range newData {
			_, err := mini.SAdd(data.key, data.value)
			assert.NoError(t, err)
		}

		// 等待黑名单更新（比更新间隔长一点以确保更新完成）
		time.Sleep(1500 * time.Millisecond)

		// 验证新数据是否生效
		assert.True(t, cpage.IsHostMatch("new-bad-host.com"), "应该匹配新添加的黑名单域名")
		assert.True(t, cpage.IsURLMatch("https://new-bad-url.com/path"), "应该匹配新添加的黑名单URL")
	})
}
