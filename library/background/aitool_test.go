package background

import (
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"sync"
	"testing"

	"github.com/BurntSushi/toml"
	"github.com/knadh/koanf"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// 创建临时配置文件供测试使用
func createTempConfFiles(t *testing.T) (string, string, func()) {
	aitoolTestConfPath := env.ConfRootPath() + "/aitool_test"
	// 创建配置目录结构
	err := os.MkdirAll(aitoolTestConfPath, 0755)
	require.NoError(t, err)

	// 创建工具配置文件
	toolConfig := entity.AiToolConfig{
		Tools: map[string]entity.AiToolItem{
			"testToolV0": {
				Schema:    "test_schema",
				Icon:      "test_icon",
				IconDark:  "test_icon_dark",
				IconNight: "test_icon_night",
				Text:      "测试工具",
				Type:      "testTool",
				PanelVer:  1,
			},
			"testToolV1": {
				Schema:    "test_schema_v1",
				Icon:      "test_icon_v1",
				IconDark:  "test_icon_dark_v1",
				IconNight: "test_icon_night_v1",
				Text:      "测试工具V1",
				Type:      "testTool",
				PanelVer:  2,
			},
		},
		ToolVer: map[string][]entity.ToolVersion{
			"i0": {
				{
					AppVer:         "15.3",
					Tools:          []string{"testToolV1"},
					IncognitoTools: []string{"testToolV1"},
				},
			},
			"default": {
				{
					AppVer:         "1.0",
					Tools:          []string{"testToolV0"},
					IncognitoTools: []string{"testToolV0"},
				},
			},
		},
	}

	// 将配置写入TOML文件
	toolFile := filepath.Join(aitoolTestConfPath, "tool.toml")
	f, err := os.Create(toolFile)
	require.NoError(t, err)
	err = toml.NewEncoder(f).Encode(toolConfig)
	require.NoError(t, err)
	f.Close()

	// 创建面板配置文件
	panelConfig := map[string]interface{}{
		"testToolV0": map[string]interface{}{
			"title": "测试工具",
			"hint":  "这是一个测试提示",
			"items": []interface{}{
				map[string]interface{}{
					"title":    "测试项目",
					"template": "wordList",
					"data":     []interface{}{},
				},
			},
		},
		"testToolV1": map[string]interface{}{
			"title": "测试工具V1",
			"hint":  "这是测试工具V1的提示",
			"items": []interface{}{
				map[string]interface{}{
					"title":    "测试项目V1",
					"template": "wordList",
					"data":     []interface{}{},
				},
			},
		},
	}

	// 将配置写入JSON文件
	panelFile := filepath.Join(aitoolTestConfPath, "panel.json")
	panelJSON, err := json.MarshalIndent(panelConfig, "", "  ")
	require.NoError(t, err)
	err = os.WriteFile(panelFile, panelJSON, 0644)
	require.NoError(t, err)

	// 返回临时文件路径和清理函数
	return "aitool_test/tool.toml", "aitool_test/panel.json", func() {
		os.RemoveAll(aitoolTestConfPath)
	}
}

// 测试AiToolManagerImpl实现
func TestAiToolManagerImpl(t *testing.T) {
	// 创建测试配置文件
	toolRelPath, panelRelPath, cleanup := createTempConfFiles(t)
	defer cleanup()

	t.Run("NewAiToolManager", func(t *testing.T) {
		// 测试创建管理器
		manager := NewAiToolManager(toolRelPath, panelRelPath)
		require.NotNil(t, manager)

		// 测试获取配置
		config := manager.GetAiToolConfig()
		assert.NotEmpty(t, config.Tools)
		assert.NotEmpty(t, config.ToolVer)

		// 验证工具项
		assert.Contains(t, config.Tools, "testToolV0")
		assert.Contains(t, config.Tools, "testToolV1")

		// 验证工具版本
		assert.Contains(t, config.ToolVer, "i0")
		assert.Contains(t, config.ToolVer, "default")

		// 测试获取默认工具
		defaultTools := manager.GetDefaultAiTools()
		assert.Len(t, defaultTools, 1)
		assert.Equal(t, "测试工具", defaultTools[0].Text)
	})

	t.Run("LoadConfig", func(t *testing.T) {
		// 创建管理器
		manager := NewAiToolManager(toolRelPath, panelRelPath)
		require.NotNil(t, manager)

		// 测试加载配置
		err := manager.LoadConfig()
		assert.NoError(t, err)

		// 验证面板配置是否被正确加载
		config := manager.GetAiToolConfig()
		assert.NotNil(t, config.Tools["testToolV0"].Panel)
		panel, ok := config.Tools["testToolV0"].Panel.(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, "测试工具", panel["title"])
	})

	t.Run("GetDefaultAiToolsEmptyConfig", func(t *testing.T) {
		// 创建空配置的管理器
		emptyManager := &AiToolManagerImpl{
			aiToolConfig:    entity.AiToolConfig{},
			configMutex:     sync.RWMutex{},
			toolConfigPath:  toolRelPath,
			panelConfigPath: panelRelPath,
			k:               koanf.New("."),
		}

		// 测试空配置下获取默认工具
		defaultTools := emptyManager.GetDefaultAiTools()
		assert.Empty(t, defaultTools)
	})
}

// 测试全局函数
func TestGlobalAiToolFunctions(t *testing.T) {
	// 设置mock
	mockManager := SetupMockAiToolManagerForTest()
	defer func() {
		// 清除mock
		AiToolManager = nil
	}()

	// 准备测试数据
	testItem := entity.AiToolItem{
		Schema: "test_schema",
		Icon:   "test_icon",
		Text:   "测试工具",
		Type:   "testTool",
	}

	defaultTools := []entity.AiToolItem{testItem}
	mockManager.SetDefaultAiTools(defaultTools)

	// 添加工具配置
	mockManager.AddToolItem("testTool", testItem)

	// 添加工具版本
	mockManager.AddToolVersion("default", entity.ToolVersion{
		AppVer: "1.0",
		Tools:  []string{"testTool"},
	})

	// 测试 GetDefaultAiTools
	t.Run("GetDefaultAiTools", func(t *testing.T) {
		tools := GetDefaultAiTools()
		assert.Equal(t, defaultTools, tools)
	})

	// 测试 GetAiToolConfig
	t.Run("GetAiToolConfig", func(t *testing.T) {
		config := GetAiToolConfig()
		assert.Contains(t, config.Tools, "testTool")
		assert.Contains(t, config.ToolVer, "default")
	})

	// 测试 GetAiToolConfig 空情况
	t.Run("GetAiToolConfigNil", func(t *testing.T) {
		// 临时置空
		oldManager := AiToolManager
		AiToolManager = nil
		defer func() {
			AiToolManager = oldManager
		}()

		config := GetAiToolConfig()
		assert.Empty(t, config.Tools)
		assert.Empty(t, config.ToolVer)
	})

	// 测试 GetDefaultAiTools 空情况
	t.Run("GetDefaultAiToolsNil", func(t *testing.T) {
		// 临时置空
		oldManager := AiToolManager
		AiToolManager = nil
		defer func() {
			AiToolManager = oldManager
		}()

		tools := GetDefaultAiTools()
		assert.Empty(t, tools)
	})
}

// 测试初始化函数
func TestInitAiToolConfigTask(t *testing.T) {

	// 创建测试上下文
	ctx := context.Background()
	LoggerService = logit.NewSimple(os.Stdout)
	// 测试初始化函数
	InitAiToolConfigTask(ctx)

	// 验证是否创建了全局实例
	assert.NotNil(t, AiToolManager)

	// 验证获取配置可用
	config := AiToolManager.GetAiToolConfig()
	assert.NotEmpty(t, config.Tools)
	assert.NotEmpty(t, config.ToolVer)
}

// MockAiToolAPI 实现了 AiToolAPI 接口，用于测试
type MockAiToolAPI struct {
	mock.Mock
}

func (m *MockAiToolAPI) GetAiToolConfig() entity.AiToolConfig {
	args := m.Called()
	return args.Get(0).(entity.AiToolConfig)
}

func (m *MockAiToolAPI) GetDefaultAiTools() []entity.AiToolItem {
	args := m.Called()
	return args.Get(0).([]entity.AiToolItem)
}

func (m *MockAiToolAPI) LoadConfig() error {
	args := m.Called()
	return args.Error(0)
}

// NewMockAiToolAPI 创建一个新的 MockAiToolAPI 实例
func NewMockAiToolAPI() *MockAiToolAPI {
	return &MockAiToolAPI{}
}

// SetupMockAiToolAPISuccess 设置一个"成功"场景的 MockAiToolAPI
func SetupMockAiToolAPISuccess() *MockAiToolAPI {
	mockAPI := NewMockAiToolAPI()

	// 设置成功返回的配置
	successConfig := entity.AiToolConfig{
		ToolVer: map[string][]entity.ToolVersion{
			"default": {
				{
					AppVer: "v1.0",
					Tools:  []string{"chat", "image", "document"},
				},
			},
		},
		Tools: map[string]entity.AiToolItem{
			"chat": {
				Schema:   "chat",
				Text:     "智能对话",
				Icon:     "chat-icon.png",
				Type:     "chat",
				PanelVer: 1,
			},
			"image": {
				Schema:   "image",
				Text:     "图像生成",
				Icon:     "image-icon.png",
				Type:     "image",
				PanelVer: 1,
			},
			"document": {
				Schema:   "document",
				Text:     "文档处理",
				Icon:     "doc-icon.png",
				Type:     "document",
				PanelVer: 1,
			},
		},
	}

	successTools := []entity.AiToolItem{
		{
			Schema:   "chat",
			Text:     "智能对话",
			Icon:     "chat-icon.png",
			Type:     "chat",
			PanelVer: 1,
		},
		{
			Schema:   "image",
			Text:     "图像生成",
			Icon:     "image-icon.png",
			Type:     "image",
			PanelVer: 1,
		},
		{
			Schema:   "document",
			Text:     "文档处理",
			Icon:     "doc-icon.png",
			Type:     "document",
			PanelVer: 1,
		},
	}

	// 设置方法返回值
	mockAPI.On("GetAiToolConfig").Return(successConfig)
	mockAPI.On("GetDefaultAiTools").Return(successTools)
	mockAPI.On("LoadConfig").Return(nil)

	return mockAPI
}

// SetupMockAiToolAPIFailed 设置一个"失败"场景的 MockAiToolAPI
func SetupMockAiToolAPIFailed() *MockAiToolAPI {
	mockAPI := NewMockAiToolAPI()

	// 设置失败场景
	emptyConfig := entity.AiToolConfig{}
	emptyTools := []entity.AiToolItem{}

	mockAPI.On("GetAiToolConfig").Return(emptyConfig)
	mockAPI.On("GetDefaultAiTools").Return(emptyTools)
	mockAPI.On("LoadConfig").Return(assert.AnError)

	return mockAPI
}

// SetupMockAiToolAPICustom 创建一个可以自定义行为的 MockAiToolAPI
func SetupMockAiToolAPICustom() *MockAiToolAPI {
	return NewMockAiToolAPI()
}

// 使用示例：在测试中使用 MockAiToolAPI
func TestMockAiToolAPI_Usage(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("成功场景", func(t *testing.T) {
		mockAPI := SetupMockAiToolAPISuccess()

		// 测试 GetAiToolConfig
		config := mockAPI.GetAiToolConfig()
		assert.NotNil(t, config.ToolVer)
		assert.NotNil(t, config.Tools)
		assert.Len(t, config.Tools, 3)

		// 测试 GetDefaultAiTools
		tools := mockAPI.GetDefaultAiTools()
		assert.NotNil(t, tools)
		assert.Len(t, tools, 3)
		assert.Equal(t, "chat", tools[0].Schema)
		assert.Equal(t, "智能对话", tools[0].Text)

		// 测试 LoadConfig
		err := mockAPI.LoadConfig()
		assert.NoError(t, err)

		mockAPI.AssertExpectations(t)
	})

	// ========== 示例2：失败场景 ==========
	t.Run("失败场景", func(t *testing.T) {
		mockAPI := SetupMockAiToolAPIFailed()

		// 测试配置为空
		config := mockAPI.GetAiToolConfig()
		assert.Equal(t, entity.AiToolConfig{}, config)

		// 测试工具列表为空
		tools := mockAPI.GetDefaultAiTools()
		assert.Empty(t, tools)

		// 测试加载配置失败
		err := mockAPI.LoadConfig()
		assert.Error(t, err)

		mockAPI.AssertExpectations(t)
	})

	// ========== 示例3：自定义场景 ==========
	t.Run("自定义场景", func(t *testing.T) {
		mockAPI := SetupMockAiToolAPICustom()

		// 自定义配置
		customConfig := entity.AiToolConfig{
			ToolVer: map[string][]entity.ToolVersion{
				"custom": {
					{
						AppVer: "v2.0",
						Tools:  []string{"custom-tool"},
					},
				},
			},
			Tools: map[string]entity.AiToolItem{
				"custom-tool": {
					Schema:   "custom-tool",
					Text:     "自定义工具",
					Icon:     "custom-icon.png",
					Type:     "custom",
					PanelVer: 1,
				},
			},
		}

		customTools := []entity.AiToolItem{
			{
				Schema:   "custom-tool",
				Text:     "自定义工具",
				Icon:     "custom-icon.png",
				Type:     "custom",
				PanelVer: 1,
			},
		}

		// 设置自定义行为
		mockAPI.On("GetAiToolConfig").Return(customConfig)
		mockAPI.On("GetDefaultAiTools").Return(customTools)
		mockAPI.On("LoadConfig").Return(nil)

		// 测试自定义行为
		config := mockAPI.GetAiToolConfig()
		assert.Equal(t, customConfig, config)
		assert.Contains(t, config.Tools, "custom-tool")

		tools := mockAPI.GetDefaultAiTools()
		assert.Len(t, tools, 1)
		assert.Equal(t, "custom-tool", tools[0].Schema)
		assert.Equal(t, "自定义工具", tools[0].Text)

		err := mockAPI.LoadConfig()
		assert.NoError(t, err)

		mockAPI.AssertExpectations(t)
	})
}
