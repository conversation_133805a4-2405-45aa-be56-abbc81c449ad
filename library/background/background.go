// Package background 该包用于执行一些后台任务，现有：【荣耀屏蔽词】任务在运行
package background

import (
	"context"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

var LoggerService logit.Logger

func InitBackgroundTask(ctx context.Context) {
	ctx = logit.NewContext(ctx) // 填充日志标记
	webLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("background/log.toml"))
	if err != nil {
		panic(err.Error())
	}
	LoggerService = webLogger
	logit.AddMetaFields(ctx,
		logit.String(logit.FieldLogID, logit.NewLogID()),
		logit.String("appname", env.AppName()),
		logit.String("idc", env.IDC()),
		logit.String("local_ip", env.LocalIP()),
		logit.String("pid", env.PIDString()),
	)
	ts := logit.NewTimeCost("cost")

	// 初始化黑名单后台任务
	InitBlackListAsyncTask(ctx, common.RedisClient)

	// 初始化AI工具配置后台任务
	InitAiToolConfigTask(ctx)

	// 初始化开关管理器后台任务
	InitSwitchTask(ctx, common.RedisClient)

	LoggerService.Notice(ctx, "init_ok", ts())
}
