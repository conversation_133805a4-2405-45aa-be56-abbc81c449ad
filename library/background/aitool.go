// Package background 包含后台任务的实现
package background

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"sync"

	"github.com/knadh/koanf"
	"github.com/knadh/koanf/parsers/json"
	"github.com/knadh/koanf/parsers/toml"
	"github.com/knadh/koanf/providers/file"

	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// AiToolAPI 定义 AI 工具服务接口
type AiToolAPI interface {
	// GetAiToolConfig 获取当前 AI 工具配置
	GetAiToolConfig() entity.AiToolConfig
	// GetDefaultAiTools 获取默认的 AI 工具列表
	GetDefaultAiTools() []entity.AiToolItem
	// LoadConfig 加载配置
	LoadConfig() error
}

// AiToolManagerImpl AI 工具管理实现
type AiToolManagerImpl struct {
	aiToolConfig    entity.AiToolConfig
	configMutex     sync.RWMutex
	toolConfigPath  string
	panelConfigPath string
	// koanf实例
	k *koanf.Koanf
}

// 全局变量
var (
	// AiToolManager 全局 AI 工具管理器实例
	AiToolManager AiToolAPI

	// 默认配置文件路径
	defaultToolConfigPath             = "ai_tool/tool.toml"
	defaultPanelConfigPath            = "ai_tool/panel.json"
	defaultPlusSignFunctionConfigPath = "ai_tool/plus_sign_function.json"
)

// NewAiToolManager 创建新的 AI 工具管理器
func NewAiToolManager(toolConfigPath, panelConfigPath string) AiToolAPI {
	if toolConfigPath == "" {
		toolConfigPath = defaultToolConfigPath
	}
	if panelConfigPath == "" {
		panelConfigPath = defaultPanelConfigPath
	}

	manager := &AiToolManagerImpl{
		configMutex:     sync.RWMutex{},
		toolConfigPath:  toolConfigPath,
		panelConfigPath: panelConfigPath,
		k:               koanf.New("."),
	}

	// 初始加载配置
	if err := manager.LoadConfig(); err != nil {
		// 只记录错误，但继续使用空配置
		fmt.Printf("Failed to load initial AI tool config: %v\n", err)
	}

	return manager
}

// GetAiToolConfig 获取当前AI工具配置（线程安全）
func (m *AiToolManagerImpl) GetAiToolConfig() entity.AiToolConfig {
	m.configMutex.RLock()
	defer m.configMutex.RUnlock()
	return m.aiToolConfig
}

// GetDefaultAiTools 获取默认的AI工具列表
func (m *AiToolManagerImpl) GetDefaultAiTools() []entity.AiToolItem {
	m.configMutex.RLock()
	defer m.configMutex.RUnlock()

	if len(m.aiToolConfig.ToolVer) == 0 {
		return []entity.AiToolItem{}
	}

	toolVer, ok := m.aiToolConfig.ToolVer["default"]
	if !ok || len(toolVer) == 0 {
		return []entity.AiToolItem{}
	}

	aiTool := []entity.AiToolItem{}
	for _, toolVer := range toolVer {
		for _, tool := range toolVer.Tools {
			if item, exists := m.aiToolConfig.Tools[tool]; exists {
				aiTool = append(aiTool, item)
			}
		}
	}
	return aiTool
}

// LoadConfig 加载AI工具配置
func (m *AiToolManagerImpl) LoadConfig() error {
	var newConfig entity.AiToolConfig

	// 清空koanf配置
	m.k.Delete("")

	// 加载工具基础配置
	toolConfigFile := filepath.Join(env.ConfRootPath(), m.toolConfigPath)
	if err := m.k.Load(file.Provider(toolConfigFile), toml.Parser()); err != nil {
		return fmt.Errorf("failed to load tool configuration file: %w", err)
	}

	// 解析工具基础配置到结构体
	if err := m.k.Unmarshal("", &newConfig); err != nil {
		return fmt.Errorf("failed to parse tool configuration: %w", err)
	}

	// 使用koanf加载面板配置（JSON格式）
	panelConfigFile := filepath.Join(env.ConfRootPath(), m.panelConfigPath)
	panelConfig := koanf.New(".")

	// 加载面板JSON配置
	if err := panelConfig.Load(file.Provider(panelConfigFile), json.Parser()); err != nil {
		return fmt.Errorf("failed to load panel configuration: %w", err)
	}
	if len(newConfig.ToolVer) == 0 {
		return fmt.Errorf("empty tool config")
	}
	// 获取面板配置数据
	panelDataMap := panelConfig.Raw()
	for toolName, panelData := range panelDataMap {
		for storedKey, tool := range newConfig.Tools {
			if strings.EqualFold(storedKey, toolName) {
				tool.Panel = panelData
				newConfig.Tools[storedKey] = tool
				break
			}
		}
	}

	// 所有配置加载成功，设置全局配置
	m.configMutex.Lock()
	defer m.configMutex.Unlock()
	m.aiToolConfig = newConfig
	return nil
}

// InitAiToolConfigTask 初始化AI工具配置后台任务
func InitAiToolConfigTask(ctx context.Context) {
	// 创建并设置全局实例
	AiToolManager = NewAiToolManager(defaultToolConfigPath, defaultPanelConfigPath)

	// 检查初始化是否成功
	if AiToolManager.GetDefaultAiTools() != nil {
		LoggerService.Notice(ctx, "AI tool configuration initialized successfully")
	}

	// 设置配置文件监听，热更新
	// 暂不进行热更新
	// setupAiToolConfigWatch(ctx)
}

// 设置配置文件监听
func setupAiToolConfigWatch(ctx context.Context) {
	// 监听工具配置文件 (TOML 文件)
	toolConfigFile := filepath.Join(env.ConfRootPath(), defaultToolConfigPath)
	toolProvider := file.Provider(toolConfigFile)

	// 设置工具配置文件变更监听
	err := toolProvider.Watch(func(event interface{}, err error) {
		if err != nil {
			LoggerService.Error(ctx, fmt.Sprintf("Tool config watch error: %v", err))
			return
		}

		LoggerService.Notice(ctx, fmt.Sprintf("Tool configuration file changed: %s", toolConfigFile))
		if err := AiToolManager.LoadConfig(); err != nil {
			LoggerService.Error(ctx, fmt.Sprintf("Failed to reload configuration: %v", err))
			return
		}
		LoggerService.Notice(ctx, "Configuration reloaded successfully")

	})
	if err != nil {
		LoggerService.Error(ctx, fmt.Sprintf("Failed to watch tool configuration file: %v", err))
	}

}

// 兼容旧的API
// GetAiToolConfig 获取当前AI工具配置（线程安全）
func GetAiToolConfig() entity.AiToolConfig {
	if AiToolManager == nil {
		return entity.AiToolConfig{}
	}
	return AiToolManager.GetAiToolConfig()
}

// GetDefaultAiTools 获取默认的AI工具列表
func GetDefaultAiTools() []entity.AiToolItem {
	if AiToolManager == nil {
		return []entity.AiToolItem{}
	}
	return AiToolManager.GetDefaultAiTools()
}
