package background

import (
	"context"
	"time"

	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// MockRongYaoBlackList 是 RongYaoBlackListAPI 的测试实现
type MockRongYaoBlackList struct {
	accurateStorage BlackListStorage
	fuzzyStorage    BlackListStorage
}

// NewMockRongYaoBlackList 创建一个新的 MockRongYaoBlackList 实例
func NewMockRongYaoBlackList() *MockRongYaoBlackList {
	return &MockRongYaoBlackList{
		accurateStorage: NewExactMatchStorage(1000),
		fuzzyStorage:    NewFuzzyMatch3CrossStorage(1000),
	}
}

// StartBackgroundUpdate 实现 BlackList 接口，测试中为空操作
func (m *MockRongYaoBlackList) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	// 空实现
	_ = ctx
	_ = updateInterval
}

// LoadData 实现 BlackList 接口，测试中为空操作
func (m *MockRongYaoBlackList) LoadData(ctx context.Context) error {
	// 空实现
	_ = ctx
	return nil
}

// IsAccurateMatch 检查是否精确匹配黑名单
func (m *MockRongYaoBlackList) IsAccurateMatch(key string) bool {
	return m.accurateStorage.Match(key)
}

// IsFuzzyMatch 检查是否模糊匹配黑名单
func (m *MockRongYaoBlackList) IsFuzzyMatch(key string) bool {
	return m.fuzzyStorage.Match(key)
}

// AddAccurate 添加精确匹配的项到黑名单
func (m *MockRongYaoBlackList) AddAccurate(key string) {
	m.accurateStorage.add(key)
}

// AddFuzzy 添加模糊匹配的项到黑名单
func (m *MockRongYaoBlackList) AddFuzzy(key string) {
	m.fuzzyStorage.add(key)
}

// SetAccurateData 设置精确匹配数据
func (m *MockRongYaoBlackList) SetAccurateData(data []string) {
	m.accurateStorage.Replace(data)
}

// SetFuzzData 设置模糊匹配数据
func (m *MockRongYaoBlackList) SetFuzzData(data []string) {
	m.fuzzyStorage.Replace(data)
}

// MockCPageHighlightBlackList 是 CPageHighlightBlackListAPI 的测试实现
type MockCPageHighlightBlackList struct {
	hostStorage BlackListStorage
	urlStorage  BlackListStorage
}

// NewMockCPageHighlightBlackList 创建一个新的 MockCPageHighlightBlackList 实例
func NewMockCPageHighlightBlackList() *MockCPageHighlightBlackList {
	return &MockCPageHighlightBlackList{
		hostStorage: NewExactMatchStorage(1000),
		urlStorage:  NewExactMatchStorage(1000),
	}
}

// StartBackgroundUpdate 实现 BlackList 接口，测试中为空操作
func (m *MockCPageHighlightBlackList) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	// 空实现
	_ = ctx
	_ = updateInterval
}

// LoadData 实现 BlackList 接口，测试中为空操作
func (m *MockCPageHighlightBlackList) LoadData(ctx context.Context) error {
	// 空实现
	_ = ctx
	return nil
}

// IsHostMatch 检查是否匹配主机名黑名单
func (m *MockCPageHighlightBlackList) IsHostMatch(host string) bool {
	return m.hostStorage.Match(host)
}

// IsURLMatch 检查是否匹配 URL 黑名单
func (m *MockCPageHighlightBlackList) IsURLMatch(url string) bool {
	return m.urlStorage.Match(url)
}

// AddHost 添加主机名到黑名单
func (m *MockCPageHighlightBlackList) AddHost(host string) {
	m.hostStorage.add(host)
}

// AddURL 添加 URL 到黑名单
func (m *MockCPageHighlightBlackList) AddURL(url string) {
	m.urlStorage.add(url)
}

// SetHostData 设置主机名黑名单数据
func (m *MockCPageHighlightBlackList) SetHostData(data []string) {
	m.hostStorage.Replace(data)
}

// SetURLData 设置 URL 黑名单数据
func (m *MockCPageHighlightBlackList) SetURLData(data []string) {
	m.urlStorage.Replace(data)
}

// MockCPageClickRecBlackList 是 CPageClickRecBlackListAPI 的测试实现
type MockCPageClickRecBlackList struct {
	hostStorage BlackListStorage
	urlStorage  BlackListStorage
}

// NewMockCPageClickRecBlackList 创建一个新的 MockCPageClickRecBlackList 实例
func NewMockCPageClickRecBlackList() *MockCPageClickRecBlackList {
	return &MockCPageClickRecBlackList{
		hostStorage: NewExactMatchStorage(1000),
		urlStorage:  NewExactMatchStorage(1000),
	}
}

// StartBackgroundUpdate 实现 BlackList 接口，测试中为空操作
func (m *MockCPageClickRecBlackList) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	// 空实现
	_ = ctx
	_ = updateInterval
}

// LoadData 实现 BlackList 接口，测试中为空操作
func (m *MockCPageClickRecBlackList) LoadData(ctx context.Context) error {
	// 空实现
	_ = ctx
	return nil
}

// IsHostMatch 检查是否匹配主机名黑名单
func (m *MockCPageClickRecBlackList) IsHostMatch(host string) bool {
	return m.hostStorage.Match(host)
}

// IsURLMatch 检查是否匹配 URL 黑名单
func (m *MockCPageClickRecBlackList) IsURLMatch(url string) bool {
	return m.urlStorage.Match(url)
}

// AddHost 添加主机名到黑名单
func (m *MockCPageClickRecBlackList) AddHost(host string) {
	m.hostStorage.add(host)
}

// AddURL 添加 URL 到黑名单
func (m *MockCPageClickRecBlackList) AddURL(url string) {
	m.urlStorage.add(url)
}

// SetHostData 设置主机名黑名单数据
func (m *MockCPageClickRecBlackList) SetHostData(data []string) {
	m.hostStorage.Replace(data)
}

// SetURLData 设置 URL 黑名单数据
func (m *MockCPageClickRecBlackList) SetURLData(data []string) {
	m.urlStorage.Replace(data)
}

// SetupMockForTest 设置全局变量用于测试
func SetupMockForTest() (*MockRongYaoBlackList, *MockCPageHighlightBlackList, *MockCPageClickRecBlackList) {
	mockRongYao := NewMockRongYaoBlackList()
	mockCPageHighlight := NewMockCPageHighlightBlackList()
	mockCPageClickRec := NewMockCPageClickRecBlackList()

	// 设置全局变量
	RongYao = mockRongYao
	CPageHighlight = mockCPageHighlight
	CPageClickRec = mockCPageClickRec

	return mockRongYao, mockCPageHighlight, mockCPageClickRec
}

// SetupMockRongYaoForTest 仅设置 RongYao 全局变量用于测试
func SetupMockRongYaoForTest() *MockRongYaoBlackList {
	mock := NewMockRongYaoBlackList()
	RongYao = mock
	return mock
}

// SetupMockCPageHighlightForTest 仅设置 CPageHighlight 全局变量用于测试
func SetupMockCPageHighlightForTest() *MockCPageHighlightBlackList {
	mock := NewMockCPageHighlightBlackList()
	CPageHighlight = mock
	return mock
}

// SetupMockCPageClickRecForTest 仅设置 CPageClickRec 全局变量用于测试
func SetupMockCPageClickRecForTest() *MockCPageClickRecBlackList {
	mock := NewMockCPageClickRecBlackList()
	CPageClickRec = mock
	return mock
}

// -------------- AI工具相关的Mock对象 --------------

// MockAiToolManager 实现 AiToolAPI 接口的测试实现
type MockAiToolManager struct {
	aiToolConfig entity.AiToolConfig
	defaultTools []entity.AiToolItem
}

// NewMockAiToolManager 创建一个新的 MockAiToolManager 实例
func NewMockAiToolManager() *MockAiToolManager {
	return &MockAiToolManager{
		aiToolConfig: entity.AiToolConfig{
			Tools:   make(map[string]entity.AiToolItem),
			ToolVer: make(map[string][]entity.ToolVersion),
		},
		defaultTools: []entity.AiToolItem{},
	}
}

// GetAiToolConfig 获取当前 AI 工具配置
func (m *MockAiToolManager) GetAiToolConfig() entity.AiToolConfig {
	return m.aiToolConfig
}

// GetDefaultAiTools 获取默认的 AI 工具列表
func (m *MockAiToolManager) GetDefaultAiTools() []entity.AiToolItem {
	return m.defaultTools
}

// LoadConfig 加载配置 - 在测试中为空实现
func (m *MockAiToolManager) LoadConfig() error {
	return nil
}

// SetAiToolConfig 设置 AI 工具配置，用于测试
func (m *MockAiToolManager) SetAiToolConfig(config entity.AiToolConfig) {
	m.aiToolConfig = config
}

// SetDefaultAiTools 设置默认工具列表，用于测试
func (m *MockAiToolManager) SetDefaultAiTools(tools []entity.AiToolItem) {
	m.defaultTools = tools
}

// AddToolItem 添加工具项，用于测试
func (m *MockAiToolManager) AddToolItem(key string, item entity.AiToolItem) {
	// 深复制使测试独立
	if m.aiToolConfig.Tools == nil {
		m.aiToolConfig.Tools = make(map[string]entity.AiToolItem)
	}
	m.aiToolConfig.Tools[key] = item
}

// AddToolVersion 添加工具版本信息，用于测试
func (m *MockAiToolManager) AddToolVersion(branch string, ver entity.ToolVersion) {
	if m.aiToolConfig.ToolVer == nil {
		m.aiToolConfig.ToolVer = make(map[string][]entity.ToolVersion)
	}
	m.aiToolConfig.ToolVer[branch] = append(m.aiToolConfig.ToolVer[branch], ver)
}

// SetupMockAiToolManagerForTest 设置 AiToolManager 全局变量用于测试
func SetupMockAiToolManagerForTest() *MockAiToolManager {
	mock := NewMockAiToolManager()
	AiToolManager = mock
	return mock
}
