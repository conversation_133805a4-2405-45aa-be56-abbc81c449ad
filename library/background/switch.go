package background

import (
	"context"
	"sync"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/redis"
)

// 常量定义
const (
	// QueryImageOffKey 查询图片开关的 Redis key
	QueryImageOffKey = "guess:query-image-off"
)

// SwitchAPI 开关管理器接口
type SwitchAPI interface {
	StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration)
	LoadData(ctx context.Context) error
	GetQueryImageOff() string
}

// SwitchManager 开关管理器实现
type SwitchManager struct {
	client        redis.Client
	lock          sync.RWMutex
	queryImageOff string
}

// 全局变量
var (
	Switch SwitchAPI
)

// NewSwitchManager 创建开关管理器
func NewSwitchManager(ctx context.Context, client redis.Client) SwitchAPI {
	manager := &SwitchManager{
		client: client,
	}

	// 初始化时加载数据
	if err := manager.LoadData(ctx); err != nil {
		LoggerService.Error(ctx, "switch_init_err", logit.Error("redis error", err))
	}

	return manager
}

// LoadData 从 Redis 加载开关数据
func (s *SwitchManager) LoadData(ctx context.Context) error {
	if s == nil {
		return nil
	}

	// 从 Redis 获取开关状态
	value, err := s.client.Get(ctx, QueryImageOffKey).Result()
	if err != nil {
		s.queryImageOff = ""
		return err
	}

	// 原子性地更新开关状态
	s.lock.Lock()
	defer s.lock.Unlock()
	s.queryImageOff = value
	return nil
}

// GetQueryImageOff 获取查询图片开关状态（线程安全）
func (s *SwitchManager) GetQueryImageOff() string {
	s.lock.RLock()
	defer s.lock.RUnlock()
	return s.queryImageOff
}

// StartBackgroundUpdate 启动后台更新任务
func (s *SwitchManager) StartBackgroundUpdate(ctx context.Context, updateInterval time.Duration) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				LoggerService.Fatal(ctx, "switch_panic", logit.AutoField("panic", r))
			}
		}()

		ticker := time.NewTicker(updateInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := s.LoadData(ctx); err != nil {
					LoggerService.Error(ctx, "switch_load_data_err", logit.AutoField("err", err))
				}
			}
		}
	}()
}

// InitSwitchTask 初始化开关管理器
func InitSwitchTask(ctx context.Context, redisClient redis.Client, updateInterval ...time.Duration) {
	Switch = NewSwitchManager(ctx, redisClient)

	// 启动后台更新
	interval := 10 * time.Second
	if len(updateInterval) > 0 {
		interval = updateInterval[0]
	}
	Switch.StartBackgroundUpdate(ctx, interval)

	logit.AddNotice(ctx, logit.AutoField("switch_init_success", 1))
}
