package background

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/redis"
)

func TestSwitchManager(t *testing.T) {
	// 启动 miniredis 测试实例
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()

	LoggerService = logit.NewSimple(os.Stdout)

	t.Run("基本功能测试", func(t *testing.T) {
		ctx := context.Background()
		client := testRedis.Client()

		// 设置测试数据
		err := client.Set(ctx, QueryImageOffKey, "off", 0).Err()
		assert.NoError(t, err)

		// 创建开关管理器
		switchManager := NewSwitchManager(ctx, client)
		assert.NotNil(t, switchManager)

		// 测试获取开关状态
		status := switchManager.GetQueryImageOff()
		assert.Equal(t, "off", status)

		// 测试更新开关状态
		err = client.Set(ctx, QueryImageOffKey, "on", 0).Err()
		assert.NoError(t, err)

		// 手动加载数据
		err = switchManager.LoadData(ctx)
		assert.NoError(t, err)

		// 验证更新后的状态
		status = switchManager.GetQueryImageOff()
		assert.Equal(t, "on", status)
	})

	t.Run("Redis key 不存在时的默认值测试", func(t *testing.T) {
		ctx := context.Background()
		client := testRedis.Client()

		// 确保 key 不存在
		client.Del(ctx, QueryImageOffKey)

		// 创建开关管理器
		switchManager := NewSwitchManager(ctx, client)
		assert.NotNil(t, switchManager)

		// 测试默认值
		status := switchManager.GetQueryImageOff()
		assert.Equal(t, "", status)
	})

	t.Run("后台更新任务测试", func(t *testing.T) {
		ctx := context.Background()
		client := testRedis.Client()

		// 设置初始值
		err := client.Set(ctx, QueryImageOffKey, "initial", 0).Err()
		assert.NoError(t, err)

		// 创建开关管理器并启动后台更新
		switchManager := NewSwitchManager(ctx, client)
		switchManager.StartBackgroundUpdate(ctx, 500*time.Millisecond)

		// 验证初始值
		status := switchManager.GetQueryImageOff()
		assert.Equal(t, "initial", status)

		// 更新 Redis 中的值
		err = client.Set(ctx, QueryImageOffKey, "updated", 0).Err()
		assert.NoError(t, err)

		// 等待后台更新
		time.Sleep(1 * time.Second)

		// 验证更新后的值
		status = switchManager.GetQueryImageOff()
		assert.Equal(t, "updated", status)
	})

}
