package common

import (
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestGetUserPrivacyString(t *testing.T) {

	ctx := createGetWebContext()

	tests := []struct {
		deviceID string
		ctx      *gdp.WebContext
		want     UserPrivacyInfo
	}{
		{
			"111",
			ctx,
			UserPrivacyInfo{
				OAID: "1234",
				IMEI: "12345678901234",
			},
		},
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetUserPrivacyString")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	utInst.MockFunc(ral.Ral, MockRalPrivacyMapping)

	for _, tt := range tests {
		got := GetUserPrivacyString(tt.deviceID, tt.ctx)
		ag.Add("Test Case Of TestGetUserPrivacyString", got, ut.ShouldResemble, tt.want)
	}
	ag.Run()

}

func MockRalPrivacyMapping(_ string, _ interface{}, response interface{}, _ ral.ConverterType) error {

	resp, _ := response.(*MappingResp)
	*resp = MappingResp{
		Head: ral.HTTPHead{},
		Body: struct {
			DevErrNo  int                    `json:"dev_err_no"`
			DevInfo   map[string]interface{} `json:"dev_info,omitempty"`
			IMEIErrNo int                    `json:"imei_err_no"`
			IMEI      string                 `json:"imei,omitempty"`
		}{
			1,
			map[string]interface{}{
				"oaid": "1234",
			},
			1,
			"12345678901234",
		},
	}

	return nil
}

func createGetWebContext() *gdp.WebContext {
	httpRspBody := httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody)
	//构造WebContext
	wc := &gdp.WebContext{
		Context:    c,
		LogContext: gdp.NewLogContext("1", "1.1.1.1"),
	}
	return wc
}
