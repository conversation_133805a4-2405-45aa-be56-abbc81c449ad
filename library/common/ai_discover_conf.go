package common

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type AiDiscoverConfTime struct {
	Start *time.Time `toml:"start"`
	End   *time.Time `toml:"end"`
}

func (p *AiDiscoverConfTime) isEnd() bool {
	return (p.End != nil) && (p.End.Before(time.Now()))
}

func (p *AiDiscoverConfTime) notAvailable() bool {
	now := time.Now()
	notStart := (p.Start != nil) && (p.Start.After(now))
	isEnd := (p.End != nil) && (p.End.Before(now))
	return (notStart) || (isEnd)
}

type AiDiscoverConfCtl struct {
	Enable int                 `toml:"enable"`
	Time   *AiDiscoverConfTime `toml:"time"`
}

func (p *AiDiscoverConfCtl) filter() bool {
	if p.Enable == 0 {
		return true
	}

	if p.Time != nil && p.Time.isEnd() {
		return true
	}
	return false
}

func (p *AiDiscoverConfCtl) notAvailable() bool {
	if p.Enable == 0 {
		return true
	}

	if p.Time != nil && p.Time.notAvailable() {
		return true
	}

	return false
}

// 这个结构conf和resp共用的，toml tag和json tag同时存在
type AiDiscoverConfStyle struct {
	Type      string `toml:"type" json:"type"`
	DayColor  string `toml:"day_color" json:"dayColor,omitempty"`
	FontType  string `toml:"font_type" json:"fontType,omitempty"`
	RangeList []struct {
		Start int `toml:"start" json:"start"`
		End   int `toml:"end" json:"end"`
	} `toml:"range" json:"ranges"`
}

type AiDiscoverConfItem struct {
	Control   AiDiscoverConfCtl     `toml:"control"`
	Title     string                `toml:"title"`
	Icon      string                `toml:"icon"`
	ItemID    int                   `toml:"itemid"`
	Trend     int                   `toml:"trend"`
	NightIcon string                `toml:"night_icon"`
	Text      string                `toml:"text"`
	StyleList []AiDiscoverConfStyle `toml:"style"`
	URL       string                `json:"url"`
}

type AiDiscoverConfTab struct {
	Info AiDiscoverConfItem   `toml:"info"`
	Item []AiDiscoverConfItem `toml:"item"`
}

type ColorConf struct {
	BdboxColor  string `toml:"bdbox_color"`
	SimpleColor string `toml:"simple_color"`
}

type AiDiscoverConf struct {
	Control   AiDiscoverConfCtl   `toml:"global_control"`
	ColorConf ColorConf           `toml:"color_conf"`
	Tab       []AiDiscoverConfTab `toml:"tab"`
}

type AiDiscoverRespPrompt struct {
	Text   string                `json:"text"`
	Styles []AiDiscoverConfStyle `json:"styles"`
}

type AiDiscoverRespItem struct {
	Index     int                  `json:"index"`
	Title     string               `json:"title"`
	Icon      string               `json:"icon"`
	ItemID    int                  `json:"itemid"`
	Trend     int                  `json:"trend"`
	NightIcon string               `json:"night_icon"`
	Prompt    AiDiscoverRespPrompt `json:"prompt"`
	URL       string               `json:"url"`
}

// 根据最新的ue稿，tab的icon暂时不用,可空
type AiDiscoverRespTab struct {
	Index       int                  `json:"tab_index"`
	Title       string               `json:"tab_title"`
	Icon        string               `json:"tab_icon,omitempty"`
	Item        []AiDiscoverRespItem `json:"tab_item"`
	BdboxColor  string
	SimpleColor string
}

type AiDiscoverResp []AiDiscoverRespTab

type PromptTrendResp struct {
	Status  int               `json:"status"`
	Message string            `json:"message"`
	Data    map[string]string `json:"data"`
}

type rawResp struct {
	Head ral.HTTPHead
	Body []byte
}

const ClickURL = "https://chat.baidu.com"

var aiDiscoverConf AiDiscoverConf

func aiDiscoverConfInit() {
	filePath := filepath.Join(env.ConfRootPath(), "aigc_discover.toml")
	_, err := toml.DecodeFile(filePath, &aiDiscoverConf)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load aigc_discover config file %s: %s\n", filePath, err.Error())
		aiDiscoverConf.Control.Enable = 0
	}
	aiDiscoverConf.check()
}

func (p *AiDiscoverConf) checkTab(tab *AiDiscoverConfTab) bool {
	// if tab.Info.Control.filter() {
	// 	return false
	// }

	if tab.Info.Title == "" {
		return false
	}

	if tab.Info.Icon == "" {
		return false
	}

	if len(tab.Item) == 0 {
		return false
	}

	filterItem := make([]AiDiscoverConfItem, 0, len(tab.Item))
	for _, item := range tab.Item {
		if item.Title == "" || item.Text == "" {
			continue
		}

		// if item.Control.filter() {
		// 	continue
		// }

		filterItem = append(filterItem, item)
	}

	if len(filterItem) == 0 {
		return false
	}

	tab.Item = filterItem
	return true
}

func (p *AiDiscoverConf) check() bool {
	if p.Control.filter() {
		return false
	}

	if len(p.Tab) == 0 {
		p.Control.Enable = 0
		return false
	}

	filterTab := make([]AiDiscoverConfTab, 0, len(p.Tab))
	for _, tab := range p.Tab {
		if p.checkTab(&tab) {
			filterTab = append(filterTab, tab)
		}
	}

	if len(filterTab) == 0 {
		p.Control.Enable = 0
		return false
	}

	p.Tab = filterTab
	return true
}

func (p *AiDiscoverConf) getData(reqdata *map[string]string) AiDiscoverResp {
	responseData := make([]AiDiscoverRespTab, 0, len(p.Tab))
	if p.Control.notAvailable() {
		return responseData
	}
	client := (*reqdata)[constant.OSNAME]
	cuid := (*reqdata)[constant.UID]
	path := "aichat/api/prompttrend?mode=0&cuid=" + cuid
	req := &ral.HTTPRequest{
		Method:    "GET",
		Path:      path,
		Converter: ral.JSONConverter,
	}
	resp := &rawResp{}
	ptResp := PromptTrendResp{}
	flag := false
	err := ral.Ral("prompt_trend", *req, resp, ral.RAWConverter)
	if err == nil {
		err = json.Unmarshal(resp.Body, &ptResp)
		if err == nil {
			flag = true
		}
	}

	for _, tab := range p.Tab {
		// if tab.Info.Control.notAvailable() {
		// 	continue
		// }
		tabData := AiDiscoverRespTab{
			Index: len(responseData),
			Title: tab.Info.Title,
			Icon:  tab.Info.Icon,
			Item:  make([]AiDiscoverRespItem, 0, len(tab.Item)),
		}

		for _, item := range tab.Item {
			// if item.Control.notAvailable() {
			// 	continue
			// }
			var trend int
			if flag {
				id := strconv.Itoa(item.ItemID)
				trend, err = strconv.Atoi(ptResp.Data[id])
				if err != nil {
					trend = 0
				}
			}

			itemData := AiDiscoverRespItem{
				Index:     len(tabData.Item),
				Title:     item.Title,
				ItemID:    item.ItemID,
				Icon:      item.Icon,
				Trend:     item.Trend + trend,
				NightIcon: item.NightIcon,
				Prompt: AiDiscoverRespPrompt{
					Text:   item.Text,
					Styles: item.StyleList,
				},
				URL: fmt.Sprintf("%v/aichat/api/prompttrend?mode=1&cuid=%v&itemid=%v", ClickURL, cuid, strconv.Itoa(item.ItemID)),
			}
			if client == "baiduboxapp" {
				itemData.Prompt.Styles[0].DayColor = p.ColorConf.BdboxColor
			}
			if client == "searchcraft" {
				itemData.Prompt.Styles[0].DayColor = p.ColorConf.SimpleColor
			}
			tabData.Item = append(tabData.Item, itemData)
		}

		if len(tabData.Item) == 0 {
			continue
		}

		responseData = append(responseData, tabData)
	}
	return responseData
}

func GetAiDiscoverData(req *map[string]string) AiDiscoverResp {
	return aiDiscoverConf.getData(req)
}
