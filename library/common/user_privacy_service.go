package common

import (
	"encoding/json"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
)

// MercaLocation 墨卡托坐标
type MercaLocation struct {
	MerX float64 `json:"mer_x"`
	MerY float64 `json:"mer_y"`
}

// CookieLocation Cookie中的位置信息
type CookieLocation struct {
	Merca    MercaLocation `json:"merca"`
	Radius   float64       `json:"radius"`
	CityCode float64       `json:"city_code"`
	TmMs     float64       `json:"tm_ms"`
	WifiMac  string        `json:"wifi_mac"`
}

// UserPrivacyInfo 用户隐私信息结构体
type UserPrivacyInfo struct {
	Model          string         `json:"model"`
	OsVer          string         `json:"os_ver"`
	IDFA           string         `json:"idfa"`
	IDFV           string         `json:"idfv"`
	OAID           string         `json:"oaid"`
	MacAddr        string         `json:"mac_addr"`
	OperatorInfo   string         `json:"operator_info"`
	CookieLocation CookieLocation `json:"cookie_location"`
	IMEI           string         `json:"imei"`
	Manu           string         `json:"manu"`
	HornorOAID     string         `json:"hornor_oaid"`
}

type MappingResp struct {
	Head ral.HTTPHead
	Body struct {
		DevErrNo  int                    `json:"dev_err_no"`
		DevInfo   map[string]interface{} `json:"dev_info,omitempty"`
		IMEIErrNo int                    `json:"imei_err_no"`
		IMEI      string                 `json:"imei,omitempty"`
	}
}

// 获取用户隐私字段：安卓：oaid； ios：idfa or caid
// !!!接入需参考文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/LS0nMWjeX4/0oycOtUSwQ/cxakIVNjGnHAn6
func GetUserPrivacyString(device string, ctx *gdp.WebContext) UserPrivacyInfo {
	type MappingBody struct {
		DeviceID  string `json:"deviceid"`
		AppName   string `json:"app_name"`
		Source    string `json:"source"`
		QueryMode int    `json:"query_mode"`
	}

	// QueryMode = 2: 只查询用户隐私公参
	body := MappingBody{
		DeviceID:  device,
		AppName:   "baiduboxapp",
		Source:    "bdapp_suggest",
		QueryMode: 2,
	}

	header := map[string][]string{
		"Log-Id": {ctx.GetLogID()},
	}

	httpReq := ral.HTTPRequest{
		Header:    header,
		Method:    "POST",
		Path:      "UserPrivacyMappingService/Mapping",
		Body:      body,
		Converter: ral.JSONConverter,
		Ctx:       ctx,
	}

	resp := MappingResp{}
	var privacyInfo UserPrivacyInfo

	err := ral.Ral("user_privacy_mapping", httpReq, &resp, ral.JSONConverter)
	if err != nil {
		ctx.AddNotice("privacyMappingErr", err)
		return privacyInfo
	}
	if resp.Body.DevErrNo != 1 {
		ctx.AddNotice("privacyMappingErr", resp.Body.DevErrNo)
		return privacyInfo
	}

	// 将map转换为UserPrivacyInfo结构体
	if jsonBytes, err := json.Marshal(resp.Body.DevInfo); err == nil {
		_ = json.Unmarshal(jsonBytes, &privacyInfo)
	} else {
		ctx.AddNotice("privacyMappingErr", err)
		return privacyInfo
	}

	if resp.Body.IMEIErrNo != 1 {

		ctx.AddNotice("IEMIMappingErr", resp.Body.IMEIErrNo)
		return privacyInfo
	}

	privacyInfo.IMEI = resp.Body.IMEI
	return privacyInfo
}

// SetUserPrivacyInfo 设置用户隐私信息到上下文中
// 从CUID中提取设备ID部分并获取相应的隐私信息
func SetUserPrivacyInfo(cuid string, ctx *gdp.WebContext) {
	// 如果CUID为空，则直接返回
	if cuid == "" {
		return
	}

	// 从CUID中提取第一部分（以|分隔）作为设备ID
	parts := strings.Split(cuid, "|")
	deviceID := parts[0]

	// 获取用户隐私信息
	privacyInfo := GetUserPrivacyString(deviceID, ctx)

	// 将隐私信息设置到上下文中
	ctx.Set("userPrivacyInfo", &privacyInfo)
}

func GetUserPrivacyInfo(ctx *gdp.WebContext) UserPrivacyInfo {
	userPrivacyInfo, _ := ctx.Get("userPrivacyInfo")
	info, _ := userPrivacyInfo.(UserPrivacyInfo)
	return info
}
