package common

import (
	"fmt"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
)

// agent配置文件的数据结构
type AgentConf struct {
	AgentMinNum int `toml:"agent_min_num"`
	AgentMaxNum int `toml:"agent_max_num"`
	QueryMinLen int `toml:"query_min_len"`
}

var AgentConfig AgentConf

// 读取agent配置文件数据信息
func GetAgentConf() {
	filePath := filepath.Join(env.ConfRootPath(), "aigc_agent.toml")
	_, err := toml.DecodeFile(filePath, &AgentConfig)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load aigc_agent config file %s: %s\n", filePath, err.Error())
	}
}

// 服务启动时加载词典
func agentConfInit() {
	GetAgentConf()
}
