package common

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
)

func DumpData(ctx *gdp.WebContext, serviceName string, data interface{}) {
	if !DumpConfMem.DumpSwitch {
		return
	}

	fileName, ok := getFileName(ctx, serviceName)
	if !ok {
		return
	}

	var out []byte
	var err error

	switch data.(type) {
	case []byte:
		out, ok = data.([]byte)
		if !ok {
			return
		}
	case protocol.HTTPRequest:
		req := data.(protocol.HTTPRequest)
		dumpField := map[string]interface{}{
			"header":      req.Header,
			"method":      req.Method,
			"path":        req.Path,
			"queryParams": req.QueryParams,
			"body":        req.Body,
		}
		out, err = json.Marshal(dumpField)
		if err != nil {
			return
		}
	case *protocol.HTTPRequest:
		req := data.(*protocol.HTTPRequest)
		dumpField := map[string]interface{}{
			"header":      req.Header,
			"method":      req.Method,
			"path":        req.Path,
			"queryParams": req.QueryParams,
			"body":        req.Body,
		}
		out, err = json.Marshal(dumpField)
		if err != nil {
			return
		}
	case *http.Request:
		req := data.(*http.Request)
		dumpField := map[string]interface{}{
			"header": req.Header,
			"method": req.Method,
			"url":    req.URL,
			"body":   req.Body,
		}
		out, err = json.Marshal(dumpField)
		if err != nil {
			return
		}
	}

	_ = ioutil.WriteFile(fileName, out, 0644)
	return
}

// 获取dump输出的文件名
func getFileName(ctx *gdp.WebContext, serviceName string) (string, bool) {
	tracePath := env.LogDir() + "/trace"
	if _, err := os.Stat(tracePath); os.IsNotExist(err) {
		_ = os.MkdirAll(tracePath, os.ModePerm)
	}

	fileName := fmt.Sprintf("%s/%s_%s", tracePath, ctx.GetLogID(), serviceName)
	// fileName := fmt.Sprintf("%s/%s_%s", tracePath, getMD5(ctx.Request.RequestURI), serviceName)

	return fileName, true
}

func getMD5(str string) string {
	data := []byte(str)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x", has)
	return md5str
}
