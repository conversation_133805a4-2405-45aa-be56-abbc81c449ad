package common

import (
	"testing"
	"time"

	"gopkg.in/go-playground/assert.v1"
)

// func Test_aiDiscoverConfInit(t *testing.T) {
// 	filePath := filepath.Join(env.ConfRootPath(), "aigc_discover.toml")
// 	//线上没有这个配置文件，走mis托送，没有文件的时候os包会因为各种优化打桩失败
// 	//最简单的就是在没有文件的时候touch一个空文件
// 	if _, err := os.Stat(filePath); err != nil {
// 		file, err := os.Create(filePath)
// 		if err != nil {
// 			t.Fatal(err)
// 		}
// 		defer os.Remove(filePath)
// 		defer file.Close()
// 	}

// 	guard := monkey.Patch(io.ReadAll, func(io.Reader) ([]byte, error) {
// 		return nil, errors.New("unit test")
// 	})
// 	aiDiscoverConfInit()
// 	// assert.Equal(t, aiDiscoverConf.Control.Enable, 0)
// 	guard.Unpatch()

// 	guard2 := monkey.Patch(io.ReadAll, func(io.Reader) ([]byte, error) {
// 		confData := `
// 		[global_control]
// 			enable = 1

// 		[[tab]]
// 		[tab.info]
// 			title = "tab-0-title"
// 			text = "tab-0-text"
// 			control.enable = 1

// 			[[tab.item]]
// 				title = "tab-0-item-0-title"
// 				icon = "tab-0-item-0-icon"
// 				night_icon = "tab-0-item-0-night_icon"
// 				text = "tab-0-item-0-text"
// 				control.enable = 1

// 				[[tab.item.style]]
// 					type = "color"
// 					day_color = "#FF0335FE"
// 					[[tab.item.style.range]]
// 						start = 3
// 						end = 6
// 				[[tab]]
// 				[tab.info]
// 					title = "tab-0-title"
// 					text = "tab-0-text"
// 					control.enable = 1

// 					[[tab.item]]
// 						title = "tab-0-item-0-title"
// 						icon = "tab-0-item-0-icon"
// 						night_icon = "night_icon"
// 						text = "tab-0-item-0-text"
// 						control.enable = 1

// 					[[tab.item]]
// 						title = "tab-0-item-0-title"
// 						icon = "tab-0-item-0-icon"
// 						night_icon = "night_icon"
// 						text = "tab-0-item-0-text"
// 						control.enable = 1

// 						[[tab.item.style]]
// 							type = "color"
// 							day_color = "#FF0335FE"
// 							[[tab.item.style.range]]
// 								start = 3
// 								end = 6

// 						[[tab.item.style]]
// 							type = "font"
// 							font_type = "blod"
// 							[[tab.item.style.range]]
// 								start = 3
// 								end = 6
// 		`
// 		return []byte(confData), nil
// 	})

// 	aiDiscoverConfInit()
// 	assert.Equal(t, aiDiscoverConf.Control.Enable, 1)
// 	assert.Equal(t, aiDiscoverConf.Control.Time, nil)
// 	assert.Equal(t, len(aiDiscoverConf.Tab), 2)
// 	assert.Equal(t, len(aiDiscoverConf.Tab[1].Item), 2)
// 	assert.Equal(t, len(aiDiscoverConf.Tab[1].Item[1].StyleList), 2)
// 	guard.Unpatch()
// 	guard2.Unpatch()
// }

func Test_aiDiscoverConfTime(t *testing.T) {
	type testCase struct {
		input       *AiDiscoverConfTime
		isEnd       bool
		isAvaliable bool
	}

	curTime := time.Now()
	timeBefore := curTime.Add(-time.Hour)
	timeAfter := curTime.Add(time.Hour)

	testCaseList := []testCase{
		{
			input:       &AiDiscoverConfTime{},
			isEnd:       false,
			isAvaliable: true,
		},
		{
			input: &AiDiscoverConfTime{
				Start: &timeBefore,
			},
			isEnd:       false,
			isAvaliable: true,
		},
		{
			input: &AiDiscoverConfTime{
				Start: &timeAfter,
			},
			isEnd:       false,
			isAvaliable: false,
		},
		{
			input: &AiDiscoverConfTime{
				End: &timeBefore,
			},
			isEnd:       true,
			isAvaliable: false,
		},
		{
			input: &AiDiscoverConfTime{
				End: &timeAfter,
			},
			isEnd:       false,
			isAvaliable: true,
		},
		{
			input: &AiDiscoverConfTime{
				Start: &timeBefore,
				End:   &timeAfter,
			},
			isEnd:       false,
			isAvaliable: true,
		},
		{
			input: &AiDiscoverConfTime{
				Start: &timeBefore,
				End:   &timeBefore,
			},
			isEnd:       true,
			isAvaliable: false,
		},
		{
			input: &AiDiscoverConfTime{
				Start: &timeAfter,
				End:   &timeAfter,
			},
			isEnd:       false,
			isAvaliable: false,
		},
	}

	for _, tc := range testCaseList {
		assert.Equal(t, tc.isEnd, tc.input.isEnd())
		assert.NotEqual(t, tc.isAvaliable, tc.input.notAvailable())
	}
}

func Test_aiDiscoverConfCtl(t *testing.T) {

	type testCase struct {
		input       *AiDiscoverConfCtl
		filter      bool
		isAvaliable bool
	}

	curTime := time.Now()
	timeBefore := curTime.Add(-time.Hour)
	timeAfter := curTime.Add(time.Hour)

	testCaseList := []testCase{
		{
			input: &AiDiscoverConfCtl{
				Enable: 0,
			},
			filter:      true,
			isAvaliable: false,
		},
		{
			input: &AiDiscoverConfCtl{
				Enable: 1,
				Time:   &AiDiscoverConfTime{},
			},
			filter:      false,
			isAvaliable: true,
		},
		{
			input: &AiDiscoverConfCtl{
				Enable: 1,
				Time: &AiDiscoverConfTime{
					Start: &timeBefore,
					End:   &timeBefore,
				},
			},
			filter:      true,
			isAvaliable: false,
		},
		{
			input: &AiDiscoverConfCtl{
				Enable: 1,
				Time: &AiDiscoverConfTime{
					Start: &timeBefore,
					End:   &timeAfter,
				},
			},
			filter:      false,
			isAvaliable: true,
		},
	}
	for _, tc := range testCaseList {
		assert.Equal(t, tc.filter, tc.input.filter())
		assert.NotEqual(t, tc.isAvaliable, tc.input.notAvailable())
	}

}

// func Test_aiDiscoverConfCheckTab(t *testing.T) {

// 	type testCase struct {
// 		input  *AiDiscoverConfTab
// 		result bool
// 	}

// 	testCaseList := []testCase{
// 		{
// 			input: &AiDiscoverConfTab{
// 				Info: AiDiscoverConfItem{
// 					Control: AiDiscoverConfCtl{
// 						Enable: 0,
// 					},
// 				},
// 			},
// 			result: false,
// 		},
// 		{
// 			input: &AiDiscoverConfTab{
// 				Info: AiDiscoverConfItem{
// 					Control: AiDiscoverConfCtl{
// 						Enable: 1,
// 					},
// 				},
// 			},
// 			result: false,
// 		},
// 		{
// 			input: &AiDiscoverConfTab{
// 				Info: AiDiscoverConfItem{
// 					Title: "tab-0-name",
// 					Icon:  "tab-0-icon",
// 					Control: AiDiscoverConfCtl{
// 						Enable: 1,
// 					},
// 				},
// 			},
// 			result: false,
// 		},
// 		{
// 			input: &AiDiscoverConfTab{
// 				Info: AiDiscoverConfItem{
// 					Title: "tab-0-name",
// 					Icon:  "tab-0-icon",
// 					Text:  "tab-0-detail",
// 					Control: AiDiscoverConfCtl{
// 						Enable: 1,
// 					},
// 				},
// 				Item: []AiDiscoverConfItem{
// 					{
// 						Title: "tab-0-name",
// 						Icon:  "tab-0-icon",
// 						Text:  "tab-0-detail",
// 						Control: AiDiscoverConfCtl{
// 							Enable: 0,
// 						},
// 					},
// 				},
// 			},
// 			result: false,
// 		},
// 		{
// 			input: &AiDiscoverConfTab{
// 				Info: AiDiscoverConfItem{
// 					Title: "tab-0-name",
// 					Icon:  "tab-0-icon",
// 					Text:  "tab-0-detail",
// 					Control: AiDiscoverConfCtl{
// 						Enable: 1,
// 					},
// 				},
// 				Item: []AiDiscoverConfItem{
// 					{
// 						Title: "",
// 						Icon:  "tab-0-icon",
// 						Text:  "tab-0-detail",
// 						Control: AiDiscoverConfCtl{
// 							Enable: 1,
// 						},
// 					},
// 				},
// 			},
// 			result: false,
// 		},
// 		{
// 			input: &AiDiscoverConfTab{
// 				Info: AiDiscoverConfItem{
// 					Title: "tab-0-name",
// 					Icon:  "tab-0-icon",
// 					Text:  "tab-0-detail",
// 					Control: AiDiscoverConfCtl{
// 						Enable: 1,
// 					},
// 				},
// 				Item: []AiDiscoverConfItem{
// 					{
// 						Title: "tab-0-name",
// 						Icon:  "tab-0-icon",
// 						Text:  "tab-0-detail",
// 						Control: AiDiscoverConfCtl{
// 							Enable: 1,
// 						},
// 					},
// 				},
// 			},
// 			result: true,
// 		},
// 	}
// 	for _, tc := range testCaseList {
// 		result := aiDiscoverConf.checkTab(tc.input)
// 		assert.Equal(t, tc.result, result)
// 	}
// }

func Test_aiDiscoverConfCheckConf(t *testing.T) {

	type testCase struct {
		input  AiDiscoverConf
		result int
	}

	testCaseList := []testCase{
		{
			input:  AiDiscoverConf{},
			result: 0,
		},
		{
			input: AiDiscoverConf{
				Control: AiDiscoverConfCtl{
					Enable: 1,
				},
			},
			result: 0,
		},
		{
			input: AiDiscoverConf{
				Control: AiDiscoverConfCtl{
					Enable: 1,
				},
				Tab: []AiDiscoverConfTab{
					{
						Info: AiDiscoverConfItem{
							Title: "",
							Icon:  "tab-0-icon",
							Text:  "tab-0-detail",
							Control: AiDiscoverConfCtl{
								Enable: 1,
							},
						},
						Item: []AiDiscoverConfItem{
							{
								Title: "tab-0-name",
								Icon:  "tab-0-icon",
								Text:  "tab-0-detail",
								Control: AiDiscoverConfCtl{
									Enable: 1,
								},
							},
						},
					},
				},
			},
			result: 0,
		},
		{
			input: AiDiscoverConf{
				Control: AiDiscoverConfCtl{
					Enable: 1,
				},
				Tab: []AiDiscoverConfTab{
					{
						Info: AiDiscoverConfItem{
							Title: "tab-0-name",
							Icon:  "tab-0-icon",
							Text:  "tab-0-detail",
							Control: AiDiscoverConfCtl{
								Enable: 1,
							},
						},
						Item: []AiDiscoverConfItem{
							{
								Title: "tab-0-name",
								Icon:  "tab-0-icon",
								Text:  "tab-0-detail",
								Control: AiDiscoverConfCtl{
									Enable: 1,
								},
							},
						},
					},
				},
			},
			result: 1,
		},
	}
	for _, tc := range testCaseList {
		tc.input.check()
		assert.Equal(t, tc.input.Control.Enable, tc.result)
	}
}

// func Test_aiDiscoverGetData(t *testing.T) {
// 	type testCase struct {
// 		input  AiDiscoverConf
// 		result []int
// 	}

// 	testCaseList := []testCase{
// 		{
// 			input: AiDiscoverConf{
// 				Control: AiDiscoverConfCtl{
// 					Enable: 0,
// 				},
// 				Tab: []AiDiscoverConfTab{
// 					{
// 						Info: AiDiscoverConfItem{
// 							Title: "tab-0-name",
// 							Icon:  "tab-0-icon",
// 							Text:  "tab-0-detail",
// 							Control: AiDiscoverConfCtl{
// 								Enable: 1,
// 							},
// 						},
// 						Item: []AiDiscoverConfItem{
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			result: []int{},
// 		},
// 		{
// 			input: AiDiscoverConf{
// 				Control: AiDiscoverConfCtl{
// 					Enable: 1,
// 				},
// 				Tab: []AiDiscoverConfTab{
// 					{
// 						Info: AiDiscoverConfItem{
// 							Title: "tab-0-name",
// 							Icon:  "tab-0-icon",
// 							Text:  "tab-0-detail",
// 							Control: AiDiscoverConfCtl{
// 								Enable: 0,
// 							},
// 						},
// 						Item: []AiDiscoverConfItem{
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			result: []int{},
// 		},
// 		{
// 			input: AiDiscoverConf{
// 				Control: AiDiscoverConfCtl{
// 					Enable: 1,
// 				},
// 				Tab: []AiDiscoverConfTab{
// 					{
// 						Info: AiDiscoverConfItem{
// 							Title: "tab-0-name",
// 							Icon:  "tab-0-icon",
// 							Text:  "tab-0-detail",
// 							Control: AiDiscoverConfCtl{
// 								Enable: 1,
// 							},
// 						},
// 						Item: []AiDiscoverConfItem{
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 0,
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			result: []int{},
// 		},
// 		{
// 			input: AiDiscoverConf{
// 				Control: AiDiscoverConfCtl{
// 					Enable: 1,
// 				},
// 				Tab: []AiDiscoverConfTab{
// 					{
// 						Info: AiDiscoverConfItem{
// 							Title: "tab-0-name",
// 							Icon:  "tab-0-icon",
// 							Text:  "tab-0-detail",
// 							Control: AiDiscoverConfCtl{
// 								Enable: 1,
// 							},
// 						},
// 						Item: []AiDiscoverConfItem{
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			result: []int{2},
// 		},
// 		{
// 			input: AiDiscoverConf{
// 				Control: AiDiscoverConfCtl{
// 					Enable: 1,
// 				},
// 				Tab: []AiDiscoverConfTab{
// 					{
// 						Info: AiDiscoverConfItem{
// 							Title: "tab-0-name",
// 							Icon:  "tab-0-icon",
// 							Text:  "tab-0-detail",
// 							Control: AiDiscoverConfCtl{
// 								Enable: 1,
// 							},
// 						},
// 						Item: []AiDiscoverConfItem{
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 						},
// 					},
// 					{
// 						Info: AiDiscoverConfItem{
// 							Title: "tab-0-name",
// 							Icon:  "tab-0-icon",
// 							Text:  "tab-0-detail",
// 							Control: AiDiscoverConfCtl{
// 								Enable: 1,
// 							},
// 						},
// 						Item: []AiDiscoverConfItem{
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 1,
// 								},
// 							},
// 							{
// 								Title: "tab-0-name",
// 								Icon:  "tab-0-icon",
// 								Text:  "tab-0-detail",
// 								Control: AiDiscoverConfCtl{
// 									Enable: 0,
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			result: []int{2, 1},
// 		},
// 	}
// 	for _, tc := range testCaseList {
// 		aiDiscoverConf = tc.input
// 		resp := GetAiDiscoverData()
// 		assert.Equal(t, len(resp), len(tc.result))
// 		for index, data := range tc.result {
// 			assert.Equal(t, len(resp[index].Item), data)
// 		}
// 	}
// }
