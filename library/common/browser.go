package common

import (
	"regexp"
	"strings"
)

func IsZbiosMain(ua string) bool {
	if isMatch, _ := regexp.MatchString(`(?i)baiduboxapp`, ua); isMatch {
		return true
	}
	return false
}

func IsZibosLite(ua string) bool {
	if isMatch, _ := regexp.MatchString(`(?i)\blite\s+baiduboxapp\b`, ua); isMatch {
		return true
	}
	if isMatch, _ := regexp.MatchString(`(?i)\binfo\s+baiduboxapp\b`, ua); isMatch {
		return true
	}
	return false
}

func GetZbiosMainVersion(ua string) string {
	if !IsZbiosMain(ua) {
		return ""
	}

	var versionStr []string
	var err error
	var re *regexp.Regexp
	// 优先匹配特殊的安卓手百的ua
	if re, err = regexp.Compile(`([\d+.]+)_(?:diordna|enohpi)_`); err != nil {
		return ""
	}
	if versionStr = re.FindStringSubmatch(ua); versionStr != nil {
		return versionStr[1]
	}

	// 匹配普通手百
	if re, err = regexp.Compile(`baiduboxapp\/([\d+.]+)`); err != nil {
		return ""
	}

	if versionStr = re.FindStringSubmatch(ua); versionStr == nil {
		return ""
	}

	return versionStr[1]
}

func GetUserOS(ua string) (os string) {
	switch {
	case strings.Contains(ua, "Android"):
		os = "android"
	case strings.Contains(ua, "iPhone"):
		os = "iphone"
	case strings.Contains(ua, "iPad"):
		os = "ipad"
	case strings.Contains(ua, "Harmony"):
		os = "harmony"
	}
	return
}
