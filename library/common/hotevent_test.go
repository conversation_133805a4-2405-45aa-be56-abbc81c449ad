package common

import "testing"

func Test_HitGameQuery(t *testing.T) {
	hotEventInit()

	type args struct {
		event HotEventBase
		query string
	}

	tests := []struct {
		name  string
		args  args
		want  string
		want1 bool
	}{
		{
			"test_HotEventQuestion_HitGameQuery",
			args{
				HotEventQuestion,
				"答题",
			},
			"答题红包",
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := tt.args.event.HitGameQuery(tt.args.query)
			if got != tt.want {
				t.Errorf("HitGameQuery() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.<PERSON><PERSON>("HitGameQuery() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}

}

func Test_GameForceOpen(t *testing.T) {
	hotEventInit()

	type args struct {
		event HotEventBase
	}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"test_HotEventQuestion_HitGameQuery",
			args{
				HotEventQuestion,
			},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.args.event.GameForceOpen()
			if got != tt.want {
				t.Errorf("GameForceOpen() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func Test_GetConf(t *testing.T) {
	hotEventInit()

	type args struct {
		event HotEventBase
	}

	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"test_HotEventQuestion_HitGameQuery",
			args{
				HotEventQuestion,
			},
			"question_game",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.args.event.GetConf()
			if got.Game.GameName != tt.want {
				t.Errorf("GetConf() got = %v, want %v", got, tt.want)
			}
		})
	}

}
