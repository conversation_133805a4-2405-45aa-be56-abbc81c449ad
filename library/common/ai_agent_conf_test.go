package common

import (
	"testing"
)

func TestGetAgentConf(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{
			name: "load_fail",
		},
	}
	// var p *monkey.PatchGuard
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			GetAgentConf()
		})
	}
}

func Test_agentConfInit(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{
			name: "init",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agentConfInit()
		})
	}
}
