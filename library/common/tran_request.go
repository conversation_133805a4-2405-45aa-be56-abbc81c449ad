/*
 * @Descripttion:
 * @Author: <EMAIL>
 * @Date: 2021-09-17 17:28:57
 */
package common

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
)

type httpResp struct {
	Head    ral.HTTPHead
	Body    interface{}
	Raw     []byte
	TagHead ral.HTTPHead `ral:"head"`
	TagBody interface{}  `ral:"body"`
	TagRaw  []byte       `ral:"raw"`
}

func TransRequest(ctx *gdp.WebContext, serviceName string, path string) (string, error) {
	req := ctx.Request

	if len(path) == 0 {
		path = req.URL.Path
	}
	requestral := &ral.HTTPRequest{
		Method:      req.Method,
		Path:        path,
		Body:        req.FormValue("data"),
		QueryParams: req.URL.Query(),
		Header:      req.<PERSON>er,
		Converter:   ral.J<PERSON><PERSON>onverter,
		LogID:       ctx.GetLogID(),
		Ctx:         ctx,
	}

	var response = httpResp{}
	err := ral.Ral(serviceName, *requestral, &response, ral.JSONConverter)
	if err != nil {
		return string(response.Raw), err
	}

	return string(response.Raw), nil
}
