package common

type HotEvent interface {
	HitGameQuery(q string) (string, bool)
	GameForceOpen() bool
	GetConf() HotEventConf
}

type HotEventBase struct {
	HotEventConf
}

type HotEventConf struct {
	Game struct {
		ForceOpen         bool              `toml:"force_open"`
		SampleOpen        string            `toml:"sample_open"`
		InsertPos         int               `toml:"insert_pos"`
		Yyfrom            string            `toml:"yyfrom"`
		Sa                string            `toml:"sa"`
		MainQ             string            `toml:"main_q"`
		MainQReplaceQuery bool              `toml:"main_q_replace_query"` // 用于搜索词替换为主q
		GameName          string            `toml:"game_name"`
		HitQuerys         map[string]string `toml:"hit_querys"`
	} `toml:"game"`
}

func (b HotEventBase) HitGameQuery(q string) (string, bool) {
	if v, ok := b.HotEventConf.Game.HitQuerys[q]; ok {
		return v, true
	}
	return "", false
}

func (b HotEventBase) GameForceOpen() bool {
	return b.HotEventConf.Game.ForceOpen
}

func (b HotEventBase) GetConf() HotEventConf {
	return b.HotEventConf
}
