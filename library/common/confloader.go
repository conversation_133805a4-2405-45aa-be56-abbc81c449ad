package common

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/go-ego/gse"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
)

// confloader主要在app启动各个配置前加载配置到内存中，减少pagecache使用

// 二合一相关配置解析
type TwoInOneConf struct {
	PackageSize       int                 `toml:"packagesize"`
	PrefetchRatio     int                 `toml:"prefetchRatio"`
	PrefetchStragtegy map[string][]string `toml:"prefetchStrategy"`
	CloseGuess        int                 `toml:"closeGuess"` // 临时加开关，用于关闭荣耀白牌搜索发现
}

// 通用配置开关配置解析
type SwitchConf struct {
	Switch              map[string]interface{} `toml:"generalswitch"`
	HisYunyingBlackCity map[string]int         `toml:"hisYunyingBlackCity"`
	Campaigns           []Campaign             `toml:"campaigns"`
	CampaignsLite       []Campaign             `toml:"campaigns_lite"`
}

// 活动召回配置
type Campaign struct {
	ID                string  `toml:"id"`
	RecallProbability float64 `toml:"recall_probability"`
}

// Dump开关
type DumpConf struct {
	DumpSwitch bool `toml:"dumpSwitch"`
}

type SampleSwitch struct {
	SampleSwitch bool `toml:"sampleSwitch"`
}

type SampleDegradeSwitch struct {
	Switch int `toml:"switch"`
}

// 配置结构定义列表
var (
	TwoInOneConfMem     TwoInOneConf
	SwitchConfMem       SwitchConf
	DumpConfMem         DumpConf
	SampleConfMem       SampleSwitch
	SampleWhiteListConf SampleWhiteList
)

func twoinoneConfInit() {
	filePath := filepath.Join(env.ConfRootPath(), "twoinone.toml")
	_, err := toml.DecodeFile(filePath, &TwoInOneConfMem)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load twoinone config file %s: %s", filePath, err.Error())
	}
}

func switchConfInit() {
	filePath := filepath.Join(env.ConfRootPath(), "switch.toml")
	_, err := toml.DecodeFile(filePath, &SwitchConfMem)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load switch config file %s: %s", filePath, err.Error())
	}
}

func dumpConfInit() {
	filePath := filepath.Join(env.ConfRootPath(), "dump.toml")
	_, err := toml.DecodeFile(filePath, &DumpConfMem)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load switch config file %s: %s", filePath, err.Error())
	}
}

func SampleSwitchConfInit() {
	filePath := filepath.Join(env.ConfRootPath(), "sample_switch.toml")
	_, err := toml.DecodeFile(filePath, &SampleConfMem)
	if err != nil {
		fmt.Fprintf(os.Stdout, "can't load switch config file %s: %s", filePath, err.Error())
	}
}

type SampleWhiteList struct {
	WhiteList []struct {
		Ctl    string `toml:"ctl"`
		Action string `toml:"action"`
	} `toml:"sample"`
}

var SampleWhiteMap = make(map[string]bool)

func SampleWhitePathConfInit() {
	filePath := filepath.Join(env.ConfRootPath(), "sample_white_list.toml")
	_, err := toml.DecodeFile(filePath, &SampleWhiteListConf)
	if err != nil {
		panic(fmt.Sprintf("can't load switch config file %s: %s", filePath, err.Error()))
	}
	for _, v := range SampleWhiteListConf.WhiteList {
		joinedStr := fmt.Sprintf("%s_%s", v.Ctl, v.Action)
		SampleWhiteMap[joinedStr] = true
	}
}

type rsWhiteConf struct {
	URL map[string]interface{} `toml:"url"`
}

var RSWhite rsWhiteConf

// rs白名单
func rsWhiteInit() {
	filePath := filepath.Join(env.ConfRootPath(), "rs_white.toml")
	_, err := toml.DecodeFile(filePath, &RSWhite)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load switch config file %s: %s", filePath, err.Error())
	}
}

// 用于获取本地榜的城市信息
var BoardCity map[string]string

func boardCityInit() {
	filePath := filepath.Join(env.ConfRootPath(), "board_city.toml")
	_, err := toml.DecodeFile(filePath, &BoardCity)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load switch config file %s: %s", filePath, err.Error())
	}
}

var SampleConf map[string]map[string]string

func sampleInit() {
	filePath := filepath.Join(env.ConfRootPath(), "sample.toml")
	_, err := toml.DecodeFile(filePath, &SampleConf)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load config file %s: %s\n", filePath, err.Error())
	}
}

var HisTagConf map[string]map[string]interface{}

func hisTagInit() {
	filePath := filepath.Join(env.ConfRootPath(), "his_tag.toml")
	_, err := toml.DecodeFile(filePath, &HisTagConf)
	if err != nil {
		fmt.Fprintf(os.Stdout, "Can't load config file %s: %s\n", filePath, err.Error())
	}
}

// sug直达
var SugDirectConf map[string]map[string]interface{}

func sugDirectInit() {
	filePath := filepath.Join(env.ConfRootPath(), "sug_direct.toml")
	_, err := toml.DecodeFile(filePath, &SugDirectConf)
	if err != nil {
		panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
	}
}

// 超级品专 sug直达
var SugDirectSpzConf map[string]map[string]interface{}

func sugDirectSpzInit() {
	filePath := filepath.Join(env.ConfRootPath(), "super_pinzhuan.toml")
	_, err := toml.DecodeFile(filePath, &SugDirectSpzConf)
	if err != nil {
		panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
	}
}

// 品专 sug直达
var SugDirectPzConf map[string]int

func sugDirectPzInit() {
	filePath := filepath.Join(env.ConfRootPath(), "normal_pinzhuan.toml")
	_, err := toml.DecodeFile(filePath, &SugDirectPzConf)
	if err != nil {
		panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
	}
}

var Seg = gse.Segmenter{}

// 分词obj加载词典初始化
func segCutInit() error {
	s1 := filepath.Join(env.ConfRootPath(), "dict", "zh", "s_1.txt")
	if err := Seg.LoadDict(s1); err != nil {
		return fmt.Errorf("segcutinit can't init seg, %s", err.Error())
	}
	return nil
}

var HotEvent24GaokaoConf, HotEventQuestion HotEventBase
var HotEventList = map[string]*HotEventBase{
	"hotevent_24gaokao.toml": &HotEvent24GaokaoConf,
	"hotevent_question.toml": &HotEventQuestion,
}

// hotEvent24GaokaoConfInit 24年高考小游戏热词
func hotEventInit() {
	// 顺序要跟HotEventList一致
	for file := range HotEventList {
		var tmp HotEventBase
		filePath := filepath.Join(env.ConfRootPath(), file)
		_, err := toml.DecodeFile(filePath, &tmp)
		if err != nil {
			panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
		}
		*HotEventList[file] = tmp
	}
}

// 电商sug
var ShopSugConf map[string]int

// 电商sug初始化
func shopSugInit() {
	filePath := filepath.Join(env.ConfRootPath(), "shop_sug.toml")
	_, err := toml.DecodeFile(filePath, &ShopSugConf)
	if err != nil {
		panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
	}
}

// 插入sug词
var SugTextConf map[string]map[string][]string

// sug词初始化
func SugTextConfInit() error {
	// 获取所有配置词
	fileNameList := []string{}
	fileSugTextPath := filepath.Join(env.ConfRootPath(), "dict", "sugtext")
	SugTextConf = make(map[string]map[string][]string)

	err := filepath.Walk(fileSugTextPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			fileNameList = append(fileNameList, info.Name())
		}
		return nil
	})

	// 统一整合map，各业务根据文件名区分
	for _, fileName := range fileNameList {
		// 过滤其他非法文件
		if !CheckSugDictName(fileName) {
			continue
		}
		// 创建空间,文件名字
		name := strings.Split(fileName, ".")[0]
		SugTextConf[name] = make(map[string][]string)
		// 生成路径
		path := filepath.Join(fileSugTextPath, fileName)
		if err := ParserSugDict(path, SugTextConf[name]); err != nil {
			return fmt.Errorf("suginit can't init seg, %s", err.Error())
		}
	}
	return err
}

// dict文件量大，初始化慢，并行加速
func normalDictInit(wg *sync.WaitGroup) chan error {
	// 并发加载函数注册
	dictFuncList := []func() error{
		segCutInit,      // 分词
		SugTextConfInit, // 健康sug
	}
	// 控制channel
	panicCh := make(chan error, len(dictFuncList))

	for _, dictFunc := range dictFuncList {
		wg.Add(1)
		go func(wg *sync.WaitGroup, dicf func() error) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					// 捕获并记录panic
					panicCh <- fmt.Errorf("%v", r)
				}
			}()
			if err := dicf(); err != nil {
				panic(err)
			}
		}(wg, dictFunc)
	}
	return panicCh
}

func ConfLoaderExecute() {
	var wg sync.WaitGroup
	// 并行加载词典
	panicCh := normalDictInit(&wg)

	twoinoneConfInit()
	switchConfInit()
	dumpConfInit()
	SampleSwitchConfInit()
	SampleWhitePathConfInit()
	rsWhiteInit()
	boardCityInit()
	sampleInit()
	hisTagInit()
	aiDiscoverConfInit()
	agentConfInit()
	sugDirectInit()
	sugDirectSpzInit()
	sugDirectPzInit()
	hotEventInit()
	shopSugInit()
	initRedis()

	// 回收等待
	go func() {
		wg.Wait()
		close(panicCh)
	}()
	// 等panic
	for err := range panicCh {
		panic(err)
	}
}
