package common

import (
	"icode.baidu.com/baidu/gdp/redis"
)

// RedisClient 全局 Redis 客户端，先叫这个，后面加了新的可以改成 map[string]*redis.Client 或者再换个名字
var RedisClient redis.Client

func initRedis() {
	opts := []redis.ClientOption{
		redis.OptHooker(redis.NewMetricsHook(nil)),
		redis.OptHooker(redis.NewLogHook()),
	}
	// 没啥特别的，暂时就这个一个 redis， 后面有需要再加吧
	var err error
	RedisClient, err = redis.NewClient("redis_mircochannel", opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return
}
