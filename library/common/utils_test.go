package common

import (
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"testing"
	"time"

	"gopkg.in/go-playground/assert.v1"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestIsUtf8(t *testing.T) {
	type args struct {
		s string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: IsUtf8 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //bool
	}{
		{
			"TestIsUtf8",
			args{
				"ceshiUTF-8",
			},
			Want{
				true,
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := IsUtf8(tt.args.s)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestIsUtf8, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func TestGetNetwork(t *testing.T) {
	type args struct {
		net string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetNetwork ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //string
	}{
		{
			"TestGetNetwork",
			args{
				"5_0",
			},
			Want{
				"1",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetNetwork",
			args{
				"",
			},
			Want{
				"1",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetNetwork",
			args{
				"3_13",
			},
			Want{
				"4",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := GetNetwork(tt.args.net)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestGetNetwork, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestIsChatSearch(t *testing.T) {
	tests := []struct {
		request map[string]string
		want    bool
	}{
		{
			map[string]string{},
			false,
		},
		{
			map[string]string{
				constant.OSNAME: "baiduboxapp",
			},
			false,
		},
		{
			map[string]string{
				constant.OSNAME: "searchcraft",
			},
			true,
		},
		{
			map[string]string{
				constant.OSNAME:        "baiduboxapp",
				constant.ChatSearchTag: "1",
			},
			true,
		},
	}
	for _, tt := range tests {
		assert.Equal(t, tt.want, IsChatSearch(&tt.request))
	}
}

func TestNewBase32Decode(t *testing.T) {
	tests := []struct {
		encodeStr string
		want      string
	}{
		{
			"GI3TKMRQGE3GELLEMUYDMLJUMQ3TGLLBG44DGLJSGVSWEYZSMM3DQOJTGA",
			"2752016b-de06-4d73-a783-25ebc2c68930",
		},
		{
			"JBSWY3DPFQQFO33SNRSCC===",
			"Hello, World!",
		},
	}

	for _, tt := range tests {
		decodeByte, err := NewBase32Decode(tt.encodeStr)
		assert.Equal(t, nil, err)
		assert.Equal(t, tt.want, string(decodeByte))
	}
}

func TestIsNewUser(t *testing.T) {
	type args struct {
		gAttr string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: IsNewUser ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	curTime := time.Now().Unix()
	okGattr := fmt.Sprintf("ut_1-at_%d", curTime)

	tests := []struct {
		testCaseTitle string
		args          args
		want          bool
	}{
		{
			testCaseTitle: "g_attr is empty",
			args: args{
				gAttr: "",
			},
			want: false,
		},
		{
			testCaseTitle: "g_attr: ut != 1",
			args: args{
				gAttr: "ut_3-at_1715084877-id_1019105u",
			},
			want: false,
		},
		{
			testCaseTitle: "g_attr: at not exist",
			args: args{
				gAttr: "ut_1-id_1019105u",
			},
			want: false,
		},
		{
			testCaseTitle: "g_attr: parse time error",
			args: args{
				gAttr: "ut_1-at_abcd-id_1019105u",
			},
			want: false,
		},
		{
			testCaseTitle: "g_attr: at not within range",
			args: args{
				gAttr: "ut_1-at_1715084877-id_1019105u",
			},
			want: false,
		},
		{
			testCaseTitle: "g_attr: at not within range",
			args: args{
				gAttr: okGattr,
			},
			want: true,
		},
	}

	for k, tt := range tests {
		ctx := createGetWebContext()
		req, _ := http.NewRequest("GET", `/`, nil)
		req.AddCookie(&http.Cookie{
			Name:     "g_attr",
			Value:    url.QueryEscape(tt.args.gAttr),
			MaxAge:   math.MaxInt64,
			Path:     "*",
			Domain:   ".baidu.com",
			Secure:   false,
			HttpOnly: false,
		})
		ctx.Request = req
		ok := IsNewUser(ctx)
		ag.Add(fmt.Sprintf("Test Case Of TestIsNewUser : %d, return Value Compare", k), ok, ut.ShouldEqual, tt.want)
	}
	ag.Run()
}

func TestIsAudit(t *testing.T) {
	ctx := createGetWebContext()
	req, _ := http.NewRequest("GET", `/suggest?ctl=sug&query=sug`, nil)
	ctx.Request = req

	// cookie没有st
	assert.Equal(t, false, IsAudit(ctx))

	// ST=1
	ctx.Request.AddCookie(&http.Cookie{
		Name:     "ST",
		Value:    "1",
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	assert.Equal(t, true, IsAudit(ctx))

	// ST=0
	req.Header.Set("Cookie", "ST=0")
	assert.Equal(t, false, IsAudit(ctx))

	// ST=a
	req.Header.Set("Cookie", "ST=a")
	assert.Equal(t, false, IsAudit(ctx))
}

func TestGetClickRecKey(t *testing.T) {
	tests := []struct {
		name    string
		qid     string
		cardPos string
		subPos  string
		want    string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "normal case",
			qid:     "123",
			cardPos: "1",
			subPos:  "2",
			want:    "123_1_2",
			wantErr: false,
		},
		{
			name:    "empty subPos",
			qid:     "123",
			cardPos: "1",
			subPos:  "",
			want:    "123_1_0",
			wantErr: false,
		},
		{
			name:    "empty qid",
			qid:     "",
			cardPos: "1",
			subPos:  "2",
			want:    "",
			wantErr: true,
			errMsg:  "GetClickRecKey fail: qid为空",
		},
		{
			name:    "empty cardPos",
			qid:     "123",
			cardPos: "",
			subPos:  "2",
			want:    "",
			wantErr: true,
			errMsg:  "GetClickRecKey fail: cardPos为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetClickRecKey(tt.qid, tt.cardPos, tt.subPos)
			if tt.wantErr {
				assert.Equal(t, tt.errMsg, err.Error())
			} else {
				assert.Equal(t, nil, err)
			}
			assert.Equal(t, tt.want, got)
		})
	}
}
func TestParserSugDict(t *testing.T) {
	// 准备测试文件
	tmpFile, err := ioutil.TempFile("", "sugdict")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpFile.Name())

	// 测试正常情况
	testContent := "text1$$text2$$text3\ntext4$$text5$$text6"
	if _, err := tmpFile.WriteString(testContent); err != nil {
		t.Fatal(err)
	}
	tmpFile.Close()

	sugConf := make(map[string][]string)
	err = ParserSugDict(tmpFile.Name(), sugConf)
	if err != nil {
		t.Errorf("ParserSugDict failed: %v", err)
	}
	if len(sugConf) != 2 {
		t.Errorf("Expected sugConf length 2, got %d", len(sugConf))
	}

	// 测试格式错误
	tmpFile, err = ioutil.TempFile("", "sugdict")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpFile.Name())

	testContent = "text1$$text2\ntext3$$text4$$text5"
	if _, err := tmpFile.WriteString(testContent); err != nil {
		t.Fatal(err)
	}
	tmpFile.Close()

	sugConf = make(map[string][]string)
	err = ParserSugDict(tmpFile.Name(), sugConf)
	if err == nil {
		t.Error("Expected format error, got nil")
	}

	// 测试文件不存在
	sugConf = make(map[string][]string)
	err = ParserSugDict("nonexistent_file", sugConf)
	if err == nil {
		t.Error("Expected file not found error, got nil")
	}
}
