package common

import (
	"testing"
)

func TestIsZbiosMain(t *testing.T) {
	tests := []struct {
		ua       string
		expected bool
	}{
		{"Mozilla/5.0 (Linux; Android 10; Mi 9) baiduboxapp/*********", true},
		{"Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 baiduboxapp", true},
		{"Mozilla/5.0 (Windows NT 10.0; Win64; x64)", false},
	}

	for _, tt := range tests {
		result := IsZbiosMain(tt.ua)
		if result != tt.expected {
			t.Errorf("IsZbiosMain(%q) = %v; want %v", tt.ua, result, tt.expected)
		}
	}
}

func TestIsZibosLite(t *testing.T) {
	tests := []struct {
		ua       string
		expected bool
	}{
		{"Mozilla/5.0 (Linux; Android 10; Mi 9) Lite baiduboxapp/*********", true},
		{"Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 Lite baiduboxapp", true},
		{"Mozilla/5.0 (Windows NT 10.0; Win64; x64) baiduboxapp/*********", false},
		{"Mozilla/5.0 (iPhone) info baiduboxapp/******* (Baidu; P2 10.3)", true},
	}

	for _, tt := range tests {
		result := IsZibosLite(tt.ua)
		if result != tt.expected {
			t.Errorf("IsZibosLite(%q) = %v; want %v", tt.ua, result, tt.expected)
		}
	}
}

func TestGetZbiosMainVersion(t *testing.T) {
	tests := []struct {
		ua       string
		expected string
	}{
		{"Mozilla/5.0 (Linux; Android 10; Mi 9) baiduboxapp/*********", "*********"},
		{"Mozilla/5.0 (Windows NT 10.0; Win64; x64)", ""},
	}

	for _, tt := range tests {
		result := GetZbiosMainVersion(tt.ua)
		if result != tt.expected {
			t.Errorf("GetZbiosMainVersion(%q) = %q; want %q", tt.ua, result, tt.expected)
		}
	}
}

func TestGetUserOS(t *testing.T) {
	tests := []struct {
		ua       string
		expected string
	}{
		{"Mozilla/5.0 (Linux; Android 10; Mi 9) baiduboxapp/*********", "android"},
		{"Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) baiduboxapp/*********", "iphone"},
		{"Mozilla/5.0 (iPad; CPU OS 13_2 like Mac OS X) baiduboxapp/*********", "ipad"},
		{"Mozilla/5.0 (Linux; HarmonyOS; ARS-AL00) baiduboxapp/*********", "harmony"},
		{"Mozilla/5.0 (Windows NT 10.0; Win64; x64)", ""},
	}

	for _, tt := range tests {
		result := GetUserOS(tt.ua)
		if result != tt.expected {
			t.Errorf("GetUserOS(%q) = %q; want %q", tt.ua, result, tt.expected)
		}
	}
}
