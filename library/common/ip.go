package common

import (
	"net"
	"net/http"
	"reflect"
	"strings"
)

func GetClientIp(request *http.Request) string {
	var strB []byte
	uip := ""
	header := request.Header
	unknownB := []byte("unknown")

	//从HTTP_X_FORWARDED_FOR头部获取
	X_FORWARDED_FOR := header.Get("X_FORWARDED_FOR")
	if X_FORWARDED_FOR != "" {
		strB = []byte(X_FORWARDED_FOR)
		if !reflect.DeepEqual(strB, unknownB) {
			uip = X_FORWARDED_FOR
			if strings.Index(uip, ",") != -1 {
				_uips := strings.Split(uip, ",")
				return _uips[0]
			}
		}
	}

	//从CLIENT_IP头部获取
	CLIENT_IP := header.Get("CLIENT_IP")
	if CLIENT_IP != "" {
		strB = []byte(CLIENT_IP)
		if !reflect.DeepEqual(strB, unknownB) {
			return CLIENT_IP
		}
	}

	REMOTE_ADDR := request.RemoteAddr
	if REMOTE_ADDR != "" {
		strB = []byte(REMOTE_ADDR)
		if !reflect.DeepEqual(strB, unknownB) {
			REMOTE_ADDRS := strings.Split(REMOTE_ADDR, ":")
			return REMOTE_ADDRS[0]
		}
	}

	return uip
}

//建议使用此方法来获取IP
func GetUserIp(request *http.Request) string {
	var strB []byte
	header := request.Header
	unknownB := []byte("unknown")

	X_BD_USERIP := header.Get("X_BD_USERIP")
	if X_BD_USERIP != "" {
		strB = []byte(X_BD_USERIP)
		if !reflect.DeepEqual(strB, unknownB) {
			return X_BD_USERIP
		}
	}

	X_Real_IP := header.Get("X-Real-IP")
	if X_Real_IP != "" {
		strB = []byte(X_Real_IP)
		if !reflect.DeepEqual(strB, unknownB) {
			return X_Real_IP
		}
	}
	return GetClientIp(request)
}

func GetFrontendIp(request *http.Request) string {
	REMOTE_ADDR := request.RemoteAddr
	if REMOTE_ADDR != "" {
		REMOTE_ADDRS := strings.Split(REMOTE_ADDR, ":")
		return REMOTE_ADDRS[0]
	}
	return ""
}

func GetLocalIp() string {
	addrs, err := net.InterfaceAddrs()
	defaultIp := "127.0.0.1"
	if err != nil {
		return defaultIp
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return defaultIp
}

func IsIPv6(address string) bool {
    return strings.Count(address,":") >= 2
}
