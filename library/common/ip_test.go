package common

import (
	"net/http"
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestGetClientIp(t *testing.T) {
	type args struct {
		request *http.Request
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetClientIp ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/sug`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "CLIENT_IP",
		Value:    "127.0.0.1",
		MaxAge:   60000,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})

	req1, _ := http.NewRequest("GET", `/sug`, nil)
	req1.Header.Set("CLIENT_IP", "127.0.0.1")

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //string
	}{
		{
			"TestGetClientIp",
			args{
				req,
			},
			Want{
				"",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetClientIp",
			args{
				req1,
			},
			Want{
				"127.0.0.1",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := GetClientIp(tt.args.request)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestGetClientIp, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestGetUserIp(t *testing.T) {
	type args struct {
		request *http.Request
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetUserIp ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/sug`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "CLIENT_IP",
		Value:    "127.0.0.1",
		MaxAge:   60000,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})

	req1, _ := http.NewRequest("GET", `/sug`, nil)
	req1.Header.Set("X_BD_USERIP", "127.0.0.1")

	req2, _ := http.NewRequest("GET", `/sug`, nil)
	req2.Header.Set("X-Real-IP", "127.0.0.1")

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //string
	}{
		{
			"TestGetUserIp",
			args{
				req,
			},
			Want{
				"",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetUserIp",
			args{
				req1,
			},
			Want{
				"127.0.0.1",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetUserIp",
			args{
				req2,
			},
			Want{
				"127.0.0.1",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := GetUserIp(tt.args.request)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestGetUserIp, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestGetFrontendIp(t *testing.T) {
	type args struct {
		request *http.Request
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetFrontendIp ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/sug`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "CLIENT_IP",
		Value:    "127.0.0.1",
		MaxAge:   60000,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //string
	}{
		{
			"TestGetFrontendIp",
			args{
				req,
			},
			Want{
				"",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := GetFrontendIp(tt.args.request)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestGetFrontendIp, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestGetLocalIp(t *testing.T) {

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetLocalIp ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	req, _ := http.NewRequest("GET", `/sug`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "CLIENT_IP",
		Value:    "127.0.0.1",
		MaxAge:   60000,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})

	tests := []struct {
		testCaseTitle string
		want          Want //string
	}{
		{
			"TestGetLocalIp",
			Want{
				"",
				ut.ShouldNotEqual,
			},
		},
	}

	for k, tt := range tests {
		got := GetLocalIp()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestGetLocalIp, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}
