package common

import (
	"os"
	"path/filepath"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp/env"
)

// 测试twoinoneConfInit函数
func TestTwoinoneConfInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "twoinone.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	twoinoneConfInit()

	// 验证配置是否正确加载
	assert.NotZero(t, TwoInOneConfMem.PackageSize, "TwoInOneConfMem.PackageSize应已加载")
	t.Logf("TwoInOneConfMem.PackageSize: %d", TwoInOneConfMem.PackageSize)
	t.Logf("TwoInOneConfMem.PrefetchRatio: %d", TwoInOneConfMem.PrefetchRatio)
}

// 测试switchConfInit函数
func TestSwitchConfInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "switch.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	switchConfInit()

	// 验证配置是否正确加载
	assert.NotNil(t, SwitchConfMem.Switch, "SwitchConfMem.Switch不应为nil")
	t.Logf("SwitchConfMem中加载了 %d 个开关配置", len(SwitchConfMem.Switch))
}

// 测试dumpConfInit函数
func TestDumpConfInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "dump.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	dumpConfInit()

	// 验证配置是否正确加载，这里只验证结构体是否被初始化
	t.Logf("DumpConfMem.DumpSwitch: %v", DumpConfMem.DumpSwitch)
}

// 测试SampleSwitchConfInit函数
func TestSampleSwitchConfInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "sample_switch.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	SampleSwitchConfInit()

	// 验证配置是否正确加载
	t.Logf("SampleConfMem.SampleSwitch: %v", SampleConfMem.SampleSwitch)
}

// 测试SampleWhitePathConfInit函数
func TestSampleWhitePathConfInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "sample_white_list.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	SampleWhitePathConfInit()

	// 验证配置是否正确加载
	assert.NotNil(t, SampleWhiteMap, "SampleWhiteMap不应为nil")
	t.Logf("SampleWhiteMap加载了 %d 个白名单项", len(SampleWhiteMap))
}

// 测试rsWhiteInit函数
func TestRsWhiteInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "rs_white.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	rsWhiteInit()

	// 验证配置是否正确加载
	assert.NotNil(t, RSWhite.URL, "RSWhite.URL不应为nil")
	t.Logf("RSWhite.URL加载了 %d 个项目", len(RSWhite.URL))
}

// 测试boardCityInit函数
func TestBoardCityInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "board_city.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	boardCityInit()

	// 验证配置是否正确加载
	assert.NotNil(t, BoardCity, "BoardCity不应为nil")
	t.Logf("BoardCity加载了 %d 个城市信息", len(BoardCity))
}

// 测试sampleInit函数
func TestSampleInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "sample.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	sampleInit()

	// 验证配置是否正确加载
	assert.NotNil(t, SampleConf, "SampleConf不应为nil")
	t.Logf("SampleConf加载了 %d 个配置项", len(SampleConf))
}

// 测试hisTagInit函数
func TestHisTagInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "his_tag.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数
	hisTagInit()

	// 验证配置是否正确加载
	assert.NotNil(t, HisTagConf, "HisTagConf不应为nil")
	t.Logf("HisTagConf加载了 %d 个配置项", len(HisTagConf))
}

// 测试sugDirectInit函数
func TestSugDirectInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "sug_direct.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Logf("初始化函数sugDirectInit发生panic: %v", r)
			}
		}()

		sugDirectInit()

		// 验证配置是否正确加载
		assert.NotNil(t, SugDirectConf, "SugDirectConf不应为nil")
		t.Logf("SugDirectConf加载了 %d 个配置项", len(SugDirectConf))
	}()
}

// 测试sugDirectSpzInit函数
func TestSugDirectSpzInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "super_pinzhuan.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Logf("初始化函数sugDirectSpzInit发生panic: %v", r)
			}
		}()

		sugDirectSpzInit()

		// 验证配置是否正确加载
		assert.NotNil(t, SugDirectSpzConf, "SugDirectSpzConf不应为nil")
		t.Logf("SugDirectSpzConf加载了 %d 个配置项", len(SugDirectSpzConf))
	}()
}

// 测试sugDirectPzInit函数
func TestSugDirectPzInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "normal_pinzhuan.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Logf("初始化函数sugDirectPzInit发生panic: %v", r)
			}
		}()

		sugDirectPzInit()

		// 验证配置是否正确加载
		assert.NotNil(t, SugDirectPzConf, "SugDirectPzConf不应为nil")
		t.Logf("SugDirectPzConf加载了 %d 个配置项", len(SugDirectPzConf))
	}()
}

// 测试segCutInit函数
func TestSegCutInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "dict", "zh", "s_1.txt")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("分词词典 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Logf("初始化函数segCutInit发生panic: %v", r)
			}
		}()

		segCutInit()

		// 这里无法直接检验分词器是否成功加载词典
		// 但可以验证Seg对象是否被初始化
		t.Logf("segCut初始化成功")
	}()
}

// 测试hotEventInit函数
func TestHotEventInit(t *testing.T) {
	configPath := env.ConfRootPath()

	// 检查两个配置文件是否至少存在一个
	gaokaoPath := filepath.Join(configPath, "hotevent_24gaokao.toml")
	questionPath := filepath.Join(configPath, "hotevent_question.toml")

	gaokaoExists := false
	questionExists := false

	if _, err := os.Stat(gaokaoPath); !os.IsNotExist(err) {
		gaokaoExists = true
	}

	if _, err := os.Stat(questionPath); !os.IsNotExist(err) {
		questionExists = true
	}

	if !gaokaoExists && !questionExists {
		t.Skipf("热门事件配置文件不存在，跳过测试")
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Logf("初始化函数hotEventInit发生panic: %v", r)
			}
		}()

		hotEventInit()

		// 验证配置是否正确加载
		if gaokaoExists {
			assert.NotNil(t, HotEvent24GaokaoConf, "HotEvent24GaokaoConf不应为nil")
		}

		if questionExists {
			assert.NotNil(t, HotEventQuestion, "HotEventQuestion不应为nil")
		}

		t.Logf("hotEventInit初始化成功")
	}()
}

// 测试shopSugInit函数
func TestShopSugInit(t *testing.T) {
	configPath := env.ConfRootPath()
	filePath := filepath.Join(configPath, "shop_sug.toml")

	// 判断配置文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Skipf("配置文件 %s 不存在，跳过测试", filePath)
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Logf("初始化函数shopSugInit发生panic: %v", r)
			}
		}()

		shopSugInit()

		// 验证配置是否正确加载
		assert.NotNil(t, ShopSugConf, "ShopSugConf不应为nil")
		t.Logf("ShopSugConf加载了 %d 个配置项", len(ShopSugConf))
	}()
}

func TestSugTextConfInit(t *testing.T) {
	configPath := env.ConfRootPath()
	dirPath := filepath.Join(configPath, "dict", "sugtext")

	// 检查sugtext目录是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		t.Skipf("sugtext目录 %s 不存在，跳过测试", dirPath)
		return
	}

	// 执行初始化函数，使用defer-recover处理可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("初始化函数SugTextConfInit发生panic: %v", r)
			}
		}()

		err := SugTextConfInit()
		if err != nil {
			t.Errorf("SugTextConfInit初始化失败: %v", err)
			return
		}

		// 验证配置是否被加载
		if len(SugTextConf) == 0 {
			t.Error("SugTextConf未被正确初始化")
		} else {
			t.Logf("SugTextConf初始化成功，加载了%d个配置文件", len(SugTextConf))
		}
	}()
}

func TestNormalDictInit(t *testing.T) {
	var wg sync.WaitGroup
	panicCh := normalDictInit(&wg)

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(panicCh)
	}()

	// 检查是否有panic发生
	for err := range panicCh {
		t.Errorf("字典初始化发生panic: %v", err)
	}

	// 验证配置是否正确加载
	assert.NotNil(t, SugTextConf, "SugTextConf不应为nil")
}
