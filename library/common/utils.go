package common

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"encoding/base32"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"math"
	"net/http"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/gdp/codec/cbrotli/cbrotli"
	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const (
	BaseTimestamp = 1601347333
	AK            = "ApWT"
	SK            = "K1yaYDBy"
)

/**
 * 检查是否为完整utf8字符集
 */
func IsUtf8(s string) bool {
	return utf8.ValidString(s)
}

/**
 * 解析network参数 返回网络类型
 * network参数 http://wiki.baidu.com/pages/viewpage.action?pageId=108777513&from=twiki
 * @param $network string 客户端上传的network参数
 * @return int 0:异常 1:wifi 2:2g 3:3g 4:4g
 */
func GetNetwork(net string) string {
	if net == "" {
		return "1"
	}

	netPlat := strings.Split(net, "_")
	if len(netPlat) <= 1 {
		return "1" // 老版本 无法识别 全部当成wifi
	}

	// apn_id := netPlat[0] //运营商逻辑 此处先不处理运营商部分
	mobilenet_id := netPlat[1]
	if _, err := strconv.ParseUint(mobilenet_id, 10, 64); err != nil {
		return "1" // 未知错误 全部当wifi
	}

	var in_array = func(target string, array []string) bool {
		for _, value := range array {
			if value == target {
				return true
			}
		}
		return false
	}

	typeWifi := []string{"0"}
	if in_array(mobilenet_id, typeWifi) {
		return "1"
	}
	type2G := []string{"1", "2", "4", "7", "11"}
	if in_array(mobilenet_id, type2G) {
		return "2"
	}
	type3G := []string{"3", "5", "6", "8", "9", "10", "12", "14", "15"}
	if in_array(mobilenet_id, type3G) {
		return "3"
	}
	type4G := []string{"13"}
	if in_array(mobilenet_id, type4G) {
		return "4"
	}
	return "1"
}

/**
 * 视觉img/query获取图片地址校验参数技术
 * @param $sign string 图片sign
 * @return string
 */

func GetGraphAuthEncrypt(sign string) (string, error) {
	nowTime := time.Now().Unix()
	normTime := math.Ceil((float64(nowTime) / 600)) * 600
	tsDiff := uint32(normTime - BaseTimestamp)
	encryptStr := SK + sign + strconv.FormatUint(uint64(tsDiff), 10)
	checkSum := crc32.ChecksumIEEE([]byte(encryptStr))
	byteBuff := bytes.NewBuffer(make([]byte, 0, 8))
	if err := binary.Write(byteBuff, binary.LittleEndian, tsDiff); err != nil {
		return "", err
	}
	if err := binary.Write(byteBuff, binary.LittleEndian, checkSum); err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(byteBuff.Bytes()), nil
}

/**
 * 组装protobuf delimited msg
 */
func WriteProtoBufDelimited(w *bytes.Buffer, msg []byte) (n int, err error) {
	msgSize := len(msg)
	sync, err := w.Write(proto.EncodeVarint(uint64(msgSize)))
	if err != nil {
		return sync, nil
	}

	n, err = w.Write(msg)
	return n + sync, err
}

/**
 * 按照k-v方式重组http.Header
 * 双端处理不一致
 */
func HttpHeaderConvert(osbranch string, header *http.Header) map[string]interface{} {
	header_map := make(map[string]interface{}, len(*header))
	for k, v := range *header {
		if k != "Set-Cookie" {
			header_map[k] = v[0]
		} else {
			if osbranch == "a0" {
				header_map[k] = v
			} else {
				header_map[k] = strings.Join(v, ", ")
			}
		}
	}
	return header_map
}

/**
 * 封装gzip压缩逻辑
 *
 */
func GzipCompress(w *gzip.Writer, buffer *bytes.Buffer, content []byte) error {
	_, gzipWriteErr := w.Write(content)
	if gzipWriteErr != nil {
		return gzipWriteErr
	}
	w.Flush() // 使用完write之后，一定得flush, buffer才有对应content的压缩内容，调用buffer.Bytes即可得到压缩数据
	return nil
}

func BrCompress(w *cbrotli.Writer, buffer *bytes.Buffer, content []byte) error {
	_, err := w.Write(content)
	if err != nil {
		return err
	}
	w.Flush()
	return nil
}

// 判断是否使用br压缩
func IsEnableBr(ctx *gdp.WebContext) bool {
	// 2023.4.17，服务端改成只支持gzip压缩

	// accept_encoding := ctx.GetHeader("Accept-Encoding")
	// items := strings.Split(accept_encoding, ",")
	// for _, v := range items {
	// 	if strings.TrimSpace(v) == "br" {
	// 		return true
	// 	}
	// }
	return false
}

func GetCompressName(ctx *gdp.WebContext) string {
	if IsEnableBr(ctx) {
		return "br"
	}
	return "gzip"
}

const (
	MAIN_APP = "main"
	BIG_APP  = "big"
	LITE_APP = "lite"
)

func GetAppBranch(osbranch string) string {
	switch osbranch {
	case "a0", "i0":
		return MAIN_APP
	case "a2", "i3":
		return LITE_APP
	case "a7", "i7":
		return BIG_APP
	default:
		return ""
	}
}

// 判断二合一接口是否给端混合协议类型
func IsUseCSMIXED(request *map[string]string) bool {
	if request == nil {
		return false
	}
	v, ok := (*request)["twoinone_csmixed"]
	if !ok || v != "1" {
		return false
	}

	return true
}

// 如果uid合法且登录，返回true
func IsLogin(requestParams *map[string]string) bool {
	_, ok := (*requestParams)[constant.SESSION_UID]
	if ok {
		return true
	}
	return false
}

func IsInArray(arr []string, one string) bool {
	for _, v := range arr {
		if v == one {
			return true
		}
	}
	return false
}

// 解析g_attr，检查时间戳是否有脏数据
func ParseGAttr(gAttr string) map[string]string {
	parsedValue := map[string]string{}

	// eg: ut_3-at_1677740171-id_1027454w-d8t_1677740171123
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/76YVMWYfJM/7fnymOz3cf/EvRFnfWdPrVFG7
	gAttrArr := strings.Split(gAttr, "-")
	for _, v := range gAttrArr {
		tmp := strings.Split(v, "_")
		if len(tmp) == 2 {
			parsedValue[tmp[0]] = tmp[1]
		}
	}

	// 检查at时间戳是否可用, 2023-03-15 00:00:00
	if parsedValue["at"] != "" {
		atime, err := strconv.ParseInt(parsedValue["at"], 10, 64)
		if err != nil {
			return map[string]string{}
		}
		if atime < 1678809600 {
			return map[string]string{}
		}
	}

	// 毫秒时间戳转换成秒
	if parsedValue["d8t"] != "" && len(parsedValue["d8t"]) >= 13 {
		parsedValue["d8t"] = parsedValue["d8t"][:len(parsedValue["d8t"])-3]
	}

	return parsedValue
}

func GetSampleValue(ctx *gdp.WebContext, key, defaultValue string) string {
	ret := ""
	if defaultValue != "" {
		ret = defaultValue
	}

	csWSids, err := ctx.Cookie("CS_W_SIDS")
	if err != nil || csWSids == "" {
		return ret
	}
	// cookie中的端sid
	csWSidsArr := strings.Split(csWSids, "-")
	if len(csWSidsArr) == 0 {
		return ret
	}

	sampleSet, ok := SampleConf[key]
	if !ok {
		return ret
	}

	for _, sid := range csWSidsArr {
		value, ok := sampleSet[sid]
		if ok {
			return value
		}
	}

	return ret
}

func GetSampleValues(ctx *gdp.WebContext, key string, defaultValue []string) (ret []string) {
	if len(defaultValue) != 0 {
		return
	}

	csWSids, err := ctx.Cookie("CS_W_SIDS")
	if err != nil || csWSids == "" {
		return ret
	}
	// cookie中的端sid
	csWSidsArr := strings.Split(csWSids, "-")
	if len(csWSidsArr) == 0 {
		return ret
	}

	sampleSet, ok := SampleConf[key]
	if !ok {
		return ret
	}

	for _, sid := range csWSidsArr {
		value, ok := sampleSet[sid]
		if ok {
			ret = append(ret, value)
		}
	}

	return ret
}

// 识别来自对话式搜索的流量
func IsChatSearch(request *map[string]string) bool {
	if request == nil {
		return false
	}

	// 对话式搜索来源有两种
	// 第一种，简单app
	osname, ok := (*request)[constant.OSNAME]
	if ok && osname == "searchcraft" {
		return true
	}

	// 第二种，带着对话搜索标记的
	tag, ok := (*request)[constant.ChatSearchTag]
	if ok && tag == "1" {
		return true
	}

	// 否则不是
	return false
}

func NewBase32Decode(encodedData string) (decodedData []byte, err error) {
	if strings.HasSuffix(encodedData, "=") {
		decodedData, err = base32.StdEncoding.DecodeString(encodedData)
		if err != nil {
			return nil, err
		}
	} else {
		decodedData, err = base32.StdEncoding.WithPadding(base32.NoPadding).DecodeString(encodedData)
		if err != nil {
			return nil, err
		}
	}
	return decodedData, nil
}

func IsNewUser(ctx *gdp.WebContext) bool {
	gAttr, err := ctx.Cookie("g_attr")
	if err != nil || gAttr == "" {
		// 不存在g_attr，则认为是老用户
		return false
	}
	parsedValue := ParseGAttr(gAttr)
	if ut, ok := parsedValue["ut"]; !ok || ut != "1" {
		return false
	}
	at, ok := parsedValue["at"]
	if !ok || len(at) != 10 {
		// 激活时间戳为秒级
		return false
	}
	atTime, err := strconv.ParseInt(at, 10, 64)
	if err != nil {
		return false
	}
	curr := time.Now().Unix()
	return curr-atTime <= 7*86400 && curr >= atTime
}

// 根据cookie中ST判断是否在审核状态下, ST>0 代表审核状态
func IsAudit(ctx *gdp.WebContext) bool {
	if st, err := ctx.Cookie("ST"); err == nil && st != "" {
		stInt, err := strconv.ParseInt(strings.TrimSpace(st), 10, 64)
		if err == nil && stInt > 0 {
			return true
		}
	}

	return false
}

// 获取点后推升级存储key
func GetClickRecKey(qid, cardPos, subPos string) (string, error) {
	curKey := ""
	if qid == "" {
		return curKey, errors.New("GetClickRecKey fail: qid为空")
	}
	if cardPos == "" {
		return curKey, errors.New("GetClickRecKey fail: cardPos为空")
	}
	if subPos == "" {
		subPos = "0"
	}
	return fmt.Sprintf("%s_%s_%s", qid, cardPos, subPos), nil
}

// 传入格式 text1\ttext2\ttext3
// 返回：
// map[text1][0] = text1
// map[text1][1] = text2
// map[text1][2] = text3
func ParserSugDict(filePath string, sugConf map[string][]string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	index := 0
	for scanner.Scan() {
		index++
		line := scanner.Text()
		suglist := strings.Split(line, "$$")
		if len(suglist) != 3 {
			// 格式不符合预期
			return errors.New("ParserSugDict fail: line[" + strconv.Itoa(index) + "] format error")
		}
		// 写入
		sugConf[suglist[0]] = suglist
	}

	return scanner.Err()
}
func CheckSugDictName(fileName string) bool {
	nameList := strings.Split(fileName, ".")
	// 限制文件aa.txt
	if len(nameList) != 2 {
		return false
	}
	// 限制txt文件
	if nameList[1] != "txt" {
		return false
	}
	// 文件名不可有下划线
	if strings.Contains(fileName, "_") {
		return false
	}
	return true
}

// 计算当前平台（32/64位）int 的上下界
var (
	intMax = int(^uint(0) >> 1)
	intMin = -intMax - 1
)

// ToInt 将任意 interface{} 尽力转换为 int：
// - 非数字返回 0；
// - 浮点向零截断；
// - 支持 string / json.Number / 自定义数字别名类型；
// - 溢出时饱和到 int 边界。
func ToInt(v interface{}) int {
	if v == nil {
		return 0
	}

	switch x := v.(type) {
	case int:
		return x
	case int8:
		return int(x)
	case int16:
		return int(x)
	case int32:
		return int(x)
	case int64:
		return clampInt64(x)
	case uint:
		return clampUint64(uint64(x))
	case uint8:
		return int(x)
	case uint16:
		return int(x)
	case uint32:
		return clampUint64(uint64(x))
	case uint64:
		return clampUint64(x)
	case uintptr:
		return clampUint64(uint64(x))
	case float32:
		return clampFloat64(float64(x))
	case float64:
		return clampFloat64(x)
	case string:
		s := strings.TrimSpace(x)
		if s == "" {
			return 0
		}
		if i64, err := strconv.ParseInt(s, 10, 64); err == nil {
			return clampInt64(i64)
		}
		if f64, err := strconv.ParseFloat(s, 64); err == nil {
			return clampFloat64(f64)
		}
		return 0
	case json.Number:
		if i64, err := x.Int64(); err == nil {
			return clampInt64(i64)
		}
		if f64, err := x.Float64(); err == nil {
			return clampFloat64(f64)
		}
		return 0
	default:
		// 兜底：支持自定义别名类型（type MyInt int 等）
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return clampInt64(rv.Int())
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
			return clampUint64(rv.Uint())
		case reflect.Float32, reflect.Float64:
			return clampFloat64(rv.Float())
		}
		return 0
	}
}

func clampInt64(i int64) int {
	if i > int64(intMax) {
		return intMax
	}
	if i < int64(intMin) {
		return intMin
	}
	return int(i)
}

func clampUint64(u uint64) int {
	if u > uint64(intMax) {
		return intMax
	}
	return int(u)
}

func clampFloat64(f float64) int {
	if math.IsNaN(f) || math.IsInf(f, 0) {
		return 0
	}
	if f > float64(intMax) {
		return intMax
	}
	if f < float64(intMin) {
		return intMin
	}
	return int(f) // 向零截断
}
