// Package crypt 包用于加密和解密
package crypt

import (
	"encoding/base64"
	"errors"

	"github.com/forgoer/openssl"
)

// Cryptor 编码器
type AESCryptor struct {
	ENCKEY []byte `json:"ENCKEY"`
	IV     []byte `json:"IV"`
}

// NewCryptor 初始化
func NewCryptor(ENCKEY, IV []byte) *AESCryptor {
	return &AESCryptor{
		ENCKEY: ENCKEY,
		IV:     IV,
	}
}

// CBCEncrypt aes 加密
func (c *AESCryptor) CBCEncrypt(text []byte) (res string, err error) {
	encryptStr, err := openssl.AesCBCEncrypt(text, c.ENCKEY, c.IV, openssl.PKCS5_PADDING)
	if err != nil {
		return res, err
	}
	base64Str := base64.URLEncoding.EncodeToString(encryptStr)
	return base64Str, nil
}

// CBCDecryptor aes 解密
func (c *AESCryptor) CBCDecryptor(base64Str string) (res []byte, err error) {
	encrypterByte, err := base64.URLEncoding.DecodeString(base64Str)
	if err != nil {
		return res, err
	}
	if len(encrypterByte) > 0 && len(encrypterByte)%16 != 0 {
		return res, errors.New("token_illegal")
	}
	res, err = openssl.AesCBCDecrypt(encrypterByte, c.ENCKEY, c.IV, openssl.PKCS5_PADDING)
	return res, err
}
