// Package container 一些用来操作各种 interface 数据的容器
package container

import (
	"reflect"
)

// KeyExtractor 用于从任意对象中提取 key。
// 返回 ok=false 或 key=="" 时，该元素会被丢弃。
type KeyExtractor func(v interface{}) (key string, ok bool)

// OrderContainer: map + 两个 slice（items、keys），顺序稳定。
type OrderContainer struct {
	index map[string]int // key -> index in items/keys
	items []interface{}  // 去重后元素，保持首次出现顺序
	keys  []string       // 与 items 对齐的 key 列表
}

// NewEmpty 创建一个空容器。
func NewEmpty() *OrderContainer {
	return &OrderContainer{
		index: make(map[string]int),
		items: make([]interface{}, 0),
		keys:  make([]string, 0),
	}
}

// NewOrderContainerFromSlice 从任意类型的 slice/array 构建容器。
// - 会按首次出现顺序保留每个 key 的第一个元素
// - 无法获取 key（或 key 为空）的元素会被丢弃
// - 输入必须是切片或数组，否则 panic（与标准库一致的做法）
func NewOrderContainerFromSlice(arr interface{}, keyFn KeyExtractor) *OrderContainer {
	if keyFn == nil {
		return nil
	}
	v := reflect.ValueOf(arr)
	if v.Kind() != reflect.Slice && v.Kind() != reflect.Array {
		return nil
	}
	c := NewEmpty()
	for i := 0; i < v.Len(); i++ {
		elem := v.Index(i).Interface()
		key, ok := keyFn(elem)
		if !ok || key == "" {
			continue // 丢弃无法提取 key 的元素
		}
		if _, exists := c.index[key]; exists {
			continue // 去重：保留首次出现
		}
		c.index[key] = len(c.items)
		c.items = append(c.items, elem)
		c.keys = append(c.keys, key)
	}
	return c
}

// Items 返回一个新的切片（拷贝），防止外部修改内部顺序/元素。
func (c *OrderContainer) Items() []interface{} {
	cp := make([]interface{}, len(c.items))
	copy(cp, c.items)
	return cp
}

// Keys 返回一个新的切片（拷贝）。
func (c *OrderContainer) Keys() []string {
	cp := make([]string, len(c.keys))
	copy(cp, c.keys)
	return cp
}

// Len 元素个数（去重后）。
func (c *OrderContainer) Len() int { return len(c.items) }

// ContainsKey 判断是否存在 key。
func (c *OrderContainer) ContainsKey(k string) bool {
	_, ok := c.index[k]
	return ok
}

// GetByKey 按 key 获取元素（第二个返回值表示是否存在）。
// 加入下标与 keys 一致性防御，避免 index 失效导致越界。
func (c *OrderContainer) GetByKey(k string) (interface{}, bool) {
	i, ok := c.index[k]
	if !ok {
		return nil, false
	}
	// 防御：下标越界或 key 不匹配则视为不存在
	if i < 0 || i >= len(c.items) || i >= len(c.keys) || c.keys[i] != k {
		return nil, false
	}
	return c.items[i], true
}

// Remove 按给定 keys 批量删除元素，返回一个新容器（顺序稳定，原容器不变）。
// 使用最小长度裁剪，确保不越界。
func (c *OrderContainer) Remove(keys []string) *OrderContainer {
	if len(keys) == 0 {
		return c.clone()
	}
	block := make(map[string]struct{}, len(keys))
	for _, k := range keys {
		if k != "" {
			block[k] = struct{}{}
		}
	}
	out := NewEmpty()
	limit := min(len(c.keys), len(c.items))
	for i := 0; i < limit; i++ {
		k := c.keys[i]
		if _, rm := block[k]; rm {
			continue // 需要删除的 key，跳过
		}
		it := c.items[i]
		out.index[k] = len(out.items)
		out.items = append(out.items, it)
		out.keys = append(out.keys, k)
	}
	return out
}

// RangeByIndex 返回 [start, end)（左闭右开）区间的子容器，采用最小长度裁剪，确保不越界。
func (c *OrderContainer) RangeByIndex(start, end int) *OrderContainer {
	n := min(len(c.items), len(c.keys)) // 防御：用最小长度作为有效区间上限

	// 裁剪边界
	if start < 0 {
		start = 0
	}
	if end > n {
		end = n
	}
	if start > end {
		start = end // 归一化为空区间
	}

	out := NewEmpty()
	for i := start; i < end; i++ {
		k := c.keys[i]
		it := c.items[i]
		out.index[k] = len(out.items)
		out.items = append(out.items, it)
		out.keys = append(out.keys, k)
	}
	return out
}

// clone 浅拷贝容器（items 中的元素是 interface{}，不深拷贝内部字段）。
func (c *OrderContainer) clone() *OrderContainer {
	out := NewEmpty()
	limit := min(len(c.keys), len(c.items))
	for i := 0; i < limit; i++ {
		k := c.keys[i]
		it := c.items[i]
		out.index[k] = len(out.items)
		out.items = append(out.items, it)
		out.keys = append(out.keys, k)
	}
	return out
}

// 放在文件顶部附近
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
