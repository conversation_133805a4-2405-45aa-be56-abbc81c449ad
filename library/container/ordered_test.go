package container

import (
	"reflect"
	"strings"
	"testing"
)

type Item struct {
	ID    int
	Name  string
	Email string
}

// 常用 key 提取器
func keyByEmail(v interface{}) (string, bool) {
	it, ok := v.(Item)
	if !ok {
		return "", false
	}
	if it.Email == "" {
		return "", false
	}
	return it.Email, true
}

func keyByLowerName(v interface{}) (string, bool) {
	it, ok := v.(Item)
	if !ok {
		return "", false
	}
	if it.Name == "" {
		return "", false
	}
	return strings.ToLower(it.Name), true
}

func TestFromSlice_DedupAndOrder(t *testing.T) {
	in := []Item{
		{ID: 1, Name: "Alice", Email: "a@x"},
		{ID: 2, Name: "Bob", Email: "b@x"},
		{ID: 3, Name: "AliceDup", Email: "a@x"}, // 重复 email，应该丢弃
		{ID: 4, Name: "", Email: ""},            // 无法提取 key，丢弃
		{ID: 5, Name: "<PERSON>", Email: "c@x"},
	}
	c := NewOrderContainerFromSlice(in, keyByEmail)

	if c.Len() != 3 {
		t.Fatalf("Len() = %d, want 3", c.Len())
	}

	wantKeys := []string{"a@x", "b@x", "c@x"}
	gotKeys := c.Keys()
	if !reflect.DeepEqual(gotKeys, wantKeys) {
		t.Fatalf("Keys() = %#v, want %#v", gotKeys, wantKeys)
	}

	// 检查 items 的顺序与首次出现一致（ID: 1,2,5）
	items := c.Items()
	if len(items) != 3 {
		t.Fatalf("Items len = %d, want 3", len(items))
	}
	gotIDs := []int{items[0].(Item).ID, items[1].(Item).ID, items[2].(Item).ID}
	wantIDs := []int{1, 2, 5}
	if !reflect.DeepEqual(gotIDs, wantIDs) {
		t.Fatalf("Items IDs = %#v, want %#v", gotIDs, wantIDs)
	}
}

func TestGetByKey_Contains(t *testing.T) {
	c := NewOrderContainerFromSlice(
		[]Item{
			{ID: 1, Name: "Alice", Email: "a@x"},
			{ID: 2, Name: "Bob", Email: "b@x"},
		},
		keyByEmail,
	)

	if !c.ContainsKey("a@x") || c.ContainsKey("nope@x") {
		t.Fatalf("ContainsKey failed")
	}
	v, ok := c.GetByKey("b@x")
	if !ok || v.(Item).ID != 2 {
		t.Fatalf("GetByKey failed, got=%#v ok=%v", v, ok)
	}
}

func TestOrderContainer_RangeByIndex_BasicAndClamp(t *testing.T) {
	// 构建一个有序、不重复的容器
	in := []Item{
		{ID: 1, Name: "A", Email: "a@x"},
		{ID: 2, Name: "B", Email: "b@x"},
		{ID: 3, Name: "C", Email: "c@x"},
		{ID: 4, Name: "D", Email: "d@x"},
		{ID: 5, Name: "E", Email: "e@x"},
	}
	c := NewOrderContainerFromSlice(in, keyByEmail) // 若你的 FromSlice 返回 *OrderContainer

	// 基本区间 [1,4) -> b,c,d
	sub := c.RangeByIndex(1, 4)
	wantKeys := []string{"b@x", "c@x", "d@x"}
	if !reflect.DeepEqual(sub.Keys(), wantKeys) {
		t.Fatalf("RangeByIndex(1,4) keys = %#v, want %#v", sub.Keys(), wantKeys)
	}
	// 顺序与对应 ID 检查
	items := sub.Items()
	gotIDs := []int{items[0].(Item).ID, items[1].(Item).ID, items[2].(Item).ID}
	if !reflect.DeepEqual(gotIDs, []int{2, 3, 4}) {
		t.Fatalf("RangeByIndex(1,4) IDs = %#v, want %#v", gotIDs, []int{2, 3, 4})
	}

	// 左边越界裁剪：[-5,2) -> a,b
	sub2 := c.RangeByIndex(-5, 2)
	if !reflect.DeepEqual(sub2.Keys(), []string{"a@x", "b@x"}) {
		t.Fatalf("RangeByIndex(-5,2) keys = %#v, want [a@x b@x]", sub2.Keys())
	}

	// 右边越界裁剪：[2,10) -> c,d,e
	sub3 := c.RangeByIndex(2, 10)
	if !reflect.DeepEqual(sub3.Keys(), []string{"c@x", "d@x", "e@x"}) {
		t.Fatalf("RangeByIndex(2,10) keys = %#v, want [c@x d@x e@x]", sub3.Keys())
	}

	// 空区间：start==end
	sub4 := c.RangeByIndex(3, 3)
	if sub4.Len() != 0 {
		t.Fatalf("RangeByIndex(3,3) len = %d, want 0", sub4.Len())
	}

	// start> end -> 归一为空区间
	sub5 := c.RangeByIndex(4, 2)
	if sub5.Len() != 0 {
		t.Fatalf("RangeByIndex(4,2) len = %d, want 0", sub5.Len())
	}

	// 原容器不应被修改
	if c.Len() != 5 {
		t.Fatalf("original container modified: Len=%d, want 5", c.Len())
	}
}

func TestPlanB_Remove_NoPanicOnMismatchedLengths(t *testing.T) {
	c := NewEmpty()
	// 人为破坏不变量：items 比 keys 多，index 还包含无效下标
	c.items = []interface{}{"i0", "i1", "i2"}
	c.keys = []string{"k0", "k1"}
	c.index = map[string]int{"k0": 0, "k1": 1, "k2": 2}

	out := c.Remove([]string{"k0"}) // 删除存在与不存在的混合 key
	// limit = min(2,3) = 2，因此只会处理前两个
	if got, want := out.Keys(), []string{"k1"}; !reflect.DeepEqual(got, want) {
		t.Fatalf("Remove keys = %#v, want %#v", got, want)
	}
	if got, want := out.Len(), 1; got != want {
		t.Fatalf("Remove len = %d, want %d", got, want)
	}
}

func TestPlanB_RangeByIndex_NoPanicOnMismatchedLengths(t *testing.T) {
	c := NewEmpty()
	// items 3, keys 2 -> n = 2
	c.items = []interface{}{"i0", "i1", "i2"}
	c.keys = []string{"k0", "k1"}
	c.index = map[string]int{"k0": 0, "k1": 1, "k2": 2}

	sub := c.RangeByIndex(0, 10) // end 会裁剪到 n=2
	if got, want := sub.Keys(), []string{"k0", "k1"}; !reflect.DeepEqual(got, want) {
		t.Fatalf("RangeByIndex keys = %#v, want %#v", got, want)
	}
	if got, want := sub.Len(), 2; got != want {
		t.Fatalf("RangeByIndex len = %d, want %d", got, want)
	}
}

func TestPlanB_Clone_NoPanicOnMismatchedLengths(t *testing.T) {
	c := NewEmpty()
	c.items = []interface{}{"i0", "i1", "i2"}
	c.keys = []string{"k0", "k1"} // limit=2
	c.index = map[string]int{"k0": 0, "k1": 1, "k2": 2}

	cp := c.clone()
	if got, want := cp.Keys(), []string{"k0", "k1"}; !reflect.DeepEqual(got, want) {
		t.Fatalf("clone keys = %#v, want %#v", got, want)
	}
	if got, want := cp.Len(), 2; got != want {
		t.Fatalf("clone len = %d, want %d", got, want)
	}
}

func TestPlanB_GetByKey_DefensiveIndex(t *testing.T) {
	c := NewEmpty()
	c.items = []interface{}{"i0", "i1"}
	c.keys = []string{"k0", "k1"}
	c.index = map[string]int{"k0": 0, "k1": 1, "k2": 2} // k2 的下标越界

	if v, ok := c.GetByKey("k0"); !ok || v.(string) != "i0" {
		t.Fatalf("GetByKey k0 failed")
	}
	if _, ok := c.GetByKey("k2"); ok { // 越界应被判为不存在
		t.Fatalf("GetByKey k2 should be false due to invalid index")
	}
}
