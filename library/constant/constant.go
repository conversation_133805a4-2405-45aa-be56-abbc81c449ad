package constant

const (
	SERVICE = "service"
	// from
	FROM        = "from"
	CFROM       = "cfrom"
	NETWORK     = "network"
	CIP         = "cip"
	CTL         = "ctl"
	ACTION      = "action"
	TYPEID      = "typeid"
	CEN         = "cen"
	CTV         = "ctv"
	VERSION     = "version"
	BDVERSION   = "bd_version"
	UA          = "ua"
	UT          = "ut"
	UID         = "uid"
	CUID        = "cuid"
	PUID        = "puid"
	QUERY       = "query"
	V           = "v"
	PQ          = "pq"
	PT          = "pt"
	BDID        = "bdid"
	VOI         = "voi"
	SID         = "sid"
	UNAME       = "uname"
	UATYPE      = "uaType"
	OSBRANCH    = "osbranch"
	FV          = "fv"
	DATA        = "data"
	ST          = "st"
	LID         = "lid"
	SUGID       = "sugid"
	DERVICETIME = "device_time"
	SETPARAMS   = "set_params"
	// sst，ipad屏蔽小程序sug，解析cookie里的SST获取
	SST            = "sst"
	SUGMODE        = "sug_mode"
	PWD            = "pwd"
	MAX_RETURN_NUM = "max_return_num" // hislist最多返回条数
	SCENE          = "scene"          // cpage接口，标识请求场景
	HOT_LAUNCH     = "hot_launch"     // sug接口，表示热启动且回框

	// output-universal接口
	OFrom  = "o_from"
	HUAWEI = "1024665s"
	OPPO_G = "1030805v"
	OPPO_F = "1030805u"
	VIVO   = "1031170v"
	XIAOMI = "1029833l"

	// 请求session服务获取的uid
	SESSION_UID = "session_uid"

	//二合一相关参数
	PRERENDERE   = "prerender"
	ISID         = "isid"
	WISECSOR     = "wise_csor"
	ANTCT        = "ant_ct"
	PD           = "pd"
	PU           = "pu"
	TN           = "tn"
	CLIST        = "clist"
	EVENTTYPE    = "event_type"
	EVENTCHANNEL = "event_channel"
	RSVSUG4      = "rsv_sug4"
	SA           = "rsv_pq"
	RSGPQ        = "sa"
	OQ           = "oq"
	RQ           = "rq"
	T_SAMP       = "t_samp"
	CKSIZE       = "cksize"
	PREMODE      = "premode"
	CSMIXED      = "twoinone_csmixed"

	//双清单参数
	PNW       = "p_nw"
	PSV       = "p_sv"
	BRACHNAME = "branchname"
	APPNAME   = "appname"
	MPV       = "mpv"

	//先知搜索用户行为参数
	PREINPUT_NUM = "pre_intput_num"
	DELETE_NUM   = "delete_num"
	PRETLIST     = "pre_tlist"
	ISDELETE     = "isdelete"

	//自定义适配参数，以上参数会配适配到自定义字段
	REQPARAMS   = "____request"
	ADAPARAMS   = "____adaption"
	NETWORKTYPE = "network_type"

	IOS     = "iphone"
	Android = "android"

	// cpage-universal接口用到的
	TUWEN_PUSH      = "tuwenpush" //图文push落地页
	ERR_CPAGE       = "errcpage"  //c页面错误页
	CPAGE_TOP       = "cpagetop"  //c页面顶部框
	CPAGE_HIGHLIGHT = "highlight" //落地页划词

	// his-universal中的scene参数
	INCOGNITO = "incognito" //无痕模式下的his页

	// ups开关名称
	PersonalSwitch = "personalSwitch" // 个性化开关，1-打开个性化，0-关闭个性化
	SugStoreSet    = "sugStoreSet"    // 搜索历史开关，1-记录，0-不记录

	//手百公共参数，为适配简单搜索读取osname识别
	OSNAME = "osname"

	// 简单环境参数
	OriLid = "ori_lid"

	//对话式搜索的标记
	ChatSearchTag = "chatsearch"

	// agentSug的请求标志
	InteractiveTag = "agentsource"

	// 荣耀白牌
	RongyaoBaipai = "rongyaoBaipai"

	// sug切词飘红
	SpanUpgrade = "span_upgrade"

	// 标识畅听版流量
	Tbundle = "tbundle"

	// talos版本
	Tversion = "tversion"
	//搜索抽样sid
	HWiseSIDs = "H_WISE_SIDS"
)
