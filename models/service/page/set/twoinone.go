package set

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/sug"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

type Set_Twoinone struct {
	BasePage
}

func (this *Set_Twoinone) Execute() {
	request := this.getRequest()
	tplData := this.defaultTpl()
	// 校验参数
	set_params, ok := (*request)["set_params"]
	if !ok {
		tplData.Status = "1001"
		tplData.Msg = "request not has set_params"
		this.response(tplData)
		return
	}
	// b64解析数据失败
	decodeRet, err := base64.Deocde(set_params, 0)
	if err != nil {
		tplData.Status = "1002"
		tplData.Msg = "set_params decode fail"
		this.response(tplData)
		return
	}
	// 转json失败
	twoinoneInfos := make(map[string]string)
	err = json.Unmarshal(decodeRet, &twoinoneInfos)
	if err != nil {
		tplData.Status = "1003"
		tplData.Msg = "set_params json decode fail"
		this.response(tplData)
		return
	}
	//  必要存在字段的校验
	twoinonedegrade, ok := twoinoneInfos["twoinone"]
	if !ok {
		tplData.Status = "1004"
		tplData.Msg = "set_params lack of twoinone"
		this.response(tplData)
		return
	}
	// 值校验
	if (twoinonedegrade != "on") && (twoinonedegrade != "off") {
		tplData.Status = "1005"
		tplData.Msg = "twoinonedegrade value not illegal"
		this.response(tplData)
		return
	}
	// 校验token字段存在
	token, has_token := twoinoneInfos["token"]
	if !has_token {
		tplData.Status = "1006"
		tplData.Msg = "set_params lack of token"
		this.response(tplData)
		return
	}
	// 校验token字段值
	re_tokenstr := "##slamy##_"
	re_tokenstr += twoinonedegrade
	re_tokenstr += "_**myasl**"
	re_token := base64.Encode([]byte(re_tokenstr), 0)
	if re_token != token {
		tplData.Status = "1007"
		tplData.Msg = "token not match"
		this.response(tplData)
		return
	}

	sug.SetTwoinoneDegrade(twoinonedegrade)
	this.addNotice("twoinonedegrade_new_value", twoinonedegrade)
	this.response(tplData)
	return
}

func (this *Set_Twoinone) defaultTpl() SetResponse {
	ret := SetResponse{
		Status: "0",
		Msg:    "",
	}
	return ret
}

func (this *Set_Twoinone) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *Set_Twoinone) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Set_Twoinone{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("set-twoinone", &Set_Twoinone{})
}
