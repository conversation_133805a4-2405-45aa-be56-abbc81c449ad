package set

import (
	"fmt"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Pager interface {
	Execute()
	GetTplData() string
	GetTplByteData() []byte
}

type Page interface {
	newSelf(ctx *gdp.WebContext) (Pager, error)
}

//page层的属性尽可能全部私有 使用interface提供外部来操作与访问
type BasePage struct {
	ctx     *gdp.WebContext
	tplData []byte
}

var adapters = make(map[string]Page)

func Register(name string, adapter Page) {
	if adapter == nil {
		panic("page: Register adapter is nil")
	}
	if _, ok := adapters[name]; ok {
		panic("page: Register called twice for adapter " + name)
	}
	adapters[name] = adapter
}

func NewPager(pageName string, ctx *gdp.WebContext) (Pager, error) {
	adapter, ok := adapters[pageName]
	if !ok {
		return nil, fmt.Errorf("page: unknown adapter %q (forgotten register?)", pageName)
	}
	retPage, nErr := adapter.newSelf(ctx)
	if nErr != nil {
		return nil, fmt.Errorf("page: create page %q fail", pageName)
	}
	return retPage, nil
}

func (this *BasePage) setTplData(str string) {
	this.tplData = []byte(str)
}

func (this *BasePage) GetTplData() string {
	return string(this.tplData)
}

func (this *BasePage) setTplByteData(bytes []byte) {
	this.tplData = bytes
}

func (this *BasePage) GetTplByteData() []byte {
	return this.tplData
}

func (this *BasePage) getRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := this.ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (this *BasePage) getAdaption() *map[string]string {
	var adaption map[string]string
	if val, exists := this.ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	return &adaption
}

func (this *BasePage) getLogidStr() string {

	return this.ctx.GetLogID()
}
func (this *BasePage) addNotice(key, value string) {
	this.ctx.AddNotice(key, value)
}

func (this *BasePage) setErr() {
	this.ctx.DealSucc = false
}
