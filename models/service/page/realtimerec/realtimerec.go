package realtimerec

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type RealtimeRec struct {
	BasePage
}

func (r *RealtimeRec) Execute() {
	r.addNotice("realtime_rec", "1")

	rtRecErr := data.RealtimeRecResp{}
	request := r.getRequest()
	dataSrv := data.NewRealtimeRec(r.ctx)

	tag, ok := (*request)[constant.ChatSearchTag]

	// 判断是否来自简搜
	if !dataSrv.IsChatSearch() && (!ok || (ok && tag != "1")) {
		rtRecErr.Errno = "1001"
		rtRecErr.Errmsg = "not chatsearch"
		rtRecErr.LogID = r.ctx.GetLogID()
		r.response(rtRecErr)
		return
	}

	dataquery := (*request)["data"]
	// 没有post数据
	if dataquery == "" {
		rtRecErr.Errno = "1000"
		rtRecErr.Errmsg = "params[data] is missing"
		rtRecErr.LogID = r.ctx.GetLogID()
		r.response(rtRecErr)
		return
	}

	var reqData data.RealtimeRecRequest
	decodeErr := json.Unmarshal([]byte(dataquery), &reqData)
	// json解析失败
	if decodeErr != nil {
		rtRecErr.Errno = "1007"
		rtRecErr.Errmsg = "json decode fail"
		rtRecErr.LogID = r.ctx.GetLogID()
		r.response(rtRecErr)
		return
	}

	// 是否合法请求
	reqType := reqData.ReqType
	if !common.IsInArray(data.VaildReqType, reqType) {
		rtRecErr.Errno = "1002"
		rtRecErr.Errmsg = "invaild req_type"
		rtRecErr.LogID = r.ctx.GetLogID()
		r.response(rtRecErr)
		return
	}

	// query不可为空
	query := reqData.Query
	if query == "" {
		rtRecErr.Errno = "1003"
		rtRecErr.Errmsg = "query is empty"
		rtRecErr.LogID = r.ctx.GetLogID()
		r.response(rtRecErr)
		return
	}

	// 请求推荐服务
	err := dataSrv.GetRTRecResponse(request, &reqData)
	if err != nil {
		r.setErr()
		r.addNotice("ralErr", "1")
		r.addNotice("ralErrMsg", err.Error())
		rtRecErr.Errno = "1004"
		rtRecErr.Errmsg = "ral quest fail"
		rtRecErr.LogID = r.ctx.GetLogID()
		r.response(rtRecErr)
		return
	}
	// 返回结果
	r.response(dataSrv.RecTplData)
}

func (r *RealtimeRec) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	r.setTplByteData(tplByte)
}

func (r *RealtimeRec) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &RealtimeRec{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("realtime-rec", &RealtimeRec{})
}
