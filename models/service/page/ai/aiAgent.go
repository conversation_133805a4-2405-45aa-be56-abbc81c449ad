package ai

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type Agent struct {
	BasePage
}

func (r *Agent) Execute() {
	aiSugErr := data.AgentSugResp{}
	request := r.getRequest()
	dataSrv := data.NewAIAgent(r.ctx)

	query := (*request)[constant.QUERY]
	from := (*request)[constant.InteractiveTag]

	// 请求agent推荐服务
	err := dataSrv.GetAgentSugResponse(query, from)
	if err != nil {
		r.setErr()
		r.addNotice("ralErr", "1")
		r.addNotice("ralErrMsg", err.Error())
		aiSugErr.Errno = "1001"
		aiSugErr.Errmsg = "ral request err"
		aiSugErr.LogID = r.ctx.GetLogID()
		r.response(aiSugErr)
		return
	}
	// 返回结果
	r.response(dataSrv.TplData)
}

func (r *Agent) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	r.setTplByteData(tplByte)
}

func (r *Agent) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Agent{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("ai-agent", &Agent{})
}
