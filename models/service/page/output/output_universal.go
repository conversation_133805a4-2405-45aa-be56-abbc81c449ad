package output

import (
	"encoding/json"
	"errors"
	"sort"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"

	"icode.baidu.com/baidu/gdp/gdp"
)

type OutputUniversal struct {
	BasePage
}

type UniversalParams struct {
	Target string `json:"target"`
}
type Response struct {
	Errno     string      `json:"errno"`
	Errmsg    string      `json:"errmsg"`
	Requestid string      `json:"requestid"`
	Data      interface{} `json:"data"`
}

type dataQuery struct {
	Target string `json:"target"`
}

func (o *OutputUniversal) Execute() {
	request := o.getRequest()
	// 需要访问的下游服务
	var targetArr []string

	if (*request)[constant.OFrom] == constant.HUAWEI ||
		(*request)[constant.OFrom] == constant.OPPO_G ||
		(*request)[constant.OFrom] == constant.OPPO_F ||
		(*request)[constant.OFrom] == constant.VIVO ||
		(*request)[constant.OFrom] == constant.XIAOMI {

		targetArr = []string{"hot"}
	}

	if len(targetArr) == 0 {
		o.handleErr("-2000", "o_from err")
		return
	}

	universalTplData := make(map[string]interface{})
	backendErr := false

	// 保证遍历的顺序固定，方便监控日志
	sort.Strings(targetArr)

	for _, backend := range targetArr {
		v, err := o.getTargetData(backend)
		if err != nil {
			o.addNotice(backend+"_pageSrcErr", err.Error())
			backendErr = true
			continue
		}

		// 数据有可能返回空，需要判空。错误码为0时，表示正常返回了数据
		if v.Errno == "0" {
			universalTplData[backend] = v.Data
		}
	}

	// 请求后端错误，并且都没结果时，返回错误
	if backendErr && len(universalTplData) == 0 {
		o.handleErr("-2001", "server error")
		return
	}

	tplData := o.defaultTpl()
	tplData.Data = universalTplData

	o.response(tplData)
	return
}

func (o *OutputUniversal) getTargetData(nodeName string) (OutputResponse, error) {
	pageName := "output" + "-" + nodeName
	pageSrv, err := NewPager(pageName, o.ctx)
	if err != nil {
		return OutputResponse{}, err
	}

	pageSrv.Execute()
	o.addNotice(pageName, "1")

	// target指定的后端执行错误
	v, ok := pageSrv.GetTplOriginData().(OutputResponse)
	if !ok {
		return OutputResponse{}, errors.New("GetTplOriginData err")
	}

	if v.Errno != "0" {
		return OutputResponse{}, errors.New(v.Errmsg)
	}

	return v, nil
}

func (o *OutputUniversal) handleErr(errno string, msg string) {
	res := Response{
		Errno:     errno,
		Errmsg:    msg,
		Requestid: o.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	o.setErr()
	o.response(res)
}

func (o *OutputUniversal) defaultTpl() Response {
	ret := Response{
		Errno:     "0",
		Errmsg:    "",
		Requestid: o.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	return ret
}

func (o *OutputUniversal) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	o.setTplByteData(tplByte)
}

func (o *OutputUniversal) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &OutputUniversal{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("output-universal", &OutputUniversal{})
}
