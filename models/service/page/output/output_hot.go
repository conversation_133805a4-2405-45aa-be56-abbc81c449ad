package output

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type OutputHot struct {
	BasePage
}

func (h *OutputHot) Execute() {
	tplData := h.defaultTpl()

	dataSrv := data.NewHot(h.ctx)
	err := dataSrv.GetHotResponse()
	if err != nil {
		h.setErr()
		h.addNotice("errMsg", err.Error())
		tplData.Errno = "-1001"
		tplData.Errmsg = "get data fail"
		tplData.Requestid = h.getLogidStr()
		h.response(tplData)
		return
	}

	resData := dataSrv.GetHISTplData()
	tpl := h.parseResData(resData, tplData)

	h.response(tpl)
	return
}

func (h *OutputHot) parseResData(resData map[string]interface{}, tpl OutputResponse) OutputResponse {
	tpl.Data = resData
	return tpl
}

func (h *OutputHot) defaultTpl() OutputResponse {
	datainfo := []interface{}{}
	ret := OutputResponse{
		Errno:     "0",
		Errmsg:    "",
		Data:      datainfo,
		Requestid: h.getLogidStr(),
	}
	return ret
}

func (h *OutputHot) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	h.setTplOriginalData(tpl)
	h.setTplByteData(tplByte)
}

func (h *OutputHot) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &OutputHot{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("output-hot", &OutputHot{})
}
