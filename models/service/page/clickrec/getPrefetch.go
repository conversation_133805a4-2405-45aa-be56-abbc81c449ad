package clickrec

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type ClickGetPrefetch struct {
	BasePage
}

type GetPrefetchResponse struct {
	Errno     string      `json:"errno"`
	Errmsg    string      `json:"errmsg"`
	RequestID string      `json:"requestid"`
	Data      interface{} `json:"data"`
}

// Execute 执行的函数入口
func (c *ClickGetPrefetch) Execute() {
	// 发出请求，获得返回的结果
	request := c.getRequest()
	// 请求创维获取展现数据
	dataMegafoilSrv := data.NewMegafoil(c.ctx)
	mp, ralMegafoilErr := dataMegafoilSrv.GetMegafoilResponse(request)
	if ralMegafoilErr != nil {
		c.handleErr("1001", ralMegafoilErr.Error())
		return
	}
	// 请求策略获取推荐数据
	dataGetRecSrv := data.NewClickGetRec(c.ctx)
	ralGetRecErr := dataGetRecSrv.GetPrefetchRecResponse(mp, dataMegafoilSrv.GetTplData())
	if ralGetRecErr != nil {
		c.handleErr("1002", ralGetRecErr.Error())
		return
	}
	tplData := c.defaultTpl()
	// 解析结果
	tplData.Data = dataGetRecSrv.GetTplData()
	// 结果返回给端
	c.response(tplData)
}

// 处理错误，返回错误信息
func (c *ClickGetPrefetch) handleErr(errno string, msg string) {
	res := GetPrefetchResponse{
		Errno:     errno,
		Errmsg:    msg,
		RequestID: c.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	c.setErr()
	c.response(res)
}

// 初始化对端协议的结构
func (c *ClickGetPrefetch) defaultTpl() GetPrefetchResponse {
	ret := GetPrefetchResponse{
		Errno:     "0",
		Errmsg:    "",
		RequestID: c.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	return ret
}

func (c *ClickGetPrefetch) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	c.setTplByteData(tplByte)
}

func (c *ClickGetPrefetch) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &ClickGetPrefetch{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("click_rec-get_prefetch", &ClickGetPrefetch{})
}
