package clickrec

import (
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type ClickGetRecData struct {
	BasePage
}

type ClickGetRecResponse struct {
	Errno     string `json:"errno"`
	Errmsg    string `json:"errmsg"`
	RequestID string `json:"requestid"`
	Data      string `json:"data"`
}

type ClickGetRecParams struct {
	Plid    string `json:"plid"`
	SubPos  string `json:"subpos"`
	CardPos string `json:"card_pos"`
}

// Execute 执行的函数入口
func (c *ClickGetRecData) Execute() {
	// 发出请求，获得返回的结果
	request := c.getRequest()
	cp, err := c.getReqParams(request)
	if err != nil {
		c.handleErr("1001", err.Error())
		return
	}
	recData, err1 := c.getRecFromRedis(cp)
	if err1 != nil {
		c.handleErr("1002", err1.Error())
		return
	}
	// 组装返回数据
	tplData := ClickGetRecResponse{
		Errno:     "0",
		Errmsg:    "",
		RequestID: c.getLogidStr(),
		Data:      recData,
	}
	// 结果返回给端
	c.response(tplData)
}

// 处理错误，返回错误信息
func (c *ClickGetRecData) handleErr(errno string, msg string) {
	res := ClickGetRecResponse{
		Errno:     errno,
		Errmsg:    msg,
		RequestID: c.getLogidStr(),
		Data:      "",
	}

	c.setErr()
	c.response(res)
}

func (c *ClickGetRecData) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	c.setTplByteData(tplByte)
}

func (c *ClickGetRecData) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &ClickGetRecData{}
	target.ctx = ctx
	return target, nil
}

func (c *ClickGetRecData) getReqParams(reqParams *map[string]string) (*ClickGetRecParams, error) {
	cp := ClickGetRecParams{}
	reqData := (*reqParams)["data"]
	if reqData == "" {
		return &cp, errors.New("reqData is nil")
	}
	jsonErr := json.Unmarshal([]byte(reqData), &cp)
	if jsonErr != nil {
		return &cp, errors.New("getReqParams json Unmarshal fail")
	}
	return &cp, nil
}

func (c *ClickGetRecData) getRecFromRedis(cp *ClickGetRecParams) (string, error) {
	client := common.RedisClient
	if client == nil {
		return "", errors.New("redis client is nil")
	}
	// 获取redis key
	curKey, keyErr := common.GetClickRecKey(cp.Plid, cp.CardPos, cp.SubPos)
	if keyErr != nil {
		return "", errors.New("get click rec key error: " + keyErr.Error())
	}
	// 获取redis数据
	val, clientErr := client.Get(c.ctx.StdContext(), curKey).Result()
	if clientErr != nil {
		return "", errors.New("getRecFromRedis error: " + clientErr.Error())
	}
	if val == "" {
		return "", errors.New("getRecFromRedis data empty")
	}
	return val, nil
}

func init() {
	Register("click_rec-get_rec", &ClickGetRecData{})
}
