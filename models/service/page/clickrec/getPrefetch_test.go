// nolint
package clickrec

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestClickGetPrefetch_Execute(t *testing.T) {
	// Setup
	reqParams := &map[string]string{
		"data": `{
			"word": "test",
			"plid": "123",
			"cuid": "456",
			"user_id": 789,
			"subpos": "0",
			"card_pos": "1",
			"abstract": "test abstract"
		}`,
	}
	ctx := createGetWebContext()
	ctx.Set(constant.REQPARAMS, *reqParams)

	page := &ClickGetPrefetch{
		BasePage: BasePage{ctx: ctx},
	}

	patches := gomonkey.NewPatches()
	defer patches.Reset()
	m := &data.Megafoil{}
	mp := &data.MegafoilParams{
		Word:     "test",
		Plid:     "123",
		CUID:     "456",
		SubPos:   "1",
		CardPos:  "1",
		Abstract: "test abstract",
	}

	// test GetMegafoilResponse error
	errno := "1001"
	patches.ApplyMethodReturn(m, "GetMegafoilResponse", mp, errors.New("test GetMegafoilResponse error"))
	page.Execute()

	tplData := page.GetTplData()
	var res GetPrefetchResponse
	if err := json.Unmarshal([]byte(tplData), &res); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}
	if res.Errno != errno {
		t.Errorf("handleErr() Errno = %v, want %v", res.Errno, errno)
	}

	// test GetPrefetchRecResponse error
	patches.ApplyMethodReturn(m, "GetMegafoilResponse", mp, nil)
	c := &data.ClickGetRec{}
	errno1 := "1002"
	patches.ApplyMethodReturn(c, "GetPrefetchRecResponse", errors.New("test GetPrefetchRecResponse error"))
	page.Execute()

	tplData = page.GetTplData()
	var res1 GetPrefetchResponse
	if err := json.Unmarshal([]byte(tplData), &res); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}
	if res.Errno != errno1 {
		t.Errorf("handleErr() Errno = %v, want %v", res1.Errno, errno1)
	}

	// test ok
	patches.ApplyMethodReturn(c, "GetPrefetchRecResponse", nil)
	page.Execute()

	tplData = page.GetTplData()
	var res2 GetPrefetchResponse
	if err := json.Unmarshal([]byte(tplData), &res2); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}
	if res2.Errno != "0" {
		t.Errorf("handleErr() Errno = %v, want %v", res2.Errno, "0")
	}
}

func TestClickGetPrefetch_newSelf(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetPrefetch{}

	// Execute
	newPage, err := page.newSelf(ctx)

	// Verify
	if err != nil {
		t.Errorf("newSelf returned error: %v", err)
	}

	if newPage == nil {
		t.Error("newSelf returned nil page")
	}

	if c, ok := newPage.(*ClickGetPrefetch); !ok {
		t.Error("newSelf did not return ClickGetPrefetch instance")
	} else if c.ctx != ctx {
		t.Error("newSelf did not set context correctly")
	}
}

func TestClickGetPrefetch_response(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetPrefetch{
		BasePage: BasePage{ctx: ctx},
	}
	testData := struct {
		Field1 string `json:"field1"`
		Field2 int    `json:"field2"`
	}{
		Field1: "test",
		Field2: 123,
	}

	// Execute
	page.response(testData)

	// Verify
	if page.tplData == nil {
		t.Error("response did not set tplByteData")
	}

	var result struct {
		Field1 string `json:"field1"`
		Field2 int    `json:"field2"`
	}
	err := json.Unmarshal(page.tplData, &result)
	if err != nil {
		t.Errorf("failed to unmarshal tplByteData: %v", err)
	}

	if result.Field1 != testData.Field1 || result.Field2 != testData.Field2 {
		t.Error("response did not marshal data correctly")
	}
}

func TestClickGetPrefetch_defaultTpl(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetPrefetch{
		BasePage: BasePage{ctx: ctx},
	}

	// Execute
	result := page.defaultTpl()

	// Verify
	if result.Errno != "0" {
		t.Errorf("defaultTpl() Errno = %v, want %v", result.Errno, "0")
	}

	if result.Errmsg != "" {
		t.Errorf("defaultTpl() Errmsg = %v, want empty string", result.Errmsg)
	}

	if result.RequestID != page.getLogidStr() {
		t.Errorf("defaultTpl() RequestID = %v, want %v", result.RequestID, page.getLogidStr())
	}

	if _, ok := result.Data.(map[string]interface{}); !ok {
		t.Error("defaultTpl() Data is not of type map[string]interface{}")
	}
}

func TestClickGetPrefetch_handleErr(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetPrefetch{
		BasePage: BasePage{ctx: ctx},
	}

	// Test data
	errno := "500"
	msg := "internal server error"

	// Execute
	page.handleErr(errno, msg)

	// Verify
	tplData := page.GetTplData()
	if len(tplData) == 0 {
		t.Fatal("handleErr() did not set template data")
	}

	var res GetPrefetchResponse
	if err := json.Unmarshal([]byte(tplData), &res); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}

	if res.Errno != errno {
		t.Errorf("handleErr() Errno = %v, want %v", res.Errno, errno)
	}

	if res.Errmsg != msg {
		t.Errorf("handleErr() Errmsg = %v, want %v", res.Errmsg, msg)
	}

	if res.RequestID != page.getLogidStr() {
		t.Errorf("handleErr() RequestID = %v, want %v", res.RequestID, page.getLogidStr())
	}

	if _, ok := res.Data.(map[string]interface{}); !ok {
		t.Error("handleErr() Data is not of type map[string]interface{}")
	}

	if page.ctx.DealSucc {
		t.Error("handleErr() did not set error flag")
	}
}
