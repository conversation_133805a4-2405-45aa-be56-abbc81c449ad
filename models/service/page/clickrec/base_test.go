// nolint
package clickrec

import (
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestRegister(t *testing.T) {
	type args struct {
		name    string
		adapter Page
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Register ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	// ctx := createGetWebContext()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"TestRegister",
			args{
				"click_rec-test",
				&ClickGetRecData{},
			},
		},
	}

	for _, tt := range tests {
		Register(tt.args.name, tt.args.adapter)
	}
	ag.Run()
}

func TestNewPager(t *testing.T) {
	type args struct {
		pageName string
		ctx      *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewPager ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	target := &ClickGetPrefetch{}
	target.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		{
			"TestNewPager",
			args{
				"click_rec-get_prefetch",
				ctx,
			},
			Want{
				target,
				ut.ShouldResemble,
			},
			false,
		},
		{
			"TestNewPager",
			args{
				"click_rec-get_prefetch11",
				ctx,
			},
			Want{
				target,
				ut.ShouldResemble,
			},
			true,
		},
	}

	for k, tt := range tests {
		got, err := NewPager(tt.args.pageName, tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestNewPager, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewPager, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}

func TestBasePage_setTplData(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}
	type args struct {
		str string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	ctx := createGetWebContext()
	bytetpl := []byte("test")
	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		{
			"TestBasePage_setTplData",
			fields{
				ctx,
				bytetpl,
			},
			args{
				"test",
			},
		},
	}

	for _, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		this.setTplData(tt.args.str)
	}
	ag.Run()
}

func TestBasePage_GetTplData(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //string
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		got := this.GetTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBasePage_GetTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestBasePage_setTplByteData(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}
	type args struct {
		bytes []byte
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setTplByteData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		this.setTplByteData(tt.args.bytes)
	}
	ag.Run()
}

func TestBasePage_GetTplByteData(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetTplByteData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //[]byte
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		got := this.GetTplByteData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBasePage_GetTplByteData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.([]byte))
	}
	ag.Run()
}

func TestBasePage_getRequest(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	requestParams := &(map[string]string{})
	(*requestParams)["network"] = "0"
	(*requestParams)["typeid"] = "1"
	ctx.Set(constant.REQPARAMS, *requestParams)

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //*middle.RequestParams
	}{
		{
			"TestBasePage_getRequest",
			fields{
				ctx,
				[]byte("test"),
			},
			Want{
				requestParams,
				ut.ShouldResemble,
			},
		},
	}
	for k, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		got := this.getRequest()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBasePage_getRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*map[string]string))
	}
	ag.Run()
}

func TestBasePage_getLogidStr(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getLogidStr ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	ctx := createGetWebContext()
	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //string
	}{
		{
			"TestBasePage_getLogidStr",
			fields{
				ctx,
				[]byte("test"),
			},
			Want{
				"1",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		got := this.getLogidStr()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBasePage_getLogidStr, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestBasePage_addNotice(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}
	type args struct {
		key   string
		value string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: addNotice ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		this.addNotice(tt.args.key, tt.args.value)
	}
	ag.Run()
}

func TestBasePage_setErr(t *testing.T) {
	type fields struct {
		ctx     *gdp.WebContext
		tplData []byte
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setErr ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &BasePage{
			ctx:     tt.fields.ctx,
			tplData: tt.fields.tplData,
		}
		this.setErr()
	}
	ag.Run()
}

var (
	httpRspBody1 *httptest.ResponseRecorder
)

func createGetWebContext() *gdp.WebContext {
	httpRspBody1 = httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	//构造Request                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        //获取Gin提供的Context，WebContext包括gin.Context
	req, _ := http.NewRequest("GET", `/suggest?query=be&cfrom=1099a&from=1099a&network=1_0&osbranch=i0&osname=baiduboxapp&puid=_a2K8jun28e6A&service=bdbox&ua=750_1334_iphone_11.3.6.10_0&uid=D21DBA569DE01513BC23847AF4C5A42E79D82E372OHFAOKEQMR&ut=iPhone8%2C1_11.3&zid=Y-KKDc8Yxi1YpH87_ea1q4kNX-dP0ZqQV_fDqPEQ1L6KtISkxgbT03ZhuHzRGtj5sqai5JIOURiBbj_no65zMWg&bdid=E7101D946E3509D10F03306AC256BAF4%3AFG%3D1&wise_csor=2&v=3`, nil)

	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "*******"),
	}
	return wc
}
