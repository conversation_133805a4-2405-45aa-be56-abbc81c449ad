// nolint
package clickrec

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestGetReqParams(t *testing.T) {
	tests := []struct {
		name      string
		reqParams *map[string]string
		want      *ClickGetRecParams
		wantErr   bool
		errMsg    string
	}{
		{
			name: "success case",
			reqParams: &map[string]string{
				"data": `{"plid":"test_plid","subpos":"test_subpos","card_pos":"test_card_pos"}`,
			},
			want: &ClickGetRecParams{
				Plid:    "test_plid",
				SubPos:  "test_subpos",
				CardPos: "test_card_pos",
			},
			wantErr: false,
		},
		{
			name: "empty data",
			reqParams: &map[string]string{
				"data": "",
			},
			want:    &ClickGetRecParams{},
			wantErr: true,
			errMsg:  "reqData is nil",
		},
		{
			name: "invalid json",
			reqParams: &map[string]string{
				"data": `{"plid":"test_plid","subpos":"test_subpos","card_pos":"test_card_pos"`,
			},
			want:    &ClickGetRecParams{},
			wantErr: true,
			errMsg:  "getReqParams json Unmarshal fail",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ClickGetRecData{}
			got, err := c.getReqParams(tt.reqParams)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestNewSelf(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetRecData{}

	// Execute
	newPage, err := page.newSelf(ctx)

	// Verify
	if err != nil {
		t.Errorf("newSelf returned error: %v", err)
	}

	if newPage == nil {
		t.Error("newSelf returned nil page")
	}

	if c, ok := newPage.(*ClickGetRecData); !ok {
		t.Error("newSelf did not return ClickGetRecData instance")
	} else if c.ctx != ctx {
		t.Error("newSelf did not set context correctly")
	}
}

func TestClickGetRecData_response(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetRecData{
		BasePage: BasePage{ctx: ctx},
	}
	testData := map[string]interface{}{
		"key": "value",
	}

	// Execute
	page.response(testData)

	// Verify
	if page.tplData == nil {
		t.Error("response did not set tplByteData")
	}

	var result map[string]interface{}
	err := json.Unmarshal(page.tplData, &result)
	if err != nil {
		t.Errorf("failed to unmarshal tplByteData: %v", err)
	}

	if result["key"] != "value" {
		t.Error("response did not marshal data correctly")
	}
}

func TestClickGetRecData_handleErr(t *testing.T) {
	// Setup
	ctx := createGetWebContext()
	page := &ClickGetRecData{
		BasePage: BasePage{ctx: ctx},
	}
	errno := "500"
	msg := "internal server error"

	// Execute
	page.handleErr(errno, msg)

	// Verify
	if page.tplData == nil {
		t.Error("handleErr did not set tplByteData")
	}

	var result ClickGetRecResponse
	err := json.Unmarshal(page.tplData, &result)
	if err != nil {
		t.Errorf("failed to unmarshal tplByteData: %v", err)
	}

	if result.Errno != errno {
		t.Errorf("expected errno %s, got %s", errno, result.Errno)
	}

	if result.Errmsg != msg {
		t.Errorf("expected errmsg %s, got %s", msg, result.Errmsg)
	}

	if result.RequestID == "" {
		t.Error("requestid should not be empty")
	}

	if result.Data != "" {
		t.Error("data should be empty")
	}

	if page.ctx.DealSucc {
		t.Error("isErr should be true")
	}
}

func TestGetRecFromRedis(t *testing.T) {
	ctx := createGetWebContext()
	page := &ClickGetRecData{
		BasePage: BasePage{ctx: ctx},
	}
	cp := &ClickGetRecParams{
		Plid:    "",
		SubPos:  "test_subpos",
		CardPos: "test_card_pos",
	}
	// redis client is nil
	_, err := page.getRecFromRedis(cp)
	assert.Equal(t, "redis client is nil", err.Error())

	// get click rec key error
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()
	common.RedisClient = testRedis.Client()

	_, err1 := page.getRecFromRedis(cp)
	assert.Equal(t, "get click rec key error: GetClickRecKey fail: qid为空", err1.Error())

	// redis key不存在
	cp.Plid = "test_plid"
	_, err2 := page.getRecFromRedis(cp)
	assert.Equal(t, "getRecFromRedis error: redis: nil", err2.Error())

	// mock数据先存一份
	mockKey := "test_plid_test_card_pos_test_subpos"
	mockVal := "test"
	_, clientErr := common.RedisClient.Set(ctx, mockKey, mockVal).Result()
	assert.NoError(t, clientErr)

	// 获取redis数据
	got, err3 := page.getRecFromRedis(cp)
	assert.Equal(t, "test", got)
	assert.NoError(t, err3)
}

func TestClickGetRecData_Execute(t *testing.T) {
	// Setup
	reqParams := &map[string]string{
		"data": "",
	}
	ctx := createGetWebContext()
	ctx.Set(constant.REQPARAMS, *reqParams)

	page := &ClickGetRecData{
		BasePage: BasePage{ctx: ctx},
	}

	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()
	common.RedisClient = testRedis.Client()

	// test getReqParams error
	errno := "1001"
	page.Execute()

	tplData := page.GetTplData()
	var res ClickGetRecResponse
	if err := json.Unmarshal([]byte(tplData), &res); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}
	if res.Errno != errno {
		t.Errorf("handleErr() Errno = %v, want %v", res.Errno, errno)
	}

	// test getRecFromRedis error
	(*reqParams)["data"] = `{"plid":"test_plid","subpos":"test_subpos","card_pos":"test_card_pos"}`
	ctx.Set(constant.REQPARAMS, *reqParams)

	errno1 := "1002"
	page.Execute()

	tplData = page.GetTplData()
	var res1 ClickGetRecResponse
	if err := json.Unmarshal([]byte(tplData), &res); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}
	if res.Errno != errno1 {
		t.Errorf("handleErr() Errno = %v, want %v", res1.Errno, errno1)
	}

	// mock数据先存一份
	mockKey := "test_plid_test_card_pos_test_subpos"
	mockVal := "test"
	_, clientErr := common.RedisClient.Set(ctx, mockKey, mockVal).Result()
	assert.NoError(t, clientErr)

	// test ok
	page.Execute()

	tplData = page.GetTplData()
	var res2 ClickGetRecResponse
	if err := json.Unmarshal([]byte(tplData), &res2); err != nil {
		t.Fatalf("handleErr() returned invalid JSON: %v", err)
	}
	if res2.Errno != "0" {
		t.Errorf("handleErr() Errno = %v, want %v", res2.Errno, "0")
	}
}
