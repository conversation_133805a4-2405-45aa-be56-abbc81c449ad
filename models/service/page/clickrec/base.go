// Package clickrec base
package clickrec

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Pager interface {
	Execute()
	GetTplData() string
}

type Page interface {
	newSelf(ctx *gdp.WebContext) (Pager, error)
}

type BasePage struct {
	ctx     *gdp.WebContext
	tplData []byte
}

var adapters = make(map[string]Page)

func Register(name string, adapter Page) {
	if adapter == nil {
		panic("page: Register adapter is nil")
	}
	if _, ok := adapters[name]; ok {
		panic("page: Register called twice for adapter " + name)
	}
	adapters[name] = adapter
}

func NewPager(pageName string, ctx *gdp.WebContext) (Pager, error) {
	adapter, ok := adapters[pageName]
	if !ok {
		return nil, fmt.Errorf("page: unknown adapter %q (forgotten register?)", pageName)
	}
	retPage, err := adapter.newSelf(ctx)
	if err != nil {
		return nil, fmt.Errorf("page: create page %q fail", pageName)
	}
	return retPage, nil
}

func (bp *BasePage) setTplData(str string) {
	bp.tplData = []byte(str)
}

func (bp *BasePage) GetTplData() string {
	return string(bp.tplData)
}

func (bp *BasePage) setTplByteData(bytes []byte) {
	bp.tplData = bytes
	common.DumpData(bp.ctx, "2shoubai", bytes)
}

func (bp *BasePage) GetTplByteData() []byte {
	return bp.tplData
}

func (bp *BasePage) getRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := bp.ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (bp *BasePage) getLogidStr() string {
	return bp.ctx.GetLogID()
}

// addNotice
func (bp *BasePage) addNotice(key, value string) {
	// 在Notice层面打点
	bp.ctx.AddNotice(key, value)
}

// setErr 用于index.log标记错误
func (bp *BasePage) setErr() {
	bp.ctx.DealSucc = false
}
