package his

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestHis_Ispri_Execute(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Execute ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
	}{
		{
			"TestHis_Ispri_Execute",
			fields{
				basePage,
			},
		},
	}

	for _, tt := range tests {
		this := &His_Ispri{
			BasePage: tt.fields.BasePage,
		}
		this.Execute()
	}
	ag.Run()
}

func TestHis_Ispri_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}

	datainfo := []interface{}{}
	switchData := map[string]interface{}(nil)
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: switchData,
	}
	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisResponse
	}{
		{
			"TestHis_Ispri_defaultTpl",
			fields{
				basePage,
			},
			Want{
				ret,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		this := &His_Ispri{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Ispri_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_Ispri_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &His_Ispri{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestHis_Ispri_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_Ispri{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Ispri_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Ispri_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
