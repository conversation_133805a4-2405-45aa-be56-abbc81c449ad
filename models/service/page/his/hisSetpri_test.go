package his

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestHis_Setpri_Execute(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Execute ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	requestParams := &(map[string]string{})
	(*requestParams)["data"] = `{"status":"1","timestamp":"1554967115"}`
	(*requestParams)["uid"] = "1"
	ctx.Set(constant.REQPARAMS, *requestParams)

	ctx1 := createGetWebContext()
	basePage1 := BasePage{
		ctx: ctx1,
	}

	ctx2 := createGetWebContext()
	basePage2 := BasePage{
		ctx: ctx2,
	}
	requestParams2 := &(map[string]string{})
	(*requestParams2)["data"] = `{"status":"","timestamp":"1554967115"}`
	(*requestParams2)["uid"] = "1"
	ctx2.Set(constant.REQPARAMS, *requestParams2)

	tests := []struct {
		testCaseTitle string
		fields        fields
	}{
		{
			"TestHis_Setpri_Execute",
			fields{
				basePage,
			},
		},
		{
			"TestHis_Setpri_Execute",
			fields{
				basePage1,
			},
		},
		{
			"TestHis_Setpri_Execute",
			fields{
				basePage2,
			},
		},
	}

	for _, tt := range tests {
		this := &His_Setpri{
			BasePage: tt.fields.BasePage,
		}
		this.Execute()
	}
	ag.Run()
}

func TestHis_Setpri_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_Setpri{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Setpri_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_Setpri_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &His_Setpri{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestHis_Setpri_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_Setpri{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Setpri_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Setpri_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
