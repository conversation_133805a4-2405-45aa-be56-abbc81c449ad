package his

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_ad struct {
	BasePage
	adData *data.AFD
}

func (h *His_ad) Execute() {
	requestFunc, parseFunc := h.ExecuteByStep()
	requestFunc()
	parseFunc()
}

// 实现 ExecuteByStep 接口
func (h *His_ad) ExecuteByStep() (func(), func()) {
	// 请求函数
	requestFunc := func() {
		request := h.getRequest()
		query := (*request)["query"]
		adaption := h.getAdaption()
		// 只需读取userPrivacyInfo参数
		userPrivacyInfoMap := new(common.UserPrivacyInfo)
		userPrivacyInfo, ok := h.ctx.Get("userPrivacyInfo")
		if ok {
			userPrivacyInfoMap, _ = userPrivacyInfo.(*common.UserPrivacyInfo)
		} else {
			h.addNotice("ad_request_error", "userPrivacyInfo not found")
		}
		h.adData = data.NewAFD(h.ctx)
		err := h.adData.GetAFDResponse(query, *request, *adaption, *userPrivacyInfoMap)
		if err != nil {
			h.addNotice("ad_request_error", err.Error())
		}

	}

	// 解析响应函数
	parseFunc := func() {
		if h.adData == nil {
			errResp := HisListErr{
				Errno:     "-1",
				Errmsg:    "ad request not initialized",
				Requestid: h.getLogidStr(),
				Switch:    common.SwitchConfMem.Switch,
			}
			h.setTplOriginalData(errResp)
		} else {
			// 直接设置原始ad数据
			h.setTplOriginalData(h.adData.TplData)
		}
	}

	return requestFunc, parseFunc
}

func (h *His_ad) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_ad{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-ad", &His_ad{})
}
