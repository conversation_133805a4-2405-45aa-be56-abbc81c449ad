package his

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	version "github.com/mcuadros/go-version"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Board struct {
	BasePage
}

// 主要执行的函数入口
func (h *His_Board) Execute() {
	tplData := h.defaultTpl()
	hisListErr := HisListErr{}
	request := h.getRequest()
	dataSrv := data.NewBoardRec(h.ctx)
	uid := (*request)[constant.SESSION_UID]
	adaptions := h.getAdaption()
	ralGetErr := dataSrv.GetResponse(request, adaptions, uid)
	if ralGetErr != nil {
		// 无痕模式热榜，不支持大字版&极速版，端上错误传参，不计入服务端错误
		if (*request)[constant.SCENE] == constant.INCOGNITO &&
			(common.GetAppBranch((*request)[constant.OSBRANCH]) == common.BIG_APP || common.GetAppBranch((*request)[constant.OSBRANCH]) == common.LITE_APP) {
			h.addNotice("incognito_board_fail", (*request)[constant.OSBRANCH])
		} else {
			h.setErr()
		}

		h.addNotice("ralErr", "1")
		h.addNotice("ralErrMsg", ralGetErr.Error())
		hisListErr.Errno = "-4002"
		hisListErr.Errmsg = "ral request fail"
		hisListErr.Requestid = h.getLogidStr()
		h.response(hisListErr)
		return
	}

	resData := dataSrv.GetHISTplData()
	boardChannel := dataSrv.GetBoardChannel()
	tpl, parseErr := h.parseRALResData(resData, tplData, boardChannel)
	if parseErr != nil {
		h.setErr()
		h.addNotice("parseErr", parseErr.Error())
		hisListErr.Errno = tpl.Errno
		hisListErr.Errmsg = tpl.Errmsg
		hisListErr.Requestid = h.getLogidStr()
		h.response(hisListErr)
		return
	}

	h.response(tpl)
	return
}

// 这里是为了把请求的调用权转移到函数外以便于并发
func (h *His_Board) ExecuteByStep() (multiRequest func(), parseResponse func()) {
	tplData := h.defaultTpl()
	hisListErr := HisListErr{}
	request := h.getRequest()
	dataSrv := data.NewBoardRec(h.ctx)
	uid := (*request)[constant.SESSION_UID]
	adaptions := h.getAdaption()

	requests, err := dataSrv.BuildRequest(request, adaptions, uid)
	multiResponseChan := make(chan data.ChRawHTTPResp, len(requests))
	if err != nil {

		// 无痕模式热榜，不支持大字版&极速版，端上错误传参，不计入服务端错误
		if (*request)[constant.SCENE] == constant.INCOGNITO &&
			(common.GetAppBranch((*request)[constant.OSBRANCH]) == common.BIG_APP || common.GetAppBranch((*request)[constant.OSBRANCH]) == common.LITE_APP) {
			h.addNotice("incognito_board_fail", (*request)[constant.OSBRANCH])
		} else {
			h.setErr()
		}

		h.addNotice("ralErr", "1")
		h.addNotice("ralErrMsg", err.Error())
		hisListErr.Errno = "-4002"
		hisListErr.Errmsg = "ral request fail"
		hisListErr.Requestid = h.getLogidStr()
		h.response(hisListErr)
		return nil, nil

	}

	return func() {
			dataSrv.MultiRequest(requests, multiResponseChan)
		}, func() {
			err := dataSrv.ParseResponse(multiResponseChan)
			if err != nil {
				// 无痕模式热榜，不支持大字版&极速版，端上错误传参，不计入服务端错误
				if (*request)[constant.SCENE] == constant.INCOGNITO &&
					(common.GetAppBranch((*request)[constant.OSBRANCH]) == common.BIG_APP || common.GetAppBranch((*request)[constant.OSBRANCH]) == common.LITE_APP) {
					h.addNotice("incognito_board_fail", (*request)[constant.OSBRANCH])
				} else {
					h.setErr()
				}

				h.addNotice("ralErr", "1")
				h.addNotice("ralErrMsg", err.Error())
				hisListErr.Errno = "-4002"
				hisListErr.Errmsg = "ral request fail"
				hisListErr.Requestid = h.getLogidStr()
				h.response(hisListErr)
				return
			}
			resData := dataSrv.GetHISTplData()
			boardChannel := dataSrv.GetBoardChannel()
			tpl, parseErr := h.parseRALResData(resData, tplData, boardChannel)
			if parseErr != nil {
				h.setErr()
				h.addNotice("parseErr", parseErr.Error())
				hisListErr.Errno = tpl.Errno
				hisListErr.Errmsg = tpl.Errmsg
				hisListErr.Requestid = h.getLogidStr()
				h.response(hisListErr)
				return
			}

			// 榜单优先顺序/仅极速版
			if common.GetAppBranch((*request)[constant.OSBRANCH]) == common.LITE_APP {
				h.processBoardRank(&tpl)
			}

			h.response(tpl)
			return
		}
}

func (h *His_Board) parseRALResData(cards []interface{}, tpl HisResponse, boardChannel string) (HisResponse, error) {
	request := h.getRequest()
	adaptions := h.getAdaption()
	osbarnch := (*request)[constant.OSBRANCH]
	bdVersion := (*adaptions)["bd_version"]
	if len(cards) < 1 {
		tpl.Errno = "-5001"
		tpl.Errmsg = "board rec display data is empty"
		return tpl, errors.New(tpl.Errmsg)
	}

	if boardChannel == "his" {
		for _, item := range cards {
			v, ok := item.(map[string]interface{})
			if !ok {
				continue
			}

			moreUrl, ok := v["moreUrl"].(string)
			if !ok {
				continue
			}
			// 拼接h5链接
			v["moreH5Url"] = moreUrl
			// 鸿蒙主板＞13.82后拼接完整榜单跳转参数
			if (*request)["realOsbranch"] == "h0" && version.Compare(bdVersion, "13.86", ">=") {
				v["moreH5Url"] = moreUrl + "&new_home_style=1&isImmerse=1"
			}

			template := "baiduboxapp://v1/browser/open?url=%s&statusBarStyle=2&notShowLandingTopBar=1"
			v["moreUrl"] = fmt.Sprintf(template, url.QueryEscape(moreUrl+"&new_home_style=1&isImmerse=1"))
			if osbarnch == "a0" && version.Compare(bdVersion, "13.72.0.10", "<") {
				template = "baiduboxapp://v1/browser/open?url=%s"
				v["moreUrl"] = fmt.Sprintf(template, url.QueryEscape(moreUrl+"&new_home_style=1"))
			}
			if v["board_key"] == "gaokao" {
				v["moreAppUrl"] = "baiduboxapp://browser/invokeTalosBeeLandingPage?startparams=%7B%22backupURL%22%3A%22https%3A%2F%2Ftop.baidu.com%2Fboard%3Ftab%3Drealtime%26sa%3Dfyb_gaokao_his%22%2C%22bundleName%22%3A%22SearchTrends%22%2C%22mainBizName%22%3A%22box.rnplugin.searchmanifest%22%2C%22moduleName%22%3A%22SearchTrends%22%2C%22subBizName%22%3A%22SearchTrends%22%7D%0A&bizparams=%7B%22routerInfo%22%3A%7B%22data%22%3A%7B%22tab%22%3A%22realtime%22%7D%7D%2C%22sa%22%3A%22fyb_gaokao_his%22%7D"
				v["moreH5Url"] = "https://top.baidu.com/board?tab=realtime&sa=fyb_gaokao_his"
				v["moreUrl"] = "baiduboxapp://talos/invokeTalosPage?startparams=%7B%22backupURL%22%3A%22https%3A%2F%2Ftop.baidu.com%2Fboard%3Ftab%3Drealtime%5Cu0026sa%3Dfyb_gaokao_his%22%2C%22bundleName%22%3A%22SearchTrends%22%2C%22mainBizName%22%3A%22box.rnplugin.searchmanifest%22%2C%22moduleName%22%3A%22SearchTrends%22%2C%22subBizName%22%3A%22SearchTrends%22%7D&bizparams=%7B%22routerInfo%22%3A%7B%22data%22%3A%7B%22tab%22%3A%22realtime%22%7D%7D%2C%22sa%22%3A%22feed_index_homepage%22%7D"
			} else if v["board_key"] == "olympic" {
				v["moreAppUrl"] = "baiduboxapp://browser/invokeTalosBeeLandingPage?startparams=%7B%22backupURL%22%3A%22https%3A%2F%2Ftop.baidu.com%2Fboard%3Ftab%3Drealtime%26sa%3Dfyb_olympic_his%22%2C%22bundleName%22%3A%22SearchTrends%22%2C%22mainBizName%22%3A%22box.rnplugin.searchmanifest%22%2C%22moduleName%22%3A%22SearchTrends%22%2C%22subBizName%22%3A%22SearchTrends%22%7D%0A&bizparams=%7B%22routerInfo%22%3A%7B%22data%22%3A%7B%22tab%22%3A%22realtime%22%7D%7D%2C%22sa%22%3A%22fyb_olympic_his%22%7D"
				v["moreH5Url"] = "https://top.baidu.com/board?tab=realtime&sa=fyb_olympic_his"
				v["moreUrl"] = "baiduboxapp://talos/invokeTalosPage?startparams=%7B%22backupURL%22%3A%22https%3A%2F%2Ftop.baidu.com%2Fboard%3Ftab%3Drealtime%5Cu0026sa%3Dfyb_olympic_his%22%2C%22bundleName%22%3A%22SearchTrends%22%2C%22mainBizName%22%3A%22box.rnplugin.searchmanifest%22%2C%22moduleName%22%3A%22SearchTrends%22%2C%22subBizName%22%3A%22SearchTrends%22%7D&bizparams=%7B%22routerInfo%22%3A%7B%22data%22%3A%7B%22tab%22%3A%22realtime%22%7D%7D%2C%22sa%22%3A%22feed_index_homepage%22%7D"
			}
		}
	}

	tpl.Data = cards
	return tpl, nil
}

// 更改热榜榜单优先顺序
func (h *His_Board) processBoardRank(resData *HisResponse) {
	if resData == nil {
		return
	}

	boardList, ok := resData.Data.([]interface{})
	if !ok {
		return
	}
	var teleplay, movie, realtiem interface{} = nil, nil, nil
	var newBoardList []interface{}

	// 提取
	for _, item := range boardList {
		v, ok := item.(map[string]interface{})
		if !ok {
			continue
		}
		// 热搜第1位
		if v["board_key"] == "realtime" {
			realtiem = item
		}
		// 电视剧2位
		if v["board_key"] == "teleplay" {
			teleplay = item
		}
		//  电影3位
		if v["board_key"] == "movie" {
			movie = item
		}
	}

	// 影视优先
	if realtiem != nil {
		newBoardList = append(newBoardList, realtiem)
	}
	if teleplay != nil {
		newBoardList = append(newBoardList, teleplay)
	}
	if movie != nil {
		newBoardList = append(newBoardList, movie)
	}

	// 后续补位
	for _, item := range boardList {
		v, ok := item.(map[string]interface{})
		if !ok {
			continue
		}
		// 排除已提取的
		if v["board_key"] == "realtime" || v["board_key"] == "teleplay" || v["board_key"] == "movie" {
			continue
		}
		newBoardList = append(newBoardList, item)
	}
	// cover
	resData.Data = newBoardList
}

// 初始化对端协议的结构
func (h *His_Board) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (h *His_Board) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	h.setTplOriginalData(tpl)
	h.setTplByteData(tplByte)
}

func (h *His_Board) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Board{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-board", &His_Board{})
}
