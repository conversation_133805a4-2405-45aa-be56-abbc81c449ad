package his

import (
	"encoding/json"
	"errors"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_AtAdd struct {
	BasePage
}

type HisResponse struct {
	Errno        string      `json:"errno"`
	Errmsg       string      `json:"errmsg"`
	BaseToken    string      `json:"base_token,omitempty"`
	SessionToken string      `json:"session_token,omitempty"`
	Data         interface{} `json:"data"`
	Switch       interface{} `json:"switch_control"`
	Requestid    string      `json:"requestid"`
}

// 主要执行的函数入口
func (this *His_AtAdd) Execute() {
	this.addNotice("sugnew", "1")
	request := this.getRequest()
	adaptions := this.getAdaption()
	tplData := this.defaultTpl()
	//空query
	dataquery := (*request)["data"]
	if dataquery == "" {
		tplData.Errno = "1001"
		tplData.Errmsg = "params[data] is missing"
		this.response(tplData)
		return
	}
	osbranch := (*request)["osbranch"]
	//版本控制,判断直达类型是否his
	direct_to_his := false
	bdVersion := (*adaptions)["bd_version"]
	if !version.Compare(bdVersion, "11.22", "<") ||
		(common.GetAppBranch(osbranch) == common.LITE_APP && version.Compare(bdVersion, "6.26", ">=")) {
		direct_to_his = true
	}

	var reqdata data.DataType
	decodeErr := json.Unmarshal([]byte(dataquery), &reqdata)

	if decodeErr != nil || reqdata.Query == "" { //json格式不符合预期
		tplData.Errno = "1001"
		tplData.Errmsg = "params[query] is missing"
		this.response(tplData)
		return
	}

	wisehispm, _ := this.ctx.Cookie("WISE_HIS_PM")
	if wisehispm != "0" && wisehispm != "1" {
		wisehispm = "0"
	}

	// 检查是否登录
	if !common.IsLogin(request) {
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", "not login")
		tplData.Errno = "1001"
		tplData.Errmsg = "params cookie is missing"
		this.response(tplData)
		return
	}

	if reqdata.Tag == "笔记" {
		// 对于sug直达笔记的搜索历史，不要再次写入his
		this.addNotice("his_no_add", reqdata.Tag)
		this.response(tplData)
		return
	}

	dataSrv := data.NewHIS(this.ctx, direct_to_his)
	if ralGetErr := dataSrv.GetHISResponse(reqdata, wisehispm); ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		tplData.Errno = "4002"
		tplData.Errmsg = "ral request fail"
		this.response(tplData)
		return
	}
	resData := dataSrv.GetHISTplData()
	tpl, parseErr := this.parseRALResData(resData, tplData)
	if parseErr != nil {
		this.setErr()
		this.addNotice("ralErr", parseErr.Error())
		this.response(tpl)
		return
	}
	//parsedData := this.GetTplData()
	this.response(tpl)
	return
}
func (this *His_AtAdd) parseRALResData(resData data.HisRALResponse, tpl HisResponse) (HisResponse, error) {

	if resData.Status != "0" && resData.Status != "2008" {
		resbyte, _ := json.Marshal(resData)
		tpl.Errno = "5001"
		tpl.Errmsg = "request sughis error "
		return tpl, errors.New(string(resbyte))
	}
	return tpl, nil
}

// 初始化对端协议的结构
func (this *His_AtAdd) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *His_AtAdd) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *His_AtAdd) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_AtAdd{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-atadd", &His_AtAdd{})
}
