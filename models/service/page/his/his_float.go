package his

import (
	"encoding/json"
	"errors"
	"strconv"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type HisFloat struct {
	BasePage
}

// 程序执行入口
func (h *HisFloat) Execute() {

	// 发出请求，获得返回的结果
	request := h.getRequest()
	dataSrv := data.NewFloat(h.ctx)
	tplData := h.defaultTpl()
	ralGetErr := dataSrv.GetFloatResponse(request)
	if ralGetErr != nil {
		// 未召回数据时，不算作请求错误
		if ralGetErr == data.ErrFloat {
			tplData.Errno = "1000"
			h.response(tplData)
		} else {
			h.handleErr("1001", ralGetErr.Error())
		}
		return
	}

	// 解析结果
	resData := dataSrv.GetHISTplData()
	tpl, parseErr := h.parseRALResData(resData, tplData)
	if parseErr != nil {
		h.handleErr("1002", parseErr.Error())
		return
	}

	// 结果返回给端
	h.response(tpl)
}

// 将结果封装为返回给端的指定样式
func (h *HisFloat) parseRALResData(resDataArr map[string]data.FloatTerm, tpl HisResponse) (HisResponse, error) {

	// 循环读取结果，找到srcid为1000532的term，封装到map中
	dataMap := make(map[string]interface{})
	for srcid, resData := range resDataArr {
		if srcid == strconv.Itoa(data.FloatSrcID) {
			if resData.Content == "" || resData.Query == "" {
				continue
			}
			dataMap["float"] = map[string]interface{}{
				"title":    resData.Title,
				"content":  resData.Content,
				"query":    resData.Query,
				"iconImgW": resData.IconImageWidth,
				"iconImgH": resData.IconImageHeight,
				"iconImg":  resData.IconImage,
				"sa":       resData.Sa,
			}
		}
	}
	if len(dataMap) == 0 {
		errmsg := "content or query empty"
		return tpl, errors.New(errmsg)
	}

	tpl.Data = dataMap
	return tpl, nil
}

// 获得默认空结果
func (h *HisFloat) defaultTpl() HisResponse {
	datainfo := make(map[string]interface{})
	ret := HisResponse{
		Errno:     "0",
		Errmsg:    "",
		Data:      datainfo,
		Requestid: h.getLogidStr(),
		Switch:    common.SwitchConfMem.Switch,
	}
	return ret
}

// 处理错误信息统一方法
func (h *HisFloat) handleErr(errno string, msg string) {
	h.setErr()
	h.addNotice("FloatErr", "1")
	h.addNotice("FloatErrMsg", msg)

	hisListErr := HisListErr{
		Errno:     errno,
		Errmsg:    msg,
		Requestid: h.getLogidStr(),
	}
	h.response(hisListErr)
}

// 返回结果给端
func (h *HisFloat) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	h.setTplOriginalData(tpl)
	h.setTplByteData(tplByte)
}

// 获得本结构体实例
func (h *HisFloat) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &HisFloat{}
	target.ctx = ctx
	return target, nil
}

// 注册本类
func init() {
	Register("his-float", &HisFloat{})
}
