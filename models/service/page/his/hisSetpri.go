package his

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Setpri struct {
	BasePage
}

type priDataType struct {
	Timestamp string `json:"timestamp"`
	Status    string `json:"status"`
	Target    string `json:"target"`
	Token     string `json:"token"`
}

//主要执行的函数入口
func (this *His_Setpri) Execute() {
	//此接口在框启动时调用 增流量降级策略
	this.addNotice("sugnew", "1")
	request := this.getRequest()
	tplData := this.defaultTpl()
	dataquery := (*request)["data"]
	if dataquery == "" {
		tplData.Errno = "1001"
		tplData.Errmsg = "params[data] is missing"
		this.response(tplData)
		return
	}
	var reqdata priDataType
	decodeErr := json.Unmarshal([]byte(dataquery), &reqdata)
	this.addNotice("dataquery", dataquery)
	if decodeErr != nil || reqdata.Timestamp == "" { //json格式不符合预期
		tplData.Errno = "1001"
		tplData.Errmsg = "params[timestamp] is missing"
		this.response(tplData)
		return
	}
	if reqdata.Status == "" { //json格式不符合预期
		tplData.Errno = "1001"
		tplData.Errmsg = "params[status] is missing"
		this.response(tplData)
		return
	}

	if !common.IsLogin(request) {
		tplData.Errno = "1001"
		tplData.Errmsg = "params cookie is missing"
		this.response(tplData)
		return
	}
	uid := (*request)[constant.SESSION_UID]

	dataSrv := data.NewHISPrive(this.ctx)

	// 没传target参数时，默认使用无痕模式开关
	target := "sugStoreSet"
	if reqdata.Target != "" {
		target = reqdata.Target
	}

	// 校验token字段
	if reqdata.Target != "" {
		checkRet := dataSrv.CheckToken(reqdata.Timestamp, reqdata.Target, reqdata.Token)
		if !checkRet {
			tplData.Errno = "1002"
			tplData.Errmsg = "token check failed"
			this.response(tplData)
			return
		}
	}

	if ralGetErr := dataSrv.SetHISResponse(uid, reqdata.Status, target); ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		tplData.Errno = "4002"
		tplData.Errmsg = ralGetErr.Error()
		this.response(tplData)
		return
	}

	this.response(tplData)
	return
}

//初始化对端协议的结构
func (this *His_Setpri) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *His_Setpri) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *His_Setpri) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Setpri{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-setpri", &His_Setpri{})
}
