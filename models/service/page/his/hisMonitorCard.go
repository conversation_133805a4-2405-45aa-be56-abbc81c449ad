package his

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type HisMonitorCard struct {
	BasePage
	monitorData *data.MonitorCard
}

func (h *HisMonitorCard) Execute() {
	requestFunc, parseFunc := h.ExecuteByStep()
	requestFunc()
	parseFunc()
}

// 实现 ExecuteByStep 接口
func (h *HisMonitorCard) ExecuteByStep() (func(), func()) {
	// 请求函数
	requestFunc := func() {
		request := h.getRequest()
		uid := (*request)["uid"]
		h.monitorData = data.NewMonitorCard(h.ctx)
		err := h.monitorData.GetResponse(uid)
		if err != nil {
			h.addNotice("HisMonitorCard_request_error", err.Error())
		}
	}

	// 解析响应函数
	parseFunc := func() {
		if h.monitorData == nil || len(h.monitorData.TplData) == 0 {
			h.setTplOriginalData(map[string]interface{}{})
		} else {
			h.setTplOriginalData(h.monitorData.TplData)
		}
	}

	return requestFunc, parseFunc
}

func (h *HisMonitorCard) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &HisMonitorCard{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-monitor-card", &HisMonitorCard{})
}
