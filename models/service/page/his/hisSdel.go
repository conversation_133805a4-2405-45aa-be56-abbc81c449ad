package his

import (
	"bytes"
	"compress/flate"
	"encoding/json"
	"errors"
	"io/ioutil"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Sdel struct {
	BasePage
}
type SdelParms struct {
	Query string `json:"query"`
}

func (this *His_Sdel) Gzdecode(data string) (string, error) {
	if data == "" {
		err := errors.New("data is empty")
		return "", err
	}
	if len(data) < 18 {
		err := errors.New("data len < 18")
		return "", err
	}
	//len := len(data) - 8
	data1 := data[10:]
	byte1 := []byte(data)
	//http://www.faqs.org/rfcs/rfc1952.html
	//gzip结构文档  The "deflate" method (CM = 8)
	if byte1[2] != 8 {
		err := errors.New("CM!=8")
		return "", err
	}
	r := flate.NewReader(bytes.NewReader([]byte(data1)))
	defer r.Close()
	out, err := ioutil.ReadAll(r)
	//fmt.Printf("==out:%v",out)
	if err != nil {
		return "", err
	}
	return string(out), nil
}

// 主要执行的函数入口
func (this *His_Sdel) Execute() {
	this.addNotice("sugnew", "1")
	request := this.getRequest()
	tplData := this.defaultTpl()

	sdel := (*request)["sdel"]
	decodeResult, err := this.Gzdecode(sdel)
	if err != nil {
		this.setErr()
		this.addNotice("parseErr", "1")
		this.addNotice("parseErrMsg", err.Error())
		tplData.Errno = "4002"
		tplData.Errmsg = "ral request fail"
		this.response(tplData)
		return
	}
	var parmsStruct SdelParms
	if json.Unmarshal([]byte(decodeResult), &parmsStruct) != nil {
		errmsg := "params[query] is missing" + decodeResult
		this.addNotice("paramsErr", "1")
		this.addNotice("ErrMsg", errmsg)
		tplData.Errno = "1001"
		tplData.Errmsg = "params[query] is missing"
		this.response(tplData)
		return
	}

	// 检查是否登录
	if !common.IsLogin(request) {
		this.addNotice("paramsErr", "1")
		this.addNotice("ErrMsg", "not login")
		tplData.Errno = "1001"
		tplData.Errmsg = "params[cookie.bduss] is missing"
		this.response(tplData)
		return
	}

	dataSrv := data.NewHSUG(this.ctx)
	if parmsStruct.Query == "" {
		errmsg := "params[query] is null" + decodeResult
		this.addNotice("paramsErr", "1")
		this.addNotice("ErrMsg", errmsg)
		tplData.Errno = "1001"
		tplData.Errmsg = "params[query] is null"
		this.response(tplData)
		return
	}
	ralGetErr := dataSrv.GetHSugSdelResponse(parmsStruct.Query)
	if ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		tplData.Errno = "4002"
		tplData.Errmsg = "ral request fail"
		this.response(tplData)
		return
	}
	resData := dataSrv.GetHISTplData()
	tpl, parseErr := this.parseRALResData(resData, tplData)
	if parseErr != nil {
		this.setErr()
		this.addNotice("ralErr", parseErr.Error())
		this.response(tpl)
		return
	}
	this.response(tplData)
	return
}
func (this *His_Sdel) parseRALResData(resData data.HisRALResponse, tpl HisResponse) (HisResponse, error) {

	if resData.Status != "" && resData.Status != "0" && resData.Status != "2008" {
		resbyte, _ := json.Marshal(resData)
		tpl.Errno = "5001"
		tpl.Errmsg = "request sughis error "
		return tpl, errors.New(string(resbyte))
	}
	return tpl, nil
}

// 初始化对端协议的结构
func (this *His_Sdel) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *His_Sdel) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *His_Sdel) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Sdel{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-sdel", &His_Sdel{})
}
