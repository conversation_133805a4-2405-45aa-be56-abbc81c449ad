package his

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type HisDiscover struct {
	BasePage
}

// 主要执行的函数入口
func (p *HisDiscover) Execute() {
	p.addNotice("sugnew", "1")
	tplData := p.defaultTpl()
	request := p.getRequest()
	// 非简搜流量没有prompt
	tag, ok := (*request)[constant.ChatSearchTag]
	if !ok || tag != "1" {
		p.ctx.Header("Content-Type", "application/json; charset=utf-8")
		p.response(tplData)
		return
	}
	tplData.Data = common.GetAiDiscoverData(request)
	p.ctx.Header("Content-Type", "application/json; charset=utf-8")
	p.response(tplData)
}

func (p *HisDiscover) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:     "0",
		Errmsg:    "",
		Data:      datainfo,
		Switch:    common.SwitchConfMem.Switch,
		Requestid: p.getLogidStr(),
	}
	return ret
}

func (p *HisDiscover) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	p.setTplOriginalData(tpl)
	p.setTplByteData(tplByte)
}

func (p *HisDiscover) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &HisDiscover{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-discover", &HisDiscover{})
}
