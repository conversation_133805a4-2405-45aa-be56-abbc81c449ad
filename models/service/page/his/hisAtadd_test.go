package his

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHis_AtAdd_Execute(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Execute ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	requestParams := &(map[string]string{})
	(*requestParams)["data"] = `{"query":"人们"}`
	(*requestParams)["typeid"] = "1"
	ctx.Set(constant.REQPARAMS, *requestParams)
	basePage := BasePage{}
	basePage.ctx = ctx

	tests := []struct {
		testCaseTitle string
		fields        fields
	}{
		{
			"TestHis_AtAdd_Execute",
			fields{
				basePage,
			},
		},
	}

	for _, tt := range tests {
		this := &His_AtAdd{
			BasePage: tt.fields.BasePage,
		}
		this.Execute()
	}
	ag.Run()
}

func TestHis_AtAdd_parseRALResData(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		resData data.HisRALResponse
		tpl     HisResponse
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}
	ctx := createGetWebContext()
	utInst := ut.New(t, "Unit tests for FUNCTION: parseRALResData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	basePage := BasePage{}
	basePage.ctx = ctx
	resData := data.HisRALResponse{}
	resData.Status = "0"
	resData1 := data.HisRALResponse{}
	resData1.Status = "2222"
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
	}
	ret1 := HisResponse{
		Errno:  "5001",
		Errmsg: "request sughis error ",
		Data:   datainfo,
	}
	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //HisResponse
		wantErr       bool
	}{
		{
			"TestHis_AtAdd_parseRALResData",
			fields{
				basePage,
			},
			args{
				resData,
				ret,
			},
			Want{
				ret,
				ut.ShouldResemble,
			},
			true,
		},
		{
			"TestHis_AtAdd_parseRALResData",
			fields{
				basePage,
			},
			args{
				resData1,
				ret,
			},
			Want{
				ret1,
				ut.ShouldResemble,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &His_AtAdd{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.parseRALResData(tt.args.resData, tt.args.tpl)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_AtAdd_parseRALResData, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_AtAdd_parseRALResData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_AtAdd_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	datainfo := []interface{}{}
	switchData := map[string]interface{}(nil)
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: switchData,
	}
	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisResponse
	}{
		{
			"TestHis_AtAdd_defaultTpl",
			fields{
				basePage,
			},
			Want{
				ret,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		this := &His_AtAdd{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_AtAdd_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_AtAdd_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &His_AtAdd{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestHis_AtAdd_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_AtAdd{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_AtAdd_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_AtAdd_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
