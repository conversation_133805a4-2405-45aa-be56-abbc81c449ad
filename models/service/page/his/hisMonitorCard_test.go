// nolint
package his

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHisMonitorCard_newSelf(t *testing.T) {
	// Setup
	ctx := &gdp.WebContext{}

	// Execute
	target, err := (&HisMonitorCard{}).newSelf(ctx)
	if err != nil {
		t.Fatalf("newSelf returned unexpected error: %v", err)
	}

	// Verify
	if target == nil {
		t.Error("newSelf returned nil target")
	}

	hisCard, ok := target.(*HisMonitorCard)
	if !ok {
		t.Errorf("newSelf returned unexpected type: %T", target)
	}

	if hisCard.ctx != ctx {
		t.<PERSON>rror("newSelf did not set context correctly")
	}
}

func TestHisMonitorCardInit(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: init")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// 通过判断能否通过名称获取页面来测试是否正确注册
	// 使用真实的 NewPager 函数
	ctx := createGetWebContext()
	pager, err := NewPager("his-monitor-card", ctx)

	// 验证结果
	ag.Add("Test Case Of TestHisMonitorCard__Error", err, ut.ShouldBeNil, nil)
	ag.Add("Test Case Of TestHisMonitorCard_NotNil", pager, ut.ShouldNotBeNil, nil)

	_, ok := pager.(*HisMonitorCard)
	ag.Add("Test Case Of TestHisMonitorCard_Init_Type", ok, ut.ShouldEqual, true)

	ag.Run()
}

func TestHisMonitorCard_ExecuteByStep(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: ExecuteByStep")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	reqParams := &map[string]string{
		"uid": "12345",
	}
	ctx := createGetWebContext()
	ctx.Set(constant.REQPARAMS, *reqParams)

	// 模拟 data.NewMonitorCard 返回
	mockData := &data.MonitorCard{}
	mockData.TplData = map[string]interface{}{
		"text": "test title",
		"survey": map[string]interface{}{
			"link": "http://test.com",
		},
		"tag_style": map[string]interface{}{
			"image":       "image",
			"night_image": "nightImage",
			"dark_image":  "darkImage",
			"tag_type":    2,
			"w_h_ratio":   1.375,
		},
	}

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(data.NewMonitorCard, func(_ *gdp.WebContext) *data.MonitorCard {
		return mockData
	})

	// 模拟 GetResponse 方法
	patches.ApplyMethod((*data.MonitorCard)(nil), "GetResponse", func(_ *data.MonitorCard, _ string) error {
		return nil
	})

	// 构建测试用例
	hisMonitorCard := &HisMonitorCard{}
	hisMonitorCard.ctx = ctx

	// 执行测试
	requestFunc, parseFunc := hisMonitorCard.ExecuteByStep()

	// 测试 requestFunc
	requestFunc()
	ag.Add("Test Case Of TestHisMonitorCard_requestFunc", hisMonitorCard.monitorData, ut.ShouldNotBeNil, nil)

	// 测试 parseFunc
	parseFunc()
	tplData := hisMonitorCard.GetTplOriginData()
	_, exists := tplData.(map[string]interface{})
	ag.Add("Test Case Of TestHisMonitorCard_parseFunc", exists, ut.ShouldEqual, true)

	ag.Run()
}

func TestHisMonitorCard_Execute(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: ExecuteByStep")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	reqParams := &map[string]string{
		"uid": "12345",
	}
	ctx := createGetWebContext()
	ctx.Set(constant.REQPARAMS, *reqParams)

	// 模拟 data.NewMonitorCard 返回
	mockData := &data.MonitorCard{}
	mockData.TplData = map[string]interface{}{
		"text": "test title",
		"survey": map[string]interface{}{
			"link": "http://test.com",
		},
		"tag_style": map[string]interface{}{
			"image":       "image",
			"night_image": "nightImage",
			"dark_image":  "darkImage",
			"tag_type":    2,
			"w_h_ratio":   1.375,
		},
	}

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(data.NewMonitorCard, func(_ *gdp.WebContext) *data.MonitorCard {
		return mockData
	})

	// 模拟 GetResponse 方法
	patches.ApplyMethod((*data.MonitorCard)(nil), "GetResponse", func(_ *data.MonitorCard, _ string) error {
		return nil
	})

	// 构建测试用例
	hisMonitorCard := &HisMonitorCard{}
	hisMonitorCard.ctx = ctx

	// 执行测试
	hisMonitorCard.Execute()

	// 测试 requestFunc
	ag.Add("Test Case Of TestHisMonitorCard_Execute_requestFunc", hisMonitorCard.monitorData, ut.ShouldNotBeNil, nil)

	tplData := hisMonitorCard.GetTplOriginData()
	_, exists := tplData.(map[string]interface{})
	ag.Add("Test Case Of TestHisMonitorCard_Execute_parseFunc", exists, ut.ShouldEqual, true)

	ag.Run()
}
