package his

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type HisListIphone struct {
	BasePage
}

// 主要执行的函数入口
func (h *HisListIphone) Execute() {
	request := h.getRequest()
	adaptions := h.getAdaption()
	tplData := h.defaultTpl()
	hisListErr := HisListErr{}
	bdVersion := (*adaptions)["bd_version"]
	uid := (*request)[constant.SESSION_UID]
	// 校验uid
	if !common.IsLogin(request) {
		h.addNotice("ralErr", "1")
		h.addNotice("ralErrMsg", "check uid fail")
		hisListErr.Errno = "-4001"
		hisListErr.Errmsg = "params[cookie.bduss] is invalid"
		hisListErr.Requestid = h.getLogidStr()
		h.response(tplData)
		return
	}

	showDirectHis := h.validateParams(request, adaptions)
	dataSrv := data.NewHISLIST(h.ctx)
	doubleStatus, _ := dataSrv.GetResponse(request, adaptions, uid).(data.HisList_Request_Error)
	h.setResponse(doubleStatus, dataSrv, showDirectHis, bdVersion)
	return
}

func (h *HisListIphone) ExecuteByStep() (multiRequest func(), parseResponse func()) {
	request := h.getRequest()
	adaptions := h.getAdaption()
	tplData := h.defaultTpl()
	hisListErr := HisListErr{}
	bdVersion := (*adaptions)["bd_version"]
	uid := (*request)[constant.SESSION_UID]

	dataSrv := data.NewHISLIST(h.ctx)
	requests, _ := dataSrv.BuildRequest(request, adaptions, uid)
	multiResponseChan := make(chan data.ChRawHTTPResp, len(requests))
	// 校验uid
	if !common.IsLogin(request) {
		h.addNotice("ralErr", "1")
		h.addNotice("ralErrMsg", "check uid fail")
		hisListErr.Errno = "-4001"
		hisListErr.Errmsg = "params[cookie.bduss] is invalid"
		hisListErr.Requestid = h.getLogidStr()
		h.response(tplData)
		return
	}

	showDirectHis := h.validateParams(request, adaptions)
	return func() {
			dataSrv.MultiRequest(requests, multiResponseChan)
		}, func() {
			doubleStatus, _ := dataSrv.ParseHisListResponse(multiResponseChan).(data.HisList_Request_Error)
			h.setResponse(doubleStatus, dataSrv, showDirectHis, bdVersion)
		}
}

func (h *HisListIphone) validateParams(request *map[string]string, adaptions *map[string]string) bool {
	bdVersion := (*adaptions)["bd_version"]
	if version.Compare("6.4", bdVersion, "<=") {
		hisMax = 20
	}
	if version.Compare("11.2", bdVersion, "<=") {
		rec = "on"
		hisMax = 40
	}
	// sug个性化，his下发条数控制到50
	if version.Compare("12.11", bdVersion, "<=") {
		hisMax = 50
	}

	osbranch := (*request)["osbranch"]
	showDirectHis := false
	if !version.Compare(bdVersion, "11.22", "<") ||
		(common.GetAppBranch(osbranch) == common.LITE_APP && version.Compare(bdVersion, "6.26", ">=")) {
		showDirectHis = true
	}
	return showDirectHis
}

// nolint:gocyclo
func (h *HisListIphone) setResponse(doubleStatus data.HisList_Request_Error, dataSrv *data.HISLIST, showDirectHis bool, bdVersion string) {
	hisListErr := HisListErr{}
	tplData := h.defaultTpl()
	if doubleStatus.IsHisListErr() {
		h.setErr()
		h.addNotice("ralErr", "1")
		h.addNotice("ralErrMsg", doubleStatus.HisList.Error())
		hisListErr.Errno = "-4001"
		hisListErr.Errmsg = "request multidatasrv error"
		hisListErr.Requestid = h.getLogidStr()
		h.response(tplData)
		return
	}

	resDataPri := dataSrv.GetHISPriTplData()
	resDataList := dataSrv.GetHISListTplData()
	if resDataList.Errno != 0 {
		h.setErr()
		h.addNotice("ralErr", "1")
		h.addNotice("ralErrMsg", "request hissrv error"+resDataList.Errmsg)
		hisListErr.Errno = "-5001"
		hisListErr.Errmsg = "request hissrv error:" + resDataList.Errmsg
		hisListErr.Requestid = h.getLogidStr()
		h.response(tplData)
		return
	}
	// TODO 新增常搜部分?
	dataRes := make(map[string]string)
	if len(resDataList.Data) == 0 {
		tplData.Data = dataRes
	}
	// type=0:普通sug type=17:语音sug type=18:图搜 type=20、21、22 推荐
	arrType := []string{"0", "17", "18", "20", "21", "22"}
	result := []interface{}{}
	for i, his := range resDataList.Data {
		// 超出个数
		if i >= hisMax {
			break
		}
		isInType := 0
		for _, v := range arrType {
			if his.Type == v {
				isInType = 1
				break
			}
		}
		if his.Type == "" || isInType == 0 || his.Vals[0] == "" || len(his.Vals) == 0 {
			continue
		}
		if len(his.Vals) == 1 && strings.Trim(his.Vals[0], " ") == "" {
			continue
		}
		word := ""
		// 图搜his
		if his.Type == "18" && len(his.Vals) > 1 {
			isWordWithPic := strings.Index(his.Vals[0], "_")
			if isWordWithPic < 0 {
				word = ""
			} else {
				// 字符串截取
				word = his.Vals[0][isWordWithPic+1:]
			}
			// thumburl := "https://graph.baidu.com/resource/%s_28-28.jpg"

			// 背景：策略存储的历史数据存在过期风险，会导致端上历史场景进入结果页异常
			// 图搜业务，需要过滤掉超过30天的数据，否则拿不到图片
			sign := his.Vals[1]
			if len(sign) < 10 {
				// 防止越界
				continue
			}
			ts, err := strconv.Atoi(sign[len(sign)-10:])
			if err != nil {
				// 解析时间戳失败，不返回本条历史数据
				continue
			}
			curTs := time.Now()
			tsTime := time.Unix(int64(ts), 0)
			if duration := curTs.Sub(tsTime); duration > 30*24*time.Hour {
				// 超过30天，不返回本条历史数据
				continue
			}

			graphsign, _ := common.GetGraphAuthEncrypt(his.Vals[1])
			values := url.Values{}
			values.Set("sign", his.Vals[1])
			values.Set("ak", common.AK)
			values.Set("tk", graphsign)
			values.Set("scale", "50-50-1-90-1")
			querystring := values.Encode()
			thumburl := "https://graph.baidu.com/img/query?" + querystring

			searchurl := "http://graph.baidu.com/s?src_position=his&&tn=baiduboxapp&sign=" + his.Vals[1]
			if word != "" {
				u := url.Values{}
				u.Set("wd", word)
				searchurl = searchurl + "&" + u.Encode()
			}
			res := map[string]interface{}{
				"type":      "2001",
				"word":      word,
				"query":     his.Vals[0],
				"thumburl":  thumburl,
				"searchurl": searchurl,
				"extra":     0,
			}
			if rec == "on" {
				res["ext"] = his.Ext
			}
			result = append(result, res)
			continue
		}
		// 推荐
		isInSugType := 0
		sugType := []string{"20", "21", "22"}
		for _, v := range sugType {
			if his.Type == v {
				isInSugType = 1
				break
			}
		}
		if isInSugType == 1 {
			if rec == "off" {
				continue
			}
			res := map[string]interface{}{
				"type":    "2002",
				"word":    his.Vals[0],
				"query":   his.Vals[0],
				"wap_url": "",
				"www_url": "",
			}
			// TODO 判断为空
			res["ext"] = his.Ext

			result = append(result, res)
			continue
		}

		if !showDirectHis && len(his.Vals) >= 3 {
			continue
		}

		res := map[string]interface{}{
			"type":    "2000",
			"word":    his.Vals[0],
			"query":   his.Vals[0],
			"wap_url": "",
			"www_url": "",
		}

		if showDirectHis && len(his.Vals) >= 3 {
			direct_pos_end := len(his.Vals)
			res["tag"] = his.Vals[direct_pos_end-2]
			res["url"] = his.Vals[direct_pos_end-1]
		}

		// 微频道
		if his.Dtype == "17" && common.GetSampleValue(h.ctx, "wpd_tag", "0") == "1" &&
			version.Compare(bdVersion, common.HisTagConf[his.Dtype]["version"].(string), ">=") {
			res["tag_style"] = common.HisTagConf[his.Dtype]["tag_style"]
			res["sa_suffix"] = common.HisTagConf[his.Dtype]["sa_suffix"]
		}
		if his.Dtype == "5" && version.Compare(bdVersion, common.HisTagConf[his.Dtype]["version"].(string), ">=") {
			res["tag"] = common.HisTagConf[his.Dtype]["tag"]
			sa := common.HisTagConf[his.Dtype]["sa"]

			searchURL := fmt.Sprintf("https://m.baidu.com/s?word=%s&sa=%s&pd=note&tab_se=1", res["query"], sa)
			searchURL = url.QueryEscape(searchURL)
			res["url"] = fmt.Sprintf("baiduboxapp://v1/browser/open?url=%s&append=1&backAllTip=1&newwindow=0&upgrade=1", searchURL)
		}

		if rec == "on" {
			// TODO 判断为空
			res["ext"] = his.Ext
		}

		result = append(result, res)
	}

	tplData.Data = result

	// 设置开关状态
	extend := map[string]interface{}{}
	for target, res := range resDataPri {
		if res.Status == nil || res.Status == "" { // 未设置开关
			extend[target] = []interface{}{}
		} else {
			extend[target] = res
		}
	}
	tplData.Extend = extend

	if rec == "on" {
		if resDataList.Ext != nil {
			tplData.Ext = resDataList.Ext
		} else {
			tplData.Ext = ""
		}
	}

	if len(resDataList.FrequentHis) != 0 {
		tplData.FrequentHis = resDataList.FrequentHis
	}
	tplData.QueryId = resDataList.QueryId
	tplData.Hasmore = resDataList.Hasmore

	h.response(tplData)
}

func (h *HisListIphone) parseRALResData(resData data.HisRALResponse, tpl HisResponse) (HisResponse, error) {
	// TODO
	if resData.Status != "0" && resData.Status != "2008" {
		resbyte, _ := json.Marshal(resData)
		tpl.Errno = "5001"
		tpl.Errmsg = "request sughis error "
		return tpl, errors.New(string(resbyte))
	}
	return tpl, nil
}

// 初始化对端协议的结构
func (h *HisListIphone) defaultTpl() HisListResponse {
	datainfo := []interface{}{}
	extend := make(map[string]string)
	ext := make(map[string]string)
	ret := HisListResponse{
		Errno:       "0",
		Errmsg:      "",
		Extend:      extend,
		Ext:         ext,
		FrequentHis: make([]map[string]string, 0),
		Data:        datainfo,
		Hasmore:     0,
		QueryId:     "",
		Switch:      common.SwitchConfMem.Switch,
	}
	return ret
}

// 校验请求是否有命中小流量 若有命中则修饰参数
func (h *HisListIphone) checkSmallFlow() bool {
	return false
}

func (h *HisListIphone) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	h.setTplOriginalData(tpl)
	h.setTplByteData(tplByte)
}

func (h *HisListIphone) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &HisListIphone{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-list-iphone", &HisListIphone{})
}
