package his

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHisRS_checkURL(t *testing.T) {
	// 1. 准备测试环境
	ctx := createGetWebContext()
	basePage := BasePage{}
	basePage.ctx = ctx
	hisRS := HisRS{
		BasePage: basePage,
	}

	// 准备测试数据
	common.RSWhite.URL = make(map[string]interface{})
	common.RSWhite.URL["www.baidu.com"] = 1

	// 创建Mock黑名单，添加测试域名和URL
	mockCPageHighlight := background.NewMockCPageHighlightBlackList()
	mockCPageHighlight.AddHost("blacklisted.com")
	mockCPageHighlight.AddURL("http://some.domain.com/blacklisted-page")
	// 替换全局实例
	originalCPageHighlight := background.CPageHighlight
	background.CPageHighlight = mockCPageHighlight
	// 测试完成后恢复原始实例
	defer func() {
		background.CPageHighlight = originalCPageHighlight
	}()

	// 2. 测试CPAGE_HIGHLIGHT场景
	t.Run("CPAGE_HIGHLIGHT场景", func(t *testing.T) {
		request := map[string]string{constant.SCENE: constant.CPAGE_HIGHLIGHT}

		// 测试域名在黑名单中的URL
		reqData := data.RSDataParams{URL: "http://blacklisted.com/some-page"}
		result := hisRS.checkURL(&request, reqData)
		assert.False(t, result, "黑名单域名应返回false")

		// 测试URL在黑名单中
		reqData.URL = "http://some.domain.com/blacklisted-page"
		result = hisRS.checkURL(&request, reqData)
		assert.False(t, result, "黑名单URL应返回false")

		// 测试正常URL
		reqData.URL = "http://allowed.com/normal-page"
		result = hisRS.checkURL(&request, reqData)
		assert.True(t, result, "正常URL应返回true")

		// 测试无效URL
		reqData.URL = "://invalid-url"
		result = hisRS.checkURL(&request, reqData)
		assert.False(t, result, "无效URL应返回false")
	})

	// 3. 测试其他场景
	t.Run("其他场景", func(t *testing.T) {
		request := map[string]string{constant.SCENE: constant.ERR_CPAGE}
		reqData := data.RSDataParams{URL: ""}
		result := hisRS.checkURL(&request, reqData)
		assert.True(t, result, "非CPAGE_TOP和CPAGE_HIGHLIGHT场景应返回true")
	})
}
