package his

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHis_Ad_Execute(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: Execute")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	// 设置必要的上下文数据
	userPrivacyInfo := common.UserPrivacyInfo{
		OAID: "test_oaid",
	}
	ctx.Set("userPrivacyInfo", &userPrivacyInfo)

	// 设置适配参数和请求参数
	reqParams := map[string]string{
		"query": "测试查询",
	}
	adapParams := map[string]string{
		"bd_version": "13.0.0",
	}
	ctx.Set(constant.REQPARAMS, reqParams)
	ctx.Set(constant.ADAPARAMS, adapParams)

	// 模拟 data.NewAFD 返回
	mockAFD := &data.AFD{}
	mockAFD.TplData = data.AFDResp{
		Errno:  "0",
		Errmsg: "success",
		LogID:  "12345",
		Data: data.AFDData{
			Ad:    []data.AFDAdItem{},
			ReqID: "req123",
		},
	}

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(data.NewAFD, func(_ *gdp.WebContext) *data.AFD {
		return mockAFD
	})

	// 模拟 GetAFDResponse 方法
	patches.ApplyMethod((*data.AFD)(nil), "GetAFDResponse", func(_ *data.AFD, _ string, _ map[string]string, _ map[string]string, _ common.UserPrivacyInfo) error {
		return nil
	})

	// 构建测试用例
	hisAd := &His_ad{}
	hisAd.ctx = ctx

	// 执行测试
	hisAd.Execute()

	// 验证执行结果
	tplData := hisAd.GetTplOriginData()
	_, exists := tplData.(data.AFDResp)
	ag.Add("Test Case Of TestHis_Ad_Execute", exists, ut.ShouldEqual, true)

	ag.Run()
}

func TestHis_Ad_ExecuteByStep(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: ExecuteByStep")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	// 设置必要的上下文数据
	userPrivacyInfo := common.UserPrivacyInfo{
		OAID: "test_oaid",
	}
	ctx.Set("userPrivacyInfo", &userPrivacyInfo)

	// 设置适配参数和请求参数
	reqParams := map[string]string{
		"query": "测试查询",
	}
	adapParams := map[string]string{
		"bd_version": "13.0.0",
	}
	ctx.Set(constant.REQPARAMS, reqParams)
	ctx.Set(constant.ADAPARAMS, adapParams)

	// 模拟 data.NewAFD 返回
	mockAFD := &data.AFD{}
	mockAFD.TplData = data.AFDResp{
		Errno:  "0",
		Errmsg: "success",
		LogID:  "12345",
		Data: data.AFDData{
			Ad:    []data.AFDAdItem{},
			ReqID: "req123",
		},
	}

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(data.NewAFD, func(_ *gdp.WebContext) *data.AFD {
		return mockAFD
	})

	// 模拟 GetAFDResponse 方法
	patches.ApplyMethod((*data.AFD)(nil), "GetAFDResponse", func(_ *data.AFD, _ string, _ map[string]string, _ map[string]string, _ common.UserPrivacyInfo) error {
		return nil
	})

	// 构建测试用例
	hisAd := &His_ad{}
	hisAd.ctx = ctx

	// 执行测试
	requestFunc, parseFunc := hisAd.ExecuteByStep()

	// 测试 requestFunc
	requestFunc()
	ag.Add("Test Case Of TestHis_Ad_ExecuteByStep_requestFunc", hisAd.adData, ut.ShouldNotBeNil, nil)

	// 测试 parseFunc
	parseFunc()
	tplData := hisAd.GetTplOriginData()
	_, exists := tplData.(data.AFDResp)
	ag.Add("Test Case Of TestHis_Ad_ExecuteByStep_parseFunc", exists, ut.ShouldEqual, true)

	ag.Run()
}

func TestHis_Ad_ExecuteByStep_Error(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: ExecuteByStep with Error")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	// 设置必要的上下文数据
	reqParams := map[string]string{
		"query": "测试查询",
	}
	ctx.Set(constant.REQPARAMS, reqParams)

	// 构建测试用例 - 不初始化 adData 来测试错误处理
	hisAd := &His_ad{}
	hisAd.ctx = ctx

	// 执行测试
	_, parseFunc := hisAd.ExecuteByStep()

	// 测试 parseFunc 错误处理
	parseFunc()
	tplData := hisAd.GetTplOriginData()
	errResp, ok := tplData.(HisListErr)
	ag.Add("Test Case Of TestHis_Ad_ExecuteByStep_Error", ok, ut.ShouldEqual, true)

	if ok {
		ag.Add("Test Case Of TestHis_Ad_ExecuteByStep_Error_Errno", errResp.Errno, ut.ShouldEqual, "-1")
		ag.Add("Test Case Of TestHis_Ad_ExecuteByStep_Error_Errmsg", errResp.Errmsg, ut.ShouldEqual, "ad request not initialized")
	}

	ag.Run()
}

func TestHis_Ad_NewSelf(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	// 构建测试用例
	hisAd := &His_ad{}

	// 执行测试
	target, err := hisAd.newSelf(ctx)

	// 验证结果
	ag.Add("Test Case Of TestHis_Ad_NewSelf_Error", err, ut.ShouldBeNil, nil)
	ag.Add("Test Case Of TestHis_Ad_NewSelf_NotNil", target, ut.ShouldNotBeNil, nil)

	targetHisAd, ok := target.(*His_ad)
	ag.Add("Test Case Of TestHis_Ad_NewSelf_Type", ok, ut.ShouldEqual, true)

	if ok {
		ag.Add("Test Case Of TestHis_Ad_NewSelf_Context", targetHisAd.ctx, ut.ShouldEqual, ctx)
	}

	ag.Run()
}

func TestHis_Ad_Init(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: init")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// 通过判断能否通过名称获取页面来测试是否正确注册
	// 使用真实的 NewPager 函数
	ctx := createGetWebContext()
	pager, err := NewPager("his-ad", ctx)

	// 验证结果
	ag.Add("Test Case Of TestHis_Ad_Init_Error", err, ut.ShouldBeNil, nil)
	ag.Add("Test Case Of TestHis_Ad_Init_NotNil", pager, ut.ShouldNotBeNil, nil)

	_, ok := pager.(*His_ad)
	ag.Add("Test Case Of TestHis_Ad_Init_Type", ok, ut.ShouldEqual, true)

	ag.Run()
}
