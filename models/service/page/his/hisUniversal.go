package his

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/container"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/service"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Universal struct {
	BasePage
}

type UniversalParams struct {
	Target string `json:"target"`
}

// 主要执行的函数入口
func (this *His_Universal) Execute() {
	hislistErr := HisListErr{}
	hislistErr.Switch = common.SwitchConfMem.Switch
	request := this.getRequest()
	dataquery := (*request)["data"]
	common.SetUserPrivacyInfo((*request)["uid"], this.ctx)

	var adaption map[string]string
	if val, exists := this.ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	if dataquery == "" {
		hislistErr.Errno = "-1001"
		hislistErr.Errmsg = "params[data] is missing"
		hislistErr.Requestid = this.getLogidStr()
		this.response(hislistErr)
		return
	}

	var reqdata UniversalParams
	decodeErr := json.Unmarshal([]byte(dataquery), &reqdata)
	// json格式不符合预期
	if decodeErr != nil {
		hislistErr.Errno = "-1002"
		hislistErr.Errmsg = "json unmarshal err"
		hislistErr.Requestid = this.getLogidStr()
		this.addNotice("hisUniversal_dataDecodeErr", dataquery)
		this.response(hislistErr)
		return
	}

	// 缺少target
	if reqdata.Target == "" {
		hislistErr.Errno = "-1003"
		hislistErr.Errmsg = "params[target] is missing"
		hislistErr.Requestid = this.getLogidStr()
		this.response(hislistErr)
		return
	}

	var targetArr []string
	decodeErr = json.Unmarshal([]byte(reqdata.Target), &targetArr)
	if decodeErr != nil { // json格式不符合预期
		this.addNotice("targetjsondecodeerror", decodeErr.Error())
		hislistErr.Errno = "-1004"
		hislistErr.Errmsg = "params[target] is missing"
		hislistErr.Requestid = this.getLogidStr()
		this.response(hislistErr)
		return
	}
	// 逻辑有点绕
	// 首先第一行是必须命中实验
	// 第二行第三行是必须是对应的版本
	// 第四行是如果调用了新路由 POST /suggest/api/his/universal，则必须设置incognito-mode为0
	bdVersion := adaption[constant.BDVERSION]
	osBranch := (*request)[constant.OSBRANCH]
	if common.GetSampleValue(this.ctx, "ad_guess_switch", "0") == "1" &&
		(osBranch == "i0" || osBranch == "a0") &&
		version.Compare(bdVersion, "15.10", ">=") &&
		(*request)["intelligent_mode"] != "1" {
		targetArr = append(targetArr, "ad")
	}
	aiToolSrv := service.NewAiToolService(this.ctx)
	this.ctx.Set("aiToolTypes", aiToolSrv.GetAiToolTypes())
	this.ctx.Set("aiToolIcons", aiToolSrv.GetAiToolIcons())
	// his 敏捷卡实验控制: 判断命中实验 && 校验版本
	if common.GetSampleValue(this.ctx, "monitor_card_switch", "") != "" &&
		version.Compare(adaption[constant.BDVERSION], "15.23", ">=") {
		targetArr = append(targetArr, "monitor-card")
	}

	// 首页展现历史数量
	var hisNumber int
	if common.GetSampleValue(this.ctx, "gosug_hislist_insert", "0") != "0" {
		// 增加 hislist下游
		targetArr, hisNumber = this.processHisTarget(targetArr)
	}

	this.ctx.AddNotice("target", targetArr)
	finalProcessErr := 0
	// 请求失败
	finalProcessReqErr := 0
	// 响应失败
	finalProcessResErr := 0
	hisUniversalTplData := make(map[string]interface{})

	reqFunces := make([]func(), 0, len(targetArr))
	paseResponseFunces := make([]func(), 0, len(targetArr))
	pagers := make([]PagerWithExecuteByStep, 0, len(targetArr))
	nodeNames := make([]string, 0, len(targetArr))

	for _, nodeName := range targetArr {
		nodeNames = append(nodeNames, nodeName)
		if nodeName == "rec_session" {
			nodeName = "rec"
		}
		if nodeName == "list" {
			if len(adaption) > 0 {
				nodeName = "list-" + adaption["platform"]
			} else {
				// 无平台信息则跳过 不处理
				continue
			}
		}

		pageName := "his" + "-" + nodeName
		this.addNotice(fmt.Sprintf("%s", pageName), "1")
		pageSrc, err := NewPagerWithExecuteByStep(pageName, this.ctx)
		if err != nil {
			finalProcessReqErr++
			this.addNotice("pageSrcErr", pageName)
			continue
		}
		multiRequestFunc, parseResponseFunc := pageSrc.ExecuteByStep()
		if multiRequestFunc != nil && parseResponseFunc != nil {
			reqFunces = append(reqFunces, multiRequestFunc)
			paseResponseFunces = append(paseResponseFunces, parseResponseFunc)
			pagers = append(pagers, pageSrc)
		}
	}

	var wg sync.WaitGroup
	wg.Add(len(reqFunces))

	for _, reqFunc := range reqFunces {
		go func(function func()) {
			defer func() {
				if r := recover(); r != nil {
					this.addNotice("goroutine_panic", fmt.Sprintf("%v", r))
				}
				wg.Done()
			}()
			function()
		}(reqFunc)
	}
	wg.Wait()

	for _, parseResponseFunc := range paseResponseFunces {
		parseResponseFunc()
	}

	for i, page := range pagers {
		hisUniversalTplData[nodeNames[i]] = page.GetTplOriginData()
		if val, ok := page.GetTplOriginData().(HisListErr); ok {
			if val.Errno != "0" {
				finalProcessResErr++
			}
		}
	}

	var aiToolTypes []string

	if (version.Compare(bdVersion, "15.18", ">=") && (osBranch == "i0" || osBranch == "a0")) ||
		(version.Compare(bdVersion, "15.24", ">=") && (*request)["realOsbranch"] == "h0") {
		aiToolTypes = ExtractAiToolTypes(hisUniversalTplData)
	}
	aiTool, err := aiToolSrv.GetAiTool(aiToolTypes)
	if err != nil {
		this.addNotice("aiToolErr", err.Error())
	} else {
		hisUniversalTplData["aiTool"] = aiTool
	}
	plusSignFunction, err := aiToolSrv.GetPlusSignFunction()
	if err != nil {
		this.addNotice("plusFuncErr", err.Error())
	} else {
		hisUniversalTplData["plusSignTool"] = plusSignFunction
	}

	hisData, _ := getHisListData(hisUniversalTplData)
	if hisData != nil {
		hisData.InitHisRowCount = 2
		hisUniversalTplData["list"] = *hisData
	}
	// 有历史结果则参与去重
	// 旧 his 页面
	if (*request)["intelligent_mode"] != "1" {
		// 合并的去重逻辑：热榜 > 用户历史 > 广告词，并将广告词插入到猜你想搜的第三位
		ok, msg := this.deduplicateAndInsertAd(hisUniversalTplData)
		if ok {
			this.addNotice("dedupRec", msg)
		}
	}
	if (*request)["intelligent_mode"] == "1" {

		// 新智能化 his 页 去掉运营词
		ok := this.changeIntelligentData(hisUniversalTplData)
		if ok {
			this.addNotice("changeIntelligentData", "ok")
		}
	}

	// 删除敏捷卡
	delete(hisUniversalTplData, "monitor-card")

	// his升级实验下，猜搜&历史融合
	if hisNumber > 0 {
		if err := this.mergeHisList2Guess(hisUniversalTplData, hisNumber); err != nil {
			this.addNotice("mergeHisList2Guess", err.Error())
		}
	}

	finalProcessErr = finalProcessReqErr + finalProcessResErr
	if finalProcessErr == len(targetArr) {
		hislistErr.Errno = "-2000"
		hislistErr.Errmsg = "server error"
		hislistErr.Requestid = this.getLogidStr()
		this.response(hislistErr)
		return
	}
	tplData := this.defaultTpl()
	tplData.Data = hisUniversalTplData

	if (version.Compare(bdVersion, "15.14", ">=") && osBranch == "a0") ||
		(version.Compare(bdVersion, "15.15", ">=") && osBranch == "i0") {
		tokenService := service.NewTokenService(this.ctx, entity.GetRequestParams(this.ctx))
		if tokenService != nil {
			tplData.BaseToken = tokenService.GetBaseToken()
			tplData.SessionToken = tokenService.GetSessionToken()
		}
	}
	this.response(tplData)
	return
}

// his页升级，历史融合进猜搜
func (h *His_Universal) mergeHisList2Guess(hisUniversalTplData map[string]interface{}, hisNum int) error {
	// 获取常搜
	hisListData, ok := hisUniversalTplData["list"].(HisListResponse)
	if !ok {
		return errors.New("hisListData is not HisListResponse")
	}
	// 获取猜搜
	reclist, ok := hisUniversalTplData["rec"].(HisResponse)
	if !ok {
		return errors.New("reclist is not HisResponse")
	}
	// 常搜
	frqIcon := "https://gips2.baidu.com/it/u=3039577158,1841315151&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f66_48"
	// 搜过
	hisIcon := "https://gips0.baidu.com/it/u=4199093731,2962015272&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f66_48"
	var newGuessList []interface{}
	var frequentQuery string
	// 常搜结构
	frequentHis := hisListData.FrequentHis
	if len(frequentHis) > 0 {
		hisNum = hisNum - 1
		frequentQuery = frequentHis[0]["query"]
		newGuessList = append(newGuessList, map[string]interface{}{
			"color": 3,
			"icon":  frqIcon,
			"sa":    "h_frequent",
			"tag":   frequentHis[0]["tag"],
			"text":  frequentHis[0]["query"],
			"tag_style": map[string]interface{}{
				"image":     frqIcon,
				"tag_type":  2,
				"w_h_ratio": 1.375,
			},
		})
	}

	// 搜索历史
	hisList, _ := hisListData.Data.([]interface{})
	for _, hisitem := range hisList {
		if hisNum == 0 {
			// 够了，不需要再取了
			break
		}
		hisitemMap, _ := hisitem.(map[string]interface{})
		text, _ := hisitemMap["query"].(string)
		if text == frequentQuery {
			// 跳过重复
			continue
		}
		newGuessList = append(newGuessList, map[string]interface{}{
			"color": 3,
			"icon":  hisIcon,
			"sa":    "ikhr",
			"tag":   "搜过",
			"text":  text,
			"tag_style": map[string]interface{}{
				"image":     hisIcon,
				"tag_type":  2,
				"w_h_ratio": 1.375,
			},
		})
		hisNum--
	}
	// 获取猜搜真实结构
	var gussData map[string]interface{}
	data, _ := reclist.Data.(map[string]interface{})
	if len(data) == 0 {
		return errors.New("reclist is nil")
	}
	gussData, _ = data["guess"].(map[string]interface{})
	if len(gussData) == 0 {
		return errors.New("gussData is nil")
	}
	gussList, _ := gussData["data"].([]interface{})
	// 向后追加
	newGuessList = append(newGuessList, gussList...)

	// 覆盖
	gussData["data"] = newGuessList
	// 删除原始hislist
	delete(hisUniversalTplData, "list")

	return nil
}

// 增加hislist返回，确定插入历史数
func (h *His_Universal) processHisTarget(targetArr []string) ([]string, int) {
	if h == nil {
		return targetArr, 0
	}
	// 请求历史
	hisNumber := 0
	var err error
	hasList := false
	for _, target := range targetArr {
		if target == "list" {
			hasList = true
			break
		}
	}
	// 无list时添加
	if !hasList {
		targetArr = append(targetArr, "list")
	}
	// 插入历史数
	if hisNumber, err = strconv.Atoi(common.GetSampleValue(h.ctx, "gosug_hislist_insert", "0")); err != nil {
		hisNumber = 0
	}
	return targetArr, hisNumber
}

// 合并的去重逻辑：热榜 > 用户历史 > 广告词，并将广告词插入到猜你想搜的第三位
// nolint:gocyclo
func (h *His_Universal) deduplicateAndInsertAd(hisUniversalTplData map[string]interface{}) (bool, string) {
	// 1. 收集热榜词，用于去重
	hotBoardMap := make(map[string]struct{})
	if board, boardOk := (hisUniversalTplData)["board"].(HisResponse); boardOk {
		boardData, ok := board.Data.([]interface{})
		if ok && len(boardData) > 0 {
			// 找到热搜榜数据
			hotIndex := -1
			for index, value := range boardData {
				data, ok := value.(map[string]interface{})
				if !ok {
					continue
				}
				boardType, ok := data["text"].(string)
				if !ok {
					continue
				}
				if boardType == "热搜榜" {
					hotIndex = index
					break
				}
			}

			if hotIndex != -1 {
				hotBoardData, ok := boardData[hotIndex].(map[string]interface{})
				if ok {
					boarditems, ok := hotBoardData["items"].([]interface{})
					if ok && len(boarditems) > 0 {
						for _, item := range boarditems {
							itemMap, ok := item.(map[string]interface{})
							if !ok {
								continue
							}
							word, ok := itemMap["word"].(string)
							if !ok {
								continue
							}
							hotBoardMap[word] = struct{}{}
						}
					}
				}
			}
		}
	}

	// 2. 收集用户历史词，用于去重
	hisListMap := make(map[string]struct{})
	hisData, _ := getHisListData(hisUniversalTplData)
	if hisData != nil {
		// 用于限制历史词数量
		idx := 0

		// 常搜历史（优先）
		for _, frequent := range hisData.FrequentHis {
			query := frequent["query"]
			hisListMap[query] = struct{}{}
			idx++
			if idx >= 5 {
				break
			}
		}

		// 如果还没有收集满5个词，继续从搜索历史中收集
		if idx < 5 {
			// 搜索历史
			hisList, ok := hisData.Data.([]interface{})
			if ok {
				for _, his := range hisList {
					hisItem, ok := his.(map[string]interface{})
					if !ok {
						continue
					}
					query, ok := hisItem["query"].(string)
					if !ok {
						continue
					}
					hisListMap[query] = struct{}{}
					idx++
					if idx >= 5 {
						break
					}
				}
			}
		}
	}

	// 3. 获取猜你想搜数据
	var guessData map[string]interface{}
	var guessDataList []interface{}
	var recResponse HisResponse
	var recOk bool

	if (hisUniversalTplData)["rec_session"] != nil {
		recResponse, recOk = (hisUniversalTplData)["rec_session"].(HisResponse)
	} else if (hisUniversalTplData)["rec"] != nil {
		recResponse, recOk = (hisUniversalTplData)["rec"].(HisResponse)
	}

	if recOk {
		recSessionData, ok := recResponse.Data.(map[string]interface{})
		if ok {
			guessData, ok = recSessionData["guess"].(map[string]interface{})
			if ok {
				guessDataList, _ = guessData["data"].([]interface{})
			}
		}
	}

	if guessData == nil || guessDataList == nil {
		return false, "guess data not found"
	}

	// his 敏捷卡实验经典模式下：敏捷卡首位插入，但是优先级最低
	if monitorCard, _ := (hisUniversalTplData)["monitor-card"].(map[string]interface{}); len(monitorCard) > 0 && len(guessDataList) > 0 {
		guessDataList = append([]interface{}{monitorCard}, guessDataList...)
	}

	// 4. 获取广告词列表
	var adMaterialList []data.AdGuess
	totalAdCount := 0
	if adResp, adOk := (hisUniversalTplData)["ad"].(data.AFDResp); adOk {
		adMaterialList = data.ExtractRecAdMaterial(adResp)
		totalAdCount = len(adMaterialList)
	}

	// 记录广告词总数
	h.addNotice("ad_total_count", strconv.Itoa(totalAdCount))
	// 5. 找出第一个可用的广告词位置
	var adQueryToInsertIndex = -1
	for index, material := range adMaterialList {
		if _, inHotBoard := hotBoardMap[material.Text]; !inHotBoard {
			if _, inHisList := hisListMap[material.Text]; !inHisList {
				adQueryToInsertIndex = index
				break
			}
		}
	}

	// 6. 处理猜你想搜列表
	// 找到运营词的位置
	gyyIndex := -1
	var gyyData interface{}

	// 创建新的猜你想搜列表，同时去重和找出运营词
	newGuessData := make([]interface{}, 0, len(guessDataList))
	adInserted := false // 标记广告词是否已插入

	// 遍历猜你想搜列表
	for index, data := range guessDataList {
		guessMap, ok := data.(map[string]interface{})
		if !ok {
			continue
		}

		text, ok := guessMap["text"].(string)
		if !ok {
			continue
		}

		sa, ok := guessMap["sa"].(string)
		if !ok {
			continue
		}
		// 检查是否被热榜或历史去重
		if _, inHotBoard := hotBoardMap[text]; inHotBoard {
			continue
		}

		if _, inHisList := hisListMap[text]; inHisList {
			continue
		}

		// 检查是否与要插入的广告词重复
		if adQueryToInsertIndex != -1 && text == adMaterialList[adQueryToInsertIndex].Text {
			continue
		}

		// 记录运营词
		if strings.Contains(sa, "igh_12345_gyy") {
			gyyIndex = index
			gyyData = data
			continue // 暂不添加运营词，稍后再处理
		}

		// 添加到新列表
		newGuessData = append(newGuessData, data)
	}

	// 先处理普通词去重后的结果
	resultList := make([]interface{}, len(newGuessData))
	copy(resultList, newGuessData)

	adPos := 2 // 默认在第三位(索引2)
	// 插入广告词（如果有）- 固定在第三位
	if adQueryToInsertIndex != -1 {
		// 计算广告词插入位置
		if len(resultList) <= 2 {
			// 如果词表不足3个，则放在末尾
			adPos = len(resultList)
		}

		// 插入广告词
		if adPos >= len(resultList) {
			resultList = append(resultList, adMaterialList[adQueryToInsertIndex])
		} else {
			resultList = append(resultList[:adPos], append([]interface{}{adMaterialList[adQueryToInsertIndex]}, resultList[adPos:]...)...)
		}
		adInserted = true
	}

	// 插入运营词（如果有）
	if gyyData != nil {
		// 决定运营词的插入位置
		insertPos := gyyIndex

		// 广告词已插入时，调整运营词位置
		if adInserted && insertPos >= adPos {
			// 运营词位置在广告词位置或之后时，向后顺移一位
			insertPos++
		}

		// 确保位置有效
		if insertPos < 0 {
			insertPos = 0
		}

		// 插入运营词
		if insertPos >= len(resultList) {
			resultList = append(resultList, gyyData)
		} else {
			resultList = append(resultList[:insertPos], append([]interface{}{gyyData}, resultList[insertPos:]...)...)
		}
	}

	// 更新猜你想搜数据
	guessData["data"] = resultList

	// 记录使用的广告词索引
	h.addNotice("ad_used_index", strconv.Itoa(adQueryToInsertIndex))
	// 删除ad
	delete(hisUniversalTplData, "ad")
	return true, "deduped and inserted ad at position 3"
}

// changeIntelligentData 修改智能模式数据，使用去重容器优化处理逻辑
// nolint:gocyclo
func (h *His_Universal) changeIntelligentData(hisUniversalTplData map[string]interface{}) bool {
	// 获取猜你想搜数据
	var guess map[string]interface{}
	var guessData []interface{}
	var recResponse HisResponse
	// 断言失败会得到一个全空的结构体
	if (hisUniversalTplData)["rec_session"] != nil {
		recResponse, _ = (hisUniversalTplData)["rec_session"].(HisResponse)
	} else if (hisUniversalTplData)["rec"] != nil {
		recResponse, _ = (hisUniversalTplData)["rec"].(HisResponse)
	}
	// 空结构体里的recResponse.Data 是 nil，断言为(map[string]interface{})也是可以访问的
	recSessionData, recSessionDataOk := recResponse.Data.(map[string]interface{})

	hisData, _ := getHisListData(hisUniversalTplData)
	hisContainer := container.NewEmpty()
	freqHis := ""
	if hisData != nil {
		hisContainer = container.NewOrderContainerFromSlice(hisData.Data, func(item interface{}) (key string, ok bool) {
			hisMap, ok := item.(map[string]interface{})
			if !ok {
				return "", false
			}
			query, ok := hisMap["query"].(string)
			return query, ok
		})

		if v, _ := (recSessionData["init_his_row_count"]).(float64); v > 0 {
			hisData.InitHisRowCount = int(v)
		}
		if common.GetSampleValue(h.ctx, "ignore_frequent_his", "") == "ignore_freq" {
			hisData.FrequentHis = nil
		} else if common.GetSampleValue(h.ctx, "ignore_frequent_his", "") == "his_prior_freq" {
			if len(hisData.FrequentHis) != 0 {
				if hisContainer.ContainsKey(hisData.FrequentHis[0]["query"]) {
					hisData.FrequentHis = nil
				} else {
					freqHis = hisData.FrequentHis[0]["query"]
					hisData.FrequentHis = hisData.FrequentHis[:1]
				}
			}

		}
		hisUniversalTplData["list"] = *hisData
	}
	if !recSessionDataOk {
		return false
	}
	guess, ok := recSessionData["guess"].(map[string]interface{})
	if !ok {
		return false
	}
	guessData, ok = guess["data"].([]interface{})
	if !ok {
		return false
	}

	guessContainer := container.NewOrderContainerFromSlice(guessData, func(item interface{}) (key string, ok bool) {
		// 优先使用prompt字段，如果没有则使用text字段
		guessMap, ok := item.(map[string]interface{})
		if !ok {
			return "", false
		}
		var query string
		if prompt, exists := guessMap["prompt"]; exists {
			if promptStr, ok := prompt.(string); ok && promptStr != "" {
				query = promptStr
			}
		}

		// 如果prompt为空或不存在，使用text字段
		if query == "" {
			if text, exists := guessMap["text"]; exists {
				if textStr, ok := text.(string); ok && textStr != "" {
					query = textStr
				}
			}
		}
		return query, true
	})

	FormData := entity.GetFormData(h.ctx)

	if hisData != nil && FormData != nil && FormData.IntelligentHisType == 1 {
		if freqHis != "" {
			guessContainer = guessContainer.Remove([]string{freqHis})
			guessContainer = guessContainer.Remove(hisContainer.RangeByIndex(0, hisData.InitHisRowCount*2-1).Keys())
		} else {
			guessContainer = guessContainer.Remove(hisContainer.RangeByIndex(0, hisData.InitHisRowCount*2).Keys())
		}
	}
	guessData = guessContainer.Items()
	// 收集查询词并获取图片
	if common.GetSampleValue(h.ctx, "guess_image", "0") == "1" && background.Switch.GetQueryImageOff() != "1" {
		h.collectAndAddGuessImages(guessData)
	}

	// his 敏捷卡智能经典模式下：敏捷卡插入置顶历史下方，但是优先级最低
	if monitorCard, _ := (hisUniversalTplData)["monitor-card"].(map[string]interface{}); len(monitorCard) > 0 && len(guessData) > 0 {
		monitorIdx := getMonitorInsertIdx(guessData)
		if monitorIdx >= 0 {
			if monitorIdx >= len(guessData) {
				guessData = append(guessData, monitorCard)
			} else {
				guessData = append(guessData[:monitorIdx], append([]interface{}{monitorCard}, guessData[monitorIdx:]...)...)
			}
		}
	}

	// 创建新的猜你想搜列表，过滤掉运营词
	newGuessDataList := make([]interface{}, 0, len(guessData))
	removed := false

	aiToolIcons := make(map[string]map[string]string)
	if v, ok := h.ctx.Get("aiToolIcons"); ok {
		aiToolIcons, _ = v.(map[string]map[string]string)
	}
	for _, data := range guessData {
		guessMap, ok := data.(map[string]interface{})
		if !ok {
			newGuessDataList = append(newGuessDataList, data)
			continue
		}

		sa, ok := guessMap["sa"].(string)
		if !ok {
			newGuessDataList = append(newGuessDataList, data)
			continue
		}

		// 跳过运营词
		if strings.Contains(sa, "igh_12345_gyy") {
			removed = true
			continue
		}
		if v, ok := guessMap["tpl"].(map[string]interface{}); ok {
			if common.ToInt(v["type"]) == 2 {
				toolType, _ := v["ai_tool_type"].(string)
				if icon3, ok := aiToolIcons[toolType]; ok {
					for iconKey, iconURL := range icon3 {
						v[iconKey] = iconURL
					}
				} else {
					// 这个 key 如果不存在的话直接跳过这个猜搜，因为aiToolIcons 中的 key 是对齐该客户端版本
					continue
				}
			}
		}
		// 保留非运营词
		newGuessDataList = append(newGuessDataList, data)
	}

	// 更新猜你想搜数据
	guess["data"] = newGuessDataList

	return removed
}

// 初始化对端协议的结构
func (this *His_Universal) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:     "0",
		Errmsg:    "",
		Data:      datainfo,
		Switch:    common.SwitchConfMem.Switch,
		Requestid: this.getLogidStr(),
	}
	return ret
}

func (this *His_Universal) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *His_Universal) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Universal{}
	target.ctx = ctx
	return target, nil
}

func getHisListData(hisUniversalTplData map[string]interface{}) (*HisListResponse, string) {
	data, ok := hisUniversalTplData["list"].(HisListResponse)
	if !ok {
		return nil, "hisUniversalTplData.list assert failed"
	}

	return &data, ""
}

// 获取猜你想搜列表from hisUniversalTplData
func getGussData(hisUniversalTplData map[string]interface{}) (map[string]interface{}, string) {
	// 猜你想搜
	var recSession HisResponse
	var ok bool
	if hisUniversalTplData["rec_session"] != nil {
		recSession, ok = hisUniversalTplData["rec_session"].(HisResponse)
		if !ok {
			return nil, "rec_session assert failed"
		}
	} else if hisUniversalTplData["rec"] != nil {
		recSession, ok = hisUniversalTplData["rec"].(HisResponse)
		if !ok {
			return nil, "rec assert failed"
		}
	} else {
		return nil, "HisResponse is nil"
	}
	data, ok := recSession.Data.(map[string]interface{})
	if !ok {
		return nil, "recSession.Data assert failed"
	}
	// guss解析
	guss, ok := data["guess"].(map[string]interface{})
	if !ok {
		return nil, "data.guess assert failed"
	}

	return guss, "ok"
}

// ExtractAiToolTypes 从历史推荐数据中提取AI工具类型
// 参数:
//   - hisUniversalTplData: 历史通用模板数据
//
// 返回值:
//   - []string: AI工具类型列表
func ExtractAiToolTypes(hisUniversalTplData map[string]interface{}) []string {
	// 初始化为空切片而不是nil
	aiToolTypes := make([]string, 0)

	// 处理nil输入
	if hisUniversalTplData == nil {
		return aiToolTypes
	}

	if rec, ok := hisUniversalTplData["rec"]; ok {
		if recData, ok := rec.(HisResponse); ok {
			if recMap, ok := recData.Data.(map[string]interface{}); ok {
				aiTool, _ := recMap["ai_tool"].(map[string]interface{})
				d, _ := aiTool["data"].([]interface{})
				for _, v := range d {
					if one, ok := v.(map[string]interface{}); ok {
						if toolType, ok := one["type"].(string); ok {
							aiToolTypes = append(aiToolTypes, toolType)
						}
					}
				}
			}
		}
	}

	return aiToolTypes
}

func getMonitorInsertIdx(guessDataList []interface{}) int {
	idx := -1
	for i, item := range guessDataList {
		// 通过is_his字段判断是否为历史记录
		guessMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}
		isHis, exit := guessMap["is_his"].(float64)
		if !exit || isHis != 1 {
			idx = i
			break
		}
	}
	return idx
}

// collectAndAddGuessImages 收集查询词并添加图片到猜你想搜数据中
func (h *His_Universal) collectAndAddGuessImages(guessDataList []interface{}) {
	if len(guessDataList) == 0 {
		return
	}

	// 收集查询词和原始数据的映射
	querySlice := make([]string, 0, len(guessDataList))
	queryToDataMap := make(map[string]map[string]interface{})

	for _, item := range guessDataList {
		guessMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		// 优先使用prompt字段，如果没有则使用text字段
		var query string
		if prompt, exists := guessMap["prompt"]; exists && guessMap["is_his"] != 1.0 {
			if promptStr, ok := prompt.(string); ok && promptStr != "" {
				query = promptStr
			}
		}

		// 如果prompt为空或不存在，使用text字段
		if query == "" {
			if text, exists := guessMap["text"]; exists && guessMap["is_his"] != 1.0 {
				if textStr, ok := text.(string); ok && textStr != "" {
					query = textStr
				}
			}
		}

		// 如果找到了查询词，添加到收集列表中
		if query != "" {
			querySlice = append(querySlice, query)
			queryToDataMap[query] = guessMap
		}
	}

	// 如果没有收集到查询词，直接返回
	if len(querySlice) == 0 {
		h.addNotice("guess_image_err", "1")
		return
	}

	// 调用GuessService获取图片
	requestParams := entity.GetRequestParams(h.ctx)
	if requestParams == nil {
		h.addNotice("guess_image_err", "2")
		return
	}

	guessService := service.NewGuessService(h.ctx, requestParams)
	imageMap, err := guessService.GetGuessImage(querySlice)
	if err != nil {
		h.addNotice("guess_image_err", "3")
		return
	}

	// 记录获取到的图片数量
	h.addNotice("guess_image_rate", strconv.FormatFloat(float64(float64(len(imageMap))/float64(len(querySlice))), 'f', -1, 64))

	// 将图片添加到对应的数据项中
	for query, imageURL := range imageMap {
		if dataMap, exists := queryToDataMap[query]; exists {
			// 添加tpl字段，包含图片信息
			dataMap["tpl"] = map[string]interface{}{
				"type":  1,
				"image": imageURL,
			}
		}
	}
}

func init() {
	Register("his-universal", &His_Universal{})
}
