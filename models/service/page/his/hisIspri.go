package his

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Ispri struct {
	BasePage
}

type priGetDataType struct {
	Timestamp string `json:"timestamp"`
	Target    string `json:"target"`
	Token     string `json:"token"`
}

type HisIspriResponse struct {
	Errno  string      `json:"errno"`
	Errmsg string      `json:"errmsg"`
	Data   interface{} `json:"data"`
}

//主要执行的函数入口
func (this *His_Ispri) Execute() {
	//此接口在框启动时调用 增流量降级策略
	this.addNotice("sugnew", "1")
	tplData := this.defaultTpl()
	request := this.getRequest()

	if !common.IsLogin(request) {
		tplData.Errno = "1001"
		tplData.Errmsg = "params cookie is missing"
		this.response(tplData)
		return
	}
	uid := (*request)[constant.SESSION_UID]

	target := "sugStoreSet"
	dataquery := (*request)["data"]
	var reqdata priGetDataType
	this.addNotice("dataquery", dataquery)
	if dataquery != "" {
		decodeErr := json.Unmarshal([]byte(dataquery), &reqdata)
		if decodeErr == nil && reqdata.Target != "" {
			target = reqdata.Target
		}
		if decodeErr != nil { //json格式不符合预期
			this.addNotice("decodeErr", decodeErr.Error())
		}
	}
	this.addNotice("target", target)
	dataSrv := data.NewHISPrive(this.ctx)
	// 校验token字段
	if reqdata.Target != "" {
		checkRet := dataSrv.CheckToken(reqdata.Timestamp, reqdata.Target, reqdata.Token)
		if !checkRet {
			tplData.Errno = "1002"
			tplData.Errmsg = "token check failed"
			this.response(tplData)
			return
		}
	}
	if ralGetErr := dataSrv.GetHISResponse(uid, target); ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		tplData.Errno = "4002"
		tplData.Errmsg = "ral request fail"
		this.response(tplData)
		return
	}
	resData := dataSrv.GetHISTplData()
	if resData.Status == "" || resData.Status == nil {
		tplData.Data = []interface{}{}
	} else {
		tplData.Data = resData
	}

	this.response(tplData)
	return
}

//初始化对端协议的结构
func (this *His_Ispri) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *His_Ispri) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *His_Ispri) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Ispri{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-ispri", &His_Ispri{})
}
