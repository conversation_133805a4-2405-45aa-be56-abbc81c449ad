package his

import (
	"fmt"
	"reflect"
	"strconv"
	"testing"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"

	"github.com/agiledragon/gomonkey/v2"
	"gopkg.in/go-playground/assert.v1"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHis_Rec_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_Rec{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Rec_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_Rec_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &His_Rec{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestHis_Rec_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_Rec{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Rec_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Rec_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}

func TestHis_Rec_parseRALResData(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		resDataArr map[string]data.TypeDispaly
		tpl        HisResponse
		dataParams data.DataParms
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}

	searchService1 := map[string]interface{}{
		"search_service": map[string]interface{}{
			"data": []interface{}{
				map[string]interface{}{
					"icon":      "http://baidu.png",
					"sa":        "osese_1",
					"show_text": "天气",
					"text":      "2024天气",
					"url":       "https://m.baidu.com/s?word=2024天气",
				},
			},
		},
	}

	searchService2 := map[string]interface{}{
		"search_service": map[string]interface{}{
			"data": []interface{}{
				map[string]interface{}{
					"icon":      "http://baidu.png",
					"sa":        "osese_1",
					"show_text": "天气",
					"text":      "2024天气",
					"url":       "https://m.baidu.com/s?word=2024天气",
				},
				map[string]interface{}{
					"icon":      "http://baidu.png",
					"sa":        "osese_2",
					"show_text": "天气",
					"text":      "2024天气",
					"url":       "https://m.baidu.com/s?word=2024天气",
				},
				map[string]interface{}{
					"icon":      "http://baidu.png",
					"sa":        "osese_3",
					"show_text": "天气",
					"text":      "2024天气",
					"url":       "https://m.baidu.com/s?word=2024天气",
				},
				map[string]interface{}{
					"icon":      "http://baidu.png",
					"sa":        "osese_4",
					"show_text": "天气",
					"text":      "2024天气",
					"url":       "https://m.baidu.com/s?word=2024天气",
				},
			},
		},
	}

	searchService3 := map[string]interface{}{
		"under_box": map[string]interface{}{
			"data": []interface{}{
				map[string]interface{}{},
			},
		},
		"box": map[string]interface{}{
			"data": []interface{}{
				map[string]interface{}{},
			},
		},
		"guess": map[string]interface{}{
			"data": []interface{}{
				map[string]interface{}{},
			},
		},
		"ai_tool": nil,
		"rsf":     "test_rsf_value_502",
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want
		wantErr       bool
	}{
		{
			"test_parseRALResData_for_1000535_1",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{"1000535": data.TypeDispaly{
					Retno: 0,
					Data:  searchService1,
				}},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: make(map[string]interface{}),
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
		{
			"test_parseRALResData_for_1000535_2",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{"1000535": data.TypeDispaly{
					Retno: 0,
					Data:  searchService2,
				}},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: searchService2,
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
		{
			"test_parseRALResData_for_1000513_1",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000513": data.TypeDispaly{
						Retno: 0,
						Data:  searchService3,
					},
					"1000502": data.TypeDispaly{
						Retno: 0,
						Data:  searchService3,
					},
					"1000503": data.TypeDispaly{
						Retno: 0,
						Data:  searchService3,
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: searchService3,
				},
				Assert: ut.ShouldResemble,
			},
			true,
		},
		{
			"测试返回了1000539",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000539": {
						Retno: 0,
						Data: map[string]interface{}{
							"activity_config": map[string]interface{}{
								"data": map[string]interface{}{
									"activity_config_id": 1,
									"method":             1,
								},
							},
						},
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: map[string]interface{}{
						"activity_config": map[string]interface{}{
							"data": map[string]interface{}{
								"activity_config_id": 1,
								"method":             1,
							},
						},
					},
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
		{
			"测试1000535 pk掉 1000539",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000539": {
						Retno: 0,
						Data: map[string]interface{}{
							"activity_config": map[string]interface{}{
								"data": map[string]interface{}{
									"activity_config_id": 1,
									"method":             1,
								},
							},
						},
					},
					"1000535": {
						Retno: 0,
						Data:  searchService2,
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: map[string]interface{}{
						"activity_config": map[string]interface{}{
							"data": map[string]interface{}{
								"activity_config_id": 1,
								"method":             1,
							},
						},
					},
				},
				Assert: ut.ShouldNotResemble,
			},
			false,
		},
		{
			"测试 strategy_intelligent_mode 字段存在时应复制到 tplData",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000502": {
						Retno: 0,
						Data: map[string]interface{}{
							"rsf": "test_rsf_value",
							"strategy_intelligent_mode": map[string]interface{}{
								"enabled": true,
								"level":   "high",
							},
							"box": map[string]interface{}{
								"data": []interface{}{},
							},
						},
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: map[string]interface{}{
						"rsf": "test_rsf_value",
						"strategy_intelligent_mode": map[string]interface{}{
							"enabled": true,
							"level":   "high",
						},
						"box": map[string]interface{}{
							"data": []interface{}{},
						},
					},
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
		{
			"测试 strategy_intelligent_mode 字段不存在时不应复制到 tplData",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000502": {
						Retno: 0,
						Data: map[string]interface{}{
							"rsf": "test_rsf_value",
							"box": map[string]interface{}{
								"data": []interface{}{},
							},
						},
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: map[string]interface{}{
						"rsf": "test_rsf_value",
						"box": map[string]interface{}{
							"data": []interface{}{},
						},
					},
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
		{
			"测试 strategy_intelligent_mode 字段为空值时不应复制到 tplData",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000503": {
						Retno: 0,
						Data: map[string]interface{}{
							"rsf":                       "test_rsf_value",
							"strategy_intelligent_mode": nil,
							"guess": map[string]interface{}{
								"data": []interface{}{},
							},
						},
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: map[string]interface{}{
						"rsf": "test_rsf_value",
						"guess": map[string]interface{}{
							"data": []interface{}{},
						},
						"ai_tool": nil,
					},
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
		{
			"100502（空框接口才会带有strategy_intelligent_mode）",
			fields{
				basePage,
			},
			args{
				resDataArr: map[string]data.TypeDispaly{
					"1000502": {
						Retno: 0,
						Data: map[string]interface{}{
							"rsf":                       "test_rsf_value",
							"strategy_intelligent_mode": 0,
							"box": map[string]interface{}{
								"data": []interface{}{},
							},
						},
					},
					"1000503": {
						Retno: 0,
						Data: map[string]interface{}{
							"rsf":                       "test_rsf_value",
							"strategy_intelligent_mode": 1,
							"guess": map[string]interface{}{
								"data": []interface{}{},
							},
						},
					},
				},
				tpl:        HisResponse{},
				dataParams: data.DataParms{},
			},
			Want{
				Value: HisResponse{
					Data: map[string]interface{}{
						"rsf":                       "test_rsf_value",
						"strategy_intelligent_mode": 0, // 应该选择值为1000502的
						"box": map[string]interface{}{
							"data": []interface{}{},
						},
						"guess": map[string]interface{}{
							"data": []interface{}{},
						},
						"ai_tool": nil,
					},
				},
				Assert: ut.ShouldResemble,
			},
			false,
		},
	}

	for k, tt := range tests {
		this := &His_Rec{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.parseRALResData(tt.args.resDataArr, tt.args.tpl, tt.args.dataParams)
		if err != nil {
			ag.Add(fmt.Sprintf("Test Case Of TestHis_Rec_parseRALResData, Result Index:%d Error Value Compare", k), err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}

		// 对于包含多个资源号的测试用例，需要特殊处理 rsf 字段的断言

		// 其他测试用例使用原来的完全匹配断言
		ag.Add(fmt.Sprintf(" Test Case Of TestHis_Rec_parseRALResData, Result Index:%d Value Compare", k), got, tt.want.Assert, tt.want.Value.(HisResponse))

	}
	ag.Run()

}

func TestModifyGuessData(t *testing.T) {
	h := &His_Rec{}
	h.ctx = createGetWebContext()

	dataParams := data.DataParms{
		RecMaxReturn: 3,
	}
	data := map[string]interface{}{}
	data["guess"] = map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "igh_12345_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "igh_12345_gghis",
				"text": "2024天气3",
			},
			map[string]interface{}{
				"sa":   "igh_12345_ggush_rr",
				"text": "2024",
			},
		},
	}

	h.modifyGuessData(data, dataParams)
	assert.Equal(t, data["guess"], map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "re_dl_se_error_rec_1_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_rec_2_gghis",
				"text": "2024天气3",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_rec_3_ggush_rr",
				"text": "2024",
			},
		},
	})

	data["guess"] = map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "igh_12345_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "igh_12345_gghis",
				"text": "2024天气3",
			},
		},
	}
	dataParams.Eventtype = "cpage_block"
	h.modifyGuessData(data, dataParams)
	assert.Equal(t, data["guess"], map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "re_dl_se_error_block_rec_1_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_block_rec_2_gghis",
				"text": "2024天气3",
			},
		},
	})

	// 修改sa
	data["guess"] = map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "igh_12345_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "igh_12345_gghis",
				"text": "2024天气3",
			},
		},
	}
	dataParams.Eventtype = "cpage_dead"
	h.modifyGuessData(data, dataParams)
	assert.Equal(t, data["guess"], map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "re_dl_se_error_dead_rec_1_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_dead_rec_2_gghis",
				"text": "2024天气3",
			},
		},
	})

	// 修改sa
	data["guess"] = map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "igh_12345_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "igh_12345_gghis",
				"text": "2024天气3",
			},
		},
	}
	dataParams.Eventtype = "cpage_medium_risk"
	h.modifyGuessData(data, dataParams)
	assert.Equal(t, data["guess"], map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "re_dl_se_error_medium_risk_rec_1_rr",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_medium_risk_rec_2_gghis",
				"text": "2024天气3",
			},
		},
	})

	dataParams.Eventtype = "novel_detail"
	h.modifyGuessData(data, dataParams)

	assert.Equal(t, data["guess"], map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "re_dl_se_error_novel_detail_rec_1",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_novel_detail_rec_2",
				"text": "2024天气3",
			},
		},
	})

	dataParams.Eventtype = "novel_reader"
	h.modifyGuessData(data, dataParams)
	assert.Equal(t, data["guess"], map[string]interface{}{
		"data": []interface{}{
			map[string]interface{}{
				"sa":   "re_dl_se_error_novel_reader_rec_1",
				"text": "2024天气12",
			},
			map[string]interface{}{
				"sa":   "re_dl_se_error_novel_reader_rec_2",
				"text": "2024天气3",
			},
		},
	})
	// guess数据为空时
	data = map[string]interface{}{}
	h.modifyGuessData(data, dataParams)
	assert.Equal(t, data, map[string]interface{}{})

}

func TestHisRecExecute(t *testing.T) {
	_ = t
	// Create a patcher to simplify patch cleanup
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Create test data
	ctx := createGetWebContext()
	target := &His_Rec{}
	target.ctx = ctx

	// Run the function under test
	target.Execute()

	patches.ApplyFuncReturn((*His_Rec).validateAndPrepareDataParams, &data.DataParms{})
	target.Execute()

}

func TestHisRecExecuteByStep(t *testing.T) {
	// Create a patcher to simplify patch cleanup
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Create test data
	ctx := createGetWebContext()
	target := &His_Rec{}
	target.ctx = ctx

	// Run the function under test
	target.ExecuteByStep()

	// Validate that the expected outcome occurred
	assert.Equal(t, "1", target.getLogidStr())
	dataSrv := &data.Rec{}
	patches.ApplyMethod(reflect.TypeOf(dataSrv), "BuildRequest",
		func(_ *data.Rec, request *map[string]string, adaptions *map[string]string, dataParams data.DataParms) ([]data.NamedHTTPReq, error) {
			return []data.NamedHTTPReq{
				{
					Name:        "mock-name",
					HTTPRequest: &protocol.HTTPRequest{},
				},
			}, nil

		})
	patches.ApplyFuncReturn(ral.Ral, nil)
	patches.ApplyFuncReturn((*His_Rec).validateAndPrepareDataParams, &data.DataParms{})
	f1, f2 := target.ExecuteByStep()
	f1()
	f2()
	patches.ApplyFuncReturn((*data.Rec).ParseHISResponse, nil)
	f1, f2 = target.ExecuteByStep()
	f1()
	f2()
}

func TestModifyActivity(t *testing.T) {
	dataMap := map[string]interface{}{
		"jump_type":                       1.0,
		"topic_day_img":                   "https://search-operate.cdn.bcebos.com/d6e79bcc24d6de451d06d69670c00ba3.png",
		"topic_night_img":                 "https://search-operate.cdn.bcebos.com/d6e79bcc24d6de451d06d69670c00ba3.png",
		"backlayer_gradient_angle":        270,
		"backlayer_gradient_point":        "0,0,0,1",
		"topicimg_day_top_backcolor":      "#FFFF2F5C",
		"topicimg_day_bottom_backcolor":   "#FFFF5D82",
		"topicimg_night_top_backcolor":    "#FFFF2F5E",
		"topicimg_night_bottom_backcolor": "#FFFF5D85",
	}
	dataMap["homepage_url"] = "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fsearchframe%3Fyyfrom%3D24gaok" +
		"ao_game&newbrowser=1&forbidautorotate=1&notShowLandingTopBar=1&bottomBarType=4"
	adptparams := map[string]string{
		"isAndroid": "1",
	}

	// method 不存在，返回false
	ret, ok := modifyActivity(dataMap, &adptparams)
	assert.Equal(t, ok, false)
	assert.Equal(t, ret, nil)

	// jump_type=1，不修改
	dataMap["method"] = 1
	ret, ok = modifyActivity(dataMap, &adptparams)
	url := ret["data"].(map[string]interface{})["homepage_url"]
	assert.Equal(t, dataMap["homepage_url"], url)
	assert.Equal(t, ok, true)

	// jump_type=2，修改url
	dataMap["jump_type"] = 2.0
	ret, ok = modifyActivity(dataMap, &adptparams)
	url = ret["data"].(map[string]interface{})["homepage_url"]
	assert.Equal(t, "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fsearchframe%3Fyyfrom%"+
		"3D24gaokao_game&newbrowser=1&forbidautorotate=1&notShowLandingTopBar=1&bottomBarType=4&activityLoadi"+
		"ngConfig=%7B%22loadingBgEndColor%22%3A%22%23FFFF5D82%22%2C%22loadingBgGradientPoint%22%3A%220%2C0%2C0%2C1%22%2C%22"+
		"loadingBgNightEndColor%22%3A%22%23FFFF5D85%22%2C%22loadingBgNightStartColor%22%3A%22%23FFFF2F5E%22%2C%22load"+
		"ingBgStartColor%22%3A%22%23FFFF2F5C%22%2C%22loadingIconUrl%22%3A%22https%3A%2F%2Fsearch-operate.cdn.bcebos.com"+
		"%2Fd6e79bcc24d6de451d06d69670c00ba3.png%22%2C%22loadingNightIconUrl%22%3A%22https%3A%2F%2Fsearch-operate.cdn.bce"+
		"bos.com%2Fd6e79bcc24d6de451d06d69670c00ba3.png%22%7D", url)
	assert.Equal(t, ok, true)
}

func TestBlackListFilter(t *testing.T) {
	// -------------------------------
	// 1. 使用 mock 对象设置测试环境
	// -------------------------------
	// 创建 mock 实例并设置为全局变量
	mockRongYao := background.SetupMockRongYaoForTest()

	// 设置黑名单数据
	mockRongYao.AddAccurate("forbidden")
	mockRongYao.AddFuzzy("evil")

	// -------------------------------
	// 2. 构造测试用的 His_Rec 实例和测试数据
	// -------------------------------
	// 模拟请求，设置 constant.RongyaoBaipai 对应的值为 "1"，表示需要过滤
	ctx := createGetWebContext()
	ctx.Set(constant.REQPARAMS, map[string]string{
		constant.RongyaoBaipai: "1",
	})
	hisRec := &His_Rec{BasePage{ctx: ctx}}

	// 构造输入数据，data 中包含多种情况：
	// - 第一项：text 为 "forbidden"，应被精确匹配过滤
	// - 第二项：text 包含 "evil"，应被模糊匹配过滤
	// - 第三项：text 为 "good"，不在黑名单中，应保留
	// - 第四项：不包含 text 字段，应保留
	data := []interface{}{
		map[string]interface{}{"text": "forbidden"},
		map[string]interface{}{"text": "this text has evil content"},
		map[string]interface{}{"text": "good"},
		map[string]interface{}{"no_text": "value"},
	}

	// -------------------------------
	// 3. 调用过滤函数并验证结果
	// -------------------------------
	filtered := hisRec.BlackListFilter(data)

	// 预期结果：第一项和第二项被过滤掉，剩下第三、第四项
	expected := []interface{}{
		map[string]interface{}{"text": "good"},
		map[string]interface{}{"no_text": "value"},
	}

	assert.Equal(t, expected, filtered)
}

// 测试 parseAndValidateRequestData 函数
func TestHis_Rec_parseAndValidateRequestData(t *testing.T) {
	ctx := createGetWebContext()
	hisRec := &His_Rec{BasePage{ctx: ctx}}
	hisListErr := HisListErr{}

	// 测试正常情况
	request := map[string]string{}
	validJSON := `{
  "event_his_from" : "home",
  "box_show_query" : "搜索或输入网址",
  "event_type" : "hissug",
  "entrance_sa" : "ho",
  "aitool_ver" : "{\"aiTour\":5,\"aiQues\":0,\"aiCall\":0,\"aiWrite\":3,\"picArt\":0,\"aiDraw\":4,\"aiChat\":0,\"music\":0,\"aiVideo\":0}",
  "private_mode" : 0,
  "vid_ctl" : "{\"rec_n\":4,\"high_n\":1,\"ins_guess\":1}",
  "show_guess" : 1,
  "guess_show_rownum" : 0,
  "guessdata" : "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",
  "event_from" : "default",
  "target" : "[\n  \"rec\"\n]",
  "event_channel" : "default",
  "boxdata" : "y_VSk8_AVk6OMV6efJVPkYR62IaPMV9o6MATzf4qw6t9MVfuqI7A96MqV9qo079S6MVq96MqV9N3rFjmyrxRqIMEVf6AaX0P6MVq96MqV9qgywz7y5m_qNrFxo9NrVk3YRsN8z_AV96NMV9q6MeuYfyoFYyt5mqiNrxKo9rzVkYNRv85_u28ijuMvigou2lM6MVq96Rgb06SMV9qyh7Pk6M6V9fSyVkQYMeO6IpLDq925GgjNNAXj9yZVIa0MV9o6MxTz8rkV9YqMVWsGSP8TxBZp7Z_QyQVGSaWcbB09UcuE4WTSEVm06R1bIa6Me_uaMVq96MGmY9zMVfC6aXk06MqV96g5E_ztMVQt6MhOnxY-NsT7G5AHxE4PdGTBIKIcx9WQES8PXL3-feNcL8k9z_AV96NMeYAIMVQf6ROmzkDIm6VflGoz44rFqIXM793q0X906Rob06SMV9q6pxn66MQ-9_Ch79S6MVo9fyNbY6IM-9QqB9yUK1sNQW0SPcyS-fUvcEGI9zZ_V9g6MeuYIMOVf6QRmzlkIm-6flqGz4d4FqBIM7A9q0tX06SRb0A6MVq96ppx66PM-9Q_h7P96MqV9f3ybYf6M-O9q-sy7KbH4QTSLaDZxBy0X_luPVzAuBjWWF-OvDrMXlM6MVq9q5OE96mPV9OkymDCkJpx_i54LoVfrwzkaMSe_za_V9g8_VSk6MQV6f1JVk3YM2gIaMSV96uMxzI8rVP9YMoeHWCFPBVQBN9ecHFNQD-3O2MLBI9vcEz4WxtbPdFH-JVeKLjrWDyDVIZaMVq96Rmxz6qM-9QqpEl6ypBwji0lACeN5FKq6_EV98Ch7k3q07A96MqV9k35w9M6PVS8z_OV96NMVYKI0Fkk6PPV9KS3yQ4GWOV2QBy0UcpH5QpD3O72LB7yTcd1NQTmxOnsmBnNdcKF9kbz_VS96MzeYIQMVfO6RmLzkI4m6fHlGzC44xFj9Mw79qg0X0P6Rbu06MqV96jpx696M-O9_hZ796qMV9Ofyb_Y6Mz-9qCBlUaKxrzicmPIQFUmOvyLB8P7cxANQw0SOeymB8bccGIJQbU1VIKaMVq96Rmxz6qM-9QqpEl6ypBwji0lACeNrwvo6_EV98Ch7k3q07A96MqV9k35w9M6PVS8z_OV96NMVYKI0Fkk6PPV9c33tQnDFP02m-Tlcc4L9QJQbOlXS-tlVchbiS3bFPlKS-T4e61h79Q6MVo9fyoVkYOMe6QIpD4q95cGgN4NAjw9yVa06R1bIa6Me_uaMVq96MGmY9zMVfC6aXk06MqV96g5E_ztMVQt6MPuVDL-8vccxlFQLDM28_qaPnPL-y-eKL7NQw8maXqH25l7c1otkzZ_V9g6MeuYIMOVf6QRmzlkIm-6flqGz4d4xjd9M7A9q0fX06SRb0A6MVq96ppx66PM-9Q_h7P96MqV9f3ybYf6M-O9qBsr7cRFJQ2FGOUeQ-9iZKGx4Qfw3PUTEa02iqgBNdhcF95kz_EV96NMeYAIMVQf6ROmzkDIm6VflGoz44rxj9rM79Sq0X906Rob06SMV9q6pxn66MQ-9_Ch79S6MVo9fyNbY6IM-9QqiWqHHLeaZxB-fv0c1fgW3GnOs1y-9BbZb9k9z_OV96NMeYAIMVQf6ROmzkDIm6VflGoz44rFjI5M7kLq0X906Rob06SMV9q6pxn66MQ-9_Ch79S6MVo9fyNbY6IM-9Qq-t_UcExrWFcmP-U3B4IdcDzkSFRWPThL-NQV6hZ796OMV9OfyVOkYMQe6IOpDq-95G6gNN-AjfGyV0m6RbfIaMqe_aAMV9o6MmvY9MOVf6KaX0P6MVq965fE_tuMVtC6MPKnwBppUcwEyWxESPyXQ-f02cIL8HzmwP4VwPS-icoA4WnADVxNaM6V96uRxzS6M-O9qpLE6yRpwjUilARCNrRwo6r_V9o8h7Pkq0a796qMV9Ok5w996PEV8zo_V9g6MVCYI0gFk6ZPV9OKSiTWwmDOZQ--fd8Kx4DQD1yPVFevJst_a2qicAG4WDU3OX8EMXZl6MSV9qo5E9u6PVQ9kylmCkDJx_Yi4LBofrNwkaAMe_kJ2qOC",
  "event_box" : "default"
}`
	request[constant.SESSION_UID] = "test_uid"

	result, err := hisRec.parseAndValidateRequestData(validJSON, &request, hisListErr)
	if result == nil {
		t.Error("Expected non-nil result for valid data")
		return
	}

	t.Logf("=== NEW DATA TEST RESULTS ===")
	t.Logf("Error: %v", err)
	if result != nil {
		t.Logf("Eventtype: %v", result.Eventtype)
		t.Logf("Eventbox: %v", result.Eventbox)
		t.Logf("Eventquery: %v", result.Eventquery)
		t.Logf("Eventchannel: %v", result.Eventchannel)
		t.Logf("Eventfrom: %v", result.Eventfrom)
		t.Logf("PrivateMode: %v", result.PrivateMode)
		t.Logf("ClientName: %v", result.ClientName)
		t.Logf("EventHisFrom: %v", result.EventHisFrom)
		t.Logf("BoxShowQuery: %v", result.BoxShowQuery)
		t.Logf("TodayFirst: %v", result.TodayFirst)
		t.Logf("DisplayWidth: %v", result.DisplayWidth)
		t.Logf("RecVideoInfo: %v", result.RecVideoInfo)
		t.Logf("ColdLaunchTime: %v", result.ColdLaunchTime)
		t.Logf("RecMaxReturn: %v", result.RecMaxReturn)
		t.Logf("TextCount: %v", result.TextCount)
		t.Logf("PrivacyString: %v", result.PrivacyString)
		t.Logf("Puid: %v", result.Puid)
		t.Logf("Guessshowrownum: %v", result.Guessshowrownum)
		t.Logf("Guessfeedbackquery length: %v", len(result.Guessfeedbackquery))
		if len(result.Guessfeedbackquery) > 0 {
			t.Logf("Guessfeedbackquery: %v", result.Guessfeedbackquery)
		}
		t.Logf("Sids length: %v", len(result.Sids))
		if len(result.Sids) > 0 {
			t.Logf("Sids: %v", result.Sids)
		}
		t.Logf("Hisdata length: %v", len(result.Hisdata))
		t.Logf("HisSearchData length: %v", len(result.HisSearchData))
		t.Logf("Boxdata length: %v", len(result.Boxdata))
		t.Logf("Showquerys length: %v", len(result.Showquerys))
		t.Logf("UnderBox length: %v", len(result.UnderBox))
		t.Logf("ContentInfo: %v", result.ContentInfo)
		t.Logf("VidCtl: %v", result.VidCtl)
		t.Logf("NeedNewUserHis: %v", result.NeedNewUserHis)

		// 基于实际数据编写断言
		if result.Eventtype != "hissug" {
			t.Errorf("Expected Eventtype to be 'hissug', got '%v'", result.Eventtype)
		}
		if result.Eventbox != "default" {
			t.Errorf("Expected Eventbox to be 'default', got '%v'", result.Eventbox)
		}
		if result.EventHisFrom != "home" {
			t.Errorf("Expected EventHisFrom to be 'home', got '%v'", result.EventHisFrom)
		}
		if result.BoxShowQuery != "搜索或输入网址" {
			t.Errorf("Expected BoxShowQuery to be '搜索或输入网址', got '%v'", result.BoxShowQuery)
		}
		if result.Eventfrom != "default" {
			t.Errorf("Expected Eventfrom to be 'default', got '%v'", result.Eventfrom)
		}
		if result.Eventchannel != "default" {
			t.Errorf("Expected Eventchannel to be 'default', got '%v'", result.Eventchannel)
		}
		if result.PrivateMode != 0 {
			t.Errorf("Expected PrivateMode to be 0, got %v", result.PrivateMode)
		}
		if result.Guessshowrownum != 0 {
			t.Errorf("Expected Guessshowrownum to be 0, got %v", result.Guessshowrownum)
		}
		if result.Puid != "test_uid" {
			t.Errorf("Expected Puid to be 'test_uid', got '%v'", result.Puid)
		}
		// 验证 VidCtl 解析
		if len(result.VidCtl) == 0 {
			t.Error("Expected VidCtl to be parsed")
		} else {
			if result.VidCtl["rec_n"] != 4 {
				t.Errorf("Expected VidCtl rec_n to be 4, got %v", result.VidCtl["rec_n"])
			}
			if result.VidCtl["high_n"] != 1 {
				t.Errorf("Expected VidCtl high_n to be 1, got %v", result.VidCtl["high_n"])
			}
			if result.VidCtl["ins_guess"] != 1 {
				t.Errorf("Expected VidCtl ins_guess to be 1, got %v", result.VidCtl["ins_guess"])
			}
		}
		// 验证数据解码
		if len(result.Guessdata) == 0 {
			t.Error("Expected Guessdata to be decoded")
		}
		if len(result.Boxdata) == 0 {
			t.Error("Expected Boxdata to be decoded")
		}
	} else {
		t.Logf("Result is nil")
	}
	t.Logf("=== END NEW DATA TEST RESULTS ===")

	// 测试简单的错误情况
	_, err = hisRec.parseAndValidateRequestData("invalid json", &request, hisListErr)
	if err == nil {
		t.Log("JSON parse error test - function may handle errors internally")
	}
}
