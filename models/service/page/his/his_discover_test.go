package his

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestHisDiscover_Execute(t *testing.T) {
	ctx := createGetWebContext()
	requestParams := &(map[string]string{})
	(*requestParams)["data"] = `{"query":"人们"}`
	(*requestParams)["typeid"] = "1"
	ctx.Set(constant.REQPARAMS, *requestParams)
	basePage := BasePage{}
	basePage.ctx = ctx

	server := HisDiscover{
		BasePage: basePage,
	}
	pager, err := server.newSelf(ctx)
	assert.Nil(t, err)
	pager.Execute()
	assert.Equal(t, pager.GetTplOriginData().(HisResponse).Errno, "0")

}
