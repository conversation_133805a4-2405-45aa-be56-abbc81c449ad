package his

import (
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHisBoardExecute(t *testing.T) {
	// Create a patcher to simplify patch cleanup
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Create test data
	ctx := createGetWebContext()
	requestParams := &(map[string]string{})
	(*requestParams)[constant.OSBRANCH] = "a0"
	adaptionParams := &(map[string]string{})
	(*adaptionParams)["bd_version"] = "13.71.0.10"
	ctx.Set(constant.REQPARAMS, *requestParams)
	ctx.Set(constant.ADAPARAMS, *adaptionParams)
	target := &His_Board{}
	target.ctx = ctx

	// Run the function under test
	target.Execute()

	// Validate that the expected outcome occurred
	assert.Equal(t, "1", target.getLogidStr())

	patches.ApplyMethod(reflect.TypeOf(&data.BoardRec{}), "GetResponse",
		func(_ *data.BoardRec, request *map[string]string, adaptions *map[string]string, uid string) error {
			return nil
		})
	patches.ApplyMethod(reflect.TypeOf(&data.BoardRec{}), "GetHISTplData",
		func(_ *data.BoardRec) []interface{} {
			return []interface{}{
				map[string]interface{}{
					"moreUrl": "123",
				},
			}
		})
	patches.ApplyMethod(reflect.TypeOf(&data.BoardRec{}), "GetBoardChannel",
		func(_ *data.BoardRec) string {
			return "his"
		})
	target.Execute()
	(*adaptionParams)["bd_version"] = "13.87.0.10"
	(*requestParams)["realOsbranch"] = "h0"
	target.Execute()
}

func TestHisBoardExecuteByStep(t *testing.T) {
	// Create a patcher to simplify patch cleanup
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Create test data
	ctx := createGetWebContext()

	target := &His_Board{}
	target.ctx = ctx

	// Mock the GetResponse function to simulate an error

	// Run the function under test
	target.ExecuteByStep()

	// Validate that the expected outcome occurred
	assert.Equal(t, "1", target.getLogidStr())
	dataSrv := &data.BoardRec{}
	patches.ApplyMethod(reflect.TypeOf(dataSrv), "BuildRequest",
		func(_ *data.BoardRec, request *map[string]string, adaptions *map[string]string, uid string) ([]data.NamedHTTPReq, error) {
			return []data.NamedHTTPReq{
				{
					Name:        "mock-name",
					HTTPRequest: &protocol.HTTPRequest{},
				},
			}, nil

		})
	patches.ApplyFuncReturn(ral.Ral, nil)

	f1, f2 := target.ExecuteByStep()
	f1()
	f2()
	patches.ApplyMethod(reflect.TypeOf(dataSrv), "ParseResponse", func(_ *data.BoardRec, ch chan data.ChRawHTTPResp) error {
		return nil
	})
	f1, f2 = target.ExecuteByStep()
	f1()
	f2()

}
func TestProcessBoardRank(t *testing.T) {
	// Create test data
	ctx := createGetWebContext()
	target := &His_Board{}
	target.ctx = ctx

	// Create test data for processBoardRank
	resData := &HisResponse{
		Data: []interface{}{
			map[string]interface{}{
				"board_key": "realtime",
			},
			map[string]interface{}{
				"board_key": "other",
			},
			map[string]interface{}{
				"board_key": "teleplay",
			},
			map[string]interface{}{
				"board_key": "movie",
			},
		},
	}

	// Run the function under test
	target.processBoardRank(resData)

	// Validate that the expected outcome occurred
	expectedData := []interface{}{
		map[string]interface{}{
			"board_key": "realtime",
		},
		map[string]interface{}{
			"board_key": "teleplay",
		},
		map[string]interface{}{
			"board_key": "movie",
		},
		map[string]interface{}{
			"board_key": "other",
		},
	}
	assert.Equal(t, expectedData, resData.Data)
}
