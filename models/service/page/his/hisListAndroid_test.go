package his

import (
	"strconv"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	data "icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
	//middle "icode.baidu.com/baidu/searchbox/go-suggest/middlewares"
)

func TestHisListAndroid_parseRALResData(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		resData data.HisRALResponse
		tpl     HisResponse
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseRALResData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //HisResponse
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HisListAndroid{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.parseRALResData(tt.args.resData, tt.args.tpl)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHisListAndroid_parseRALResData, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHisListAndroid_parseRALResData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHisListAndroid_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisListResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HisListAndroid{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHisListAndroid_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisListResponse))
	}
	ag.Run()
}

func TestHisListAndroid_checkSmallFlow(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: checkSmallFlow ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HisListAndroid{
			BasePage: tt.fields.BasePage,
		}
		got := this.checkSmallFlow()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHisListAndroid_checkSmallFlow, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func TestHisListAndroid_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HisListAndroid{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestHisListAndroid_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HisListAndroid{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHisListAndroid_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHisListAndroid_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}

func TestHisListAndroidExecute(t *testing.T) {
	// Create a patcher to simplify patch cleanup
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Create test data
	ctx := createGetWebContext()
	target := &HisListAndroid{}
	target.ctx = ctx

	// Run the function under test
	target.Execute()

	// Validate that the expected outcome occurred
	assert.Equal(t, "1", target.getLogidStr())
	patches.ApplyFuncReturn(common.IsLogin, true)
	target.Execute()

}

func TestHisListAndroidExecuteByStep(t *testing.T) {
	_ = t
	// Create a patcher to simplify patch cleanup
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Create test data
	ctx := createGetWebContext()
	target := &HisListAndroid{}
	target.ctx = ctx

	// Run the function under test
	target.ExecuteByStep()

	patches.ApplyFuncReturn(common.IsLogin, true)
	patches.ApplyFuncReturn(ral.Ral, nil)
	patches.ApplyFuncReturn((*data.Rec).BuildRequest, []data.NamedHTTPReq{
		{
			Name:        "mock-name",
			HTTPRequest: &protocol.HTTPRequest{},
		},
	}, make(chan data.NamedHTTPReq, 10))

	f1, f2 := target.ExecuteByStep()
	f1()
	f2()
	patches.ApplyFuncReturn((*data.Rec).ParseHISResponse, nil)
	f1, f2 = target.ExecuteByStep()
	f1()
	f2()
}
