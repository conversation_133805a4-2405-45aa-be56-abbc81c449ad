package his

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	data "icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestHis_Sdel_Gzdecode(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		data string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Gzdecode ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	bytestr := []byte{
		117, 123, 8, 0, 0, 0, 0, 0, 0, 19, 171, 86, 42, 44, 77, 45, 170, 84, 178, 82, 122, 190, 175, 239, 229, 226, 249, 74, 181, 0, 209, 27, 75, 83, 18, 0, 0, 0,
	}
	str := string(bytestr)
	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //string
		wantErr       bool
	}{
		{
			"TestHis_Sdel_Gzdecode",
			fields{
				basePage,
			},
			args{
				str,
			},
			Want{
				`{"query":"美食"}`,
				ut.ShouldEqual,
			},
			false,
		},
	}

	for k, tt := range tests {
		this := &His_Sdel{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.Gzdecode(tt.args.data)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_Gzdecode, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_Gzdecode, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestHis_Sdel_parseRALResData(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		resData data.HisRALResponse
		tpl     HisResponse
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseRALResData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	datainfo := []interface{}{}
	ralRes := data.HisRALResponse{
		Msg:    "err",
		Status: "errmsg",
	}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
	}
	resP := HisResponse{
		Errno:  "5001",
		Errmsg: "request sughis error ",
		Data:   datainfo,
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //HisResponse
		wantErr       bool
	}{
		{
			"TestHis_Sdel_parseRALResData",
			fields{
				basePage,
			},
			args{
				ralRes,
				ret,
			},
			Want{
				resP,
				ut.ShouldResemble,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &His_Sdel{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.parseRALResData(tt.args.resData, tt.args.tpl)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_parseRALResData, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_parseRALResData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_Sdel_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: map[string]interface{}(nil),
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisResponse
	}{
		{
			"TestHis_Sdel_defaultTpl",
			fields{
				basePage,
			},
			Want{
				ret,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		this := &His_Sdel{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisResponse))
	}
	ag.Run()
}

func TestHis_Sdel_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &His_Sdel{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestHis_Sdel_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &His_Sdel{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHis_Sdel_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
