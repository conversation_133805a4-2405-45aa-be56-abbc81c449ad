package his

import (
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/service"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestDeduplicateAndInsertAd(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: deduplicateAndInsertAd")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// 创建His_Universal对象来调用方法
	hisUniversal := &His_Universal{}
	// 设置上下文
	hisUniversal.ctx = createGetWebContext()

	// 测试用例1：所有数据都存在的情况
	t.Run("TestCase1_AllDataExist", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{}

		// 1. 构造热榜数据
		boardData := []interface{}{
			map[string]interface{}{
				"text": "热搜榜",
				"items": []interface{}{
					map[string]interface{}{
						"word": "热搜词1",
					},
					map[string]interface{}{
						"word": "热搜词2",
					},
				},
			},
		}

		boardResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   boardData,
		}
		hisUniversalTplData["board"] = boardResp

		// 2. 构造历史搜索数据
		hisListResp := HisListResponse{
			Errno:  "0",
			Errmsg: "success",
			Data: []interface{}{
				map[string]interface{}{
					"query": "历史搜索词1",
				},
				map[string]interface{}{
					"query": "热搜词1", // 与热搜榜重复
				},
				map[string]interface{}{
					"query": "历史搜索词2",
				},
			},
			FrequentHis: []map[string]string{
				{
					"query": "常搜词1",
				},
				{
					"query": "运营投放词3",
				},
			},
		}
		hisUniversalTplData["list"] = hisListResp

		// 3. 构造广告数据

		adResp := data.AFDResp{
			Errno:  "0",
			Errmsg: "success",
			Data: data.AFDData{
				Ad: []data.AFDAdItem{
					{
						AdInfo: []data.AFDAdInfo{
							{
								Material: []data.AFDMaterial{
									{
										ID:   "material1",
										Info: `{"title": "广告词1", "jumpurl": "https://example.com"}`,
									},
								},
							},
						},
					},
				},
			},
		}

		// 模拟 ExtractRecAdMaterial 返回的结果
		hisUniversalTplData["ad"] = adResp

		// 4. 构造猜你想搜数据（按照函数期望的格式）
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{
					map[string]interface{}{
						"text": "推荐词1",
						"sa":   "sa_1",
					},
					map[string]interface{}{
						"text": "推荐词2",
						"sa":   "sa_2",
					},
					map[string]interface{}{
						"text": "运营投放词1",
						"sa":   "igh_12345_gyy_123",
					},
					map[string]interface{}{
						"text": "推荐词3",
						"sa":   "sa_3",
					},
					map[string]interface{}{
						"text": "运营投放词2",
						"sa":   "igh_12345_gyy_123",
					},
					map[string]interface{}{
						"text": "推荐词4",
						"sa":   "sa_4",
					},
					map[string]interface{}{
						"text": "运营投放词3",
						"sa":   "igh_12345_gyy_123",
					},
				},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 调用方法
		ok, message := hisUniversal.deduplicateAndInsertAd(hisUniversalTplData)

		// 验证结果
		assert.True(t, ok, "deduplicateAndInsertAd should return true")
		assert.NotEmpty(t, message, "deduplicateAndInsertAd should return non-empty message")

		// 验证广告是否插入到猜你想搜的第三位
		recData, ok := hisUniversalTplData["rec"].(HisResponse)
		assert.True(t, ok, "rec should be a HisResponse")

		if ok {
			recSessionData, ok := recData.Data.(map[string]interface{})
			assert.True(t, ok, "recData.Data should be a map[string]interface{}")

			if ok {
				guess, ok := recSessionData["guess"].(map[string]interface{})
				assert.True(t, ok, "guess should be a map[string]interface{}")

				if ok {
					guessData, ok := guess["data"].([]interface{})
					assert.True(t, ok, "guessData should be a []interface{}")

					if ok && len(guessData) >= 3 {
						// 第三位应该是广告
						adItem, ok := guessData[2].(data.AdGuess)
						if ok {
							assert.Equal(t, "广告词1", adItem.Text, "Third item should be the ad")
						}
						adItem, ok = guessData[5].(data.AdGuess)
						if ok {
							assert.Equal(t, "运营投放词2", adItem.Text, "Third item should be the ad")
						}
					}
				}
			}
		}
	})

	// 测试用例2：无广告数据的情况
	t.Run("TestCase2_NoAdData", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{}

		// 只构造猜你想搜数据
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{
					map[string]interface{}{
						"text": "推荐词1",
						"sa":   "sa_1",
					},
					map[string]interface{}{
						"text": "推荐词2",
						"sa":   "sa_2",
					},
				},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 执行测试
		ok, msg := hisUniversal.deduplicateAndInsertAd(hisUniversalTplData)

		// 验证结果
		assert.True(t, ok, "deduplicateAndInsertAd should return true even without ad data")
		assert.Equal(t, "deduped and inserted ad at position 3", msg, "Message should indicate success even without ad data")
	})

	// 测试用例3：无猜你想搜数据的情况
	t.Run("TestCase3_NoRecData", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{}

		// 只构造广告数据
		adResp := data.AFDResp{
			Errno:  "0",
			Errmsg: "success",
			Data: data.AFDData{
				Ad: []data.AFDAdItem{
					{
						AdInfo: []data.AFDAdInfo{
							{
								Material: []data.AFDMaterial{
									{
										ID:   "material1",
										Info: `{"title": "广告词1", "jumpurl": "https://example.com"}`,
									},
								},
							},
						},
					},
				},
			},
		}
		hisUniversalTplData["ad"] = adResp

		// 执行测试
		ok, msg := hisUniversal.deduplicateAndInsertAd(hisUniversalTplData)

		// 验证结果
		assert.False(t, ok, "deduplicateAndInsertAd should return false when no rec data")
		assert.Equal(t, "guess data not found", msg, "Message should indicate no guess data")
	})

	// 测试用例4：猜你想搜数据格式不正确的情况
	t.Run("TestCase4_InvalidRecFormat", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{}

		// 构造格式不正确的猜你想搜数据
		hisUniversalTplData["rec"] = "invalid format"

		// 构造广告数据
		adResp := data.AFDResp{
			Errno:  "0",
			Errmsg: "success",
			Data: data.AFDData{
				Ad: []data.AFDAdItem{
					{
						AdInfo: []data.AFDAdInfo{
							{
								Material: []data.AFDMaterial{
									{
										ID:   "material1",
										Info: `{"title": "广告词1", "jumpurl": "https://example.com"}`,
									},
								},
							},
						},
					},
				},
			},
		}
		hisUniversalTplData["ad"] = adResp

		// 执行测试
		ok, msg := hisUniversal.deduplicateAndInsertAd(hisUniversalTplData)

		// 验证结果
		assert.False(t, ok, "deduplicateAndInsertAd should return false with invalid rec format")
		assert.Equal(t, "guess data not found", msg, "Message should indicate no guess data")
	})

	// 测试用例5：猜你想搜数据格式正确但缺少guess字段的情况
	t.Run("TestCase5_NoGuessField", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{}

		// 构造缺少guess字段的猜你想搜数据
		recSessionData := map[string]interface{}{
			"other_field": "other_value",
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 构造广告数据
		adResp := data.AFDResp{
			Errno:  "0",
			Errmsg: "success",
			Data: data.AFDData{
				Ad: []data.AFDAdItem{
					{
						AdInfo: []data.AFDAdInfo{
							{
								Material: []data.AFDMaterial{
									{
										ID:   "material1",
										Info: `{"title": "广告词1", "jumpurl": "https://example.com"}`,
									},
								},
							},
						},
					},
				},
			},
		}
		hisUniversalTplData["ad"] = adResp

		// 执行测试
		ok, msg := hisUniversal.deduplicateAndInsertAd(hisUniversalTplData)

		// 验证结果
		assert.False(t, ok, "deduplicateAndInsertAd should return false without guess field")
		assert.Equal(t, "guess data not found", msg, "Message should indicate no guess data")
	})

	ag.Run()
}

func TestMergeHisList2Guess(t *testing.T) {
	tests := []struct {
		name                string
		hisUniversalTplData map[string]interface{}
		hisNum              int
		wantErr             bool
	}{
		{
			name: "normal case with frequent his and history",
			hisUniversalTplData: map[string]interface{}{
				"list": HisListResponse{
					FrequentHis: []map[string]string{
						{"query": "test1", "tag": "常搜"},
					},
					Data: []interface{}{
						map[string]interface{}{"query": "test2"},
						map[string]interface{}{"query": "test3"},
					},
				},
				"rec": HisResponse{
					Data: map[string]interface{}{
						"guess": map[string]interface{}{
							"data": []interface{}{
								map[string]interface{}{"text": "guess1"},
								map[string]interface{}{"text": "guess2"},
							},
						},
					},
				},
			},
			hisNum:  3,
			wantErr: false,
		},
		{
			name: "empty his list",
			hisUniversalTplData: map[string]interface{}{
				"list": HisListResponse{},
				"rec": HisResponse{
					Data: map[string]interface{}{
						"guess": map[string]interface{}{
							"data": []interface{}{
								map[string]interface{}{"text": "guess1"},
							},
						},
					},
				},
			},
			hisNum:  2,
			wantErr: false,
		},
		{
			name: "invalid his list type",
			hisUniversalTplData: map[string]interface{}{
				"list": "invalid",
				"rec":  HisResponse{},
			},
			hisNum:  1,
			wantErr: true,
		},
		{
			name: "empty rec list",
			hisUniversalTplData: map[string]interface{}{
				"list": HisListResponse{
					Data: []interface{}{
						map[string]interface{}{"query": "test1"},
					},
				},
				"rec": HisResponse{},
			},
			hisNum:  1,
			wantErr: true,
		},
		{
			name: "empty guess data",
			hisUniversalTplData: map[string]interface{}{
				"list": HisListResponse{
					Data: []interface{}{
						map[string]interface{}{"query": "test1"},
					},
				},
				"rec": HisResponse{
					Data: map[string]interface{}{},
				},
			},
			hisNum:  1,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &His_Universal{}
			err := h.mergeHisList2Guess(tt.hisUniversalTplData, tt.hisNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("mergeHisList2Guess() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProcessHisTarget(t *testing.T) {
	ctx := &gdp.WebContext{}
	h := &His_Universal{BasePage: BasePage{ctx: ctx}}
	// 创建patches
	patches := gomonkey.ApplyFunc(common.GetSampleValue, func(ctx *gdp.WebContext, key, defaultValue string) string {
		return "1"
	})

	defer patches.Reset() // 确保测试结束后重置patches
	tests := []struct {
		name      string
		targetArr []string
		setup     func()
		want      int
	}{
		{
			name:      "target contains list",
			targetArr: []string{"list", "other"},
			want:      1,
		},
		{
			name:      "target does not contain list",
			targetArr: []string{"other"},
			want:      1,
		},
		{
			name:      "zero hislist_insert value",
			targetArr: []string{"list"},
			want:      1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, got := h.processHisTarget(tt.targetArr); got != tt.want {
				t.Errorf("processHisTarget() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestRemoveYYC 测试removeYYC函数
func TestRemoveYYC(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: changeIntelligentData")
	defer utInst.RestoreAll()

	// 创建His_Universal对象来调用方法
	hisUniversal := &His_Universal{}
	// 设置上下文
	hisUniversal.ctx = createGetWebContext()

	// 测试用例1：包含运营词的情况 - 使用rec数据
	t.Run("TestCase1_WithYYCInRec", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 构造包含运营词的猜你想搜数据
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{
					map[string]interface{}{
						"text": "正常推荐词1",
						"sa":   "normal_sa",
					},
					map[string]interface{}{
						"text": "运营词1",
						"sa":   "igh_12345_gyy",
					},
					map[string]interface{}{
						"text": "正常推荐词2",
						"sa":   "normal_sa_2",
					},
					map[string]interface{}{
						"text": "运营词2",
						"sa":   "iph_igh_12345_gyy",
					},
					map[string]interface{}{
						"text": "正常推荐词3",
						"sa":   "normal_sa_3",
					},
				},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.True(t, removed, "changeIntelligentData should return true when YYC items are removed")

		// 验证运营词已被移除
		recData, ok := hisUniversalTplData["rec"].(HisResponse)
		assert.True(t, ok, "rec should be a HisResponse")

		if ok {
			recSessionData, ok := recData.Data.(map[string]interface{})
			assert.True(t, ok, "recData.Data should be a map[string]interface{}")

			if ok {
				guess, ok := recSessionData["guess"].(map[string]interface{})
				assert.True(t, ok, "guess should be a map[string]interface{}")

				if ok {
					guessData, ok := guess["data"].([]interface{})
					assert.True(t, ok, "guessData should be a []interface{}")

					// 验证只剩下正常推荐词，运营词已被移除
					assert.Equal(t, 3, len(guessData), "Should have 3 items after removing YYC")

					// 验证剩余的都是正常推荐词
					expectedTexts := []string{"正常推荐词1", "正常推荐词2", "正常推荐词3"}
					for i, item := range guessData {
						itemMap, ok := item.(map[string]interface{})
						assert.True(t, ok, "item should be a map")
						if ok {
							text, ok := itemMap["text"].(string)
							assert.True(t, ok, "text should be a string")
							if ok {
								assert.Equal(t, expectedTexts[i], text, "Text should match expected")
							}

							sa, ok := itemMap["sa"].(string)
							assert.True(t, ok, "sa should be a string")
							if ok {
								assert.NotEqual(t, "igh_12345_gyy", sa, "Should not contain igh_12345_gyy")
								assert.NotEqual(t, "iph_igh_12345_gyy", sa, "Should not contain iph_igh_12345_gyy")
							}
						}
					}
				}
			}
		}
	})

	// 测试用例2：包含运营词的情况 - 使用rec_session数据
	t.Run("TestCase2_WithYYCInRecSession", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 构造包含运营词的猜你想搜数据
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{
					map[string]interface{}{
						"text": "正常词",
						"sa":   "normal",
					},
					map[string]interface{}{
						"text": "运营词",
						"sa":   "igh_12345_gyy",
					},
				},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec_session"] = recResp

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.True(t, removed, "changeIntelligentData should return true when YYC items are removed")

		// 验证运营词已被移除
		recData, ok := hisUniversalTplData["rec_session"].(HisResponse)
		assert.True(t, ok, "rec_session should be a HisResponse")

		if ok {
			recSessionData, ok := recData.Data.(map[string]interface{})
			assert.True(t, ok, "recData.Data should be a map[string]interface{}")

			if ok {
				guess, ok := recSessionData["guess"].(map[string]interface{})
				assert.True(t, ok, "guess should be a map[string]interface{}")

				if ok {
					guessData, ok := guess["data"].([]interface{})
					assert.True(t, ok, "guessData should be a []interface{}")

					// 验证只剩下1个正常推荐词
					assert.Equal(t, 1, len(guessData), "Should have 1 item after removing YYC")
				}
			}
		}
	})

	// 测试用例3：不包含运营词的情况
	t.Run("TestCase3_WithoutYYC", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 构造不包含运营词的猜你想搜数据
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{
					map[string]interface{}{
						"text": "正常推荐词1",
						"sa":   "normal_sa_1",
					},
					map[string]interface{}{
						"text": "正常推荐词2",
						"sa":   "normal_sa_2",
					},
				},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.False(t, removed, "changeIntelligentData should return false when no YYC items to remove")

		// 验证数据没有变化
		recData, ok := hisUniversalTplData["rec"].(HisResponse)
		assert.True(t, ok, "rec should be a HisResponse")

		if ok {
			recSessionData, ok := recData.Data.(map[string]interface{})
			assert.True(t, ok, "recData.Data should be a map[string]interface{}")

			if ok {
				guess, ok := recSessionData["guess"].(map[string]interface{})
				assert.True(t, ok, "guess should be a map[string]interface{}")

				if ok {
					guessData, ok := guess["data"].([]interface{})
					assert.True(t, ok, "guessData should be a []interface{}")

					// 验证数据数量没有变化
					assert.Equal(t, 2, len(guessData), "Should still have 2 items")
				}
			}
		}
	})

	// 测试用例4：无rec和rec_session数据
	t.Run("TestCase4_NoRecData", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 不添加rec或rec_session数据

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.False(t, removed, "changeIntelligentData should return false when no rec data exists")
	})

	// 测试用例5：rec数据格式不正确
	t.Run("TestCase5_InvalidRecData", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 添加格式不正确的rec数据
		hisUniversalTplData["rec"] = "invalid_data"

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.False(t, removed, "changeIntelligentData should return false when rec data format is invalid")
	})

	// 测试用例6：guess数据不存在
	t.Run("TestCase6_NoGuessData", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 构造没有guess数据的rec数据
		recSessionData := map[string]interface{}{
			"other": map[string]interface{}{
				"data": []interface{}{},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.False(t, removed, "changeIntelligentData should return false when guess data doesn't exist")
	})

	// 测试用例7：空的guess data列表
	t.Run("TestCase7_EmptyGuessData", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 构造空的guess数据
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.False(t, removed, "changeIntelligentData should return false when guess data is empty")
	})

	// 测试用例8：guess数据项缺少sa字段
	t.Run("TestCase8_MissingSaField", func(t *testing.T) {
		hisUniversalTplData := map[string]interface{}{}

		// 构造缺少sa字段的数据
		recSessionData := map[string]interface{}{
			"guess": map[string]interface{}{
				"data": []interface{}{
					map[string]interface{}{
						"text": "推荐词1",
						// 缺少sa字段
					},
					map[string]interface{}{
						"text": "运营词",
						"sa":   "igh_12345_gyy",
					},
				},
			},
		}
		recResp := HisResponse{
			Errno:  "0",
			Errmsg: "success",
			Data:   recSessionData,
		}
		hisUniversalTplData["rec"] = recResp

		// 调用方法
		removed := hisUniversal.changeIntelligentData(hisUniversalTplData)

		// 验证结果
		assert.True(t, removed, "changeIntelligentData should return true when some YYC items are removed")

		// 验证只有运营词被移除
		recData, ok := hisUniversalTplData["rec"].(HisResponse)
		assert.True(t, ok, "rec should be a HisResponse")

		if ok {
			recSessionData, ok := recData.Data.(map[string]interface{})
			assert.True(t, ok, "recData.Data should be a map[string]interface{}")

			if ok {
				guess, ok := recSessionData["guess"].(map[string]interface{})
				assert.True(t, ok, "guess should be a map[string]interface{}")

				if ok {
					guessData, ok := guess["data"].([]interface{})
					assert.True(t, ok, "guessData should be a []interface{}")

					// 验证只剩下缺少sa字段的那一项
					assert.Equal(t, 1, len(guessData), "Should have 1 item after removing YYC")
				}
			}
		}
	})
}

func TestExtractAiToolTypes(t *testing.T) {
	// 测试用例1：正常情况 - 包含AI工具数据
	t.Run("TestCase1_NormalCase_WithAiToolData", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data: map[string]interface{}{
					"ai_tool": map[string]interface{}{
						"data": []interface{}{
							map[string]interface{}{
								"type": "file_search",
								"name": "文件搜索",
							},
							map[string]interface{}{
								"type": "image_gen",
								"name": "图像生成",
							},
							map[string]interface{}{
								"type": "text_summary",
								"name": "文本摘要",
							},
						},
					},
				},
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		expected := []string{"file_search", "image_gen", "text_summary"}
		assert.Equal(t, expected, result, "Should extract all AI container types correctly")
		assert.Len(t, result, 3, "Should return 3 AI container types")
	})

	// 测试用例2：无rec数据
	t.Run("TestCase2_NoRecData", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"other": "some_data",
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when no rec data")
		assert.NotNil(t, result, "Should return non-nil slice")
	})

	// 测试用例3：rec数据类型错误
	t.Run("TestCase3_WrongRecDataType", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": "invalid_data_type",
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when rec data type is wrong")
	})

	// 测试用例4：rec.Data类型错误
	t.Run("TestCase4_WrongRecDataMapType", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data:   "invalid_data_type", // 应该是map[string]interface{}
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when rec.Data type is wrong")
	})

	// 测试用例5：无ai_tool字段
	t.Run("TestCase5_NoAiToolField", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data: map[string]interface{}{
					"other_field": "some_value",
				},
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when no ai_tool field")
	})

	// 测试用例6：ai_tool.data为空
	t.Run("TestCase6_EmptyAiToolData", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data: map[string]interface{}{
					"ai_tool": map[string]interface{}{
						"data": []interface{}{},
					},
				},
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when ai_tool.data is empty")
	})

	// 测试用例7：ai_tool.data中包含无效项
	t.Run("TestCase7_InvalidItemsInAiToolData", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data: map[string]interface{}{
					"ai_tool": map[string]interface{}{
						"data": []interface{}{
							"invalid_string", // 无效项
							map[string]interface{}{
								"type": "valid_type",
								"name": "有效工具",
							},
							map[string]interface{}{
								// 缺少type字段
								"name": "无type字段",
							},
							map[string]interface{}{
								"type": 123, // type字段类型错误
								"name": "type类型错误",
							},
						},
					},
				},
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		expected := []string{"valid_type"}
		assert.Equal(t, expected, result, "Should only extract valid AI container types")
		assert.Len(t, result, 1, "Should return only 1 valid AI container type")
	})

	// 测试用例8：ai_tool字段类型错误
	t.Run("TestCase8_WrongAiToolType", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data: map[string]interface{}{
					"ai_tool": "invalid_type", // 应该是map[string]interface{}
				},
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when ai_tool type is wrong")
	})

	// 测试用例9：ai_tool.data字段类型错误
	t.Run("TestCase9_WrongAiToolDataType", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{
			"rec": HisResponse{
				Errno:  "0",
				Errmsg: "success",
				Data: map[string]interface{}{
					"ai_tool": map[string]interface{}{
						"data": "invalid_type", // 应该是[]interface{}
					},
				},
			},
		}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice when ai_tool.data type is wrong")
	})

	// 测试用例10：空输入
	t.Run("TestCase10_EmptyInput", func(t *testing.T) {
		// 构造输入数据
		hisUniversalTplData := map[string]interface{}{}

		// 调用函数
		result := ExtractAiToolTypes(hisUniversalTplData)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice for empty input")
		assert.NotNil(t, result, "Should return non-nil slice")
	})

	// 测试用例11：nil输入
	t.Run("TestCase11_NilInput", func(t *testing.T) {
		// 调用函数
		result := ExtractAiToolTypes(nil)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice for nil input")
		assert.NotNil(t, result, "Should return non-nil slice")
	})
}

func TestGetMonitorInsertIdx(t *testing.T) {
	tests := []struct {
		name          string
		guessDataList []interface{}
		want          int
	}{
		{
			name:          "empty list",
			guessDataList: []interface{}{},
			want:          -1,
		},
		{
			name: "no his items",
			guessDataList: []interface{}{
				map[string]interface{}{"is_his": float64(0)},
				map[string]interface{}{"is_his": float64(0)},
			},
			want: 0,
		},
		{
			name: "all his items",
			guessDataList: []interface{}{
				map[string]interface{}{"is_his": float64(1)},
				map[string]interface{}{"is_his": float64(1)},
			},
			want: -1,
		},
		{
			name: "mixed items",
			guessDataList: []interface{}{
				map[string]interface{}{"is_his": float64(1)},
				map[string]interface{}{"is_his": float64(0)},
				map[string]interface{}{"is_his": float64(1)},
			},
			want: 1,
		},
		{
			name: "invalid item type",
			guessDataList: []interface{}{
				"invalid",
				map[string]interface{}{"is_his": float64(0)},
			},
			want: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMonitorInsertIdx(tt.guessDataList); got != tt.want {
				t.Errorf("getMonitorInsertIdx() = %v, want %v", got, tt.want)
			}
		})
	}
}

// MockGuessService 实现了 service.GuessService 接口，用于测试
type MockGuessService struct {
	mock.Mock
}

func (m *MockGuessService) GetGuessImage(query []string) (map[string]string, error) {
	args := m.Called(query)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]string), args.Error(1)
}

func TestCollectAndAddGuessImages(t *testing.T) {
	tests := []struct {
		name              string
		guessDataList     []interface{}
		mockRequestParams *entity.RequestParams
		mockImageMap      map[string]string
		mockError         error
		expectedNotices   map[string]string
		expectedTplFields map[string]map[string]interface{}
	}{
		{
			name:              "empty guess data list",
			guessDataList:     []interface{}{},
			expectedNotices:   map[string]string{},
			expectedTplFields: map[string]map[string]interface{}{},
		},
		{
			name: "valid data with prompt field",
			guessDataList: []interface{}{
				map[string]interface{}{
					"prompt": "苹果",
					"text":   "apple",
				},
				map[string]interface{}{
					"prompt": "香蕉",
				},
			},
			mockRequestParams: &entity.RequestParams{UID: "test-uid"},
			mockImageMap: map[string]string{
				"苹果": "https://example.com/apple.jpg",
				"香蕉": "https://example.com/banana.jpg",
			},
			expectedNotices: map[string]string{
				"guess_image_count": "got 2 images for 2 queries",
			},
			expectedTplFields: map[string]map[string]interface{}{
				"苹果": {
					"type":  1,
					"image": "https://example.com/apple.jpg",
				},
				"香蕉": {
					"type":  1,
					"image": "https://example.com/banana.jpg",
				},
			},
		},
		{
			name: "fallback to text field when prompt is empty",
			guessDataList: []interface{}{
				map[string]interface{}{
					"prompt": "",
					"text":   "橘子",
				},
				map[string]interface{}{
					"text": "葡萄",
				},
			},
			mockRequestParams: &entity.RequestParams{UID: "test-uid"},
			mockImageMap: map[string]string{
				"橘子": "https://example.com/orange.jpg",
				"葡萄": "https://example.com/grape.jpg",
			},
			expectedNotices: map[string]string{
				"guess_image_count": "got 2 images for 2 queries",
			},
			expectedTplFields: map[string]map[string]interface{}{
				"橘子": {
					"type":  1,
					"image": "https://example.com/orange.jpg",
				},
				"葡萄": {
					"type":  1,
					"image": "https://example.com/grape.jpg",
				},
			},
		},
		{
			name: "invalid data types in list",
			guessDataList: []interface{}{
				"invalid_string",
				123,
				map[string]interface{}{
					"prompt": "有效数据",
				},
			},
			mockRequestParams: &entity.RequestParams{UID: "test-uid"},
			mockImageMap: map[string]string{
				"有效数据": "https://example.com/valid.jpg",
			},
			expectedNotices: map[string]string{
				"guess_image_count": "got 1 images for 1 queries",
			},
			expectedTplFields: map[string]map[string]interface{}{
				"有效数据": {
					"type":  1,
					"image": "https://example.com/valid.jpg",
				},
			},
		},
		{
			name: "no valid queries found",
			guessDataList: []interface{}{
				map[string]interface{}{
					"prompt": "",
					"text":   "",
				},
				map[string]interface{}{
					"other_field": "value",
				},
			},
			expectedNotices: map[string]string{
				"guess_image_no_queries": "no valid queries found",
			},
			expectedTplFields: map[string]map[string]interface{}{},
		},
		{
			name: "request params is nil",
			guessDataList: []interface{}{
				map[string]interface{}{
					"prompt": "测试",
				},
			},
			mockRequestParams: nil,
			expectedNotices: map[string]string{
				"guess_image_error": "requestParams is nil",
			},
			expectedTplFields: map[string]map[string]interface{}{},
		},
		{
			name: "guess service returns error",
			guessDataList: []interface{}{
				map[string]interface{}{
					"prompt": "测试查询",
				},
			},
			mockRequestParams: &entity.RequestParams{UID: "test-uid"},
			mockError:         errors.New("redis connection failed"),
			expectedNotices: map[string]string{
				"guess_image_err": "1",
			},
			expectedTplFields: map[string]map[string]interface{}{},
		},
		{
			name: "empty image results",
			guessDataList: []interface{}{
				map[string]interface{}{
					"prompt": "无图片查询",
				},
			},
			mockRequestParams: &entity.RequestParams{UID: "test-uid"},
			mockImageMap:      map[string]string{},
			expectedNotices: map[string]string{
				"guess_image_count": "got 0 images for 1 queries",
			},
			expectedTplFields: map[string]map[string]interface{}{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			ctx := createGetWebContext()
			h := &His_Universal{BasePage: BasePage{ctx: ctx}}

			// 创建patches来模拟依赖
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			// Mock entity.GetRequestParams
			patches.ApplyFunc(entity.GetRequestParams, func(ctx *gdp.WebContext) *entity.RequestParams {
				return tt.mockRequestParams
			})

			// Mock service.NewGuessService 和 GetGuessImage
			if tt.mockRequestParams != nil {
				mockGuessService := &MockGuessService{}
				if tt.mockError != nil {
					mockGuessService.On("GetGuessImage", mock.AnythingOfType("[]string")).Return(nil, tt.mockError)
				} else {
					mockGuessService.On("GetGuessImage", mock.AnythingOfType("[]string")).Return(tt.mockImageMap, nil)
				}

				patches.ApplyFunc(service.NewGuessService, func(ctx *gdp.WebContext, requestParams *entity.RequestParams) service.GuessService {
					return mockGuessService
				})
			}

			// 执行测试方法
			h.collectAndAddGuessImages(tt.guessDataList)

			// 验证tpl字段是否正确添加
			for i, item := range tt.guessDataList {
				if guessMap, ok := item.(map[string]interface{}); ok {
					var query string
					if prompt, exists := guessMap["prompt"]; exists {
						if promptStr, ok := prompt.(string); ok && promptStr != "" {
							query = promptStr
						}
					}
					if query == "" {
						if text, exists := guessMap["text"]; exists {
							if textStr, ok := text.(string); ok && textStr != "" {
								query = textStr
							}
						}
					}

					if query != "" {
						if expectedTpl, exists := tt.expectedTplFields[query]; exists {
							actualTpl, hasTpl := guessMap["tpl"]
							assert.True(t, hasTpl, "Expected tpl field to be added for query: %s", query)
							if hasTpl {
								actualTplMap, ok := actualTpl.(map[string]interface{})
								assert.True(t, ok, "tpl field should be a map")
								if ok {
									assert.Equal(t, expectedTpl["type"], actualTplMap["type"], "tpl type should match")
									assert.Equal(t, expectedTpl["image"], actualTplMap["image"], "tpl image should match")
								}
							}
						} else {
							_, hasTpl := guessMap["tpl"]
							assert.False(t, hasTpl, "Should not have tpl field for query: %s (item %d)", query, i)
						}
					}
				}
			}
		})
	}
}
