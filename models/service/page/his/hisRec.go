package his

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Rec struct {
	BasePage
}

type RecParms struct {
	Hisdata            string      `json:"hisdata"`
	HisSearchData      string      `json:"his_search_data"`
	Boxdata            string      `json:"boxdata"`
	Guessdata          string      `json:"guessdata"`
	Showquerys         string      `json:"showquerys"`
	Eventtype          string      `json:"event_type"`
	Eventbox           string      `json:"event_box"`
	Eventquery         string      `json:"event_query"`
	Eventchannel       string      `json:"event_channel"`
	Eventfrom          string      `json:"event_from"`
	Guessfeedbackquery string      `json:"guess_feedback_query"`
	Guessshowrownum    interface{} `json:"guess_show_rownum"`
	PrivateMode        int         `json:"private_mode"`
	ClientName         string      `json:"clientname"`
	ContentInfo        string      `json:"content_info"`
	UnderBox           string      `json:"under_box"`
	Target             string      `json:"target"`
	EventHisFrom       string      `json:"event_his_from"`
	BoxShowQuery       string      `json:"box_show_query"` // his框下推荐: 表示当前query词
	TodayFirst         int         `json:"today_first"`    // 是否今日首次启动
	DisplayWidth       int         `json:"display_width"`  // 屏幕宽度
	RecVideoInfo       int         `json:"rec_video_info"`
	ColdLaunchTime     string      `json:"cold_launch_time"` // 用户冷启进入手百的时间（毫秒时间戳）
	ShowGuess          int         `json:"show_guess"`       // 是否返回搜索发现数据
	TopicPageNum       int         `json:"topic_page_num"`
	VidCtl             string      `json:"vid_ctl"`        // 控制视频推荐词
	RecMaxReturn       int         `json:"rec_max_return"` // 最大返回数量控制
	TextCount          int         `json:"text_count"`     // 设备可展现最大中文字符数
	IntelligentHisType int         `json:"intelligent_his_type"`
	EncodePrivacyData  struct {
		PrivacyString string `json:"v"`
		HonorOaID     string `json:"h_v"`
	} `json:"od"` // 用户隐私数据, 安卓传加密oaid，ios传加密idfa
}

// 主要执行的函数入口
func (r *His_Rec) Execute() {
	request := r.getRequest()
	adaptions := r.getAdaption()
	common.SetUserPrivacyInfo((*request)["uid"], r.ctx)
	dataParams := r.validateAndPrepareDataParams(request, adaptions)

	if dataParams == nil {
		return
	}

	dataSrv := data.NewRec(r.ctx)
	err := dataSrv.GetRecResponse(request, adaptions, *dataParams)
	r.setResponse(err, dataSrv, dataParams)
	return
}

// nolint:gocyclo
func (r *His_Rec) validateAndPrepareDataParams(request *map[string]string, adaptions *map[string]string) *data.DataParms {
	hisListErr := HisListErr{}
	// 12.7版本用户隐私授权涉及公参变化，uid，puid参数会缺失，去除强校验
	/*uid := (*request)["uid"]
	if uid == "" {
		hisListErr.Errno = "-1001"
		hisListErr.Errmsg = "params[uid] is missing"
		hisListErr.Requestid = r.getLogidStr()
		r.response(hisListErr)
		return
	}*/
	dataquery := (*request)["data"]
	if dataquery == "" {
		hisListErr.Errno = "-1001"
		hisListErr.Errmsg = "params[data] is missing"
		hisListErr.Requestid = r.getLogidStr()
		r.response(hisListErr)
		return nil
	}

	// .addNotice("dataquery", dataquery)

	// 低版本手百，端上是对整体data数据encode过的，因此需要解码
	var bdVersion string
	if (*request)["fv"] != "" {
		bdVersion = (*request)["fv"]
	} else {
		bdVersion = (*adaptions)["bd_version"]
	}

	if version.Compare(bdVersion, "12.20", "<") {
		v, err := url.QueryUnescape(dataquery)
		if err == nil {
			dataquery = v
		}
	}

	dataParms, err := r.parseAndValidateRequestData(dataquery, request, hisListErr)
	if err != nil {
		return nil
	}

	return dataParms
}

func (r *His_Rec) setResponse(err error, dataSrv *data.Rec, dataParams *data.DataParms) {
	hisListErr := HisListErr{}
	tplData := r.defaultTpl()
	if err != nil {
		r.setErr()
		r.addNotice("ralErr", "1")
		r.addNotice("ralErrMsg", err.Error())
		hisListErr.Errno = "-4002"
		hisListErr.Errmsg = "ral request fail"
		hisListErr.Requestid = r.getLogidStr()
		r.response(hisListErr)
		return
	}

	resData := dataSrv.GetHISTplData()
	tpl, parseErr := r.parseRALResData(resData, tplData, *dataParams)
	if parseErr != nil {
		r.setErr()
		r.addNotice("parseErr", parseErr.Error())
		hisListErr.Errno = tpl.Errno
		hisListErr.Errmsg = tpl.Errmsg
		hisListErr.Requestid = r.getLogidStr()
		r.response(hisListErr)
		return
	}

	r.response(tpl)
}

func (r *His_Rec) ExecuteByStep() (multiRequest func(), parseResponse func()) {
	request := r.getRequest()
	adaptions := r.getAdaption()
	hisListErr := HisListErr{}
	hisListErr.Switch = common.SwitchConfMem.Switch

	dataParams := r.validateAndPrepareDataParams(request, adaptions)
	if dataParams == nil {
		return nil, nil
	}

	dataSrv := data.NewRec(r.ctx)
	requests, _ := dataSrv.BuildRequest(request, adaptions, *dataParams)
	multiResponseChan := make(chan data.ChRawHTTPResp, len(requests))
	return func() {
			dataSrv.MultiRequest(requests, multiResponseChan)
		}, func() {
			err := dataSrv.ParseHISResponse(multiResponseChan)
			r.setResponse(err, dataSrv, dataParams)
		}
}

// nolint:gocyclo
func (r *His_Rec) parseRALResData(resDataArr map[string]data.TypeDispaly, tpl HisResponse, dataParams data.DataParms) (HisResponse, error) {
	// 荣耀白牌暂时屏蔽搜索发现数据
	reparams := r.getRequest()
	adptparams := r.getAdaption()
	if (*reparams)[constant.RongyaoBaipai] == "1" && common.TwoInOneConfMem.CloseGuess == 1 {
		tpl.Data = map[string]interface{}{
			"default_box": "搜索或输入网址",
		}
		return tpl, nil
	}

	// 新户金刚位1000535优先级 > his红包运营1000539
	v1, ok1 := resDataArr["1000535"]
	v2, ok2 := resDataArr["1000539"]
	if ok1 && v1.Retno == 0 && ok2 && v2.Retno == 0 {
		delete(resDataArr, "1000539")
	}

	tplData := map[string]interface{}{}
	RetnoFailed := 0

	for srcID, resData := range resDataArr {
		if resData.Retno != 0 {
			RetnoFailed++
			continue
		}

		ralRespData := resData.Data
		if srcID == "1000513" { // 目前数组大小只可能为3
			if v, ok := ralRespData["under_box"]; ok {
				tplData["under_box"] = v
			}
			continue
		} else if srcID == "1000514" { // 先处理完其他数据，后面单独处理rec_video(1000514)数据
			continue
		} else if srcID == "1000535" {
			// 搜索精选服务
			if searchService, ok := ralRespData["search_service"]; ok {
				if searchServiceData, ok := searchService.(map[string]interface{}); ok {
					dataList := searchServiceData["data"]
					if l, ok := dataList.([]interface{}); ok && len(l) >= 4 {
						tplData["search_service"] = searchService
					}
				}
			}
			continue
		} else if srcID == "1000539" {
			// his红包运营
			if activity, ok := resData.Data["activity_config"]; ok {
				if activityMap, ok := activity.(map[string]interface{}); ok {
					if dataMap, ok := activityMap["data"].(map[string]interface{}); ok && len(dataMap) > 0 {
						if activicty, ok := modifyActivity(dataMap, adptparams); ok {
							tplData["activity_config"] = activicty
							r.addNotice("activity", "1")
						}
					}
				}
			}
			continue
		}
		// 下面的代码中根据 srcID 又进行了一次返回值的覆盖，因为 Universal 接口存在同时调用 1000502，1000503 两个下游资源的情况
		// 两个资源号都会同时返回 box 和 guess，但是 502 box 中有 data guess 没有 data，503 反之，可以抓包看一下
		// 不确定两个请求谁先谁后，所以按照资源号优先赋值，但是注意 renameAndDeleteBoxItemRedundantField， modifyGuessData
		// 两个函数不幂等，不能调用两次！！！！！
		if srcID == "1000502" {
			if box, ok := ralRespData["box"].(map[string]interface{}); ok {
				if boxData, ok := box["data"].([]interface{}); ok {
					boxData = r.BlackListFilter(boxData)
					for _, v := range boxData {
						item, ok := v.(map[string]interface{})
						if ok {
							renameAndDeleteBoxItemRedundantField(item)
						}
					}
					box["data"] = boxData
				}
				tplData["box"] = ralRespData["box"]
			}
			if ralRespData["strategy_intelligent_mode"] != nil {
				tplData["strategy_intelligent_mode"] = ralRespData["strategy_intelligent_mode"]
			}

		}

		if srcID == "1000503" {
			if guess, ok := ralRespData["guess"].(map[string]interface{}); ok {
				if guessData, ok := guess["data"].([]interface{}); ok {
					guessData = r.BlackListFilter(guessData)
					// 更新 guess 中的 data 字段
					guess["data"] = guessData
				}
				tplData["guess"] = guess
			}
			if ralRespData["init_his_row_count"] != nil {
				tplData["init_his_row_count"] = ralRespData["init_his_row_count"]
			}
			tplData["ai_tool"] = ralRespData["ai_tool"]

		}

		if _, ok := ralRespData["rsf"]; !ok {
			tpl.Errno = "-5001"
			tpl.Errmsg = "request sughisrec error:rsf is empty"
			return tpl, errors.New(tpl.Errmsg)
		}

		tplData["rsf"] = ralRespData["rsf"]

		if top, ok := ralRespData["top"]; ok {
			tplData["top"] = top
		}

		// 11.26 搜索发现新增banner
		if banner, ok := ralRespData["banner"]; ok {
			tplData["banner"] = banner
		}

		// 11.16新增box default_box
		// 判断下游返回是否存在 box
		if box, ok := ralRespData["box"].(map[string]interface{}); ok {
			// 返回中是否存在 data 数组（即空框词列表）
			// tplData 中如果已经存在了 box 那么就不需要赋值了。 502 的情况已经一开始就处理过了，还有其他资源号也能召回这个 boxrec，所以在这里也要判断并且处理一次
			if tplData["box"] == nil {
				if boxData, ok := box["data"].([]interface{}); ok {
					boxData = r.BlackListFilter(boxData)
					for _, v := range boxData {
						// 重命名空框词项，并去除不需要的字段
						if item, ok := v.(map[string]interface{}); ok {
							// 函数不幂等，不能调用多次
							renameAndDeleteBoxItemRedundantField(item)
						}
					}
					box["data"] = boxData
				}
				tplData["box"] = ralRespData["box"]
			}
		}

		if _, ok := ralRespData["default_box"]; ok {
			tplData["default_box"] = ralRespData["default_box"]
		}
		// guess也是一样, 如果没有赋值过那么就覆盖一次
		if _, ok := ralRespData["guess"]; ok && tplData["guess"] == nil {
			if guess, ok := ralRespData["guess"].(map[string]interface{}); ok {
				if guessData, ok := guess["data"].([]interface{}); ok {
					guessData = r.BlackListFilter(guessData)
					// 更新 guess 中的 data 字段
					guess["data"] = guessData
				}
				tplData["guess"] = guess
			}
		}
	}

	// 错误页场景修改guess
	if (*reparams)[constant.SCENE] == constant.ERR_CPAGE {
		r.modifyGuessData(tplData, dataParams)
	}
	// 处理rec_video视频数据
	if v, ok := resDataArr["1000514"]; ok {
		if len(v.QueryList) >= 3 {
			err := r.processRecVideo(tplData, v.QueryList, dataParams)
			if err != nil {
				r.addNotice("procRecVideoErr", err.Error())
			} else {
				r.addNotice("procRecVideoErr", "0")
			}
		} else {
			r.addNotice("procRecVideoErr", "less than 3")
		}
	}

	// 插入视频推荐词 content_info中的数据
	if err := r.insertContentInfoVideo(tplData, dataParams); err != nil {
		r.addNotice("insContVideoFail", err.Error())
	}
	r.addNotice("VidRecVidCtlSize", fmt.Sprintf("%d,%d", len(dataParams.VidRec), len(dataParams.VidCtl)))
	if filterNum, ok := r.ctx.Get("rongyao_blacklist_filter"); ok {
		r.addNotice("rongyao_blacklist_filter", fmt.Sprintf("%v", filterNum))
	}

	if RetnoFailed >= len(resDataArr) {
		tpl.Errno = "-5001"
		tpl.Errmsg = "request sughis error:resData.Retno!=0 "
		return tpl, errors.New(tpl.Errmsg)
	}

	tpl.Data = tplData
	return tpl, nil
}

// renameAndDeleteBoxItemRedundantField 将 item 中的字段重命名并删除多余的字段，包括：
//   - query -> text
//   - sTime -> start_time
//   - eTime -> stop_time
//   - priority（如果存在）
func renameAndDeleteBoxItemRedundantField(item map[string]interface{}) {
	item["query"] = item["text"]
	item["sTime"] = item["start_time"]
	item["eTime"] = item["stop_time"]
	delete(item, "text")
	delete(item, "open_search")
	delete(item, "start_time")
	delete(item, "stop_time")
	delete(item, "priority")
}

// 修改搜索发现数据
func (r *His_Rec) modifyGuessData(data map[string]interface{}, dataParams data.DataParms) {
	// 原有搜索发现数据
	guess, ok := data["guess"].(map[string]interface{})
	if !ok {
		return
	}
	guessList, ok := guess["data"].([]interface{})
	if !ok {
		return
	}

	saPre := "re_dl_se_error_rec_"
	if dataParams.Eventtype == "cpage_block" {
		saPre = "re_dl_se_error_block_rec_"
	} else if dataParams.Eventtype == "cpage_dead" {
		saPre = "re_dl_se_error_dead_rec_"
	} else if dataParams.Eventtype == "cpage_medium_risk" {
		saPre = "re_dl_se_error_medium_risk_rec_"
	} else if dataParams.Eventtype == "cpage_high_risk" {
		saPre = "re_dl_se_error_high_risk_rec_"
	} else if dataParams.Eventtype == "novel_detail" {
		saPre = "re_dl_se_error_novel_detail_rec_"
	} else if dataParams.Eventtype == "novel_reader" {
		saPre = "re_dl_se_error_novel_reader_rec_"
	}

	for i := 0; i < len(guessList); i++ {
		oneData, ok := guessList[i].(map[string]interface{})
		if !ok {
			continue
		}
		sa, ok := oneData["sa"].(string)
		if !ok {
			continue
		}

		// 原搜索发现sa：iph_igh_12345_gghis_rr
		// 修改后 - 错误页场景搜索发现sa：re_dl_se_error_rec_1_gghis_rr
		newSa := saPre + strconv.Itoa(i+1)
		exPos := 100000
		for j, x := range strings.Split(sa, "_") {
			if x == "12345" {
				exPos = j
			}
			if j > exPos {
				newSa += "_" + x
			}
		}
		oneData["sa"] = newSa
	}

	// 修改数量
	if dataParams.RecMaxReturn > 0 && dataParams.RecMaxReturn < len(guessList) {
		guess["data"] = guessList[:dataParams.RecMaxReturn]
	}

	v, ok := guess["data"].([]interface{})
	if ok && len(v) > 0 {
		r.addNotice("errPageGuessNum", strconv.Itoa(len(v)))
	}
}

func (r *His_Rec) processRecVideo(oriData map[string]interface{}, videoQuery interface{}, dataParams data.DataParms) error {
	// rec视频接口返回的数据
	videoQueryList, ok := videoQuery.([]interface{})
	if !ok {
		return errors.New("parse videoQuery err")
	}

	first, ok := videoQueryList[1].(map[string]interface{})
	if !ok {
		return errors.New("parse videoQueryList 1 err")
	}
	second, ok := videoQueryList[2].(map[string]interface{})
	if !ok {
		return errors.New("parse videoQueryList 2 err")
	}

	// 原有搜索发现数据
	guess, ok := oriData["guess"].(map[string]interface{})
	if !ok {
		return errors.New("parse oriData guess err")
	}
	old_guess_list, ok := guess["data"].([]interface{})
	if !ok {
		return errors.New("parse guess data err")
	}

	// 去重
	for i := 0; i < len(old_guess_list); i++ {
		oneData, ok := old_guess_list[i].(map[string]interface{})
		if !ok {
			continue
		}

		// 如果旧搜索发现和前2个videoQuery重复，删除旧搜索发现的query
		if oneData["text"] == first["query"] || oneData["text"] == second["query"] {
			old_guess_list = append(old_guess_list[:i], old_guess_list[i+1:]...)
			i--
		}
	}

	// 在搜索发现前面插入videoQuery的2个query
	new_first := map[string]interface{}{
		"text":  first["query"],
		"sa":    first["sa"],
		"color": 0,
		"tag":   "",
		"icon":  "",
	}

	new_second := map[string]interface{}{
		"text":  second["query"],
		"sa":    second["sa"],
		"color": 0,
		"tag":   "",
		"icon":  "",
	}

	// 构造新的搜索发现数据
	new_list := []interface{}{
		new_first,
		new_second,
	}

	new_list = append(new_list, old_guess_list...)

	// 控制文字显示蓝色
	text_style := map[string]string{
		"day_color":   "#4E6EF2", // 日间
		"dark_color":  "#4E6EF2", // 暗黑
		"night_color": "#263778", // 夜间
	}

	branch := data.GetVideoInfoBranch(dataParams)
	if branch == 2 {
		new_first["text_style"] = text_style
	} else if branch == 3 {
		new_first["text_style"] = text_style
		new_second["text_style"] = text_style
	}

	// 将新搜索发现数据赋值回去
	guess["data"] = new_list

	// 用于结果页空框展示
	hissug_box := hissug_box{
		ChangeWord: 1,
		Nid:        dataParams.ContentInfo["nid"],
		Data: []boxOne{
			{
				Query:    first["query"],
				ShowText: first["query"],
				Sa:       first["sa"],
				Idx:      0,
				Count:    5,
				STime:    1500000000,
				ETime:    time.Now().Unix() + 7*24*3600,
			},
		},
	}

	// 结果页框内词
	oriData["hissug_box"] = hissug_box

	return nil
}

// 插入视频推荐词 content_info中的数据
// nolint:gocyclo
func (r *His_Rec) insertContentInfoVideo(oriData map[string]interface{}, dataParams data.DataParms) error {
	if dataParams.Eventtype != "hissug" {
		return nil
	}
	if len(dataParams.VidRec) == 0 || len(dataParams.VidCtl) == 0 {
		return nil
	}

	reqParams := r.getRequest()
	// 插入视频推荐词 content_info中的数据
	saPrefix := "iph_igh_12345"
	_, ok := (*reqParams)[constant.SESSION_UID]
	// 已登录
	if ok {
		saPrefix = "igh_12345"
	}

	sa := ""
	if dataParams.VidRecSa != "" {
		sa = saPrefix + "_" + dataParams.VidRecSa
	}

	// 选rec_n个视频推荐词，插入插入搜索发现
	if dataParams.VidCtl["ins_guess"] == 1 && dataParams.VidCtl["rec_n"] != 0 {
		rec_n := dataParams.VidCtl["rec_n"]
		high_n := dataParams.VidCtl["high_n"]
		new_guess := []interface{}{}

		// 用于后续去重
		videoRecMap := map[string]struct{}{}
		// 已经插入数量
		ins_i := 0
		for i := 0; ins_i < rec_n && i < len(dataParams.VidRec); i++ {
			if dataParams.VidRec[i]["q"] == "" {
				continue
			}

			// 优先使用vidRec内层的rec_sa
			innerSa := sa
			if dataParams.VidRec[i]["rec_sa"] != "" {
				innerSa = saPrefix + "_" + dataParams.VidRec[i]["rec_sa"]
			}

			tmp := map[string]interface{}{
				"text":   dataParams.VidRec[i]["q"],
				"sa":     innerSa,
				"color":  0,
				"tag":    "",
				"icon":   "",
				"source": "custom",
			}

			if dataParams.VidRec[i]["recall_sa"] != "" {
				tmp["recall_sa"] = dataParams.VidRec[i]["recall_sa"]
			}

			// 前high_n个词高亮
			if ins_i < high_n {
				tmp["highlight"] = 1
			}
			new_guess = append(new_guess, tmp)
			videoRecMap[dataParams.VidRec[i]["q"]] = struct{}{}
			ins_i++
		}

		// 追加原有的guess数据
		guess, ok := oriData["guess"].(map[string]interface{})
		if !ok {
			return errors.New("parse oriData.guess err")
		}
		old_guess_list, ok := guess["data"].([]interface{})
		if !ok {
			// 策略未召回情况下，直接返回视频推荐词，不限制数量
			r.addNotice("insContVideoInfo", "parse guess.data err")
		} else {
			r.addNotice("insOldGuess", fmt.Sprintf("%d", len(old_guess_list)))
			for i := 0; i < len(old_guess_list); i++ {
				one, ok := old_guess_list[i].(map[string]interface{})
				if !ok {
					continue
				}
				text, ok := one["text"].(string)
				if !ok {
					continue
				}
				// guess数据在视频推荐词中出现过
				if _, ok := videoRecMap[text]; ok {
					continue
				}
				new_guess = append(new_guess, one)
			}
		}

		// 将新搜索发现数据赋值回去
		guess["data"] = new_guess
		r.addNotice("ins_guess", fmt.Sprintf("%d,%d,%d,%d,%d", ins_i, len(new_guess), rec_n, high_n, len(dataParams.VidRec)))
	}

	// 插入1个视频推荐词到underbox中
	if dataParams.VidCtl["ins_underbox"] == 1 {
		// 用于后续去重
		videoRecMap := map[string]struct{}{}
		new_underbox := []interface{}{}

		ins_i := 0
		for i := 0; ins_i < 1 && i < len(dataParams.VidRec); i++ {
			if dataParams.VidRec[i]["q"] == "" {
				continue
			}
			tmp := map[string]interface{}{
				"text":   dataParams.VidRec[i]["q"],
				"sa":     sa,
				"color":  0,
				"tag":    "",
				"icon":   "",
				"source": "custom",
			}
			new_underbox = append(new_underbox, tmp)
			videoRecMap[dataParams.VidRec[i]["q"]] = struct{}{}
			ins_i++
		}
		// 原有的underbox数据
		underbox, ok := oriData["under_box"].(map[string]interface{})
		if !ok {
			return errors.New("parse oriData.under_box err")
		}
		old_underbox_list, ok := underbox["data"].([]interface{})
		if !ok {
			return errors.New("parse under_box.data err")
		}

		for i := 0; i < len(old_underbox_list); i++ {
			one, ok := old_underbox_list[i].(map[string]interface{})
			if !ok {
				continue
			}
			text, ok := one["text"].(string)
			if !ok {
				continue
			}
			// unerbox数据在视频推荐词中出现过
			if _, ok := videoRecMap[text]; ok {
				continue
			}
			new_underbox = append(new_underbox, one)
			if len(new_underbox) >= 2 {
				break
			}
		}
		underbox["data"] = new_underbox
		r.addNotice("ins_underbox", fmt.Sprintf("%d,%d,%d,%d", ins_i, len(new_underbox), len(old_underbox_list), len(dataParams.VidRec)))
	}

	return nil
}

func modifyActivity(dataMap map[string]interface{}, adptparams *map[string]string) (map[string]interface{}, bool) {
	// 判断 投放方式 是否存在
	_, ok := dataMap["method"]
	if !ok {
		return nil, false
	}

	// jumpType=2表示跳转到C页面, 需要拼接c页面scheme
	if jumpType, ok := dataMap["jump_type"].(float64); ok && int(jumpType) == 2 {
		if homepageURL, ok := dataMap["homepage_url"].(string); ok && !strings.Contains(homepageURL, "&activityLoadingConfig=") {
			// ios 使用 dark
			params := map[string]interface{}{
				"loadingDarkIconUrl":      dataMap["topic_dark_img"],
				"loadingBgDarkStartColor": dataMap["topicimg_dark_top_backcolor"],
				"loadingBgDarkEndColor":   dataMap["topicimg_dark_bottom_backcolor"],
			}
			// 安卓 使用 night
			if (*adptparams)["isAndroid"] == "1" {
				params = map[string]interface{}{
					"loadingNightIconUrl":      dataMap["topic_night_img"],
					"loadingBgNightStartColor": dataMap["topicimg_night_top_backcolor"],
					"loadingBgNightEndColor":   dataMap["topicimg_night_bottom_backcolor"],
				}
			}

			params["loadingIconUrl"] = dataMap["topic_day_img"]
			params["loadingBgGradientPoint"] = dataMap["backlayer_gradient_point"]
			params["loadingBgStartColor"] = dataMap["topicimg_day_top_backcolor"]
			params["loadingBgEndColor"] = dataMap["topicimg_day_bottom_backcolor"]

			loading, ok := dataMap["is_display_loading_click"].(float64)
			// 开启loading时，增加参数
			if ok && int(loading) == 1 {
				params["loadingStatusBarStyle"] = fmt.Sprintf("%v", dataMap["dropdown_statusbar_color"]) // 下拉状态颜色
				params["loadingSearchBoxStyle"] = fmt.Sprintf("%v", dataMap["searchbox_a_style"])        // A页框样式
			}

			jsonParams, err := json.Marshal(params)
			if err == nil {
				dataMap["homepage_url"] = homepageURL + "&activityLoadingConfig=" + url.QueryEscape(string(jsonParams))
			}
			dataMap["homepage_url_no_load"] = homepageURL
		}
	}

	ret := map[string]interface{}{
		"data": dataMap,
	}

	return ret, true
}

type hissug_box struct {
	ChangeWord int         `json:"change_word"`
	Nid        interface{} `json:"nid"` // 视频nid
	Data       []boxOne    `json:"data"`
}

type boxOne struct {
	Query    interface{} `json:"query"`
	ShowText interface{} `json:"show_text"`
	Sa       interface{} `json:"sa"`
	Idx      int         `json:"idx"`
	Count    int         `json:"count"`
	ETime    int64       `json:"eTime"`
	STime    int64       `json:"sTime"`
}

// 初始化对端协议的结构
func (r *His_Rec) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:     "0",
		Errmsg:    "",
		Data:      datainfo,
		Switch:    common.SwitchConfMem.Switch,
		Requestid: r.getLogidStr(),
	}
	return ret
}

func (r *His_Rec) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	r.setTplOriginalData(tpl)
	r.setTplByteData(tplByte)
}

func (r *His_Rec) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Rec{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-rec", &His_Rec{})
}

func (r *His_Rec) BlackListFilter(data []interface{}) []interface{} {
	// 从上下文中获取过滤计数，并转换为 int 类型
	var filterCount int
	if v, ok := r.ctx.Get("rongyao_blacklist_filter"); ok {
		if count, ok := v.(int); ok {
			filterCount = count
		}
	}

	// 使用闭包在函数返回时更新上下文中的计数值
	defer func() {
		r.ctx.Set("rongyao_blacklist_filter", filterCount)
	}()

	// 当不需要过滤时，直接返回原始数据
	if (*r.getRequest())[constant.RongyaoBaipai] != "1" {
		return data
	}

	// 创建一个新的切片存放过滤后的数据
	filteredData := make([]interface{}, 0, len(data))
	for _, item := range data {
		// 尝试将每个元素转换为 map[string]interface{}
		m, ok := item.(map[string]interface{})
		if !ok {
			filteredData = append(filteredData, item)
			continue
		}

		// 检查是否存在 "text" 字段
		textVal, exists := m["text"]
		if !exists {
			filteredData = append(filteredData, item)
			continue
		}

		// 检查 "text" 字段是否为字符串
		str, ok := textVal.(string)
		if !ok {
			filteredData = append(filteredData, item)
			continue
		}

		// 如果满足黑名单条件，则增加计数并跳过该项
		if background.RongYao.IsAccurateMatch(str) || background.RongYao.IsFuzzyMatch(str) {
			filterCount++
			continue
		}

		// 不满足过滤条件则保留该项
		filteredData = append(filteredData, item)
	}

	return filteredData
}

// parseAndValidateRequestData 解析和验证请求数据
func (r *His_Rec) parseAndValidateRequestData(dataquery string, request *map[string]string, hisListErr HisListErr) (*data.DataParms, error) {
	var reqdata RecParms
	reqdata.ShowGuess = 1
	decodeErr := json.Unmarshal([]byte(dataquery), &reqdata)
	var dataParms data.DataParms

	// json格式不符合预期
	if decodeErr != nil {
		hisListErr.Errno = "-1001"
		hisListErr.Errmsg = "json unmarshal err"
		hisListErr.Requestid = r.getLogidStr()
		r.addNotice("hisRec_dataDecodeErr", dataquery)
		r.response(hisListErr)
		return nil, decodeErr
	}

	// 缺少 eventtype
	if reqdata.Eventtype == "" {
		hisListErr.Errno = "-1002"
		hisListErr.Errmsg = "params[Eventtype] is missing"
		hisListErr.Requestid = r.getLogidStr()
		r.response(hisListErr)
		return nil, errors.New("eventtype is missing")
	}

	dataParms.Eventtype = reqdata.Eventtype
	dataParms.BoxShowQuery = reqdata.BoxShowQuery

	// 缺少eventbox, errcpage场景下不校验
	if reqdata.Eventbox == "" && (*request)[constant.SCENE] != constant.ERR_CPAGE {
		hisListErr.Errno = "-1003"
		hisListErr.Errmsg = "params[Eventbox] is missing"
		hisListErr.Requestid = r.getLogidStr()
		r.response(hisListErr)
		return nil, errors.New("eventbox is missing")
	}
	dataParms.Eventbox = reqdata.Eventbox

	if reqdata.Hisdata != "" {
		decodeRet, _ := base64.Deocde(reqdata.Hisdata, 0)
		decodeErr := json.Unmarshal([]byte(decodeRet), &dataParms.Hisdata)
		if decodeErr != nil { // json格式不符合预期
			// 尝试解析ios的数据格式 add by shaoling
			type Text struct {
				T string `json:"text"`
			}
			var texts []Text
			var hisItems []string
			decodeErr = json.Unmarshal([]byte(decodeRet), &texts)
			if decodeErr != nil {
				dataParms.Hisdata = []string{}
			} else {
				for _, hisItem := range texts {
					hisItems = append(hisItems, hisItem.T)
				}
				if len(hisItems) != 0 {
					dataParms.Hisdata = hisItems
				} else {
					dataParms.Hisdata = []string{}
				}
			}
		}
	} else {
		dataParms.Hisdata = []string{}
	}

	if reqdata.HisSearchData != "" {
		decodeRet, _ := base64.Deocde(reqdata.HisSearchData, 0)
		decodeErr := json.Unmarshal([]byte(decodeRet), &dataParms.HisSearchData)
		if decodeErr != nil { // json格式不符合预期
			dataParms.HisSearchData = []map[string]interface{}{}
		}
	} else {
		dataParms.HisSearchData = []map[string]interface{}{}
	}

	if reqdata.Guessdata != "" {
		decodeRet, _ := base64.Deocde(reqdata.Guessdata, 0)
		decodeErr := json.Unmarshal([]byte(decodeRet), &dataParms.Guessdata)
		if decodeErr != nil { // json格式不符合预期
			dataParms.Guessdata = []map[string]interface{}{}
		}
	} else {
		dataParms.Guessdata = []map[string]interface{}{}
	}

	if reqdata.Boxdata != "" {
		decodeRet, _ := base64.Deocde(reqdata.Boxdata, 0)
		decodeErr := json.Unmarshal([]byte(decodeRet), &dataParms.Boxdata)
		if decodeErr != nil { // json格式不符合预期
			dataParms.Boxdata = []map[string]interface{}{}
		}
	} else {
		dataParms.Boxdata = []map[string]interface{}{}
	}

	if reqdata.Showquerys != "" {
		decodeRet, _ := base64.Deocde(reqdata.Showquerys, 0)
		decodeErr := json.Unmarshal([]byte(decodeRet), &dataParms.Showquerys)
		if decodeErr != nil { // json格式不符合预期
			dataParms.Showquerys = []string{}
		}
	} else {
		dataParms.Showquerys = []string{}
	}

	if reqdata.Eventquery != "" {
		dataParms.Eventquery = reqdata.Eventquery
	}

	if reqdata.Eventchannel != "" {
		dataParms.Eventchannel = reqdata.Eventchannel
	}
	if reqdata.Eventfrom != "" {
		dataParms.Eventfrom = reqdata.Eventfrom
	}
	if reqdata.Guessfeedbackquery != "" {
		decodeErr := json.Unmarshal([]byte(reqdata.Guessfeedbackquery), &dataParms.Guessfeedbackquery)
		if decodeErr != nil { // json格式不符合预期
			dataParms.Guessfeedbackquery = []string{}
			r.addNotice("feedbackqueryjsondecodeerror", decodeErr.Error())
		}
	} else {
		dataParms.Guessfeedbackquery = []string{}
	}

	switch reqdata.Guessshowrownum.(type) {
	case float64:
		dataParms.Guessshowrownum = reqdata.Guessshowrownum.(float64)
	default:
	}

	// 12.12版本解决WISE_HIS_PM带来的隐私模式判断问题
	dataParms.PrivateMode = reqdata.PrivateMode

	// 用于标识各种app不同场景下的接入
	dataParms.ClientName = reqdata.ClientName

	// 12.27 his框推荐相关参数
	if reqdata.ContentInfo != "" {
		decodeErr := json.Unmarshal([]byte(reqdata.ContentInfo), &dataParms.ContentInfo)
		if decodeErr != nil { // json格式不符合预期
			dataParms.ContentInfo = map[string]interface{}{}
			r.addNotice("ContentInfoErr", decodeErr.Error())
		} else {
			if v, ok := dataParms.ContentInfo["url"]; ok {
				if urlInfo, ok1 := v.(string); ok1 {
					urlInfoRes, err := url.QueryUnescape(urlInfo)
					if err == nil {
						dataParms.ContentInfo["url"] = urlInfoRes
					}
				}
			}

			// vid_rec格式：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/76YVMWYfJM/7fnymOz3cf/rWFAgJafrlpV4x
			if v, ok := dataParms.ContentInfo["vid_rec"]; ok {
				if vid_rec, ok := v.([]interface{}); ok {
					for _, v := range vid_rec {
						if one, ok := v.(map[string]interface{}); ok {
							tmp := map[string]string{}
							if q, ok := one["q"].(string); ok {
								tmp["q"] = q
							}
							if sa, ok := one["rec_sa"].(string); ok {
								tmp["rec_sa"] = sa
							}
							if sa, ok := one["recall_sa"].(string); ok {
								tmp["recall_sa"] = sa
							}
							if len(tmp) > 0 {
								dataParms.VidRec = append(dataParms.VidRec, tmp)
							}
						}
					}
				}
				// 不传给ral下游
				delete(dataParms.ContentInfo, "vid_rec")
			}

			if v, ok := dataParms.ContentInfo["rec_sa"]; ok {
				rec_sa, ok := v.(string)
				if ok {
					dataParms.VidRecSa = rec_sa
				}
				// 不传给ral下游
				delete(dataParms.ContentInfo, "rec_sa")
			}
		}
	} else {
		dataParms.ContentInfo = map[string]interface{}{}
	}

	// 控制视频推荐词
	if reqdata.VidCtl != "" {
		err := json.Unmarshal([]byte(reqdata.VidCtl), &dataParms.VidCtl)
		if err != nil {
			r.addNotice("VidCtlErr", err.Error())
		}
	}

	if reqdata.Target != "" && strings.Contains(reqdata.Target, "rec_session") {
		r.addNotice("rec_session", "1")
		// TODO: 有可能在这里引起了多个 Execute 读写request map 产生并发 fatel
		(*request)["rec_session"] = "1"
	}

	if reqdata.UnderBox != "" {
		decodeErr := json.Unmarshal([]byte(reqdata.UnderBox), &dataParms.UnderBox)
		if decodeErr != nil {
			dataParms.UnderBox = []map[string]interface{}{}
			r.addNotice("UnderBox", decodeErr.Error())
		}
	} else {
		dataParms.UnderBox = []map[string]interface{}{}
	}

	if reqdata.EventHisFrom != "" {
		dataParms.EventHisFrom = reqdata.EventHisFrom
	}

	// 获取uid
	uid, ok := (*request)[constant.SESSION_UID]
	if !ok {
		uid = "0"
	}
	dataParms.Puid = uid

	// 获取搜索sid
	sids, errck := r.ctx.Cookie("H_WISE_SIDS")
	if errck != nil || sids == "" {
		sids = ""
	}
	arrsid := strings.Split(sids, "_")
	var intsid []int
	for _, strsid := range arrsid {
		numid, _ := strconv.Atoi(strsid)
		intsid = append(intsid, numid)
	}

	// 搜索精选服务：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/MYFIkxNl2o/G89EI77DsJ/unnKbpqytF31k7
	// 命中全量新户his实验时，请求下游服务
	if common.GetSampleValue(r.ctx, "new_user_his", "0") == "1" {
		dataParms.NeedNewUserHis = true
	}
	// 7日内端新，请求下游服务，实验转全
	if common.IsNewUser(r.ctx) {
		dataParms.NeedNewUserHis = true
	}

	dataParms.Sids = intsid

	dataParms.TodayFirst = reqdata.TodayFirst
	dataParms.DisplayWidth = reqdata.DisplayWidth
	dataParms.RecVideoInfo = reqdata.RecVideoInfo
	dataParms.ColdLaunchTime = reqdata.ColdLaunchTime
	dataParms.PrivacyString = reqdata.EncodePrivacyData.HonorOaID
	// honor oaid 存在跳变问题，优先使用HonorOaID，为空时取正常的oaid
	if reqdata.EncodePrivacyData.HonorOaID == "" {
		dataParms.PrivacyString = reqdata.EncodePrivacyData.PrivacyString
	}
	dataParms.RecMaxReturn = reqdata.RecMaxReturn
	dataParms.TextCount = reqdata.TextCount
	dataParms.IntelligentHisType = reqdata.IntelligentHisType

	return &dataParms, nil
}
