package his

import (
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type His_Tcate struct {
	BasePage
}

//主要执行的函数入口
func (this *His_Tcate) Execute() {
	this.addNotice("sugnew", "1")
	request := this.getRequest()
	tplData := this.defaultTpl()

	// 检查是否登录
	if !common.IsLogin(request) {
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", "not login")
		tplData.Errno = "1001"
		tplData.Errmsg = "params[cookie.bduss] is missing"
		this.response(tplData)
		return
	}

	dataSrv := data.NewHSUG(this.ctx)
	ralGetErr := dataSrv.GetHSUGResponse()
	if ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		tplData.Errno = "4002"
		tplData.Errmsg = "ral request fail"
		this.response(tplData)
		return
	}
	resData := dataSrv.GetHISTplData()
	tpl, parseErr := this.parseRALResData(resData, tplData)
	if parseErr != nil {
		this.setErr()
		this.addNotice("ralErr", parseErr.Error())
		this.response(tpl)
		return
	}
	this.response(tplData)
	return
}

func (this *His_Tcate) parseRALResData(resData data.HisRALResponse, tpl HisResponse) (HisResponse, error) {

	if resData.Status == "0" || resData.Status == "2008" || resData.Status == "" {
		return tpl, nil
	}
	resbyte, _ := json.Marshal(resData)
	tpl.Errno = "5001"
	tpl.Errmsg = "request sughis error "
	return tpl, errors.New(string(resbyte))
}

//初始化对端协议的结构
func (this *His_Tcate) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *His_Tcate) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *His_Tcate) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &His_Tcate{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-tcate", &His_Tcate{})
}
