package his

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Pager interface {
	Execute()
	GetTplData() string
	GetTplByteData() []byte
	GetTplOriginData() interface{}
}

type PagerWithExecuteByStep interface {
	Pager
	ExecuteByStep() (multiRequest func(), parseResponse func())
}

type Page interface {
	newSelf(ctx *gdp.WebContext) (Pager, error)
}

// page层的属性尽可能全部私有 使用interface提供外部来操作与访问
type BasePage struct {
	ctx             *gdp.WebContext
	tplData         []byte
	tplDataOriginal interface{}
}

var adapters = make(map[string]Page)

func Register(name string, adapter Page) {
	if adapter == nil {
		panic("page: Register adapter is nil")
	}
	if _, ok := adapters[name]; ok {
		panic("page: Register called twice for adapter " + name)
	}
	adapters[name] = adapter
}

func NewPager(pageName string, ctx *gdp.WebContext) (Pager, error) {
	adapter, ok := adapters[pageName]
	if !ok {
		return nil, fmt.Errorf("page: unknown adapter %q (forgotten register?)", pageName)
	}
	retPage, nErr := adapter.newSelf(ctx)
	if nErr != nil {
		return nil, fmt.Errorf("page: create page %q fail", pageName)
	}
	return retPage, nil
}

func NewPagerWithExecuteByStep(pageName string, ctx *gdp.WebContext) (PagerWithExecuteByStep, error) {
	adapter, ok := adapters[pageName]
	if !ok {
		return nil, fmt.Errorf("page: unknown adapter %q (forgotten register?)", pageName)
	}
	retPage, nErr := adapter.newSelf(ctx)
	if nErr != nil {
		return nil, fmt.Errorf("page: create page %q fail", pageName)
	}
	if page, ok := retPage.(PagerWithExecuteByStep); ok {
		return page, nil
	}
	return nil, fmt.Errorf("page: create page %q fail", pageName)

}

func (b *BasePage) setTplData(str string) {
	b.tplData = []byte(str)
}

func (b *BasePage) GetTplData() string {
	return string(b.tplData)
}

func (b *BasePage) setTplByteData(bytes []byte) {
	b.tplData = bytes
	common.DumpData(b.ctx, "2shoubai", bytes)
}

func (b *BasePage) setTplOriginalData(tplData interface{}) {
	b.tplDataOriginal = tplData
}

func (b *BasePage) GetTplOriginData() interface{} {
	return b.tplDataOriginal
}

func (b *BasePage) GetTplByteData() []byte {
	return b.tplData
}

func (b *BasePage) getRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := b.ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (b *BasePage) getAdaption() *map[string]string {
	var adaption map[string]string
	if val, exists := b.ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	return &adaption
}

func (b *BasePage) getLogidStr() string {

	return b.ctx.GetLogID()
}
func (b *BasePage) addNotice(key, value string) {
	b.ctx.AddNotice(key, value)
}

func (b *BasePage) setErr() {
	b.ctx.DealSucc = false
}
