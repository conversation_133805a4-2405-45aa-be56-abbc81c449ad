package his

import (
	"encoding/json"
	"errors"
	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type HisRS struct {
	BasePage
}

func (h *HisRS) Execute() {
	request := h.getRequest()
	adaptions := h.getAdaption()
	tplData := h.defaultTpl()

	// 获取data参数
	reqData, err := h.getDataParams(request)
	if err != nil {
		h.handleErr("-3001", err.Error())
		return
	}

	// 检查url是否在白名单中。不计入错误
	if !h.checkURL(request, reqData) {
		tplData.Errno = "200"
		h.response(tplData)
		return
	}

	dataSrv := data.NewRS(h.ctx)
	ralGetErr := dataSrv.GetResponse(request, adaptions, reqData)
	if ralGetErr != nil {
		// 未召回数据时，不算作请求错误
		if errors.Is(ralGetErr, data.EMPTY_ERR) {
			tplData.Errno = "100"
			h.response(tplData)
			return
		} else {
			h.handleErr("-4002", ralGetErr.Error())
			return
		}
	}

	tplData.Data = dataSrv.GetHISTplData()
	h.response(tplData)

	return
}

func (h *HisRS) getDataParams(request *map[string]string) (data.RSDataParams, error) {
	reqData := data.RSDataParams{}
	dataquery := (*request)["data"]
	if dataquery == "" {
		return reqData, errors.New("data is empty")
	}

	err := json.Unmarshal([]byte(dataquery), &reqData)
	if err != nil {
		return reqData, err
	}

	return reqData, nil
}

// 检查参数白名单
func (h *HisRS) checkURL(request *map[string]string, reqData data.RSDataParams) bool {
	scene := (*request)[constant.SCENE]

	if scene == constant.CPAGE_HIGHLIGHT {
		u, err := url.Parse(reqData.URL)
		if err != nil {
			h.addNotice("parseUrlErr", err.Error())
			return false
		}
		if background.CPageHighlight.IsHostMatch(u.Host) || background.CPageHighlight.IsURLMatch(reqData.URL) {
			h.addNotice("inBlackList", reqData.URL)
			return false
		}
	}

	return true
}

func (h *HisRS) handleErr(errno string, msg string) {
	h.setErr()
	h.addNotice("ralErr", "1")
	h.addNotice("ralErrMsg", msg)

	hisListErr := HisListErr{
		Errno:     errno,
		Errmsg:    msg,
		Requestid: h.getLogidStr(),
	}

	h.response(hisListErr)
	return
}

// 初始化对端协议的结构
func (h *HisRS) defaultTpl() HisResponse {
	datainfo := []interface{}{}
	ret := HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (h *HisRS) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	h.setTplOriginalData(tpl)
	h.setTplByteData(tplByte)
}

func (h *HisRS) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &HisRS{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("his-rs", &HisRS{})
}
