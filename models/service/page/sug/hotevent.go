package sug

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

const (
	gaokao24Game = "24gaokao_game"
	questionGame = "question_game"
)

func hitHotEvent(ctx *gdp.WebContext, request *map[string]string) (string, string, bool) {
	if len(common.HotEventList) == 0 {
		return "", "", false
	}

	for _, event := range common.HotEventList {
		if event == nil {
			continue
		}
		if q, ok := event.HitGameQuery((*request)["query"]); ok {
			// 抽样开启
			if common.GetSampleValue(ctx, event.Game.SampleOpen, "-1") == "1" {
				return event.Game.GameName, q, true
			}

			// 配置强制开启
			if event.GameForceOpen() {
				return event.Game.GameName, q, true
			}
		}
	}
	return "", "", false
}

func getHotEventData(eventName string) (data.Wisesugnew_Type, int) {
	item := data.Wisesugnew_Type{}
	if len(common.HotEventList) == 0 {
		return item, -1
	}
	for _, event := range common.HotEventList {
		if event == nil {
			continue
		}
		if eventName != event.Game.GameName {
			continue
		}
		return getGameData(event, eventName)
	}
	return item, -1
}

func getGameData(event common.HotEvent, eventName string) (data.Wisesugnew_Type, int) {
	item := data.Wisesugnew_Type{}
	game, ok := common.SugDirectConf[eventName]
	if !ok {
		return item, -1
	}
	var flag interface{} = eventName
	if v, ok := game["flag"]; ok {
		flag = v
	}

	item.Type = "direct_new"
	sa := event.GetConf().Game.Sa
	if len(sa) == 0 {
		sa = event.GetConf().Game.Yyfrom
	}
	item.Sa = sa
	info := map[string]interface{}{
		"type":  SUG_DIR_GENERAL,
		"flag":  flag,
		"value": game,
	}

	infoBytes, _ := json.Marshal(info)
	item.Info = map[string]interface{}{
		"vec_str_raw": []interface{}{string(infoBytes)},
	}

	return item, event.GetConf().Game.InsertPos
}

func insertHotEvent(pos int, resG []data.Wisesugnew_Type, sigData data.Wisesugnew_Type) []data.Wisesugnew_Type {
	if pos < 0 {
		return resG
	}
	// 判断首条是否为异型sug，如果为异型sug，则不插入
	if len(resG) > 0 && resG[0].Type == "direct_new" {
		return resG
	}

	if pos >= len(resG) {
		pos = len(resG)
	}

	resG = append(resG, data.Wisesugnew_Type{}) // 对resG进行扩容
	copy(resG[pos+1:], resG[pos:])              // 往后移动
	resG[pos] = sigData                         // 插入目标数据

	return resG
}
