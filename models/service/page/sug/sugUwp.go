package sug

import (
	"encoding/json"
	//"fmt"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type Sug_Sug_Uwp struct {
	BasePage
}

func (this *Sug_Sug_Uwp) Execute() {
	request := this.getRequest()
	tplData := this.defaultTpl()

	//空query
	query := (*request)["query"]
	if query == "" {
		tplData.Errno = "9001"
		tplData.Msg = "no query"
		tplData.Type = "sug"
		this.response(tplData)
		return
	}

	//非法攻击或超长直接拒绝 utf8中文长度按3算
	if len(query) > 50 {
		this.response(tplData)
		return
	}

	vType, ok := (*request)["v"]
	if !ok || (vType != "1" && vType != "2" && vType != "3") {
		vType = "2" //降级处理版本号
	}

	//请求后端
	dataSrv := data.NewWiseSug(this.ctx)
	err := dataSrv.GetWiseSugResponse(query)
	if err != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		tplData.Errno = "9002"
		tplData.Msg = "wisesug request fail"
		this.response(tplData)
		return
	}

	//解析后端参数
	requestData := dataSrv.GetWisesugTplData()
	parsedData, err := this.responseWithType(requestData, vType)
	if err != nil {
		this.setErr()
		this.addNotice("parseErr", "1")
		tplData.Errno = "9003"
		tplData.Msg = "flow err"
		this.response(tplData)
		return
	}

	this.response(parsedData)
	return
}

func (this *Sug_Sug_Uwp) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *Sug_Sug_Uwp) responseWithType(res data.Wisesug_Response, v string) (Sug_Sug_Response, error) {
	return this.parseType_2(res)
}

func (this *Sug_Sug_Uwp) parseType_2(res data.Wisesug_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()
	if len(res.Suglist) == 0 {
		return tplData, nil //无sug列表
	}

	for _, value := range res.Suglist {
		if value == "" {
			continue
		}
		tmp := Sug_Sug_Response_Type2_Data_Uwp{
			Word: value,
			Type: "0",
		}
		tplData.Data = append(tplData.Data, tmp)
	}
	return tplData, nil
}

//初始化复杂的结构
func (this *Sug_Sug_Uwp) defaultTpl() Sug_Sug_Response {
	ext_body := Sug_Sug_Response_Extend_Query{
		Type: "0",
		Word: (*this.getRequest())["query"],
	}
	ext := Sug_Sug_Response_Extend{
		Query: ext_body,
	}
	data := []interface{}{}

	ret := Sug_Sug_Response{
		Errno:  "0",
		Msg:    "",
		Type:   "sug",
		Extend: ext,
		Data:   data,
		Switch: common.SwitchConfMem.Switch,
	}

	return ret
}

func (this *Sug_Sug_Uwp) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Sug_Sug_Uwp{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("sug-uwp", &Sug_Sug_Uwp{})
}
