package sug

import (
	"fmt"
	"math"
	"net/http"
	"net/url"
	"path/filepath"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func hotEventInit() {
	// 顺序要跟HotEventList一致
	for file := range common.HotEventList {
		var tmp common.HotEventBase
		filePath := filepath.Join(env.ConfRootPath(), file)
		_, err := toml.DecodeFile(filePath, &tmp)
		if err != nil {
			panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
		}
		*common.HotEventList[file] = tmp
	}
}

func Test_hitHotEvent(t *testing.T) {
	hotEventInit()

	type args struct {
		ctx     *gdp.WebContext
		request *map[string]string
	}

	ctx := createGetWebContext()
	ctx.Request.AddCookie(&http.Cookie{
		Name:     "CS_W_SIDS",
		Value:    url.QueryEscape("123"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	request := &map[string]string{
		"query": "答题",
	}
	common.SampleConf = map[string]map[string]string{
		"question_game": map[string]string{
			"123": "1",
		},
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
		want2 bool
	}{
		{
			"test_question_hitHotEvent",
			args{
				ctx,
				request,
			},
			"question_game",
			"答题红包",
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := hitHotEvent(tt.args.ctx, tt.args.request)
			if got != tt.want {
				t.Errorf("hitHotEvent() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("hitHotEvent() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("hitHotEvent() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}

}

func Test_getHotEventData(t *testing.T) {
	hotEventInit()
	sugDirectInit()

	type args struct {
		name string
	}

	tests := []struct {
		name  string
		args  args
		want  string
		want1 int
	}{
		{
			"test_question_hitHotEvent",
			args{
				"question_game",
			},
			"searchpromo_selftask_dati_sug",
			0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getHotEventData(tt.args.name)
			if got.Sa != tt.want {
				t.Errorf("getHotEventData() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("getHotEventData() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}

}

func Test_insertHotEvent(t *testing.T) {
	type args struct {
		pos     int
		resG    []data.Wisesugnew_Type
		sigData data.Wisesugnew_Type
	}

	tests := []struct {
		name    string
		args    args
		wantQ   string
		wantLen int
	}{
		{
			"test_insertHotEvent",
			args{
				-1,
				[]data.Wisesugnew_Type{
					{
						Q: "test0",
					},
				},
				data.Wisesugnew_Type{
					Q: "insert",
				},
			},
			"test0",
			1,
		},
		{
			"test_insertHotEvent",
			args{
				0,
				[]data.Wisesugnew_Type{
					{
						Q:    "test0",
						Type: "direct_new",
					},
				},
				data.Wisesugnew_Type{
					Q: "insert",
				},
			},
			"test0",
			1,
		},
		{
			"test_insertHotEvent",
			args{
				0,
				[]data.Wisesugnew_Type{
					{
						Q:    "test0",
						Type: "sug",
					},
				},
				data.Wisesugnew_Type{
					Q: "insert",
				},
			},
			"insert",
			2,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := insertHotEvent(tt.args.pos, tt.args.resG, tt.args.sigData)
			if len(got) != tt.wantLen {
				t.Errorf("insertHotEvent() len = %v, want %v", len(got), tt.wantLen)
			}
			if got[0].Q != tt.wantQ {
				t.Errorf("insertHotEvent() got_Q = %v, want %v", got[0].Q, tt.wantQ)
			}
		})
	}

}
