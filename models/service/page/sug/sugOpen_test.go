package sug

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestSug_Sug_Open_parseNewRequestData(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		resData data.Wisesugnew_Response
		tpl     Sug_Sug_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseNewRequestData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Open{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		err := this.parseNewRequestData(tt.args.resData, tt.args.tpl)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Open_parseNewRequestData, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestSug_Sug_Open_parseOldRequestData(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		resData interface{}
		tpl     Sug_Sug_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseOldRequestData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Open{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		err := this.parseOldRequestData(tt.args.resData, tt.args.tpl)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Open_parseOldRequestData, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestSug_Sug_Open_defaultTpl(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		typeStr string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Open{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got := this.defaultTpl(tt.args.typeStr)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Open_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Open_response(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Sug_Sug_Open{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestSug_Sug_Open_newSelf(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Open{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Open_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Open_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
