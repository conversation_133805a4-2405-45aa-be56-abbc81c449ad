package sug

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"unicode"

	"github.com/go-ego/gse"
	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type Sug_Sug_Iphone_New struct {
	Sug_Sug_Common
}

// 虚词标记
var virtualPos = [...]string{"d", "p", "c", "u", "e"}
var puncList = [...][2]rune{
	{',', '，'}, {'.', '。'}, {';', '；'}, {'?', '？'},
	{'(', '（'}, {')', '）'}, {'!', '！'}, {':', '：'},
}

var numCh = map[string]struct{}{"0": {}, "1": {}, "2": {}, "3": {}, "4": {}, "5": {}, "6": {}, "7": {}, "8": {}, "9": {}}

var (
	engNumReg = regexp.MustCompile(`^[A-Za-z]+[0-9]+$`)
	numEngReg = regexp.MustCompile(`^[0-9]+[A-Za-z]+$`)
	numReg    = regexp.MustCompile(`[0-9]+`)
	engReg    = regexp.MustCompile(`[A-Za-z]+`)
)

type StrongSug struct {
	pos         int
	maxPriority int
}

type WeakSug struct {
	flag     string
	priority int
	pos      int
}

var StrongSugPriority = map[string]int{
	"yunying":        8,
	"24gaokao_game":  7,
	"super_pinzhuan": 6,
	"fuwu":           5,
	"entity":         4,
	"university":     4,
	"answer":         4,
	"postcode":       4,
	"zonecode":       4,
	"constellation":  4,
	"baijiahao":      3,
	"mini_app":       2,
	"note":           1,
}

const (
	DirictSug = "direct_new"

	SugHealthType = "health"
)

// 主要执行的函数入口
func (this *Sug_Sug_Iphone_New) Execute() {
	request := this.getRequest()
	tplData := this.defaultTpl()

	// 空query
	query := (*request)["query"]
	if query == "" {
		tplData.Errno = "9001"
		tplData.Msg = "no query"
		tplData.Type = "sug"
		this.response(tplData)
		return
	}

	// 非法攻击或超长直接拒绝 utf8中文长度按3算
	if len(query) > 50 {
		this.response(tplData)
		return
	}

	vType, ok := (*request)["v"]
	if !ok || (vType != "1" && vType != "2" && vType != "3") {
		vType = "1" // 降级处理版本号
	}

	// 请求后端
	dataSrv := data.NewWiseSugNew(this.ctx)
	dataSrv.NeedPredictSug = this.needPredict // 用于判断是否需要并行请求sug预测服务
	if ralGetErr, responseStr := dataSrv.GetWiseSugResponse(query); ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", fmt.Sprintf(`errmsg:%s,responseStr:%#v`, ralGetErr.Error(), responseStr))
		tplData.Errno = "9002"
		tplData.Msg = "wisesug request fail"
		this.response(tplData)
		return
	}

	// 解析后端参数
	requestData := dataSrv.GetWisesugTplData()
	parsedData, err := this.responseWithType(requestData, vType)
	if err != nil {
		this.setErr()
		this.addNotice("parseErr", "1")
		tplData.Errno = "9003"
		tplData.Msg = "flow err"
		this.response(tplData)
		return
	}
	this.response(parsedData)

	if this.needPredict {
		presearchSugRespData := dataSrv.GetPresearchSugData()
		if presearchSugRespData.Presearch != "1" {
			this.setPresearchData("", "") // 预取词置空
			return
		}
		this.setPresearchData(query, "")

	}
	return
}

func (this *Sug_Sug_Iphone_New) responseWithType(res data.Wisesugnew_Response, v string) (Sug_Sug_Response, error) {
	switch v {
	case "1":
		return this.parseV1(res)
	case "2":
		return this.parseV2(res)
	case "3":
		return this.parseV3(res)
	default:
		return Sug_Sug_Response{}, nil
	}
}

func (this *Sug_Sug_Iphone_New) parseV1(res data.Wisesugnew_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()

	if len(res.G) == 0 {
		return tplData, nil // 无sug列表
	}

	for index, value := range res.G {
		if value.Type != "sug" || value.Q == "" {
			continue
		}
		tmp := Sug_Sug_Response_Type1_Data{
			Word: value.Q,
			Type: "0",
			Sa:   value.Sa,
		}
		if index == 0 && res.Q == value.Q {
			this.setPresearchData(value.Q, value.Sa)
		}
		tplData.Data = append(tplData.Data, tmp)
	}
	return tplData, nil
}

func (this *Sug_Sug_Iphone_New) parseV2(res data.Wisesugnew_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()

	if len(res.G) == 0 {
		return tplData, nil // 无sug列表
	}

	for index, value := range res.G {
		if value.Type != "sug" || value.Q == "" {
			continue
		}
		tmp := Sug_Sug_Response_Type2_Data{
			Word: value.Q,
			Type: 0,
			Sa:   value.Sa,
		}
		if index == 0 && res.Q == value.Q {
			this.setPresearchData(value.Q, value.Sa)
		}
		tplData.Data = append(tplData.Data, tmp)
	}

	// 简单搜索app基础库较旧，基于v2协议开发
	// 识别流量进行部分字段适配
	if common.IsChatSearch(this.getRequest()) {
		tplData.Slid = res.Slid
	}

	return tplData, nil
}

func (this *Sug_Sug_Iphone_New) parseV3(res data.Wisesugnew_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()
	tplData.Slid = res.Slid
	tplData.GMotStyle = res.GMotStyle
	tplData.GMotTitle = res.GMotTitle
	request := this.getRequest()
	adaptions := this.getAdaption()

	if len(res.GMot) != 0 {
		for i, v := range res.GMot {
			parsed, ok := this.parseType(&v, i, nil)
			if !ok {
				continue
			}
			tplData.GMot = append(tplData.GMot, parsed)
		}
	}

	if len(res.G) == 0 {
		return tplData, nil // 无sug列表
	}

	// 切词处理
	queryCut, regexpList := preProcessPos(this.ctx, request, adaptions, res.Q)

	needHotEvent := true
	if v, err := this.ctx.Cookie("ST"); err == nil {
		iv, _ := strconv.ParseInt(strings.TrimSpace(v), 10, 64)
		if iv > 0 {
			needHotEvent = false
		}
	}

	// 运营事件插入
	if needHotEvent {
		if eventName, changeQ, hit := hitHotEvent(this.ctx, this.getRequest()); hit {
			if sigG, pos := getHotEventData(eventName); pos >= 0 {
				sigG.Q = changeQ
				res.G = insertHotEvent(pos, res.G, sigG)
			}
		}
	}

	if common.GetSampleValue(this.ctx, "pinzhuan_sug_style", "0") == "1" {
		var strongSugData = StrongSug{
			pos:         -1,
			maxPriority: 0,
		}
		for index := range res.G {
			// 特型sug PK
			processStrongSugPk(this.ctx, &res.G[index], index, &strongSugData)

			// 增加品专特型sug
			ok, msg := addPinzhuanStrongSug(&res.G[index], index, &strongSugData)
			if ok {
				this.addNotice("pzSugDirectOK_"+strconv.Itoa(index), msg)
			}

			// 增加品专标签
			ok, msg = addPinzhuanWeakSug(&res.G[index], index, strongSugData)
			if ok {
				this.addNotice("addPinzhuanWeakSugOk_"+strconv.Itoa(index), msg)
			}
		}
	}

	// 添加电商sug
	if common.GetSampleValue(this.ctx, "sug_shop", "0") != "0" {
		for index := range res.G {
			ok := addShopSug(this.ctx, &res.G[index])
			if ok {
				this.addNotice("addShopSugOK_"+strconv.Itoa(index), "1")
			}
		}
	}

	// 按优先级改写置顶sug
	processInsertSugText(this.ctx, res.G)

	if checkSugPk(this.ctx) {
		// 标签sug PK
		pkQueue := make([]WeakSug, 0, len(res.G))
		for index := range res.G {
			if err := processWeakSugPk(&res.G[index], index, &pkQueue); err != nil {
				this.addNotice("error: weakPk_", err.Error())
			}
		}
		// 删除多余的标签sug，只保留2条
		if len(pkQueue) > 2 {
			// 对队列按照优先级和输入顺序进行排序
			sortPkFlag(pkQueue)

			this.addNotice("weakPkQueue", strconv.Itoa(len(pkQueue)))
			for i := 2; i < len(pkQueue); i++ {
				setNormalSug(&res.G[pkQueue[i].pos], pkQueue[i].pos+1)
				this.addNotice("weakPk_"+strconv.Itoa(i), pkQueue[i].flag)
			}
		}
	}

	for index, value := range res.G {
		// sug直达样式数据迁移处理
		if value.Type == "direct_new" {
			ok, msg := processSugDirect(&value, res.Slid, index, request, adaptions, this.ctx)
			if ok {
				this.addNotice("sugDirectOK_"+strconv.Itoa(index), msg)
			} else {
				this.addNotice("sugDirectFail_"+strconv.Itoa(index), msg)
			}
		}
		cutPos := getHighlight(this.ctx, queryCut, regexpList, res.Q, value.Q, &value)
		// if len(cutPos) != 0 {
		// 	this.ctx.AddNotice(fmt.Sprintf("posMap_%d", index), fmt.Sprintf("s:%s,m:%v", value.Q, cutPos))
		// }

		parsed, ok := this.parseType(&value, index, cutPos)
		if !ok {
			continue
		}
		if index == 0 && res.Q == value.Q {
			this.setPresearchData(value.Q, value.Sa)
		}
		tplData.Data = append(tplData.Data, parsed)
	}

	return tplData, nil
}
func checkSugPk(ctx *gdp.WebContext) bool {
	// sug电商实验/添加了电商sug
	if common.GetSampleValue(ctx, "sug_shop", "0") != "0" {
		return true
	}
	// 品专sug
	if common.GetSampleValue(ctx, "pinzhuan_sug_style", "0") != "0" {
		return true
	}
	return false
}

// 为第一条完全匹配的sug，添加图片/简介
// ctx：上下文
// sugType: 垂类名，需要与新增词典名保持一致
// sgu：本次检索第一条sug对象的指针
// return:返回值表示是否匹配成功
func processSugText(ctx *gdp.WebContext, sugTpye string, sug *data.Wisesugnew_Type) bool {
	if sug == nil {
		return false // 首位sug为空，直接返回
	}
	vecStrRaw := BuildVecStrRaw(ctx, sugTpye, sug)

	// 未匹配
	if vecStrRaw == nil {
		return false
	}
	// 首条sug
	vecStr, err := json.Marshal(vecStrRaw)
	if err != nil {
		ctx.AddNotice("processHealthSug_marshal", err.Error())
		return false
	}
	sug.Type = DirictSug
	sug.Info = map[string]interface{}{
		"vec_str_raw": []interface{}{
			string(vecStr),
		},
	}
	ctx.AddNotice("addsugType_top1: ", sugTpye)
	return true
}

func processInsertSugText(ctx *gdp.WebContext, sugG []data.Wisesugnew_Type) {
	pklist, err := getSugConfPklist()
	if err != nil {
		ctx.AddNotice("getSugConfPklist_fail: ", err.Error())
	}
	for _, v := range pklist {
		// 不存在首条sug，直接退出
		if len(sugG) == 0 {
			break
		}
		// 词典不存在
		if _, has := common.SugTextConf[v.flag]; !has {
			continue
		}
		// 健康
		if v.flag == SugHealthType && common.GetSampleValue(ctx, "sug_health", "0") != "0" {
			// 匹配成功返回，优先级高优先匹配
			if processSugText(ctx, v.flag, &sugG[0]) {
				break
			}
		}
	}
}

// 构造默认vec_str_raw和全部vecStrAllType，对用户协程安全的map
func buildDefaultVecStrRaw(ctx *gdp.WebContext) (map[string]interface{}, map[string]interface{}) {
	// 默认vecStr
	var defaultVecStr map[string]interface{}
	// 配置下的全部vecStr
	var vecStrAllType map[string]interface{}

	// sugConf
	rawbt, err := json.Marshal(common.SugDirectConf["sugConf"])
	if err != nil {
		ctx.AddNotice("buildDefaultVecStrRaw_sugConf", err.Error())
		return nil, nil
	}
	err = json.Unmarshal(rawbt, &vecStrAllType)
	if err != nil {
		ctx.AddNotice("buildDefaultVecStrRaw_Unmarshal", err.Error())
	}

	// defaultVecStrRaw
	rawbt, err = json.Marshal(vecStrAllType["default"])
	if err != nil {
		ctx.AddNotice("buildDefaultVecStrRaw_Marshal", err.Error())
	}
	err = json.Unmarshal(rawbt, &defaultVecStr)
	if err != nil {
		ctx.AddNotice("buildDefaultVecStrRaw_Unmarshal", err.Error())
	}
	return defaultVecStr, vecStrAllType
}

// 首位sug出图,
func BuildVecStrRaw(ctx *gdp.WebContext, sugTpye string, sug *data.Wisesugnew_Type) map[string]interface{} {
	if sug == nil {
		return nil
	}
	vecStr, vecStrAllType := buildDefaultVecStrRaw(ctx)
	if vecStr == nil || vecStrAllType == nil {
		return nil
	}

	// 初始化置空，新增sug词典名需与sugType一致
	healthSugTextConf := common.SugTextConf[sugTpye]
	if healthSugTextConf == nil || len(healthSugTextConf) == 0 {
		ctx.AddNotice("BuildVecStrRaw_HealthSugConf = ", "nil")
		return nil
	}
	// 获取首位sug信息
	query := sug.Q
	// 匹配失败不处理
	if _, exits := healthSugTextConf[query]; !exits {
		return nil
	}

	img := healthSugTextConf[query][1]
	brief := healthSugTextConf[query][2]
	healthConf, _ := vecStrAllType[sugTpye].(map[string]interface{})

	// health覆盖默认
	for k, v := range healthConf {
		vecStr[k] = v
	}

	// 构造value
	value, ok := vecStr["value"].(map[string]interface{})
	if !ok {
		// 不存在则创建
		value = make(map[string]interface{})
	}

	value["img"] = img
	value["brief"] = brief
	value["name"] = []interface{}{
		map[string]interface{}{
			"color": "#000",
			"text":  query,
		},
	}
	value["query"] = query
	vecStr["value"] = value
	vecStr["type"] = SUG_DIR_GENERAL
	return vecStr
}

// 此处根据不同的type分类 做不同的对端协议转换和特殊处理
// 详细可参考 http://wiki.baidu.com/pages/viewpage.action?pageId=576601410
func (s *Sug_Sug_Iphone_New) parseType(t *data.Wisesugnew_Type, index int, cutPos []map[string]int) (interface{}, bool) {
	requests := s.getRequest()
	adaptions := s.getAdaption()

	switch t.Type {
	case "sug": // 普通SUG
		return Sug_Sug_Response_Type1_Data{
			Word: t.Q,
			Type: "0",
			Sa:   t.Sa,
			Pos:  cutPos,
		}, true
	case "his": // 搜索历史
		// 满足版本要求，才返回 SUG_HIS 类型
		if ((*requests)["osbranch"] == "i0" && version.Compare((*adaptions)["bd_version"], "13.11", ">=")) ||
			((*requests)["osbranch"] == "i3" && version.Compare((*adaptions)["bd_version"], "5.38", ">=")) {
			return Sug_Sug_Response_Type2_Data_Uwp{
				Word: t.Q,
				Type: SUG_HIS,
				Sa:   t.Sa,
				Pos:  cutPos,
			}, true
		}

		return Sug_Sug_Response_Type1_Data{
			Word: t.Q,
			Type: "0",
			Sa:   t.Sa,
			Pos:  cutPos,
		}, true
	case "direct_website": // 网址直达
		his := Sug_Sug_Response_Dir_Hissync{
			Query: t.Info["site"].(string),
		}
		hissync, _ := json.Marshal(his) // 不是跳转到结果页的 都需要手动加一个his逻辑
		return Sug_Sug_Response_Dir_Url_Iphone{
			Word:    t.Q,
			Type:    "1", // 固定
			Sa:      t.Sa,
			Hissync: string(hissync),
			Wapurl:  t.Info["show_url"].(string),
			Wwwurl:  t.Info["show_url"].(string),
		}, true
	case "direct_new": // 小程序类或特殊手百数据类
		return s.parseTypeDirectNew(t, index, cutPos)
	}

	return nil, false
}

// type为direct_website的数据处理
// 当数据指定为direct_website时，内部使用另外一套结构 整体数据为json字符串 需要反序列化
// 目前所有类型的数据分为两类处理方式
// 1.非透传类 例如小说 这类数据有较复杂的处理逻辑 SU只提供源数据 需要在下发前做加工 仅支持单个
// 2.透传类 例如小程序小游戏 这类数据只做版本控制逻辑 不对里面的值进一步处理 支持多个组合成一个数组
func (s *Sug_Sug_Iphone_New) parseTypeDirectNew(t *data.Wisesugnew_Type, index int, cutPos []map[string]int) (interface{}, bool) {
	ji, ok := t.Info["vec_str_raw"]
	if !ok {
		return nil, false
	}
	jsonStrArr := ji.([]interface{})
	if len(jsonStrArr) == 0 {
		return nil, false
	}

	var vecRaw data.Swan_Sug
	var swanRes []data.Swan_Sug_Res
	isNewTemplate := false
	requests := s.getRequest()
	adaptions := s.getAdaption()
	bdVersion := (*adaptions)["bd_version"]
	osbranch := (*requests)["osbranch"]

	// versionControl比较对象
	versionCompareTarget := bdVersion
	if osbranch != "i0" && (*requests)["fv"] != "" {
		bdVersion = (*requests)["fv"] // 其他百度系app有fv标识同步的基线版本代码
	}

	st := (*requests)["st"]
	shieldSt, _ := strconv.Atoi(st)
	// sst, ipad屏蔽sug小程序结果
	sst := (*requests)["sst"]
	shieldSST, _ := strconv.Atoi(sst)
	s.addNotice("sst", sst)

	// 大搜SU每一条数据 对应一个客户端type
	// 若同时出现非透传和透传类型 此处以第一个值作为判断标准
	// 即第一个若为非透传类型，则后面的多个全部不解析；若第一个为透传类型，后面出现费透传类型全部不解析
	for i := range jsonStrArr {
		jsonStrArrString, ok := jsonStrArr[i].(string)
		if !ok {
			continue
		}
		s.addNotice("vecRawNew_"+strconv.Itoa(index), jsonStrArrString)

		err := json.Unmarshal([]byte(jsonStrArrString), &vecRaw)
		if err != nil {
			continue
		}

		if vecRaw.Flag == "mini_app" && (shieldSt > 0 || shieldSST == 1) {
			continue
		}

		allowLite := false
		if vecRaw.Type == SUG_DIR_GENERAL && t.Sa == "24gaokao_game" && common.GetAppBranch(osbranch) == common.LITE_APP {
			allowLite = true
		}

		// 优先走通用直达模板逻辑
		if (vecRaw.Type == SUG_DIR_GENERAL && !version.Compare(bdVersion, "11.21", "<")) || allowLite {
			versionControlInfo := vecRaw.VersionControl
			versionControlRet := s.versionControl(versionControlInfo, osbranch, versionCompareTarget, allowLite, vecRaw.Flag)
			if versionControlRet {
				isNewTemplate = true
				tmp := data.Swan_Sug_Res{
					Flag:  vecRaw.Flag,
					Value: vecRaw.Value,
				}
				swanRes = append(swanRes, tmp)
			}
		}

		if !isNewTemplate {
			if vecRaw.Type == SUG_DIR_GENERAL && vecRaw.VersionControl1 != nil {
				vecRaw.VersionControl = vecRaw.VersionControl1
				vecRaw.Value = vecRaw.Value1
			}

			// 当且仅当第一个为非透传的时候 截断处理下发。小说非透传
			if i == 0 && vecRaw.Flag == "novel" {
				return s.parseTypeNovel(t, &vecRaw)
			}

			versionControlInfo := vecRaw.VersionControl
			versionControlRet := s.versionControl(versionControlInfo, osbranch, versionCompareTarget, false, vecRaw.Flag)
			if versionControlRet {
				tmp := data.Swan_Sug_Res{
					Flag:  vecRaw.Flag,
					Value: vecRaw.Value,
				}
				swanRes = append(swanRes, tmp)
			}
		}
	}

	if swanRes == nil { // 全部过滤或无下发数据
		if t.Q != "" && t.Sa != "24gaokao_game" { // 使用Q 对版本不兼容的数据做降级
			// 且不是高考强插的sug，就会降级保留
			return Sug_Sug_Response_Type1_Data{
				Word: t.Q,
				Type: "0",
				Sa:   t.Sa,
				Pos:  cutPos,
			}, true
		}
		// 否则如果q为空或者是高考强插sug，就删除丢弃
		return nil, false
	}
	swanResStr, encodeErr := json.Marshal(swanRes)
	if encodeErr != nil { // 序列化失败
		return nil, false
	}

	sugType := SUG_DIR_SWAN
	if isNewTemplate {
		sugType = SUG_DIR_GENERAL
	}
	return Sug_Sug_Response_Dir_Swan{
		Type: sugType,
		Data: string(swanResStr),
		Sa:   t.Sa,
		Pos:  cutPos,
	}, true
}

func (this *Sug_Sug_Iphone_New) versionControl(versionControlInfo data.VersionCtr, osbranch string, bdVersion string,
	allowLite bool, flag string) bool {

	// 极速版只用6.26以上, 不判断versionControlInfo中的控制
	if common.GetAppBranch(osbranch) == common.LITE_APP && version.Compare(bdVersion, "6.26", ">=") {
		if flag == "note" || flag == "mini_app" || flag == "baijiahao" || flag == "aichat" {
			return false
		}
		return true
	}

	if versionControlInfo != nil { // 带VersionControl的数据 走osbranch和min+max逻辑
		dataConfig, dataConfigOk := versionControlInfo[osbranch]
		if !dataConfigOk {
			return false // 客户端osbranch不属于下发范畴
		}

		if (dataConfig.Min != "" && version.Compare(bdVersion, dataConfig.Min, "<")) ||
			(dataConfig.Max != "" && version.Compare(bdVersion, dataConfig.Max, ">")) {
			return false // 客户端bd_version不属于下发范畴
		}
	} else {
		if allowLite && common.GetAppBranch(osbranch) == common.LITE_APP && version.Compare(bdVersion, "6.26", ">=") {
			return true // 极速版只用6.26以上
		}
		if version.Compare(bdVersion, "10.11", "<") {
			return false // 默认按10.11发版的分割线一刀切 小于10.11的全部跳过 大于等于的全部下发
		}
	}
	return true
}

// 小说SUG的处理方法
func (this *Sug_Sug_Iphone_New) parseTypeNovel(t_original *data.Wisesugnew_Type, t *data.Swan_Sug) (interface{}, bool) {
	requests := this.getRequest()
	adaptions := this.getAdaption()
	osBranch := (*requests)["osbranch"]
	bdVersion := (*adaptions)["bd_version"]
	query := (*requests)["query"]
	ivalue := t.Value.(map[string]interface{})
	nvalue := make(map[string]string, len(ivalue))
	for key, i := range ivalue {
		switch v := i.(type) {
		case string:
			nvalue[key] = v
		default:
			nvalue[key] = ""
		}
	}

	// ios主线版11.0以上有小说直达sug
	if version.Compare(bdVersion, "********", ">=") && osBranch == "i0" {
		detailSchemeParams := url.Values{}
		detailSchemeParams.Set("title", "小说详情")
		detailSchemeParams.Set("url", "https://boxnovel.baidu.com/boxnovel/detail?action=novel&type=detail")
		detailSchemeParams.Set("method", "post")
		detailSchemeParams.Set("needParams", "1")
		detailSchemeParams.Set("pagetype", "1")
		detailSchemeParams.Set("args", fmt.Sprintf(`data={"fromaction":"search_sug","gid":"%s"}`, nvalue["bookid"]))
		detailSchemeParams.Set("upgrade", "1")
		detailSchemeEncode := "baiduboxapp://v2/novel/openSubPage?" + detailSchemeParams.Encode()
		chapterSchemeParam := Sug_Novel_Scheme{
			Gid:           nvalue["bookid"],
			Name:          nvalue["title_main"],
			Author:        nvalue["author"],
			Image:         nvalue["img"],
			NewChapter:    nvalue["ch_0_title"],
			Cpsrc:         "",
			Islastchapter: "0",
			Cid:           "",
			Slog:          "{\"page\":\"search_sug\",\"flag\":1}",
			Free:          "0",
		}
		tmpStr, err := json.Marshal(chapterSchemeParam)
		if err != nil {
			this.addNotice("novelsugErr", "chapter scheme tojson error")
			return nil, false
		}
		chapterSchemeEncode := "baiduboxapp://novel?action=openReader&param=" + url.QueryEscape((string(tmpStr)))

		novel_res := Sug_Sug_Response_Dir_Novel{
			Type:       SUG_DIR_NOVEL,
			Word:       query,
			Sa:         t_original.Sa,
			Title:      nvalue["title_main"],
			Cate:       nvalue["category"],
			Tag:        nvalue["tag"],
			Des:        nvalue["desc"],
			Icon:       nvalue["img"],
			Author:     nvalue["author"],
			Status:     nvalue["finish_stat"],
			Allchapter: nvalue["chapter_all_num"],
			Hissync:    fmt.Sprintf(`{"query":"%s"}`, query),
			Cmd:        detailSchemeEncode,
			Button:     "阅读",
			Btncmd:     detailSchemeEncode, // 默认为普通流量合作去详情页
		}
		// 普通流量合作去详情页
		if nvalue["content_type"] == "0" && nvalue["ex_content_type"] == "0" {
			novel_res.Btncmd = detailSchemeEncode
		}
		// 内容合作
		if nvalue["content_type"] == "1" {
			novel_res.Btncmd = chapterSchemeEncode
		}
		// mip流量合作去详情页
		if nvalue["content_type"] == "0" && nvalue["ex_content_type"] == "1" {
			novel_res.Btncmd = detailSchemeEncode
		}
		return novel_res, true
	}

	// 默认无小说直达sug
	return nil, false
}

// 处理sug直达数据
func processSugDirect(t *data.Wisesugnew_Type, slid string, index int, req *map[string]string, adaptions *map[string]string, ctx *gdp.WebContext) (bool, string) {
	bdVersion := (*adaptions)["bd_version"]
	vecRaw := data.Swan_Sug{}
	// 搜索焕新
	newStyle := ctx.GetHeader("new-style")

	vec, ok := t.Info["vec_str_raw"]
	if !ok {
		return false, "vec_str_raw empty"
	}
	vecArr, ok := vec.([]interface{})
	if !ok || len(vecArr) == 0 {
		return false, "vec_str_raw len error"
	}

	// 确保第一个元素是字符串类型
	vecStr, ok := vecArr[0].(string)
	if !ok {
		return false, "vec_str_raw[0] not string"
	}

	// 添加类型检查，确保JSON可以正确解析为Swan_Sug
	var rawMap map[string]interface{}
	if err := json.Unmarshal([]byte(vecStr), &rawMap); err != nil {
		return false, "vec_str_raw[0] json parse error"
	}

	// 确保Value字段是map类型
	if value, exists := rawMap["value"]; exists {
		if _, ok := value.(map[string]interface{}); !ok {
			return false, "value field is not map[string]interface{}"
		}
	}

	// 解析为Swan_Sug结构体
	err := json.Unmarshal([]byte(vecStr), &vecRaw)
	if err != nil {
		return false, "vec_str_raw[0] error: " + err.Error()
	}

	// 存放拼接后的map
	newRaw := map[string]interface{}{}
	if vecRaw.Flag == "note" {
		newRaw["flag"] = "note"
		newRaw["type"] = SUG_DIR_GENERAL

		noteConf := common.SugDirectConf["note"]
		nameColor, ok := noteConf["name_color"].(string)
		if !ok {
			return false, "note name_color error"
		}

		value := map[string]interface{}{}
		value["name"] = []map[string]string{
			{
				"color": nameColor,
				"text":  t.Q,
			},
		}

		// 批量取值
		keyList := [...]string{"tag", "tagColor", "ubcSource", "img", "imgDark", "imgNight",
			"btnIcon", "btnIconDark", "btnIconNight"}
		for _, key := range keyList {
			v, ok := noteConf[key].(string)
			if !ok {
				continue
			}
			value[key] = v
		}

		// 低版本使用黑色图标
		if version.Compare(bdVersion, "13.49.0.10", "<") {
			for _, key := range [...]string{"img", "imgDark", "imgNight",
				"btnIcon", "btnIconDark", "btnIconNight"} {
				v, ok := noteConf[key+"Old"].(string)
				if !ok {
					continue
				}
				value[key] = v
			}
		}

		value["tag_style_list"] = []interface{}{noteConf["tag_style_list"]}

		if newStyle == "1" {
			// 焕新图标
			for _, key := range [...]string{"img", "imgDark", "imgNight",
				"btnIcon", "btnIconDark", "btnIconNight"} {
				v, ok := noteConf[key+"New"].(string)
				if !ok {
					continue
				}
				value[key] = v
			}
			// 焕新标签
			value["tag_style"] = noteConf["tag_style_new"]
			delete(value, "tag_style_list")
		}

		// 拼link跳转链接
		sa := "ks_" + strconv.Itoa(index) + "_zd_note"
		mURL := "https://m.baidu.com/s?word=" + url.QueryEscape(t.Q) + "&sa=" + sa + "&pd=note&tab_se=1&sugid=" + slid
		value["link"] = "baiduboxapp://v1/browser/open?url=" + url.QueryEscape(mURL) + "&append=1&backAllTip=1&newwindow=0&upgrade=1"

		// 未登录时，his=1
		if _, ok := (*req)[constant.SESSION_UID]; !ok {
			value["his"] = 1
		}

		newRaw["value"] = value
		resData, err := json.Marshal(newRaw)
		if err != nil {
			return false, "note json error"
		}
		// 覆盖原有的vec_str_raw
		t.Info["vec_str_raw"] = []interface{}{string(resData)}
		return true, vecRaw.Flag
	} else if vecRaw.Flag == "24gaokao_game" {
		// 拼link跳转链接
		mapValue, ok := vecRaw.Value.(map[string]interface{})
		if !ok {
			return true, vecRaw.Flag
		}
		// 删除配置中的flag
		delete(mapValue, "flag")

		mainQReplaceQuery := false
		yyfrom := ""
		mainQ := ""
		sa := ""
		mainQReplaceQuery = common.HotEvent24GaokaoConf.Game.MainQReplaceQuery
		yyfrom = common.HotEvent24GaokaoConf.Game.Yyfrom
		sa = common.HotEvent24GaokaoConf.Game.Sa
		mainQ = common.HotEvent24GaokaoConf.Game.MainQ

		if valueNameSlice, ok := mapValue["name"].([]interface{}); ok && len(valueNameSlice) > 0 {
			if valueNameMap, ok1 := valueNameSlice[0].(map[string]interface{}); ok1 {
				q := (*req)["query"]
				if len(t.Q) > 0 {
					q = t.Q
				}
				valueNameMap["text"] = q
				if mainQReplaceQuery && len(mainQ) > 0 {
					valueNameMap["text"] = mainQ
				}
			}
		}

		mURL := "https://m.baidu.com/searchframe?xaction=huodong&yyfrom=" + yyfrom + "&sa=" + sa + "&sugid=" + slid + "&word=" + url.QueryEscape(mainQ)
		mapValue["link"] = "baiduboxapp://v1/browser/open?url=" + url.QueryEscape(mURL) + "&append=1&backAllTip=1&newwindow=0&upgrade=1" +
			"&notShowLandingTopBar=1&bottomBarType=4&landingPageType=mailuo"

		resData, err := adaptColor(&vecRaw, newStyle, common.SugDirectConf["other"])
		if err != nil {
			return false, "24gaokao_game json error"
		}
		// 覆盖原有的vec_str_raw
		t.Info["vec_str_raw"] = []interface{}{string(resData)}

		return true, vecRaw.Flag
	} else if vecRaw.Flag == questionGame {
		// 拼link跳转链接
		mapValue, ok := vecRaw.Value.(map[string]interface{})
		if !ok {
			return true, vecRaw.Flag
		}
		// 删除配置中的flag
		delete(mapValue, "flag")

		mainQReplaceQuery := false
		mainQ := ""
		mainQReplaceQuery = common.HotEventQuestion.Game.MainQReplaceQuery
		sa := common.HotEventQuestion.Game.Sa
		mainQ = common.HotEventQuestion.Game.MainQ

		if valueNameSlice, ok := mapValue["name"].([]interface{}); ok && len(valueNameSlice) > 0 {
			if valueNameMap, ok1 := valueNameSlice[0].(map[string]interface{}); ok1 {
				q := (*req)["query"]
				if len(t.Q) > 0 {
					q = t.Q
				}
				valueNameMap["text"] = q
				if mainQReplaceQuery && len(mainQ) > 0 {
					valueNameMap["text"] = mainQ
				}
			}
		}

		// mURL := "http://sefe-shangyan.bcc-bdbl.baidu.com:8846/s?sa=" + sa + "&sugid=" + slid + "&word=" + url.QueryEscape(mainQ)
		mURL := "https://m.baidu.com/s?sa=" + sa + "&sugid=" + slid + "&word=" + url.QueryEscape(mainQ)
		mapValue["link"] = "baiduboxapp://v1/browser/open?url=" + url.QueryEscape(mURL) + "&append=1&backAllTip=1&newwindow=0&upgrade=1" +
			"&notShowLandingTopBar=1&bottomBarType=4&landingPageType=mailuo"
		resData, err := adaptColor(&vecRaw, newStyle, common.SugDirectConf["other"])
		if err != nil {
			return false, "question_game json error"
		}
		// 覆盖原有的vec_str_raw
		t.Info["vec_str_raw"] = []interface{}{string(resData)}
		return true, vecRaw.Flag
	} else if vecRaw.Flag == "hot_flow" {
		// 判断tag_style是否存在
		vecRawValue, ok := vecRaw.Value.(map[string]interface{})
		if !ok {
			return false, "vec_str_raw[0] value error"
		}
		tagStyle, ok := vecRawValue["tag_style"].(map[string]interface{})
		if !ok {
			return false, "tag_style assert error"
		}
		text, ok := tagStyle["text"].(string)
		if !ok {
			return false, "tagStyle text assert error"
		}

		var conf map[string]interface{}
		switch text {
		case "新":
			conf = common.SugDirectConf["new"]
		case "热":
			conf = common.SugDirectConf["hot"]
		default:
			return false, "unknown text" + text
		}

		// 检查配置中的 name_color
		nameColor, ok := conf["name_color"].(string)
		if !ok {
			return false, text + " name_color error"
		}

		// 构建 value
		value := map[string]interface{}{}
		value["btnKuang"] = conf["btnKuang"]
		value["name"] = []map[string]string{
			{
				"color": nameColor,
				"text":  t.Q,
			},
		}
		value["query"] = t.Q

		// 检查配置中的 tag_style
		value["tag_style"], ok = conf["tag_style"].(map[string]interface{})
		if !ok {
			return false, text + " tag_style error"
		}

		// 构建 newRaw 并转换为 JSON
		newRaw := map[string]interface{}{
			"flag":  "hot_flow",
			"type":  SUG_DIR_GENERAL,
			"value": value,
		}
		resData, err := json.Marshal(newRaw)
		if err != nil {
			return false, text + " json error"
		}

		// 覆盖原有的 vec_str_raw
		t.Info["vec_str_raw"] = []interface{}{string(resData)}
		return true, vecRaw.Flag
	} else if vecRaw.Flag == "mini_app" || vecRaw.Flag == "baijiahao" ||
		vecRaw.Flag == "yunying" || vecRaw.Flag == "entity" || vecRaw.Flag == "fuwu" ||
		vecRaw.Flag == "university" || vecRaw.Flag == "constellation" ||
		vecRaw.Flag == "postcode" || vecRaw.Flag == "zonecode" || vecRaw.Flag == "answer" {

		resData, err := adaptColor(&vecRaw, newStyle, common.SugDirectConf["other"])
		if err != nil {
			return false, vecRaw.Flag + " adapt color error: " + err.Error()
		}

		// 赋值给原来的 vec_str_raw
		t.Info["vec_str_raw"] = []interface{}{resData}

		return true, vecRaw.Flag
	} else if vecRaw.Flag == "aichat" {

		mapValue, ok := vecRaw.Value.(map[string]interface{})
		if !ok {
			return false, vecRaw.Flag
		}
		// 删除下游返回的tag_style_list数据
		delete(mapValue, "tag_style_list")

		// AI助手直达处理
		mapValue["flag"] = "aichat"
		mapValue["type"] = SUG_DIR_GENERAL

		aiConf := common.SugDirectConf["aichat"]

		// 读取配置值
		keyList := [...]string{"tag", "tagColor", "img", "imgDark", "imgNight",
			"btnIcon", "btnIconDark", "btnIconNight"}
		for _, key := range keyList {
			v, ok := aiConf[key].(string)
			if !ok {
				continue
			}
			mapValue[key] = v
		}
		mapValue["tag_style"] = aiConf["tag_style"]

		// 拼接端跳转的shcema
		mURL := "https://m.baidu.com/s?word=" + t.Q + "&pd=csaitab&sa=iks_purpose&enterType=sug_direct"
		mapValue["link"] = "baiduboxapp://v1/browser/open?url=" + url.QueryEscape(mURL)

		resData, err := adaptColor(&vecRaw, newStyle, common.SugDirectConf["other"])
		if err != nil {
			return false, "aichat json error"
		}

		// 更新vec_str_raw
		t.Info["vec_str_raw"] = []interface{}{resData}
		return true, vecRaw.Flag
	}

	return false, "vecRaw.Flag type err: " + vecRaw.Flag
}

// 对tag标签颜色修改
func adaptColor(vecRaw *data.Swan_Sug, newStyle string, conf map[string]interface{}) (string, error) {
	vecRawValue, ok := vecRaw.Value.(map[string]interface{})
	if !ok {
		return "", errors.New("vec_str_raw[0] value error")
	}
	nameArr, ok := vecRawValue["name"].([]interface{})
	if !ok || len(nameArr) != 1 {
		return "", errors.New("vec_str_raw value name assert error")
	}
	nameMap, ok := nameArr[0].(map[string]interface{})
	if !ok {
		return "", errors.New("vec_str_raw value nameArr assert error")
	}

	nameMap["color"] = "#000"
	if vecRaw.Flag == "mini_app" || vecRaw.Flag == "baijiahao" {
		vecRawValue["tagColor"] = "blue"
	}

	if newStyle == "1" {
		// 存在tag标签时，修改成焕新样式
		tagText, ok1 := vecRawValue["tag"].(string)
		tagColor, ok2 := vecRawValue["tagColor"].(string)
		if ok1 && ok2 && tagText != "" && tagColor == "blue" {
			colorConf, ok := conf[tagColor].(map[string]interface{})
			if ok {
				tagStyle := colorConf
				tagStyle["text"] = tagText

				vecRawValue["tag_style"] = tagStyle
			}
		}
	}

	vecRaw.Value = vecRawValue
	resData, err := json.Marshal(vecRaw)
	if err != nil {
		return "", errors.New("vecRaw json error")
	}

	return string(resData), nil
}

// 添加电商sug
func addShopSug(ctx *gdp.WebContext, value *data.Wisesugnew_Type) bool {
	if value == nil {
		return false
	}
	smp := common.GetSampleValue(ctx, "sug_shop", "0")
	if smp == "0" {
		return false
	}

	ok := false
	prefix := value.Q
	// 读取词典中prefix
	srcid, ok := common.ShopSugConf[prefix]
	if !ok {
		return false
	}

	// sug样式配置文件
	conf := map[string]interface{}{}
	if conf, ok = common.SugDirectConf["shop"]; !ok {
		return false
	}

	// 实验1：优选，蓝色
	// 实验2：优选，红色
	// 实验3：根据资源号选择文案，蓝色
	tagText := "优选"
	if smp == "3" {
		if srcid == 60338 {
			tagText = "全网比价"
		} else if srcid == 51910 {
			tagText = "选购指南"
		} else if srcid == 5881 {
			tagText = "权威榜单"
		} else if srcid == 5882 {
			tagText = "权威榜单"
		}
	}

	saTag := ""
	// 根据资源号选择sa标记
	if srcid == 60338 {
		saTag = "bijia"
	} else if srcid == 51910 {
		saTag = "xuang"
	} else if srcid == 5881 {
		saTag = "danpb"
	} else if srcid == 5882 {
		saTag = "pinpb"
	}

	// 获取tag_style_list
	v, ok := conf["tag_style_list"]
	if !ok {
		return false
	}
	tag_style_list, ok := v.(map[string]interface{})
	if !ok {
		return false
	}

	// 蓝色标签
	if smp == "1" || smp == "3" {
		v, ok := conf["tag_style_list_blue"]
		if !ok {
			return false
		}
		tag_style_list, ok = v.(map[string]interface{})
		if !ok {
			return false
		}
	}

	tag_style_list["text"] = tagText

	// 构造数据
	newRaw := map[string]interface{}{
		"flag": "shop",
		"type": SUG_DIR_GENERAL,
		"value": map[string]interface{}{
			"name": []map[string]interface{}{
				{
					"color": conf["name_color"],
					"text":  prefix,
				},
			},
			"query":          prefix,
			"btnKuang":       conf["btnKuang"],
			"tag_style_list": []interface{}{tag_style_list},
			"ubcSource":      "ecommerce",
		},
	}

	resData, err := json.Marshal(newRaw)
	if err != nil {
		return false
	}

	value.Type = DirictSug
	value.Info = map[string]interface{}{
		"vec_str_raw": []interface{}{string(resData)},
	}

	// 在sa的第二段pos后面拼上标记
	arr := strings.Split(value.Sa, "_")
	sa := ""
	for i, one := range arr {
		sa += one + "_"
		if i == 1 {
			sa += saTag + "_"
		}
	}
	sa = strings.TrimRight(sa, "_")
	if sa != "" {
		value.Sa = sa
	}

	return true
}

// 对切词数据预处理
func preProcessPos(ctx *gdp.WebContext, request *map[string]string, adaptions *map[string]string, query string) ([]gse.SegPos, []*regexp.Regexp) {
	// 请求参数控制
	if v := (*request)[constant.SpanUpgrade]; v != "1" {
		return nil, nil
	}

	// 回框场景不飘红
	if (*request)[constant.SUGMODE] == "2" {
		return nil, nil
	}

	// 切词
	queryCut := common.Seg.Pos(query, false)
	ctx.AddNotice("query", query)
	// 扩展切词结果
	for i, v := range queryCut {
		// 英文+数字，数字+英文组合，拆开
		if engNumReg.MatchString(v.Text) || numEngReg.MatchString(v.Text) {
			eng := engReg.FindString(v.Text)
			num := numReg.FindString(v.Text)
			// 拆成2部分
			queryCut[i].Text = eng
			queryCut = append(queryCut, gse.SegPos{
				Text: num,
				Pos:  "x",
			})
		}
	}
	ctx.AddNotice("queryCut", queryCut)

	// 构建正则匹配器
	regexpList := make([]*regexp.Regexp, len(queryCut))
	for i, v := range queryCut {
		if v.Text == "" || v.Text == " " {
			continue
		}
		if tmp, err := regexp.Compile("(?i)" + v.Text); err == nil {
			regexpList[i] = tmp
		} else {
			ctx.AddNotice("regexpErr", v.Text+"|"+err.Error())
		}
	}

	return queryCut, regexpList
}

// 获取sug直达中的flag
func getSugDirectFlag(value *data.Wisesugnew_Type) string {
	if value == nil {
		return ""
	}
	vec, ok := value.Info["vec_str_raw"]
	if !ok {
		return ""
	}
	vecArr, ok := vec.([]interface{})
	if !ok || len(vecArr) != 1 {
		return ""
	}
	vecStr, ok := vecArr[0].(string)
	if !ok {
		return ""
	}

	vecRaw := data.Swan_Sug{}
	err := json.Unmarshal([]byte(vecStr), &vecRaw)
	if err != nil {
		return ""
	}

	return vecRaw.Flag
}

// 超级品专sug直达数据
func addPinzhuanStrongSug(t *data.Wisesugnew_Type, index int, strongSugData *StrongSug) (bool, string) {
	sugQuery := strings.ToLower(t.Q)
	superPzQuery := common.SugDirectSpzConf[sugQuery]
	if strongSugData.maxPriority > StrongSugPriority["super_pinzhuan"] {
		return false, "super pinzhuan were PK"
	}
	if t.Q == "" {
		vecStrRaw, ok := t.Info["vec_str_raw"].([]interface{})
		if !ok || len(vecStrRaw) == 0 {
			return false, "vec_str_raw is either not a slice or is empty"
		}

		// 获取JSON字符串并将其转为字节切片
		jsonStr, ok := vecStrRaw[0].(string)
		if !ok {
			return false, "vec_str_raw[0] is not a string"
		}
		jsonBytes := []byte(jsonStr)

		// 用于存储解析后的数据的变量
		var parsedRaw map[string]interface{}

		// 反序列化JSON字符串到parsedRaw
		err := json.Unmarshal(jsonBytes, &parsedRaw)
		if err != nil {
			return false, "error unmarshaling JSON"
		}
		valueMap, ok := parsedRaw["value"].(map[string]interface{})
		if !ok {
			return false, "error: 'value' is not a map or is missing"
		}

		nameSlice, ok := valueMap["name"].([]interface{})
		if !ok {
			return false, "error: 'name' is not a slice or is missing"
		}
		if len(nameSlice) == 0 {
			return false, "error: 'name' is a empty"
		}
		nameMap, ok := nameSlice[0].(map[string]interface{})
		if !ok {
			return false, "error: 'name' item is not a map"
		}
		if text, ok := nameMap["text"].(string); ok {
			sugQuery = strings.ToLower(text)
			superPzQuery = common.SugDirectSpzConf[sugQuery]
		} else {
			return false, "error: 'text' is not a string"
		}
	}

	if len(superPzQuery) > 0 && index == 0 {
		var text string
		if query, ok := superPzQuery["query"].(string); ok {
			text = query
		}
		t.Type = "direct_new"
		newRaw := map[string]interface{}{
			"flag": "super_pinzhuan",
			"type": SUG_DIR_GENERAL,
			"value": map[string]interface{}{
				"name": []map[string]string{
					{
						"color": "#000",
						"text":  text,
					},
				},
				"ubcSource": "super_pinzhuan",
				"brief":     superPzQuery["brief"],
				"img":       superPzQuery["imgUrl"],
				"query":     superPzQuery["query"],
				"btnKuang":  1,
			},
		}

		resData, err := json.Marshal(newRaw)
		if err != nil {
			return false, "super pinzhuan json error"
		}
		strongSugData.pos = index
		t.Info = make(map[string]interface{})
		t.Info["vec_str_raw"] = []interface{}{string(resData)}
	}
	return true, ""
}

// 普通品专标签样式
func addPinzhuanWeakSug(t *data.Wisesugnew_Type, index int, strongSugData StrongSug) (bool, string) {
	sugQuery := strings.ToLower(t.Q)
	_, hasSuperPinzhuan := common.SugDirectSpzConf[sugQuery]
	_, hasPinzhuan := common.SugDirectPzConf[sugQuery]
	flag := getSugDirectFlag(t)
	var pzConf map[string]interface{}
	if hasSuperPinzhuan && index != 0 {
		t.Type = "direct_new"
		pzConf = common.SugDirectConf["pinzhuan"]
	} else if hasPinzhuan && (strongSugData.pos == -1 || (strongSugData.pos != -1 && index != 0 && flag != "note")) {
		// 首位没有特型，首位有品专出品专标签
		// 有特型，非首位有品专且特型不是笔记直达，展示品专标签
		t.Type = "direct_new"
		// 品专配置文件
		pzConf = common.SugDirectConf["pinzhuan"]
	}
	if pzConf != nil {
		nameColor, _ := pzConf["name_color"].(string)
		newRaw := map[string]interface{}{
			"flag": "pinzhuan",
			"type": SUG_DIR_GENERAL,
			"value": map[string]interface{}{
				"name": []map[string]string{
					{
						"color": nameColor,
						"text":  t.Q,
					},
				},
				"query":     t.Q,
				"btnKuang":  1,
				"tag_style": pzConf["tag_style_list"],
				"ubcSource": "pinzhuan",
			},
		}
		resData, err := json.Marshal(newRaw)
		if err != nil {
			return false, "pinzhuan json error"
		}
		t.Info = make(map[string]interface{})
		t.Info["vec_str_raw"] = []interface{}{string(resData)}
	}
	return true, ""
}

/*
 * 同一个SUG页面内，至多有1个特型与2个带标签的SUG。当命中特型或带标签多于该数量时，优先级如下：
 * 运营特型 > 摘满特型 > C页直达 > B页直达
 * 运营标签 > 时效性标签 > 资源标签
 */
func processStrongSugPk(ctx *gdp.WebContext, t *data.Wisesugnew_Type, index int, strongSugData *StrongSug) {
	flag := getSugDirectFlag(t)
	// 特型数据pk
	if priority, exists := StrongSugPriority[flag]; exists {
		if priority > strongSugData.maxPriority {
			if strongSugData.pos != -1 {
				setNormalSug(t, strongSugData.pos+1)
				ctx.AddNotice("index_"+strconv.Itoa(strongSugData.pos)+"strong sug were pk", flag)
			} else {
				strongSugData.maxPriority = priority
				strongSugData.pos = index
			}
		} else {
			setNormalSug(t, index+1)
			ctx.AddNotice("index_"+strconv.Itoa(index)+"strong sug were pk", flag)
		}
	}
}

// sug标签pk，在keylist中的flag，会进入pk队列
func processWeakSugPk(value *data.Wisesugnew_Type, index int, pkQueue *[]WeakSug) error {
	flag := getSugDirectFlag(value)
	sugConf := common.SugDirectConf["sugConf"]
	if len(sugConf) == 0 {
		return errors.New("error: 'name' item is not a map")
	}
	// pk优先级
	keyList, ok := sugConf["pklist"].(map[string]interface{})
	if !ok {
		return errors.New("error: 'pklist' item is not a map")
	}
	if _, exists := keyList[flag]; !exists {
		return errors.New("error: pklist['" + flag + "'] is not a map")
	}

	// BurntSushi库解析 默认int64
	priority, ok := keyList[flag].(int64)
	if !ok {
		return errors.New("error: priority is not int64 info[" + flag + "]")
	}

	newFlag := WeakSug{
		flag:     flag,
		priority: int(priority),
		pos:      index,
	}
	// 将新数据插入队列
	*pkQueue = append(*pkQueue, newFlag)
	return nil
}

// 获取sug标签pk优先级列表，全局中获取（只读）
func getSugConfPklist() ([]WeakSug, error) {
	sugConf := common.SugDirectConf["sugConf"]
	if len(sugConf) == 0 {
		return nil, errors.New("error: 'name' item is not a map")
	}
	// pk优先级
	keyList, ok := sugConf["pklist"].(map[string]interface{})
	if !ok {
		return nil, errors.New("error: 'pklist' item is not a map")
	}
	pklist := []WeakSug{}
	for k, v := range keyList {
		priority, _ := v.(int64)
		// 解析错误
		if priority == 0 {
			continue
		}

		pklist = append(pklist, WeakSug{
			flag:     k,
			priority: int(priority),
		})
	}
	sortPkFlag(pklist)
	return pklist, nil
}

// 排序
func sortPkFlag(queue []WeakSug) {
	sort.Slice(queue, func(i, j int) bool {
		if queue[i].priority == queue[j].priority {
			return queue[i].pos < queue[j].pos
		}
		return queue[i].priority < queue[j].priority
	})
}

// 被PK掉的特型sug/带标签的sug退化为普通纯文本sug
func setNormalSug(t *data.Wisesugnew_Type, index int) {
	t.Type = "sug"
	t.Sa = "s_" + strconv.Itoa(index)
	delete(t.Info, "vec_str_raw")
}

// 获取切词好的结果
func getHighlight(ctx *gdp.WebContext, queryCut []gse.SegPos, regexpList []*regexp.Regexp, query string, sug string, value *data.Wisesugnew_Type) []map[string]int {
	if len(queryCut) == 0 || len(regexpList) == 0 || sug == "" || query == "" {
		return nil
	}

	// 多行sug直达不飘红
	if value.Type == "direct_new" {
		flag := getSugDirectFlag(value)
		doubleSugFlag := []string{"baijiahao", "mini_app", "entity", "fuwu", "university", "constellation", "postcode", "zonecode", "answer"}
		for _, one := range doubleSugFlag {
			if flag == one {
				return nil
			}
		}
	}

	// 用于标记哪些词匹配过
	match := make([]int, len([]rune(sug)))

	// 1. 对非虚词和多字符虚词匹配
	for i, one := range queryCut {
		if len(one.Text) == 0 || one.Text == " " || regexpList[i] == nil {
			ctx.AddNotice("queryCutErr_"+strconv.Itoa(i), fmt.Sprintf("q:%s,text:%s", query, one.Text))
			continue
		}
		// 排除单虚词和标点符号
		if isSingleVirtual(one) || unicode.IsPunct([]rune(one.Text)[0]) {
			continue
		}

		substrings := regexpList[i].FindAllStringIndex(sug, -1)
		for _, sub := range substrings {
			begin := len([]rune(sug[:sub[0]]))
			end := len([]rune(sug[:sub[1]]))
			// 每个匹配项只匹配一次，且sug中同一段内容不被重复匹配
			if begin >= 0 && begin < len(match) && match[begin] == 0 {
				for j := begin; j < end; j++ {
					if j >= 0 && j < len(match) {
						match[j] = 1
					}
				}
				break
			}
		}
	}

	// 2. 对单字符虚词匹配
	for i, one := range queryCut {
		if len([]rune(one.Text)) != 1 || one.Text == " " || regexpList[i] == nil {
			continue
		}
		if !isSingleVirtual(one) {
			continue
		}
		for j, word := range []rune(sug) {
			// 左或右有匹配，才匹配。每个匹配项只匹配一次，且sug中同一段内容不被重复匹配
			if word == []rune(one.Text)[0] &&
				((j > 0 && match[j-1] == 1) || (j < len(match)-1 && match[j+1] == 1)) &&
				(j >= 0 && j < len(match) && match[j] == 0) {
				match[j] = 1
				break
			}
		}
	}

	// 3. 匹配标点符号
	for _, one := range queryCut {
		if len([]rune(one.Text)) != 1 || one.Text == " " {
			continue
		}

		punct := []rune(one.Text)[0]
		if !unicode.IsPunct(punct) {
			continue
		}

		// 找到标点对应的中英文标点
		puncIndex := -1
		for i, pair := range puncList {
			if punct == pair[0] || punct == pair[1] {
				puncIndex = i
				break
			}
		}

		// 对当前sug遍历，找到匹配的标点
		for i, word := range []rune(sug) {
			// puncList中没找到标点
			if puncIndex == -1 {
				// 左或右有匹配，才匹配
				if word == punct &&
					((i > 0 && match[i-1] == 1) || (i < len(match)-1 && match[i+1] == 1)) &&
					(i >= 0 && i < len(match)) {
					match[i] = 1
				}
			} else {
				// 中英文标点任意一个有匹配，且左或右有匹配，才匹配
				if (word == puncList[puncIndex][0] || word == puncList[puncIndex][1]) &&
					((i > 0 && match[i-1] == 1) || (i < len(match)-1 && match[i+1] == 1)) &&
					(i >= 0 && i < len(match)) {
					match[i] = 1
				}
			}
		}
	}

	// 4. 生成匹配区间
	ret := []map[string]int{}
	begin, end := -1, -1
	for i, v := range match {
		if v == 1 {
			if begin < 0 {
				begin = i
			}
			end = i
		} else {
			if begin >= 0 {
				ret = append(ret, map[string]int{
					"begin": begin,
					"end":   end,
				})
				begin, end = -1, -1
			}
		}
	}

	if begin >= 0 {
		ret = append(ret, map[string]int{
			"begin": begin,
			"end":   end,
		})
	}

	return ret
}

// 判断是否为单字符虚词
func isSingleVirtual(one gse.SegPos) bool {
	if len([]rune(one.Text)) != 1 {
		return false
	}
	// 遍历虚词词性表
	for _, v := range virtualPos {
		if len(one.Pos) >= 1 && one.Pos[:1] == v {
			return true
		}
	}
	return false
}

// 初始化对端协议的结构
func (this *Sug_Sug_Iphone_New) defaultTpl() Sug_Sug_Response {
	ext_body := Sug_Sug_Response_Extend_Query{
		Type: "0",
		Word: (*(this.getRequest()))["query"],
	}
	prefetch := GetPrefetch()
	ext := Sug_Sug_Response_Extend{
		Query:    ext_body,
		Prefetch: prefetch,
	}
	data := []interface{}{}

	ret := Sug_Sug_Response{
		Errno:  "",
		Msg:    "",
		Type:   "sug",
		Slid:   "",
		Extend: ext,
		Data:   data,
		GMot:   make([]interface{}, 0),
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *Sug_Sug_Iphone_New) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
	// this.addNotice("sug_finalres", string(tplByte))
}

func (this *Sug_Sug_Iphone_New) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Sug_Sug_Iphone_New{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("sug-iphone", &Sug_Sug_Iphone_New{})
}
