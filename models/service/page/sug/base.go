package sug

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Pager interface {
	Execute()
	GetTplData() string
	GetTplByteData() []byte
	SetNeedPredict(bool)
	GetPresearchData() PresearchType
}

type Page interface {
	newSelf(ctx *gdp.WebContext) (Pager, error)
}

type PresearchType struct {
	Query string //预取query
	Sa    string //sa标识
}

// page层的属性尽可能全部私有 使用interface提供外部来操作与访问
type BasePage struct {
	ctx           *gdp.WebContext
	tplData       []byte
	needPredict   bool          //仅对主sug生效， 用于sug&预取二合一，需要请求预测服务作为预取query并行请求处理
	presearchData PresearchType //预取搜索数据
}

var adapters = make(map[string]Page)

func Register(name string, adapter Page) {
	if adapter == nil {
		panic("page: Register adapter is nil")
	}
	if _, ok := adapters[name]; ok {
		panic("page: Register called twice for adapter " + name)
	}
	adapters[name] = adapter
}

func NewPager(pageName string, ctx *gdp.WebContext) (Pager, error) {
	adapter, ok := adapters[pageName]
	if !ok {
		return nil, fmt.Errorf("page: unknown adapter %q (forgotten register?)", pageName)
	}
	retPage, nErr := adapter.newSelf(ctx)
	if nErr != nil {
		return nil, fmt.Errorf("page: create page %q fail", pageName)
	}
	return retPage, nil
}

func (this *BasePage) setTplData(str string) {
	this.tplData = []byte(str)
}

func (this *BasePage) GetTplData() string {
	return string(this.tplData)
}

func (this *BasePage) SetNeedPredict(needPredict bool) {
	this.needPredict = needPredict
}

func (this *BasePage) setTplByteData(bytes []byte) {
	this.tplData = bytes
	common.DumpData(this.ctx, "2shoubai", bytes)
}

func (this *BasePage) GetTplByteData() []byte {
	return this.tplData
}

func (this *BasePage) getRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := this.ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (this *BasePage) GetPresearchData() PresearchType {
	return this.presearchData
}

func (this *BasePage) setPresearchData(query string, sa string) {
	presearchData := PresearchType{}
	presearchData.Query = query
	presearchData.Sa = sa
	this.presearchData = presearchData
}

func (this *BasePage) getAdaption() *map[string]string {
	var adaption map[string]string
	if val, exists := this.ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	return &adaption
}

func (this *BasePage) getLogidStr() string {

	return this.ctx.GetLogID()
}
func (this *BasePage) addNotice(key, value string) {
	this.ctx.AddNotice(key, value)
}

func (this *BasePage) setErr() {
	this.ctx.DealSucc = false
}
