package sug

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type Sug_Sug_Android_New struct {
	Sug_Sug_Common
}

const (
	SUG_DIR_WEATHER       = 1001   // 天气直达
	SUG_DIR_URL           = 1002   // 网址直达
	SUG_DIR_APP           = 1003   // app直达
	SUG_DIR_LOTTERY       = 1004   // 彩票直达
	SUG_DIR_PHONE         = 1005   // 手机号码直达
	SUG_DIR_WORLDTIME     = 1006   // 世界时间
	SUG_DIR_LIMIT         = 1007   // 限行
	SUG_DIR_ZIPCODE       = 1008   // 邮编
	SUG_DIR_ZOONNUM       = 1009   // 区号
	SUG_DIR_METRIC        = 1010   // 度量
	SUG_DIR_RARE          = 1011   // 生僻字
	SUG_DIR_HOLIDAY       = 1012   // 节假日
	SUG_DIR_POETRY        = 1013   // 诗词
	SUG_DIR_CONSTELLATION = 1014   // 星座
	SUG_DIR_CUSSERVICE    = 1015   // 客服
	AT_LIGHTAPP_TYPE      = "1016" // 直达号type
	SUG_DIR_EC_SUG        = "1017" // 语音纠错sug
	SUG_DIR_SWAN          = "1018" // 小程序直达
	SUG_DIR_NOVEL         = "1019" // 小说直达
	SUG_DIR_GENERAL       = "1020" // 通用直达模板，包含1001-1018
	SUG_HIS               = "1021" // 搜索历史sug
)

// 通用extende结构
type Sug_Sug_Response_Extend struct {
	Query    Sug_Sug_Response_Extend_Query `json:"query"`
	Prefetch string                        `json:"prefetch"`
}

type Sug_Sug_Response_Extend_Query struct {
	Word string `json:"word"`
	Type string `json:"type"`
}

// 返回给端的结构
type Sug_Sug_Response struct {
	Errno     string                  `json:"errno"`
	Msg       string                  `json:"msg"`
	Type      string                  `json:"type"`
	Slid      string                  `json:"slid"`
	Extend    Sug_Sug_Response_Extend `json:"extend"`
	Data      []interface{}           `json:"data"`
	GMot      []interface{}           `json:"g_mot"`
	Switch    interface{}             `json:"switch_control"`
	GMotStyle int                     `json:"g_mot_style"`
	GMotTitle string                  `json:"g_mot_title"`
}

type Sug_Sug_Response_Dir_Hissync struct {
	Query string `json:"query"`
}

// 普通sug
type Sug_Sug_Response_Type1_Data struct {
	Word   string           `json:"word"`
	Type   string           `json:"type"`
	Wapurl string           `json:"wap_url"`
	Wwwurl string           `json:"www_url"`
	Sa     string           `json:"sa"`
	Pos    []map[string]int `json:"pos,omitempty"`
}

type Sug_Sug_Response_Type2_Data struct {
	Word string `json:"word"`
	Type int    `json:"type"`
	Sa   string `json:"sa"`
}

// 历史sug
type Sug_Sug_Response_Type2_Data_Uwp struct {
	Word string           `json:"word"`
	Type string           `json:"type"`
	Sa   string           `json:"sa"`
	Pos  []map[string]int `json:"pos,omitempty"`
}

type Sug_Sug_Response_Type3_Data []map[string]string

// 直达协议
// 网址直达
type Sug_Sug_Response_Dir_Url struct {
	Type        int                      `json:"type"`
	Word        string                   `json:"word"`
	Title       string                   `json:"title"`
	Sa          string                   `json:"sa"`
	Hissync     string                   `json:"hissync"`
	Icon        string                   `json:"icon"`
	Description string                   `json:"description"`
	Param       []map[string]interface{} `json:"param"`
}

// 网址直达-iphone版
type Sug_Sug_Response_Dir_Url_Iphone struct {
	Type    string `json:"type"`
	Word    string `json:"word"`
	Sa      string `json:"sa"`
	Hissync string `json:"hissync"`
	Wapurl  string `json:"wap_url"`
	Wwwurl  string `json:"www_url"`
}

// App直达
type Sug_Sug_Response_Dir_App struct {
	Type        int           `json:"type"`
	Word        string        `json:"word"`
	Sa          string        `json:"sa"`
	Title       string        `json:"title"`
	Description string        `json:"description"`
	Icon        string        `json:"icon"`
	Hissync     string        `json:"hissync"`
	Param       []interface{} `json:"param"`
}

// 邮编直达
type Sug_Sug_Response_Dir_Zipcode struct {
	Type        int    `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 区号
type Sug_Sug_Response_Dir_Zoonnumber struct {
	Type        int    `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 度量衡
type Sug_Sug_Response_Dir_Metric struct {
	Type  int    `json:"type"`
	Word  string `json:"word"`
	Sa    string `json:"sa"`
	Title string `json:"title"`
}

// 生僻字
type Sug_Sug_Response_Dir_Rare struct {
	Type        int    `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 节假日
type Sug_Sug_Response_Dir_Holiday struct {
	Type        int    `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 诗词
type Sug_Sug_Response_Dir_Poetry struct {
	Type        int    `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 星座
type Sug_Sug_Response_Dir_Constellation struct {
	Type        int    `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 语音纠错
type Sug_Sug_Response_Dir_Ecsug struct {
	Type        string `json:"type"`
	Word        string `json:"word"`
	Sa          string `json:"sa"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// 直达sug
type Sug_Sug_Response_Dir_Swan struct {
	Type string           `json:"type"`
	Sa   string           `json:"sa"`
	Data string           `json:"data"`
	Pos  []map[string]int `json:"pos,omitempty"`
}

// 小说直达
type Sug_Sug_Response_Dir_Novel struct {
	Type       string              `json:"type"`
	Word       string              `json:"word"`
	Sa         string              `json:"sa"`
	Gid        string              `json:"gid"`
	Title      string              `json:"title"`
	Cate       string              `json:"cate"`
	Tag        string              `json:"tag"`
	Des        string              `json:"description"`
	Author     string              `json:"author"`
	Status     string              `json:"status"`
	Allchapter string              `json:"allchapter"`
	Icon       string              `json:"icon"`
	Hissync    string              `json:"hissync"`
	Cmd        string              `json:"cmd"`
	Button     string              `json:"button"`
	Btncmd     string              `json:"button_cmd"`
	Chapters   []Sug_Novel_Chapter `json:"chapters"`
}

// lite小说额外字段
type Sug_Novel_Chapter struct {
	Title string `json:"title"`
	Cmd   string `json:"cmd"`
}

// 章节页scheme字段
type Sug_Novel_Scheme struct {
	Gid           string `json:"gid"`
	Name          string `json:"name"`
	Author        string `json:"author"`
	Image         string `json:"image"`
	NewChapter    string `json:"newchapter"`
	Cpsrc         string `json:"cpsrc"`
	Islastchapter string `json:"islastchapter"`
	Cid           string `json:"cid"`
	Slog          string `json:"slog"`
	Free          string `json:"free"`
}

// 主要执行的函数入口
func (this *Sug_Sug_Android_New) Execute() {
	request := this.getRequest()
	tplData := this.defaultTpl()

	// 空query
	query := (*request)["query"]
	if query == "" {
		tplData.Errno = "9001"
		tplData.Msg = "no query"
		tplData.Type = "sug"
		this.response(tplData)
		return
	}

	// 非法攻击或超长直接拒绝 utf8中文长度按3算
	if len(query) > 50 {
		this.response(tplData)
		return
	}

	vType, ok := (*request)["v"]
	if !ok || (vType != "1" && vType != "2" && vType != "3") {
		vType = "2" // 降级处理版本号
	}

	// 请求后端
	dataSrv := data.NewWiseSugNew(this.ctx)
	dataSrv.NeedPredictSug = this.needPredict // 用于判断是否需要并行请求sug预测服务

	if ralGetErr, responseStr := dataSrv.GetWiseSugResponse(query); ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", fmt.Sprintf(`errmsg:%s,responseStr:%#v`, ralGetErr.Error(), responseStr))
		tplData.Errno = "9002"
		tplData.Msg = "wisesug request fail"
		this.response(tplData)
		return
	}

	// 解析后端参数
	requestData := dataSrv.GetWisesugTplData()
	parsedData, err := this.responseWithType(requestData, vType)
	if err != nil {
		this.setErr()
		this.addNotice("parseErr", "1")
		tplData.Errno = "9003"
		tplData.Msg = "flow err"
		this.response(tplData)
		return
	}

	this.response(parsedData)
	if this.needPredict {
		presearchSugRespData := dataSrv.GetPresearchSugData()
		if presearchSugRespData.Presearch != "1" {
			this.setPresearchData("", "") // 预取词置空
			return
		}
		this.setPresearchData(query, "")
	}
	return
}

// 历史问题 协议分裂
func (this *Sug_Sug_Android_New) responseWithType(res data.Wisesugnew_Response, v string) (Sug_Sug_Response, error) {
	switch v {
	case "1":
		return this.parseV1(res)
	case "2":
		return this.parseV2(res)
	case "3":
		return this.parseV3(res)
	default:
		return Sug_Sug_Response{}, nil
	}
}

func (this *Sug_Sug_Android_New) parseV1(res data.Wisesugnew_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()

	if len(res.G) == 0 {
		return tplData, nil // 无sug列表
	}

	for index, value := range res.G {
		if value.Type != "sug" || value.Q == "" {
			continue
		}
		tmp := Sug_Sug_Response_Type1_Data{
			Word: value.Q,
			Type: "0",
			Sa:   value.Sa,
		}
		if index == 0 && res.Q == value.Q {
			this.setPresearchData(value.Q, value.Sa)
		}
		tplData.Data = append(tplData.Data, tmp)
	}
	return tplData, nil
}

func (this *Sug_Sug_Android_New) parseV2(res data.Wisesugnew_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()

	if len(res.G) == 0 {
		return tplData, nil // 无sug列表
	}
	for index, value := range res.G {
		if value.Type != "sug" || value.Q == "" {
			continue
		}
		tmp := Sug_Sug_Response_Type2_Data{
			Word: value.Q,
			Type: 0,
			Sa:   value.Sa,
		}
		if index == 0 && res.Q == value.Q {
			this.setPresearchData(value.Q, value.Sa)
		}
		tplData.Data = append(tplData.Data, tmp)
	}

	// 简单搜索app基础库较旧，基于v2协议开发
	// 识别流量进行部分字段适配
	if common.IsChatSearch(this.getRequest()) {
		tplData.Slid = res.Slid
	}

	return tplData, nil
}

// 目前主要在用的v3协议
func (this *Sug_Sug_Android_New) parseV3(res data.Wisesugnew_Response) (Sug_Sug_Response, error) {
	tplData := this.defaultTpl()
	tplData.Slid = res.Slid
	tplData.GMotStyle = res.GMotStyle
	tplData.GMotTitle = res.GMotTitle
	request := this.getRequest()
	adaptions := this.getAdaption()

	if len(res.GMot) != 0 {
		for i, v := range res.GMot {
			parsed, ok := this.parseType(&v, i, nil)
			if !ok {
				continue
			}
			tplData.GMot = append(tplData.GMot, parsed)
		}
	}

	if len(res.G) == 0 {
		return tplData, nil // 无sug列表
	}

	queryCut, regexpList := preProcessPos(this.ctx, request, adaptions, res.Q)
	// 运营事件插入
	if eventName, changeQ, hit := hitHotEvent(this.ctx, this.getRequest()); hit {
		if sigG, pos := getHotEventData(eventName); pos >= 0 {
			sigG.Q = changeQ
			res.G = insertHotEvent(pos, res.G, sigG)
		}
	}

	if common.GetSampleValue(this.ctx, "pinzhuan_sug_style", "0") == "1" {
		// 品专sug数据处理
		var strongSugData = StrongSug{
			pos:         -1,
			maxPriority: 0,
		}
		for index := range res.G {
			// 特型sug PK
			processStrongSugPk(this.ctx, &res.G[index], index, &strongSugData)

			// 增加品专特型sug
			ok, msg := addPinzhuanStrongSug(&res.G[index], index, &strongSugData)
			if ok {
				this.addNotice("pzSugDirectOK_"+strconv.Itoa(index), msg)
			}

			// 增加品专标签
			ok, msg = addPinzhuanWeakSug(&res.G[index], index, strongSugData)
			if ok {
				this.addNotice("addPinzhuanWeakSugOk_"+strconv.Itoa(index), msg)
			}
		}
	}

	// 添加电商sug
	if common.GetSampleValue(this.ctx, "sug_shop", "0") != "0" {
		for index := range res.G {
			ok := addShopSug(this.ctx, &res.G[index])
			if ok {
				this.addNotice("addShopSugOK_"+strconv.Itoa(index), "1")
			}
		}
	}
	// 按优先级改写置顶sug
	processInsertSugText(this.ctx, res.G)

	if checkSugPk(this.ctx) {
		// 标签sug PK
		pkQueue := make([]WeakSug, 0, len(res.G))
		for index := range res.G {
			if err := processWeakSugPk(&res.G[index], index, &pkQueue); err != nil {
				this.addNotice("error: weakPk_", err.Error())
			}
		}
		// 删除多余的标签sug，只保留2条
		if len(pkQueue) > 2 {
			// 对队列按照优先级和输入顺序进行排序
			sortPkFlag(pkQueue)

			this.addNotice("weakPkQueue", strconv.Itoa(len(pkQueue)))
			for i := 2; i < len(pkQueue); i++ {
				setNormalSug(&res.G[pkQueue[i].pos], pkQueue[i].pos+1)
				this.addNotice("weakPk_"+strconv.Itoa(i), pkQueue[i].flag)
			}
		}
	}

	for index, value := range res.G {
		// sug直达样式数据迁移处理
		if value.Type == "direct_new" {
			ok, msg := processSugDirect(&value, res.Slid, index, request, adaptions, this.ctx)
			if ok {
				this.addNotice("sugDirectOK_"+strconv.Itoa(index), msg)
			} else {
				this.addNotice("sugDirectFail_"+strconv.Itoa(index), msg)
			}
		}

		cutPos := getHighlight(this.ctx, queryCut, regexpList, res.Q, value.Q, &value)
		// if len(cutPos) != 0 {
		// 	this.ctx.AddNotice(fmt.Sprintf("posMap_%d", index), fmt.Sprintf("s:%s,m:%v", value.Q, cutPos))
		// }
		parsed, ok := this.parseType(&value, index, cutPos)
		if !ok {
			continue
		}
		if index == 0 && res.Q == value.Q {
			this.setPresearchData(value.Q, value.Sa)
		}
		tplData.Data = append(tplData.Data, parsed)
	}
	return tplData, nil
}

// 此处根据不同的type分类 做不同的对端协议转换和特殊处理
// 详细可参考 http://wiki.baidu.com/pages/viewpage.action?pageId=576601410
func (s *Sug_Sug_Android_New) parseType(t *data.Wisesugnew_Type, index int, cutPos []map[string]int) (interface{}, bool) {
	requests := s.getRequest()
	adaptions := s.getAdaption()

	switch t.Type {
	case "sug": // 普通SUG
		return Sug_Sug_Response_Type1_Data{
			Word: t.Q,
			Type: "0",
			Sa:   t.Sa,
			Pos:  cutPos,
		}, true
	case "his": // 搜索历史
		// 满足版本要求，才返回 SUG_HIS 类型
		if ((*requests)["osbranch"] == "a0" && version.Compare((*adaptions)["bd_version"], "13.9", ">=")) ||
			((*requests)["osbranch"] == "a2" && version.Compare((*adaptions)["bd_version"], "5.38", ">=")) {
			return Sug_Sug_Response_Type2_Data_Uwp{
				Word: t.Q,
				Type: SUG_HIS,
				Sa:   t.Sa,
				Pos:  cutPos,
			}, true
		}

		return Sug_Sug_Response_Type1_Data{
			Word: t.Q,
			Type: "0",
			Sa:   t.Sa,
			Pos:  cutPos,
		}, true
	case "direct_website": // 网址直达
		his := Sug_Sug_Response_Dir_Hissync{
			Query: t.Info["site"].(string),
		}
		hissync, _ := json.Marshal(his) // 不是跳转到结果页的 都需要手动加一个his逻辑
		param := []map[string]interface{}{{
			"item_click": url.QueryEscape(t.Info["show_url"].(string)),
			"action":     1,
		}}
		return Sug_Sug_Response_Dir_Url{
			Type:        SUG_DIR_URL,
			Word:        "",
			Sa:          t.Sa,
			Title:       t.Info["site"].(string),
			Hissync:     string(hissync),
			Icon:        t.Info["icon_url"].(string),
			Description: t.Info["show_url"].(string),
			Param:       param,
		}, true
	case "direct_mail": // 邮编直达
		return Sug_Sug_Response_Dir_Zipcode{
			Type:        SUG_DIR_ZIPCODE,
			Word:        "",
			Sa:          t.Sa,
			Title:       t.Info["post_code"].(string),
			Description: "|" + t.Info["post_city"].(string) + " 邮编",
		}, true
	case "direct_uncommon_word": // 生僻字
		return Sug_Sug_Response_Dir_Rare{
			Type:        SUG_DIR_RARE,
			Word:        "",
			Sa:          t.Sa,
			Title:       t.Info["word"].(string) + t.Info["word_pinyin"].(string),
			Description: "[字义]" + t.Info["word_meaning"].(string),
		}, true
	case "direct_zone_num": // 区号
		return Sug_Sug_Response_Dir_Zoonnumber{
			Type:        SUG_DIR_ZOONNUM,
			Word:        "",
			Sa:          t.Sa,
			Title:       t.Info["zone_num"].(string),
			Description: "|" + t.Info["zone_city"].(string) + " 区号",
		}, true
	case "direct_constellation": // 星座
		return Sug_Sug_Response_Dir_Constellation{
			Type:        SUG_DIR_CONSTELLATION,
			Word:        "",
			Sa:          t.Sa,
			Title:       t.Info["constellation"].(string),
			Description: t.Info["constellation_date"].(string),
		}, true
	case "direct_new": // 小程序类或特殊手百数据类
		return s.parseTypeDirectNew(t, index, cutPos)
	}

	return nil, false
}

// type为direct_website的数据处理
// 当数据指定为direct_website时，内部使用另外一套结构 整体数据为json字符串 需要反序列化
// 目前所有类型的数据分为两类处理方式
// 1.非透传类 例如小说 这类数据有较复杂的处理逻辑 SU只提供源数据 需要在下发前做加工 仅支持单个
// 2.透传类 例如小程序小游戏 这类数据只做版本控制逻辑 不对里面的值进一步处理 支持多个组合成一个数组
func (s *Sug_Sug_Android_New) parseTypeDirectNew(t *data.Wisesugnew_Type, index int, cutPos []map[string]int) (interface{}, bool) {
	ji, ok := t.Info["vec_str_raw"]
	if !ok {
		return nil, false
	}
	jsonStrArr := ji.([]interface{})
	if len(jsonStrArr) == 0 {
		return nil, false
	}

	var vecRaw data.Swan_Sug
	var swanRes []data.Swan_Sug_Res
	isNewTemplate := false
	requests := s.getRequest()
	adaptions := s.getAdaption()
	bdVersion := (*adaptions)["bd_version"]
	osbranch := (*requests)["osbranch"]
	// versionControl比较对象
	versionCompareTarget := bdVersion
	if osbranch != "a0" && (*requests)["fv"] != "" {
		bdVersion = (*requests)["fv"] // 其他百度系app有fv标识同步的基线版本代码
	}
	// 大搜SU每一条数据 对应一个客户端type
	// 若同时出现非透传和透传类型 此处以第一个值作为判断标准
	// 即第一个若为非透传类型，则后面的多个全部不解析；若第一个为透传类型，后面出现费透传类型全部不解析
	for i := range jsonStrArr {
		jsonStrArrString, ok := jsonStrArr[i].(string)
		if !ok {
			continue
		}
		s.addNotice("vecRawNew_"+strconv.Itoa(index), jsonStrArrString)

		err := json.Unmarshal([]byte(jsonStrArrString), &vecRaw)
		if err != nil {
			continue
		}
		allowLite := false
		if vecRaw.Type == SUG_DIR_GENERAL && t.Sa == "24gaokao_game" && common.GetAppBranch(osbranch) == common.LITE_APP {
			allowLite = true
		}

		// 优先走通用直达模板逻辑
		if vecRaw.Type == SUG_DIR_GENERAL && !version.Compare(bdVersion, "11.21", "<") || // 主线版本通用直达模板11.21
			allowLite {
			versionControlInfo := vecRaw.VersionControl
			versionControlRet := s.versionControl(versionControlInfo, osbranch, versionCompareTarget, allowLite, vecRaw.Flag)
			if versionControlRet {
				isNewTemplate = true
				tmp := data.Swan_Sug_Res{
					Flag:  vecRaw.Flag,
					Value: vecRaw.Value,
				}
				swanRes = append(swanRes, tmp)
			}
		}

		if !isNewTemplate {
			if vecRaw.Type == SUG_DIR_GENERAL && vecRaw.VersionControl1 != nil {
				vecRaw.VersionControl = vecRaw.VersionControl1
				vecRaw.Value = vecRaw.Value1
			}

			// 当且仅当第一个为非透传的时候 截断处理下发，小说非透传
			if i == 0 && vecRaw.Flag == "novel" {
				return s.parseTypeNovel(t, &vecRaw)
			}
			versionControlInfo := vecRaw.VersionControl
			versionControlRet := s.versionControl(versionControlInfo, osbranch, versionCompareTarget, false, vecRaw.Flag)
			if versionControlRet {
				tmp := data.Swan_Sug_Res{
					Flag:  vecRaw.Flag,
					Value: vecRaw.Value,
				}
				swanRes = append(swanRes, tmp)
			}
		}
	}

	if swanRes == nil { // 全部过滤或无下发数据
		if t.Q != "" && t.Sa != "24gaokao_game" { // 使用Q 对版本不兼容的数据做降级
			// 且不是高考强插的sug，就会降级保留
			return Sug_Sug_Response_Type1_Data{
				Word: t.Q,
				Type: "0",
				Sa:   t.Sa,
				Pos:  cutPos,
			}, true
		}
		// 否则如果q为空或者是高考强插sug，就删除丢弃
		return nil, false
	}

	swanResStr, encodeErr := json.Marshal(swanRes)
	if encodeErr != nil { // 序列化失败
		return nil, false
	}

	sugType := SUG_DIR_SWAN
	if isNewTemplate {
		sugType = SUG_DIR_GENERAL
	}
	return Sug_Sug_Response_Dir_Swan{
		Type: sugType,
		Data: string(swanResStr),
		Sa:   t.Sa,
		Pos:  cutPos,
	}, true
}

func (this *Sug_Sug_Android_New) versionControl(versionControlInfo data.VersionCtr, osbranch string,
	bdVersion string, allowLite bool, flag string) bool {
	// 极速版只用6.26以上, 不判断versionControlInfo中的控制
	if common.GetAppBranch(osbranch) == common.LITE_APP && version.Compare(bdVersion, "6.26", ">=") {
		if flag == "note" || flag == "mini_app" || flag == "baijiahao" || flag == "aichat" {
			return false
		}
		return true
	}

	if versionControlInfo != nil { // 带VersionControl的数据 走osbranch和min+max逻辑
		dataConfig, dataConfigOk := versionControlInfo[osbranch]
		if !dataConfigOk {
			return false // 客户端osbranch不属于下发范畴
		}

		if (dataConfig.Min != "" && version.Compare(bdVersion, dataConfig.Min, "<")) ||
			(dataConfig.Max != "" && version.Compare(bdVersion, dataConfig.Max, ">")) {
			return false // 客户端bd_version不属于下发范畴
		}
	} else {
		if allowLite && common.GetAppBranch(osbranch) == common.LITE_APP && version.Compare(bdVersion, "6.26", ">=") {
			return true // 极速版只用6.26以上
		}
		if version.Compare(bdVersion, "10.11", "<") {
			return false // 默认按10.11发版的分割线一刀切 小于10.11的全部跳过 大于等于的全部下发
		}
	}
	return true
}

// 小说SUG的处理方法
func (this *Sug_Sug_Android_New) parseTypeNovel(t_original *data.Wisesugnew_Type, t *data.Swan_Sug) (interface{}, bool) {
	requests := this.getRequest()
	adaptions := this.getAdaption()
	osBranch := (*requests)["osbranch"]
	bdVersion := (*adaptions)["bd_version"]
	query := (*requests)["query"]
	ivalue := t.Value.(map[string]interface{})
	nvalue := make(map[string]string, len(ivalue))
	for key, i := range ivalue {
		switch v := i.(type) {
		case string:
			nvalue[key] = v
		default:
			nvalue[key] = ""
		}
	}

	// android主线版11.0以上有小说直达sug
	if version.Compare(bdVersion, "11.0.0.0", ">=") && osBranch == "a0" {
		detailSchemeParams := url.Values{}
		detailSchemeParams.Set("title", "小说详情")
		detailSchemeParams.Set("url", "https://boxnovel.baidu.com/boxnovel/detail?action=novel&type=detail")
		detailSchemeParams.Set("method", "post")
		detailSchemeParams.Set("needParams", "1")
		detailSchemeParams.Set("pagetype", "1")
		detailSchemeParams.Set("args", fmt.Sprintf(`data={"fromaction":"search_sug","gid":"%s"}`, nvalue["bookid"]))
		detailSchemeParams.Set("upgrade", "1")
		detailSchemeEncode := "baiduboxapp://v2/novel/openSubPage?" + detailSchemeParams.Encode()
		chapterSchemeParam := Sug_Novel_Scheme{
			Gid:           nvalue["bookid"],
			Name:          nvalue["title_main"],
			Author:        nvalue["author"],
			Image:         nvalue["img"],
			NewChapter:    nvalue["ch_0_title"],
			Cpsrc:         "",
			Islastchapter: "0",
			Cid:           "",
			Slog:          "{\"page\":\"search_sug\",\"flag\":1}",
			Free:          "0",
		}
		tmpStr, err := json.Marshal(chapterSchemeParam)
		if err != nil {
			this.addNotice("novelsugErr", "chapter scheme tojson error")
			return nil, false
		}
		chapterSchemeEncode := "baiduboxapp://novel?action=openReader&param=" + url.QueryEscape((string(tmpStr)))
		novel_res := Sug_Sug_Response_Dir_Novel{
			Type:       SUG_DIR_NOVEL,
			Word:       query,
			Gid:        nvalue["bookid"],
			Title:      nvalue["title_main"],
			Cate:       nvalue["category"],
			Tag:        nvalue["tag"],
			Des:        nvalue["desc"],
			Icon:       nvalue["img"],
			Author:     nvalue["author"],
			Status:     nvalue["finish_stat"],
			Allchapter: nvalue["chapter_all_num"],
			Hissync:    `{"query":"` + query + `"}`,
			Cmd:        detailSchemeEncode,
			Button:     "阅读",
			Btncmd:     detailSchemeEncode, // 默认为普通流量合作去详情页
		}
		// 普通流量合作去详情页
		if nvalue["content_type"] == "0" && nvalue["ex_content_type"] == "0" {
			novel_res.Btncmd = detailSchemeEncode
		}
		// 内容合作
		if nvalue["content_type"] == "1" {
			novel_res.Btncmd = chapterSchemeEncode
		}
		// mip流量合作去详情页
		if nvalue["content_type"] == "0" && nvalue["ex_content_type"] == "1" {
			novel_res.Btncmd = detailSchemeEncode
		}

		return novel_res, true
	}
	// lite版本
	if version.Compare(bdVersion, "*******", ">=") && osBranch == "a2" {
		schemeParams := url.Values{}
		schemeParams.Set("appid", "com.baidu.searchbox.novel")
		schemeParams.Set("entry", "invokeScheme")
		schemeParams.Set("params", fmt.Sprintf(`{"gid":"%s","fromaction":"search_sug", "type":"detail"}`, nvalue["bookid"]))
		// 详情页
		detailScheme := "baiduboxlite://v1/vendor/open?" + schemeParams.Encode()
		// 倒数第一章
		schemeParams.Set("params", fmt.Sprintf(`{"gid":"%s","fromaction":"search_sug","bookName":"%s","coverUrl":"%s","type":"reader","cid":"%s"}`, nvalue["bookid"], nvalue["title_main"], nvalue["img"], nvalue["bookid"]+"|"+nvalue["ch_0_cid"]))
		chapterOneScheme := "baiduboxlite://v1/vendor/open?" + schemeParams.Encode()
		// 倒数第二章
		schemeParams.Set("params", fmt.Sprintf(`{"gid":"%s","fromaction":"search_sug","bookName":"%s","coverUrl":"%s","type":"reader","cid":"%s"}`, nvalue["bookid"], nvalue["title_main"], nvalue["img"], nvalue["bookid"]+"|"+nvalue["ch_1_cid"]))
		chapterTwoScheme := "baiduboxlite://v1/vendor/open?" + schemeParams.Encode()
		// 首章
		schemeParams.Set("params", fmt.Sprintf(`{"gid":"%s","fromaction":"search_sug","bookName":"%s","coverUrl":"%s","type":"reader"}`, nvalue["bookid"], nvalue["title_main"], nvalue["img"]))
		chapterIndexScheme := "baiduboxlite://v1/vendor/open?" + schemeParams.Encode()
		chapterOne := Sug_Novel_Chapter{
			Title: nvalue["ch_0_title"],
			Cmd:   chapterOneScheme,
		}
		chapterTwo := Sug_Novel_Chapter{
			Title: nvalue["ch_1_title"],
			Cmd:   chapterTwoScheme,
		}
		novel_res := Sug_Sug_Response_Dir_Novel{
			Type:       SUG_DIR_NOVEL,
			Word:       query,
			Sa:         t_original.Sa,
			Title:      nvalue["title_main"],
			Cate:       nvalue["category"],
			Tag:        nvalue["tag"],
			Des:        nvalue["desc"],
			Icon:       nvalue["img"],
			Author:     nvalue["author"],
			Status:     nvalue["finish_stat"],
			Allchapter: nvalue["chapter_all_num"],
			Hissync:    fmt.Sprintf(`{"query":"%s"}`, query),
			Cmd:        detailScheme,
			Button:     "阅读",
			Btncmd:     detailScheme, // 默认为普通流量合作去详情页
		}

		// 普通流量合作去详情页
		if nvalue["content_type"] == "0" && nvalue["ex_content_type"] == "0" {
			novel_res.Btncmd = detailScheme
		}
		// 内容合作
		if nvalue["content_type"] == "1" {
			novel_res.Btncmd = chapterIndexScheme
			novel_res.Chapters = []Sug_Novel_Chapter{chapterOne, chapterTwo}
		}
		// mip流量合作去详情页
		if nvalue["content_type"] == "0" && nvalue["ex_content_type"] == "1" {
			novel_res.Btncmd = detailScheme
		}

		return novel_res, true
	}

	// 默认无小说直达sug
	return nil, false
}

// 初始化对端协议的结构
func (this *Sug_Sug_Android_New) defaultTpl() Sug_Sug_Response {
	ext_body := Sug_Sug_Response_Extend_Query{
		Type: "0",
		Word: (*(this.getRequest()))["query"],
	}
	prefetch := GetPrefetch()
	ext := Sug_Sug_Response_Extend{
		Query:    ext_body,
		Prefetch: prefetch,
	}
	data := []interface{}{}

	ret := Sug_Sug_Response{
		Errno:  "",
		Msg:    "",
		Type:   "sug",
		Extend: ext,
		Data:   data,
		GMot:   make([]interface{}, 0),
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *Sug_Sug_Android_New) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
	// this.addNotice("sug_finalres", string(tplByte))
}

func (this *Sug_Sug_Android_New) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Sug_Sug_Android_New{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("sug-android", &Sug_Sug_Android_New{})
}
