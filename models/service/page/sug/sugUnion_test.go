package sug

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestSug_Sug_Union_parseNewRequestData(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		resData data.Wisesugnew_Response
		tpl     Sug_Sug_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseNewRequestData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	wtype := data.Wisesugnew_Type{
		"sug",
		"test",
		"test",
		map[string]interface{}{
			"type": "sug",
			"sa":   "s_1",
			"q":    "金牛座汽车",
		},
	}
	wrps := data.Wisesugnew_Response{
		Q:    "test",
		P:    true,
		G:    []data.Wisesugnew_Type{wtype},
		Slid: "slid",
		GMot: []data.Wisesugnew_Type{wtype},
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	ext_body := Sug_Sug_Response_Extend_Query{
		Type: "0",
		Word: "",
	}
	ext := Sug_Sug_Response_Extend{
		Query: ext_body,
	}
	tmp1 := Sug_Sug_Response_Type2_Data{
		Word: "test",
		Type: 0,
	}
	dataresult1 := []interface{}{}
	dataresult1 = append(dataresult1, tmp1)
	ret1 := Sug_Sug_Response{
		Errno:  "",
		Msg:    "",
		Type:   "sug",
		Extend: ext,
		Data:   dataresult1,
	}

	wtype1 := data.Wisesugnew_Type{
		"sug",
		"test",
		"test",
		map[string]interface{}{
			"type": "sug",
			"sa":   "s_1",
			"q":    "金牛座汽车",
		},
	}
	wrps1 := data.Wisesugnew_Response{
		Q:    "",
		P:    true,
		G:    []data.Wisesugnew_Type{wtype1},
		Slid: "slid",
		GMot: []data.Wisesugnew_Type{wtype},
	}

	wrps2 := data.Wisesugnew_Response{
		Q:    "sug",
		P:    true,
		G:    []data.Wisesugnew_Type{wtype1},
		Slid: "slid",
		GMot: []data.Wisesugnew_Type{wtype},
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestSug_Sug_Union_parseNewRequestData",
			fields{
				common,
			},
			args{
				wrps,
				ret1,
			},
			false,
		},
		{
			"TestSug_Sug_Union_parseNewRequestData",
			fields{
				common,
			},
			args{
				wrps1,
				ret1,
			},
			true,
		},
		{
			"TestSug_Sug_Union_parseNewRequestData",
			fields{
				common,
			},
			args{
				wrps2,
				ret1,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &Sug_Sug_Union{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		err := this.parseNewRequestData(tt.args.resData, tt.args.tpl)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Union_parseNewRequestData, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestSug_Sug_Union_defaultTpl(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Sug_Sug_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Union{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Union_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Union_response(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Sug_Sug_Union{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestSug_Sug_Union_newSelf(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Union{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Union_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Union_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
