package sug

// NA化预取降级方案, 默认为关
var default_prefetch = "off"

// SUG预取二合一降级开关，默认关闭
var default_twoinonedegrade = "off"

// 获取预取开关状态
func GetPrefetch() (prefetch string) {
	prefetch = default_prefetch
	return
}

// 设置预取开发状态
func SetPrefetch(prefetch string) {
	default_prefetch = prefetch
	return
}

// 设置SUG预取二合一降级开关状态
func SetTwoinoneDegrade(degrade string) {
	default_twoinonedegrade = degrade
	return
}

// 获取预取降级开关状态
func GetTwoinoneDegrade() (degrade string) {
	degrade = default_twoinonedegrade
	return
}
