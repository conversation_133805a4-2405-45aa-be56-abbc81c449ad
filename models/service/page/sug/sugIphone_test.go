package sug

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"reflect"
	"strconv"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func sugDirectInit() {
	filePath := filepath.Join(env.ConfRootPath(), "sug_direct.toml")
	_, err := toml.DecodeFile(filePath, &common.SugDirectConf)
	if err != nil {
		panic(fmt.Sprintf("Can't load config file %s: %s\n", filePath, err.Error()))
	}
}

func TestSug_Sug_Iphone_New_responseWithType(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
		v   string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: responseWithType ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	wtype := data.Wisesugnew_Type{
		"sug",
		"test",
		"test",
		map[string]interface{}{
			"test": "ceshi",
		},
	}

	wrps := data.Wisesugnew_Response{
		Q:    "test",
		P:    true,
		G:    []data.Wisesugnew_Type{wtype},
		Slid: "",
		GMot: []data.Wisesugnew_Type{wtype},
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	tmp := Sug_Sug_Response_Type1_Data{
		Word: "test",
		Type: "0",
		Sa:   "test",
	}
	ext_body := Sug_Sug_Response_Extend_Query{
		Type: "0",
		Word: "",
	}
	ext := Sug_Sug_Response_Extend{
		Query:    ext_body,
		Prefetch: "off",
	}
	dataresult := []interface{}{}
	dataresult = append(dataresult, tmp)

	ret := Sug_Sug_Response{
		Errno:  "",
		Msg:    "",
		Type:   "sug",
		Extend: ext,
		Data:   dataresult,
		GMot:   []interface{}{},
		Switch: map[string]interface{}(nil),
	}

	tmp1 := Sug_Sug_Response_Type2_Data{
		Word: "test",
		Type: 0,
		Sa:   "test",
	}
	dataresult1 := []interface{}{}
	dataresult1 = append(dataresult1, tmp1)
	// gmot1 := Sug_Sug_Response_Type1_Data{
	// 	Word: "test",
	// 	Sa:   "test",
	// }
	ret1 := Sug_Sug_Response{
		Errno:  "",
		Msg:    "",
		Type:   "sug",
		Slid:   "",
		Extend: ext,
		Data:   dataresult1,
		GMot:   []interface{}{},
		Switch: map[string]interface{}(nil),
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		{
			"TestSug_Sug_Iphone_New_responseWithType",
			fields{
				common,
			},
			args{
				wrps,
				"1",
			},
			Want{
				ret,
				ut.ShouldResemble,
			},
			false,
		},
		{
			"TestSug_Sug_Iphone_New_responseWithType",
			fields{
				common,
			},
			args{
				wrps,
				"2",
			},
			Want{
				ret1,
				ut.ShouldResemble,
			},
			false,
		},
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.responseWithType(tt.args.res, tt.args.v)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_responseWithType, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_responseWithType, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_parseV1(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseV1 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.parseV1(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseV1, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseV1, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_parseV2(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseV2 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.parseV2(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseV2, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseV2, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_parseV3(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseV3 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.parseV3(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseV3, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseV3, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_parseType(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		t *data.Wisesugnew_Type
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseType ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	wtype := data.Wisesugnew_Type{
		"direct_website",
		"test",
		"test",
		map[string]interface{}{
			"site":     "ceshi",
			"show_url": "a.url",
			"icon_url": "a.url",
		},
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	tmp := Sug_Sug_Response_Dir_Url_Iphone{
		Word:    "test",
		Type:    "1",
		Sa:      "test",
		Hissync: `{"query":"ceshi"}`,
		Wapurl:  "a.url",
		Wwwurl:  "a.url",
	}

	// wtype1 := data.Wisesugnew_Type{
	// 	"direct_mail",
	// 	"test",
	// 	"test",
	// 	map[string]interface{}{
	// 			"post_code":"ceshi",
	// 			"post_city":"北京",
	// 	},
	// }
	// tmp1 := Sug_Sug_Response_Dir_Zipcode{
	// 	Word: "",
	// 	Type: 1008,
	// 	Title:"ceshi",
	// 	Description:"|北京 邮编",
	// }

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //interface{}
		want1         Want //bool
	}{
		{
			"TestSug_Sug_Iphone_New_parseType",
			fields{
				common,
			},
			args{
				&wtype,
			},
			Want{
				tmp,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
		// {
		// 	"TestSug_Sug_Iphone_New_parseType",
		// 	fields{
		// 		common,
		// 	},
		// 	args{
		// 		&wtype1,
		// 	},
		// 	Want{
		// 		tmp1,
		// 		ut.ShouldResemble,
		// 	},
		// 	Want{
		// 		true,
		// 		ut.ShouldBeTrue,
		// 	},
		// },

	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, got1 := this.parseType(tt.args.t, k, nil)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseType, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(interface{}))
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseType, Result Index:1 Value Compare", got1, tt.want1.Assert, tt.want1.Value.(bool))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_parseTypeDirectNew(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		t *data.Wisesugnew_Type
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseTypeDirectNew ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	info := [](interface{}){
		`{ "flag": "constellation", "versionControl": { "a0": { "max": "", "min": "********" }, "i0": { "max": "", "min": "********" } }, "value": { "query": "金牛座", "name": "金牛座 4月20日-5月20日" }, "port_control": 8 }`,
	}

	wtype := data.Wisesugnew_Type{
		"direct_new",
		"di_1",
		"test",
		map[string]interface{}{
			"vec_str_raw": info,
		},
	}
	ctx := createGetWebContext()
	adapterParams := &(map[string]string{
		"device_id": "E18ADD27D3B4460AB7D2CFE0CDD11E7A",
	})
	(*adapterParams)["bd_version"] = "*********"
	requestParams := &(map[string]string{})
	(*requestParams)["osbranch"] = "i0"
	ctx.Set(constant.ADAPARAMS, *adapterParams)
	ctx.Set(constant.REQPARAMS, *requestParams)
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	tmp2 := Sug_Sug_Response_Dir_Swan{
		Type: "1018",
		Sa:   "di_1",
		Data: `[{"flag":"constellation","value":{"name":"金牛座 4月20日-5月20日","query":"金牛座"}}]`,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //interface{}
		want1         Want //bool
	}{
		{
			"TestSug_Sug_Android_New_parseType",
			fields{
				common,
			},
			args{
				&wtype,
			},
			Want{
				tmp2,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, got1 := this.parseTypeDirectNew(tt.args.t, k, nil)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseTypeDirectNew, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(interface{}))
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseTypeDirectNew, Result Index:1 Value Compare", got1, tt.want1.Assert, tt.want1.Value.(bool))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_parseTypeNovel(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		t_original *data.Wisesugnew_Type
		t          *data.Swan_Sug
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseTypeNovel ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	vecRaw := data.Swan_Sug{}
	t_original := data.Wisesugnew_Type{}
	str := `{"flag":"novel","value":{"name":"金牛座 4月20日-5月20日","query":"金牛座","content_type":"0"}}`
	json.Unmarshal([]byte(str), &vecRaw)

	ctx := createGetWebContext()
	adapterParams := &(map[string]string{})
	(*adapterParams)["bd_version"] = "********"
	requestParams := &(map[string]string{})
	(*requestParams)["osbranch"] = "i0"
	ctx.Set(constant.ADAPARAMS, *adapterParams)
	ctx.Set(constant.REQPARAMS, *requestParams)
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	tmp2 := Sug_Sug_Response_Dir_Novel{
		Type: "1019",
		Gid:  `[{"flag":"constellation","value":{"bookid":"2344","title_main":"金牛座"}}]`,
	}

	// ctx1 := createGetWebContext()
	// adapterParams1 := &(map[string]string{})
	// (*adapterParams1)["bd_version"] = "4.2.0.0"
	// requestParams1 := &(map[string]string{})
	// (*requestParams1)["osbranch"] = "i3"
	// ctx1.Set(constant.ADAPARAMS,*adapterParams1)
	// ctx1.Set(constant.REQPARAMS,*requestParams1)
	// basePage1 := BasePage{
	// 	ctx:ctx1,
	// }
	// common1 := Sug_Sug_Common{
	// 	basePage1,
	// }

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //interface{}
		want1         Want //bool
	}{
		{
			"TestSug_Sug_Android_New_parseTypeNovel",
			fields{
				common,
			},
			args{
				&t_original,
				&vecRaw,
			},
			Want{
				tmp2,
				ut.ShouldNotBeNil,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
		// {
		// 	"TestSug_Sug_Android_New_parseTypeNovel",
		// 	fields{
		// 		common1,
		// 	},
		// 	args{
		// 		&vecRaw,
		// 	},
		// 	Want{
		// 		tmp2,
		// 		ut.ShouldNotBeNil,
		// 	},
		// 	Want{
		// 		true,
		// 		ut.ShouldBeTrue,
		// 	},
		// },
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, got1 := this.parseTypeNovel(tt.args.t_original, tt.args.t)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseTypeNovel, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(interface{}))
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_parseTypeNovel, Result Index:1 Value Compare", got1, tt.want1.Assert, tt.want1.Value.(bool))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_defaultTpl(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Sug_Sug_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_response(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestSug_Sug_Iphone_New_newSelf(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Iphone_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Iphone_New_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}

func Test_processSugDirect(t *testing.T) {
	sugDirectInit()
	jsonStr := `{
		"flag": "aichat",
		"type": "1020",
		"versionControl": {
			"a0": {
				"max": "",
				"min": "*********"
			},
			"i0": {
				"max": "",
				"min": "*********"
			}
		},
		"port_control": 8,
		"value": {
			"name": [{
				"color": "#000",
				"text": "产品承诺书范文"
			}],
			"tag": "",
			"tagColor": "blue",
			"img": "https://ai-chat.bj.bcebos.com/im_logo/normal_icon.png",
			"link": "link_to_aitab",
			"his": 1,
			"tag_style_list": [{
				"tag_type": 1,
				"text": "AI助手",
				"text_color": "#FF3366FF",
				"border_color": "#663366FF",
				"bg_color": "#00FFFFFF",
				"dark_text_color": "#FF3366FF",
				"dark_border_color": "#803366FF",
				"dark_bg_color": "",
				"night_text_color": "#FF263678",
				"night_border_color": "#80263678",
				"night_bg_color": "#00000000"
			}],
			"btnIcon": "https://search-operate.cdn.bcebos.com/b7285257fcc2b4d8e0d7080147baee06.png"
		}
	}`
	jsonQuestionStr := `{
		"flag": "question_game",
		"type": "1020",
		"value": {
			"brief": "最高得648元，新人立领200元",
			"btnKuang": 1,
			"flag": "question_game",
			"his": 1,
			"img": "https://search-operate.cdn.bcebos.com/5e5de3886410789a6bd5da6a25228e3f.png",
			"info": "",
			"name": [{
				"color": "#000",
				"text": "答题找红包"
			}],
			"query": "答题找红包",
			"tag": "小游戏",
			"tagColor": "blue",
			"ubcSource": "question_game"
		}
	}`
	type args struct {
		t         *data.Wisesugnew_Type
		slid      string
		index     int
		req       *map[string]string
		adaptions *map[string]string
		ctx       *gdp.WebContext
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		want1 string
	}{
		// TODO: Add test cases.
		{
			name: "aichat",
			args: args{
				ctx: createGetWebContext(),
				t: &data.Wisesugnew_Type{
					Info: map[string]interface{}{
						"vec_str_raw": []interface{}{jsonStr},
					},
				},
				adaptions: &map[string]string{"bd_version": "13.60.1"},
			},
			want:  true,
			want1: "aichat",
		},
		{
			name: "question_game",
			args: args{
				ctx: createGetWebContext(),
				t: &data.Wisesugnew_Type{
					Info: map[string]interface{}{
						"vec_str_raw": []interface{}{jsonQuestionStr},
					},
				},
				req: &map[string]string{
					"query": "答题找红包",
				},
				adaptions: &map[string]string{"bd_version": "13.60.1"},
			},
			want:  true,
			want1: "question_game",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := processSugDirect(tt.args.t, tt.args.slid, tt.args.index, tt.args.req, tt.args.adaptions, tt.args.ctx)
			if got != tt.want {
				t.Errorf("processSugDirect() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("processSugDirect() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestAddPinzhuanStrongSug_1300(_ *testing.T) {

	type EpArgs struct {
		T *data.Wisesugnew_Type `json:"t"`

		Index int `json:"index"`

		Strongsugdata *StrongSug `json:"strongSugData"`
	}
	EpData := `{"t":{"type":"","sa":"","q":"","info":{"vec_str_raw":["{\"value\": {\"name\": []}}"]}},"index":0,"strongSugData":{}}`
	var epArgs EpArgs
	// 将json数据放序列化成方法测试参数struct
	err := json.Unmarshal([]byte(EpData), &epArgs)
	if err != nil {
		fmt.Printf("JSON to struct error，FunctionName=addPinzhuanStrongSug,input=%s", EpData)
		return
	}
	// 对context.Context类型特殊处理
	VWbox := reflect.ValueOf(&epArgs).Elem()
	TWbox := VWbox.Type()
	for i := 0; i < TWbox.NumField(); i++ {
		field := TWbox.Field(i)
		if field.Type == reflect.TypeOf((*context.Context)(nil)).Elem() {
			VWbox.Field(i).Set(reflect.ValueOf(context.Background()))
		}
	}

	addPinzhuanStrongSug(
		epArgs.T,
		epArgs.Index,
		epArgs.Strongsugdata,
	)

}

func Test_processSugText(t *testing.T) {
	type args struct {
		ctx     *gdp.WebContext
		sugType string
		sug     *data.Wisesugnew_Type
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: processSugText")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// Setup test data
	common.SugTextConf = map[string]map[string][]string{
		"health": {
			"感冒": {"感冒", "img_url", "感冒简介"},
		},
	}
	common.SugDirectConf = map[string]map[string]interface{}{
		"sugConf": {
			"pklist": map[string]interface{}{
				"insert_interv": 1,
				"hot_flow":      2,
				"pinzhuan":      3,
				"shop":          4,
				"health":        5,
			},
			"default": map[string]interface{}{},
			"health": map[string]interface{}{
				"flag": "health",
			},
		},
	}

	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "nil sug",
			args: args{
				ctx:     &gdp.WebContext{},
				sugType: "health",
				sug:     nil,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "valid sug with matching type",
			args: args{
				ctx:     &gdp.WebContext{},
				sugType: "health",
				sug: &data.Wisesugnew_Type{
					Q: "感冒",
				},
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "valid sug with non-matching type",
			args: args{
				ctx:     &gdp.WebContext{},
				sugType: "nonexistent",
				sug: &data.Wisesugnew_Type{
					Q: "感冒",
				},
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "valid sug with non-matching query",
			args: args{
				ctx:     &gdp.WebContext{},
				sugType: "health",
				sug: &data.Wisesugnew_Type{
					Q: "发烧",
				},
			},
			want:    false,
			wantErr: false,
		},
	}
	ctx := &gdp.WebContext{
		LogContext: gdp.NewLogContext("123", "0:0:0:0"),
	}
	// ctx.AddNotice()
	patches := gomonkey.ApplyMethodFunc(reflect.TypeOf(ctx), "AddNotice", func(k string, v interface{}) {
		return
	})
	defer patches.Reset()

	// ctx.AddNotice()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := processSugText(ctx, tt.args.sugType, tt.args.sug)
			ag.Add(tt.name, got, ut.ShouldEqual, tt.want)
		})
	}
	ag.Run()
}

func TestBuildVecStrRaw(t *testing.T) {
	utInst := ut.New(t, "Unit tests for FUNCTION: BuildVecStrRaw")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// Setup test data
	common.SugTextConf = map[string]map[string][]string{
		"health": {
			"感冒": {"感冒", "img_url", "感冒简介"},
		},
	}
	common.SugDirectConf = map[string]map[string]interface{}{
		"sugConf": {
			"pklist": map[string]interface{}{
				"insert_interv": 1,
				"hot_flow":      2,
				"pinzhuan":      3,
				"shop":          4,
				"health":        5,
			},
			"default": map[string]interface{}{},
			"health": map[string]interface{}{
				"flag": "health",
			},
		},
	}

	ctx := &gdp.WebContext{
		LogContext: gdp.NewLogContext("123", "0:0:0:0"),
	}

	tests := []struct {
		name    string
		ctx     *gdp.WebContext
		sugType string
		sug     *data.Wisesugnew_Type
		want    map[string]interface{}
		wantNil bool
	}{
		{
			name:    "nil sug",
			ctx:     ctx,
			sugType: "health",
			sug:     nil,
			wantNil: true,
		},
		{
			name:    "invalid sug type",
			ctx:     ctx,
			sugType: "invalid",
			sug: &data.Wisesugnew_Type{
				Q: "感冒",
			},
			wantNil: true,
		},
		{
			name:    "non-matching query",
			ctx:     ctx,
			sugType: "health",
			sug: &data.Wisesugnew_Type{
				Q: "发烧",
			},
			wantNil: true,
		},
		{
			name:    "valid case",
			ctx:     ctx,
			sugType: "health",
			sug: &data.Wisesugnew_Type{
				Q:    "感冒",
				Info: map[string]interface{}{},
			},

			want: map[string]interface{}{
				"flag": "health",
				"type": SUG_DIR_GENERAL,
				"value": map[string]interface{}{
					"img":   "img_url",
					"brief": "感冒简介",
					"name": []interface{}{
						map[string]interface{}{
							"color": "#000",
							"text":  "感冒",
						},
					},
					"query": "感冒",
				},
			},
			wantNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := BuildVecStrRaw(tt.ctx, tt.sugType, tt.sug)
			if tt.wantNil {
				ag.Add(tt.name, got, ut.ShouldBeNil)
			} else {
				ag.Add(tt.name, got, ut.ShouldResemble, tt.want)
			}
		})
	}
	ag.Run()
}

func Test_processInsertSugText(t *testing.T) {
	type args struct {
		ctx  *gdp.WebContext
		sugG []data.Wisesugnew_Type
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: processInsertSugText ")
	defer utInst.RestoreAll()
	// ag := utInst.NewAssertGroup()
	// Setup test data
	common.SugDirectConf = map[string]map[string]interface{}{
		"sugConf": {
			"pklist": map[string]interface{}{
				"insert_interv": int64(1),
				"hot_flow":      int64(2),
				"pinzhuan":      int64(3),
				"shop":          int64(4),
				"health":        int64(5),
			},
			"default": map[string]interface{}{},
			"health": map[string]interface{}{
				"flag": "health",
			},
		},
	}

	common.SugTextConf = map[string]map[string][]string{
		"health": {
			"test": {"test", "img1", "brief1"},
		},
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "empty suggestions",
			args: args{
				ctx: &gdp.WebContext{
					LogContext: gdp.NewLogContext("112233", "0:0:0:0"),
				},
				sugG: []data.Wisesugnew_Type{},
			},
			wantErr: false,
		},
		{
			name: "health suggestion match",
			args: args{
				ctx: &gdp.WebContext{
					LogContext: gdp.NewLogContext("112233", "0:0:0:0"),
				},
				sugG: []data.Wisesugnew_Type{
					{
						Q: "test",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "no matching suggestion",
			args: args{
				ctx: &gdp.WebContext{
					LogContext: gdp.NewLogContext("112233", "0:0:0:0"),
				},
				sugG: []data.Wisesugnew_Type{
					{
						Q: "no-match",
					},
				},
			},
			wantErr: false,
		},
	}

	patches := gomonkey.ApplyFunc(common.GetSampleValue, func(ctx *gdp.WebContext, key string, defaultValue string) string {
		return "1" // 总是返回2
	})
	defer patches.Reset()

	// ctx :=
	for _, tt := range tests {
		processInsertSugText(tt.args.ctx, tt.args.sugG)
	}
	// check
	ctx := &gdp.WebContext{
		LogContext: gdp.NewLogContext("112233", "0:0:0:0"),
	}
	checkSugPk(ctx)
}

func Test_processWeakSugPk(t *testing.T) {
	type args struct {
		value   *data.Wisesugnew_Type
		index   int
		pkQueue *[]WeakSug
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: processWeakSugPk ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// Setup test data
	common.SugDirectConf = map[string]map[string]interface{}{
		"sugConf": {
			"pklist": map[string]interface{}{
				"test_flag": int64(1),
				"heal":      int64(2),
			},
		},
	}

	validValue := &data.Wisesugnew_Type{
		Info: map[string]interface{}{
			"vec_str_raw": []interface{}{`{"flag":"test_flag"}`},
		},
	}

	invalidValue := &data.Wisesugnew_Type{
		Info: map[string]interface{}{
			"vec_str_raw": []interface{}{`{"flag":"invalid_flag"}`},
		},
	}

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
		wantErr       bool
	}{
		{
			testCaseTitle: "success case",
			args: args{
				value:   validValue,
				index:   0,
				pkQueue: &[]WeakSug{},
			},
			want: Want{
				Value: []WeakSug{
					{
						flag:     "test_flag",
						priority: 1,
						pos:      0,
					},
				},
				Assert: ut.ShouldEqual,
			},
			wantErr: false,
		},
		{
			testCaseTitle: "invalid flag case",
			args: args{
				value:   invalidValue,
				index:   0,
				pkQueue: &[]WeakSug{},
			},
			wantErr: true,
		},
		{
			testCaseTitle: "nil value case",
			args: args{
				value:   nil,
				index:   0,
				pkQueue: &[]WeakSug{},
			},
			wantErr: true,
		},
	}

	for k, tt := range tests {
		err := processWeakSugPk(tt.args.value, tt.args.index, tt.args.pkQueue)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of processWeakSugPk, Error Value Compare", err != nil, ut.ShouldResemble, tt.wantErr)
			continue
		}
		if tt.wantErr {
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of processWeakSugPk, Result Value Compare", *tt.args.pkQueue, ut.ShouldResemble, tt.want.Value.([]WeakSug))
	}
	ag.Run()
}
func Test_getSugConfPklist(t *testing.T) {
	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getSugConfPklist ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// Setup test data
	common.SugDirectConf = map[string]map[string]interface{}{
		"sugConf": {
			"pklist": map[string]interface{}{
				"flag1": int64(3),
				"flag2": int64(1),
				"flag3": int64(2),
			},
		},
	}

	emptyConf := map[string]map[string]interface{}{
		"sugConf": {},
	}

	invalidConf := map[string]map[string]interface{}{
		"sugConf": {
			"pklist": "invalid_type",
		},
	}

	tests := []struct {
		testCaseTitle string
		setup         func()
		want          Want
		wantErr       bool
	}{
		{
			testCaseTitle: "success case",
			setup: func() {
				common.SugDirectConf = map[string]map[string]interface{}{
					"sugConf": {
						"pklist": map[string]interface{}{
							"flag1": int64(3),
							"flag2": int64(1),
							"flag3": int64(2),
						},
					},
				}
			},
			want: Want{
				Value: []WeakSug{
					{flag: "flag2", priority: 1},
					{flag: "flag3", priority: 2},
					{flag: "flag1", priority: 3},
				},
				Assert: ut.ShouldResemble,
			},
			wantErr: false,
		},
		{
			testCaseTitle: "empty sugConf case",
			setup: func() {
				common.SugDirectConf = emptyConf
			},
			wantErr: true,
		},
		{
			testCaseTitle: "invalid pklist type case",
			setup: func() {
				common.SugDirectConf = invalidConf
			},
			wantErr: true,
		},
	}

	for k, tt := range tests {
		if tt.setup != nil {
			tt.setup()
		}
		result, err := getSugConfPklist()
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of getSugConfPklist, Error Value Compare", err != nil, ut.ShouldResemble, tt.wantErr)
			continue
		}
		if tt.wantErr {
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of getSugConfPklist, Result Value Compare", result, tt.want.Assert, tt.want.Value.([]WeakSug))
	}
	ag.Run()
}
