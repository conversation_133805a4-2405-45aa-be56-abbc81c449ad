package sug

import (
	"encoding/json"
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestSug_Sug_Android_New_parseV1(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseV1 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.parseV1(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseV1, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseV1, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_parseV2(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseV2 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.parseV2(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseV2, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseV2, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_parseV3(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		res data.Wisesugnew_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseV3 ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.parseV3(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseV3, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseV3, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_parseType(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		t *data.Wisesugnew_Type
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseType ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	wtype := data.Wisesugnew_Type{
		"direct_website",
		"test",
		"test",
		map[string]interface{}{
			"site":     "ceshi",
			"show_url": "a.url",
			"icon_url": "a.url",
		},
	}
	ctx := createGetWebContext()
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	param := []map[string]interface{}{{
		"item_click": "a.url",
		"action":     1,
	}}
	tmp := Sug_Sug_Response_Dir_Url{
		Word:        "",
		Type:        1002,
		Title:       "ceshi",
		Sa:          "test",
		Hissync:     `{"query":"ceshi"}`,
		Icon:        "a.url",
		Description: "a.url",
		Param:       param,
	}

	wtype1 := data.Wisesugnew_Type{
		"direct_mail",
		"test",
		"test",
		map[string]interface{}{
			"post_code": "ceshi",
			"post_city": "北京",
		},
	}
	tmp1 := Sug_Sug_Response_Dir_Zipcode{
		Word:        "",
		Type:        1008,
		Sa:          "test",
		Title:       "ceshi",
		Description: "|北京 邮编",
	}

	wtype2 := data.Wisesugnew_Type{
		"direct_uncommon_word",
		"test",
		"test",
		map[string]interface{}{
			"word_pinyin":  "ceshi",
			"word":         "测试",
			"word_meaning": "ceshi",
		},
	}
	tmp2 := Sug_Sug_Response_Dir_Rare{
		Word:        "",
		Type:        1011,
		Sa:          "test",
		Title:       "测试ceshi",
		Description: "[字义]ceshi",
	}

	wtype3 := data.Wisesugnew_Type{
		"direct_zone_num",
		"test",
		"test",
		map[string]interface{}{
			"zone_num":  "10",
			"zone_city": "ceshi",
		},
	}
	tmp3 := Sug_Sug_Response_Dir_Zoonnumber{
		Word:        "",
		Type:        1009,
		Sa:          "test",
		Title:       "10",
		Description: "|ceshi 区号",
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //interface{}
		want1         Want //bool
	}{
		{
			"TestSug_Sug_Android_New_parseType",
			fields{
				common,
			},
			args{
				&wtype,
			},
			Want{
				tmp,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
		{
			"TestSug_Sug_Android_New_parseType",
			fields{
				common,
			},
			args{
				&wtype1,
			},
			Want{
				tmp1,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
		{
			"TestSug_Sug_Android_New_parseType",
			fields{
				common,
			},
			args{
				&wtype2,
			},
			Want{
				tmp2,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
		{
			"TestSug_Sug_Android_New_parseType",
			fields{
				common,
			},
			args{
				&wtype3,
			},
			Want{
				tmp3,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, got1 := this.parseType(tt.args.t, k, nil)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseType, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(interface{}))
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseType, Result Index:1 Value Compare", got1, tt.want1.Assert, tt.want1.Value.(bool))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_parseTypeDirectNew(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		t *data.Wisesugnew_Type
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseTypeDirectNew ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	info := [](interface{}){
		`{ "flag": "constellation", "versionControl": { "a0": { "max": "", "min": "11.0.0.0" }, "i0": { "max": "", "min": "11.0.0.0" } }, "value": { "query": "金牛座", "name": "金牛座 4月20日-5月20日" }, "port_control": 8 }`,
	}

	wtype := data.Wisesugnew_Type{
		"direct_new",
		"di_1",
		"test",
		map[string]interface{}{
			"vec_str_raw": info,
		},
	}
	ctx := createGetWebContext()
	adapterParams := &(map[string]string{
		"device_id": "E18ADD27D3B4460AB7D2CFE0CDD11E7A",
	})
	(*adapterParams)["bd_version"] = "13.33.5.0"
	requestParams := &(map[string]string{})
	(*requestParams)["osbranch"] = "a0"
	ctx.Set(constant.ADAPARAMS, *adapterParams)
	ctx.Set(constant.REQPARAMS, *requestParams)
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	tmp2 := Sug_Sug_Response_Dir_Swan{
		Type: "1018",
		Sa:   "di_1",
		Data: `[{"flag":"constellation","value":{"name":"金牛座 4月20日-5月20日","query":"金牛座"}}]`,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //interface{}
		want1         Want //bool
	}{
		{
			"TestSug_Sug_Android_New_parseType",
			fields{
				common,
			},
			args{
				&wtype,
			},
			Want{
				tmp2,
				ut.ShouldResemble,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}

		got, got1 := this.parseTypeDirectNew(tt.args.t, k, nil)
		// if k == 1 {
		// 	got = got.(Sug_Sug_Response_Dir_Swan).Data
		// }
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseTypeDirectNew, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(interface{}))
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseTypeDirectNew, Result Index:1 Value Compare", got1, tt.want1.Assert, tt.want1.Value.(bool))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_parseTypeNovel(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		t_original *data.Wisesugnew_Type
		t          *data.Swan_Sug
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseTypeNovel ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	vecRaw := data.Swan_Sug{}
	t_original := data.Wisesugnew_Type{}
	str := `{"flag":"novel","value":{"name":"金牛座 4月20日-5月20日","query":"金牛座","content_type":"0"}}`
	json.Unmarshal([]byte(str), &vecRaw)

	ctx := createGetWebContext()
	adapterParams := &(map[string]string{})
	(*adapterParams)["bd_version"] = "11.2.0.0"
	requestParams := &(map[string]string{})
	(*requestParams)["osbranch"] = "a0"
	ctx.Set(constant.ADAPARAMS, *adapterParams)
	ctx.Set(constant.REQPARAMS, *requestParams)
	basePage := BasePage{
		ctx: ctx,
	}
	common := Sug_Sug_Common{
		basePage,
	}
	tmp2 := Sug_Sug_Response_Dir_Novel{
		Type: "1019",
		Gid:  `[{"flag":"constellation","value":{"bookid":"2344","title_main":"金牛座"}}]`,
	}

	ctx1 := createGetWebContext()
	adapterParams1 := &(map[string]string{})
	(*adapterParams1)["bd_version"] = "4.2.0.0"
	requestParams1 := &(map[string]string{})
	(*requestParams1)["osbranch"] = "a2"
	ctx1.Set(constant.ADAPARAMS, *adapterParams1)
	ctx1.Set(constant.REQPARAMS, *requestParams1)
	basePage1 := BasePage{
		ctx: ctx1,
	}
	common1 := Sug_Sug_Common{
		basePage1,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //interface{}
		want1         Want //bool
	}{
		{
			"TestSug_Sug_Android_New_parseTypeNovel",
			fields{
				common,
			},
			args{
				&t_original,
				&vecRaw,
			},
			Want{
				tmp2,
				ut.ShouldNotBeNil,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
		{
			"TestSug_Sug_Android_New_parseTypeNovel",
			fields{
				common1,
			},
			args{
				&t_original,
				&vecRaw,
			},
			Want{
				tmp2,
				ut.ShouldNotBeNil,
			},
			Want{
				true,
				ut.ShouldBeTrue,
			},
		},
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, got1 := this.parseTypeNovel(tt.args.t_original, tt.args.t)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseTypeNovel, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(interface{}))
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_parseTypeNovel, Result Index:1 Value Compare", got1, tt.want1.Assert, tt.want1.Value.(bool))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_defaultTpl(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Sug_Sug_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Android_New_response(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestSug_Sug_Android_New_newSelf(t *testing.T) {
	type fields struct {
		Sug_Sug_Common Sug_Sug_Common
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Android_New{
			Sug_Sug_Common: tt.fields.Sug_Sug_Common,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Android_New_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
