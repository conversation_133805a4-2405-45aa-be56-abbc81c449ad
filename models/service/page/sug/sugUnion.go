package sug

import (
	"encoding/json"
	"fmt"
	"reflect"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type Sug_Sug_Union struct {
	Sug_Sug_Common
}

// 主要执行的函数入口
func (this *Sug_Sug_Union) Execute() {
	request := this.getRequest()
	tplData := this.defaultTpl()

	// 空query
	query := (*request)["query"]
	if query == "" {
		tplData.Errno = "9001"
		tplData.Msg = "no query"
		tplData.Type = "sug"
		this.response(tplData)
		return
	}

	// 非法攻击或超长直接拒绝 utf8中文长度按3算
	if len(query) > 50 {
		this.response(tplData)
		return
	}

	// 请求后端
	dataSrv := data.NewUnionSugNew(this.ctx)
	if ralGetErr := dataSrv.GetUnionSugResponse(query); ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		tplData.Errno = "9002"
		tplData.Msg = "opensug request fail"
		this.response(tplData)
		return
	}

	// 解析后端参数
	resData := dataSrv.GetUnionsugTplData()
	parseErr := this.parseNewRequestData(resData, tplData)
	if parseErr != nil {
		this.setErr()
		this.addNotice("parseErr", "1")
		tplData.Errno = "9003"
		tplData.Msg = "flow err"
		this.response(tplData)
		return
	}
	// parsedData := this.GetTplData()
	// this.response(parsedData)
	return
}

func (this *Sug_Sug_Union) parseNewRequestData(resData data.Wisesugnew_Response, tpl Sug_Sug_Response) error {
	if resData.Q == "" {
		tplByte, _ := json.Marshal(tpl)
		this.setTplByteData(tplByte)
		return nil
	}
	// 无sug列表
	if resData.G == nil || len(resData.G) == 0 {
		tplByte, _ := json.Marshal(tpl)
		this.setTplByteData(tplByte)
		return nil
	}

	request := this.getRequest()
	adaptions := this.getAdaption()
	queryCut, regexpList := preProcessPos(this.ctx, request, adaptions, resData.Q)

	sugRes := Sug_Sug_Response_Type2_Data_Uwp{}
	for index, v := range resData.G {
		if v.Q == "" || reflect.TypeOf(v.Q).Name() != "string" {
			continue
		}

		cutPos := getHighlight(this.ctx, queryCut, regexpList, resData.Q, v.Q, &v)
		if len(cutPos) != 0 {
			this.ctx.AddNotice(fmt.Sprintf("posMap_%d", index), fmt.Sprintf("s:%s,m:%v", v.Q, cutPos))
			sugRes.Pos = cutPos
		}

		sugRes.Word = v.Q
		sugRes.Type = "0"
		sugRes.Sa = v.Sa
		tpl.Data = append(tpl.Data, sugRes)
	}
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
	// this.addNotice("sug_finalres", string(tplByte))
	return nil
}

// 初始化对端协议的结构
func (this *Sug_Sug_Union) defaultTpl() Sug_Sug_Response {
	ext_body := Sug_Sug_Response_Extend_Query{
		Type: "0",
		Word: (*(this.getRequest()))["query"],
	}
	ext := Sug_Sug_Response_Extend{
		Query: ext_body,
	}
	data := []interface{}{}
	ret := Sug_Sug_Response{
		Errno:  "",
		Msg:    "",
		Type:   "union",
		Extend: ext,
		Data:   data,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *Sug_Sug_Union) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *Sug_Sug_Union) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Sug_Sug_Union{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("sug-union", &Sug_Sug_Union{})
}
