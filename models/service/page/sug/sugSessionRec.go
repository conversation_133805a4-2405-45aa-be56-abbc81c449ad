package sug

import (
	"encoding/json"
	"errors"
	"net/url"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/his"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
	"icode.baidu.com/baidu/searchbox/golang-lib/passport"
)

type sugSessionRec struct {
	BasePage
}

type sugSessionRecErr struct {
	Errno     string      `json:"errno"`
	Errmsg    string      `json:"errmsg"`
	Requestid string      `json:"requestid"`
	Switch    interface{} `json:"switch_control"`
}

func (this *sugSessionRec) Execute() {
	this.addNotice("sugnew", "1")
	request := this.getRequest()
	adaptions := this.getAdaption()
	tplData := this.defaultTpl()
	sugSessionRecErr := sugSessionRecErr{}
	sugSessionRecErr.Switch = common.SwitchConfMem.Switch

	dataquery := (*request)["data"]
	if dataquery == "" {
		sugSessionRecErr.Errno = "-1001"
		sugSessionRecErr.Errmsg = "params[data] is missing"
		sugSessionRecErr.Requestid = this.getLogidStr()
		this.response(sugSessionRecErr)
		return
	}
	m, _ := url.QueryUnescape(dataquery)
	dataqueryStr := strings.Replace(dataquery, "\n", "", -2)
	this.addNotice("postData", dataqueryStr)
	var reqdata his.RecParms
	decodeErr := json.Unmarshal([]byte(m), &reqdata)
	var dataParms data.DataParms

	if decodeErr != nil { //json格式不符合预期
		sugSessionRecErr.Errno = "-1001"
		sugSessionRecErr.Errmsg = "params[Eventtype] is missing"
		sugSessionRecErr.Requestid = this.getLogidStr()
		this.response(sugSessionRecErr)
		return
	}

	if reqdata.Eventbox == "" { //json格式不符合预期
		sugSessionRecErr.Errno = "-1001"
		sugSessionRecErr.Errmsg = "params[Eventbox] is missing"
		sugSessionRecErr.Requestid = this.getLogidStr()
		this.response(sugSessionRecErr)
		return
	}
	dataParms.Eventbox = reqdata.Eventbox
	if reqdata.Hisdata != "" {
		decodeRet, _ := base64.Deocde(reqdata.Hisdata, 0)
		decodeErr := json.Unmarshal([]byte(decodeRet), &dataParms.Hisdata)
		if decodeErr != nil { //json格式不符合预期
			// 尝试解析ios的数据格式 add by shaoling
			type Text struct {
				T string `json:"text"`
			}
			var texts []Text
			var hisItems []string
			decodeErr = json.Unmarshal([]byte(decodeRet), &texts)
			if decodeErr != nil {
				dataParms.Hisdata = []string{}
			} else {
				for _, hisItem := range texts {
					hisItems = append(hisItems, hisItem.T)
				}
				if len(hisItems) != 0 {
					dataParms.Hisdata = hisItems
				} else {
					dataParms.Hisdata = []string{}
				}
			}
		}
	} else {
		dataParms.Hisdata = []string{}
	}

	dataParms.Guessdata = []map[string]interface{}{}
	dataParms.Boxdata = []map[string]interface{}{}
	dataParms.Showquerys = []string{}
	dataParms.Guessfeedbackquery = []string{}
	dataParms.Eventtype = "sug_session"
	dataParms.ClientName = "sug_session_box"

	if reqdata.Eventquery != "" {
		dataParms.Eventquery = reqdata.Eventquery
	}
	if reqdata.Eventchannel != "" {
		dataParms.Eventchannel = reqdata.Eventchannel
	}
	if reqdata.Eventfrom != "" {
		dataParms.Eventfrom = reqdata.Eventfrom
	}

	//12.12版本解决WISE_HIS_PM带来的隐私模式判断问题
	dataParms.PrivateMode = reqdata.PrivateMode

	//cookie := this.ctx.Context.Request.Cookies()
	ckbduss, errck := this.ctx.Cookie("BDUSS")
	if errck != nil {
		dataParms.Puid = "0"
	} else {
		user, err := passport.Decode(ckbduss)
		if err == nil {
			dataParms.Puid = strconv.FormatInt(user.Id, 10)
		} else {
			dataParms.Puid = "0"
		}
	}
	sids, errck := this.ctx.Cookie("H_WISE_SIDS")
	if errck != nil || sids == "" {
		sids = ""
	}
	arrsid := strings.Split(sids, "_")
	var intsid []int
	for _, strsid := range arrsid {
		numid, _ := strconv.Atoi(strsid)
		intsid = append(intsid, numid)
	}
	dataParms.Sids = intsid

	dataSrv := data.NewRec(this.ctx)

	ralGetErr := dataSrv.GetRecResponse(request, adaptions, dataParms)
	if ralGetErr != nil {
		this.setErr()
		this.addNotice("ralErr", "1")
		this.addNotice("ralErrMsg", ralGetErr.Error())
		sugSessionRecErr.Errno = "-4002"
		sugSessionRecErr.Errmsg = "ral request fail"
		sugSessionRecErr.Requestid = this.getLogidStr()
		this.response(sugSessionRecErr)
		return
	}
	resData := dataSrv.GetHISTplData()
	tpl, parseErr := this.parseRALResData(resData, tplData)
	if parseErr != nil {
		this.setErr()
		this.addNotice("parseErr", parseErr.Error())
		sugSessionRecErr.Errno = tpl.Errno
		sugSessionRecErr.Errmsg = tpl.Errmsg
		sugSessionRecErr.Requestid = this.getLogidStr()
		this.response(sugSessionRecErr)
		return
	}
	this.response(tpl)
	return
}

func (this *sugSessionRec) parseRALResData(resDataMap map[string]data.TypeDispaly, tpl his.HisResponse) (his.HisResponse, error) {
	resData := resDataMap["1000510"]
	if resData.Retno != 0 {
		tpl.Errno = "-5001"
		tpl.Errmsg = "request sughis error:resData.Retno!=0 "
		return tpl, errors.New(tpl.Errmsg)
	}
	_, okrsf := resData.Data["rsf"]
	if !okrsf {
		tpl.Errno = "-5001"
		tpl.Errmsg = "request sughisrec error:rsf is empty"
		return tpl, errors.New(tpl.Errmsg)
	}

	data := map[string]interface{}{
		"rsf": resData.Data["rsf"],
	}

	_, oksug := resData.Data["sug"]
	if oksug {
		data["sug"] = resData.Data["sug"]
	}

	_, okdftbox := resData.Data["default"]
	if okdftbox {
		data["default"] = resData.Data["default"]
	}

	tpl.Data = data
	return tpl, nil
}

// 初始化对端协议的结构
func (this *sugSessionRec) defaultTpl() his.HisResponse {
	datainfo := []interface{}{}
	ret := his.HisResponse{
		Errno:  "0",
		Errmsg: "",
		Data:   datainfo,
		Switch: common.SwitchConfMem.Switch,
	}
	return ret
}

func (this *sugSessionRec) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *sugSessionRec) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &sugSessionRec{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("sug-session_rec", &sugSessionRec{})
}
