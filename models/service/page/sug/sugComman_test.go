package sug

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestSug_Sug_Common_formatSwanDirect(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		res data.Wisesug_Response
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: formatSwanDirect ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response_Dir_Swan
		wantErr       bool
	}{
	// TODO: Add test cases.
	}

	for k, tt := range tests {
		t := &Sug_Sug_Common{
			BasePage: tt.fields.BasePage,
		}
		got, err := t.formatSwanDirect(tt.args.res)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Common_formatSwanDirect, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Common_formatSwanDirect, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response_Dir_Swan))
	}
	ag.Run()
}
