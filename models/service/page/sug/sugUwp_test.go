package sug

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestSug_Sug_Uwp_response(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		tpl interface{}
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: response ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Sug_Sug_Uwp{
			BasePage: tt.fields.BasePage,
		}
		this.response(tt.args.tpl)
	}
	ag.Run()
}

func TestSug_Sug_Uwp_responseWithType(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		res data.Wisesug_Response
		v   string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: responseWithType ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Sug_Sug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Uwp{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.responseWithType(tt.args.res, tt.args.v)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Uwp_responseWithType, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Uwp_responseWithType, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Uwp_defaultTpl(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: defaultTpl ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Sug_Sug_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Uwp{
			BasePage: tt.fields.BasePage,
		}
		got := this.defaultTpl()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Uwp_defaultTpl, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Sug_Sug_Response))
	}
	ag.Run()
}

func TestSug_Sug_Uwp_newSelf(t *testing.T) {
	type fields struct {
		BasePage BasePage
	}
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: newSelf ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Pager
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Sug_Sug_Uwp{
			BasePage: tt.fields.BasePage,
		}
		got, err := this.newSelf(tt.args.ctx)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Uwp_newSelf, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestSug_Sug_Uwp_newSelf, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Pager))
	}
	ag.Run()
}
