package sug

import (
	"encoding/json"
	"errors"
	"net/url"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

//direct_new透传白名单
var directNewFlagWhiteList = map[string]bool{
	//novel需要特殊处理
	"mini_app":      true,
	"download":      true,
	"bear":          true,
	"pic":           true,
	"video":         true,
	"constellation": true,
	"postcode":      true,
	"zonecode":      true,
	"website":       true,
	"custom":        true,
	"music":         true,
	"movie":         true,
	"weather":       true,
	"translate":     true,
	"answer":        true,
	"entity":        true,
	"hot":           true,
}

//对于一些不需要分OS写的函数 可以统一写到一起维护
type Sug_Sug_Common struct {
	BasePage
}

func (t *Sug_Sug_Common) formatSwanDirect(res data.Wisesug_Response) (Sug_Sug_Response_Dir_Swan, error) {
	if len(res.Extend) != 4 || res.Extend[3] == "" {
		t.addNotice("swansug_ret", url.QueryEscape(res.Extend[3]))
		return Sug_Sug_Response_Dir_Swan{}, errors.New("not enough data for formatSwanDirect")
	}

	swanRet := data.Swan_Sug_Arr{}
	err := json.Unmarshal([]byte(res.Extend[3]), &swanRet)
	if err != nil { //解析json失败
		t.addNotice("swansug_ret", url.QueryEscape(res.Extend[3]))
		t.addNotice("swansug", "parse to jsonObject error")
		return Sug_Sug_Response_Dir_Swan{}, errors.New("parse to jsonObject error")
	}

	swanNum := len(swanRet)
	adaptions := t.getAdaption()
	requests := t.getRequest()
	bdVersion := (*adaptions)["bd_version"]
	osbranch := (*requests)["osbranch"]
	var swanRes []data.Swan_Sug_Res
	for i := 0; i < swanNum; i++ {
		if swanRet[i].VersionControl == nil { //不带VersionControl的数据均属于后端旧版数据
			if version.Compare(bdVersion, "10.11", "<") {
				continue //默认按10.11发版的分割线一刀切 小于10.11的全部跳过 大于等于的全部下发
			}
		} else { //带VersionControl的数据 走osbranch和min+max逻辑
			dataConfig, dataConfigOk := swanRet[i].VersionControl[osbranch]
			if !dataConfigOk { //客户端osbranch不属于下发范畴
				continue
			}

			if (dataConfig.Min != "" && version.Compare(bdVersion, dataConfig.Min, "<")) ||
				(dataConfig.Max != "" && version.Compare(bdVersion, dataConfig.Max, ">")) {
				continue //客户端bd_version不属于下发范畴
			}
		}

		tmp := data.Swan_Sug_Res{
			Flag:  swanRet[i].Flag,
			Value: swanRet[i].Value,
		}
		swanRes = append(swanRes, tmp)
	}

	if swanRes == nil {
		return Sug_Sug_Response_Dir_Swan{}, errors.New("data config-filter faild")
	}

	data, err := json.Marshal(swanRes)
	if err != nil {
		return Sug_Sug_Response_Dir_Swan{}, errors.New("tojson faild")
	}

	ret := Sug_Sug_Response_Dir_Swan{
		Type: SUG_DIR_SWAN,
		Data: string(data),
	}
	return ret, nil
}
