// @Title		models/service/page/sfs/rec.go
// @Description	sfs service类
// <AUTHOR>
// @Update		2022-04-07 11:00
package sfs

import (
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

// ServiceRecPath 推荐服务path
const ServiceRecPath = "rec"

type RecJSONResponse struct {
	Error     string      `json:"error"`
	LogID     string      `json:"logid"`
	Timestamp int         `json:"timestamp"`
	Data      interface{} `json:"data"`
}

type Rec struct {
	BasePage
}

// 主要执行的函数入口
func (r *Rec) Execute() {
	res, err := common.TransRequest(r.ctx, ServiceName, ServiceRecPath)
	if err != nil {
		r.SetErr()
		r.SetJSONResponse(ErrRecJSONResponseData(r.ctx, ServiceModelRec, err))
		return
	}
	r.SetJSONResponse(res)
	return
}

// ErrRecJSONResponseData 推荐异常json返回格式
func ErrRecJSONResponseData(ctx *gdp.WebContext, ServiceNameModel string, err error) string {
	ret := RecJSONResponse{
		Error:     "-1",
		LogID:     ctx.GetLogID(),
		Timestamp: int(time.Now().Unix()),
		Data:      []int{},
	}
	res, _ := json.Marshal(ret)
	ctx.Warning(fmt.Sprintf("%s err: %s", ServiceNameModel, err.Error()))
	return string(res)
}

// newSelf 实例化
func (r *Rec) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &Rec{}
	target.ctx = ctx
	return target, nil
}

// init
func init() {
	Register(ServiceModelRec, &Rec{})
}
