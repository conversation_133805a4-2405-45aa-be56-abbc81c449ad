// @Title		models/service/page/sfs/base.go
// @Description	sfs service类
// <AUTHOR>
// @Update		2022-04-07 11:00
package sfs

import (
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/gdp/gdp"
)

type Pager interface {
	Execute()
}

type Page interface {
	newSelf(ctx *gdp.WebContext) (Pager, error)
}

type BasePage struct {
	ctx *gdp.WebContext
}

// ServiceName 服务名称
const ServiceName = "search_forecast_service"

// ServiceModelRec
const ServiceModelRec = "sfs-rec"

var adapters = make(map[string]Page)

func Register(name string, adapter Page) {
	if adapter == nil {
		panic("page: Register adapter is nil")
	}
	if _, ok := adapters[name]; ok {
		panic("page: Register called twice for adapter " + name)
	}
	adapters[name] = adapter
}

func NewPager(pageName string, ctx *gdp.WebContext) (Pager, error) {
	adapter, ok := adapters[pageName]
	if !ok {
		return nil, fmt.Errorf("page: unknown adapter %q (forgotten register?)", pageName)
	}
	retPage, err := adapter.newSelf(ctx)
	if err != nil {
		return nil, fmt.Errorf("page: create page %q fail", pageName)
	}
	return retPage, nil
}

// SetJSONResponse
func (bp *BasePage) SetJSONResponse(bodyData string) {
	bp.ctx.Header("Content-Type", "application/json; charset=utf-8")
	bp.ctx.String(http.StatusOK, "%s", bodyData)
}

// addNotice
func (bp *BasePage) addNotice(key, value string) {
	bp.ctx.AddNotice(key, value)
}

// warning
func (bp *BasePage) warning(key, value string) {
	bp.ctx.Warning(fmt.Errorf("%s:%s", key, value))
}

// SetErr 用于index.log标记错误
func (bp *BasePage) SetErr() {
	bp.ctx.DealSucc = false
}
