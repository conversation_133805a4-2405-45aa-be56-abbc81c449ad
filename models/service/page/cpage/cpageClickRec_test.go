package cpage

import (
	"encoding/json"
	"errors"
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

func TestCPageClickRec_Execute(t *testing.T) {
	ctx := createGetWebContext()
	t.Run("success", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		cr := &data.ClickRec{}
		patch := gomonkey.ApplyMethodReturn(cr, "GetClickRecResponse", nil)
		defer patch.Reset()
		c.Execute()
		tplData := c.defaultTpl()
		assert.Equal(t, "0", tplData.Errno)
		assert.Equal(t, "", tplData.Errmsg)
		assert.NotEmpty(t, tplData.RequestID)
	})

	t.Run("data empty", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		cr := &data.ClickRec{}
		patch := gomonkey.ApplyMethodReturn(cr, "GetClickRecResponse", data.ErrClickRec)
		defer patch.Reset()
		c.Execute()
		tplData := c.GetTplData()
		gotBody := ClickResponse{}
		_ = json.Unmarshal([]byte(tplData), &gotBody)
		assert.Equal(t, "1000", gotBody.Errno)
		assert.Equal(t, "data empty", gotBody.Errmsg)
	})

	t.Run("handle error", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		cr := &data.ClickRec{}
		patch := gomonkey.ApplyMethodReturn(cr, "GetClickRecResponse", errors.New("test error"))
		defer patch.Reset()
		c.Execute()
		tplData := c.GetTplData()
		gotBody := ClickResponse{}
		_ = json.Unmarshal([]byte(tplData), &gotBody)
		assert.Equal(t, "1001", gotBody.Errno)
		assert.Equal(t, "test error", gotBody.Errmsg)
	})
}

func TestCPageClickRec_handleErr(t *testing.T) {
	ctx := createGetWebContext()

	t.Run("error handling", func(t *testing.T) {
		c := &CPageClickRec{BasePage: BasePage{ctx: ctx}}
		errno := "500"
		msg := "Internal Server Error"
		c.handleErr(errno, msg)

		resp := ClickResponse{}
		_ = json.Unmarshal(c.GetTplByteData(), &resp)

		if resp.Errno != errno {
			t.Errorf("Expected Errno to be '%s', got '%s'", errno, resp.Errno)
		}

		if resp.Errmsg != msg {
			t.Errorf("Expected Errmsg to be '%s', got '%s'", msg, resp.Errmsg)
		}

		if resp.RequestID != c.getLogidStr() {
			t.Errorf("Expected RequestID to be '%s', got '%s'", c.getLogidStr(), resp.RequestID)
		}

		if resp.Data == nil {
			t.Error("Expected Data to be non-nil")
		}
	})
}

func TestCPageClickRec_defaultTpl(t *testing.T) {
	ctx := createGetWebContext()

	t.Run("success", func(t *testing.T) {
		c := &CPageClickRec{BasePage: BasePage{ctx: ctx}}
		resp := c.defaultTpl()

		if resp.Errno != "0" {
			t.Errorf("Expected Errno to be '0', got '%s'", resp.Errno)
		}

		if resp.Errmsg != "" {
			t.Errorf("Expected Errmsg to be empty, got '%s'", resp.Errmsg)
		}

		if resp.RequestID != c.getLogidStr() {
			t.Errorf("Expected RequestID to be '%s', got '%s'", c.getLogidStr(), resp.RequestID)
		}

		if resp.Data == nil {
			t.Error("Expected Data to be non-nil")
		}
	})
}

func TestCPageClickRec_newSelf(t *testing.T) {
	ctx := createGetWebContext()
	t.Run("success", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		pager, err := c.newSelf(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, pager)
		assert.IsType(t, &CPageClickRec{}, pager)
	})
}

func TestCPageClickRec_response(t *testing.T) {
	ctx := createGetWebContext()
	t.Run("success", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		tpl := c.defaultTpl()
		c.response(tpl)
		assert.NotEqual(t, "", tpl.Data)
	})

	t.Run("nil_template", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		c.response(nil)
		assert.NotEqual(t, "", c.BasePage.GetTplData())
	})

	t.Run("empty_template", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		c.response(struct{}{})
		assert.NotNil(t, c.BasePage.GetTplData())
	})

	t.Run("invalid_template", func(t *testing.T) {
		c := &CPageClickRec{
			BasePage: BasePage{ctx: ctx},
		}
		invalidTpl := make(chan int)
		c.response(invalidTpl)
		assert.Equal(t, c.BasePage.GetTplData(), "")
	})
}

func createGetWebContext() *gdp.WebContext {
	httpRspBody1 := httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	//构造Request
	req, _ := http.NewRequest("GET", `/suggest?ctl=sug&query=sug&v=3&voi=0&wise_csor=3`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "1.1.1.1"),
	}
	return wc
}
