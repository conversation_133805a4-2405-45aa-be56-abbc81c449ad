package cpage

import (
	"encoding/json"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data"
)

type CPageClickRec struct {
	BasePage
}

type ClickResponse struct {
	Errno     string      `json:"errno"`
	Errmsg    string      `json:"errmsg"`
	RequestID string      `json:"requestid"`
	Data      interface{} `json:"data"`
}

// Execute 执行的函数入口
func (c *CPageClickRec) Execute() {
	// 发出请求，获得返回的结果
	request := c.getRequest()
	dataSrv := data.NewClickRec(c.ctx)
	tplData := c.defaultTpl()
	ralGetErr := dataSrv.GetClickRecResponse(request)
	if ralGetErr != nil {
		// 未召回数据时，不算作请求错误
		if ralGetErr == data.ErrClickRec {
			tplData.Errno = "1000"
			tplData.Errmsg = "data empty"
			tplData.Data = dataSrv.GetTplData()
			c.response(tplData)
		} else {
			c.handleErr("1001", ralGetErr.Error())
		}
		return
	}

	// 解析结果
	tplData.Data = dataSrv.GetTplData()

	// 结果返回给端
	c.response(tplData)
}

// 处理错误，返回错误信息
func (c *CPageClickRec) handleErr(errno string, msg string) {
	res := ClickResponse{
		Errno:     errno,
		Errmsg:    msg,
		RequestID: c.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	c.setErr()
	c.response(res)
}

// 初始化对端协议的结构
func (c *CPageClickRec) defaultTpl() ClickResponse {
	ret := ClickResponse{
		Errno:     "0",
		Errmsg:    "",
		RequestID: c.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	return ret
}

func (c *CPageClickRec) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	c.setTplByteData(tplByte)
}

func (c *CPageClickRec) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &CPageClickRec{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("cpage-click-rec", &CPageClickRec{})
}
