package cpage

import (
	"encoding/json"
	"errors"
	"sort"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/his"
)

type CPageUniversal struct {
	BasePage
}

type UniversalParams struct {
	Target string `json:"target"`
}

type Response struct {
	Errno     string      `json:"errno"`
	Errmsg    string      `json:"errmsg"`
	Requestid string      `json:"requestid"`
	Data      interface{} `json:"data"`
}

// Execute 执行的函数入口
func (this *CPageUniversal) Execute() {
	request := this.getRequest()

	dataquery := (*request)["data"]
	if dataquery == "" {
		this.handleErr("-1001", "params[data] is missing")
		return
	}

	var reqdata UniversalParams
	decodeErr := json.Unmarshal([]byte(dataquery), &reqdata)
	if decodeErr != nil || reqdata.Target == "" {
		this.handleErr("-1001", "params[data] is missing")
		return
	}

	var targetArr []string
	decodeErr = json.Unmarshal([]byte(reqdata.Target), &targetArr)
	if decodeErr != nil {
		this.addNotice("targetErr", decodeErr.Error())
		this.handleErr("-1002", "params[target] is missing")
		return
	}

	universalTplData := make(map[string]interface{})
	backendErr := false

	// 保证遍历的顺序一定，方便监控日志
	sort.Strings(targetArr)
	rsErrno := ""
	for _, backend := range targetArr {
		// 端下发参数错误，这里过滤下只需要rs
		if (*request)[constant.SCENE] == constant.CPAGE_TOP && backend != "rs" {
			continue
		}

		v, err := this.getTargetData(backend)
		if err != nil {
			this.addNotice(backend+"_pageSrcErr", err.Error())
			backendErr = true
			continue
		}
		// rs数据有可能返回空，需要判空。错误码为0时，表示正常返回了数据
		if v.Errno == "0" {
			universalTplData[backend] = v.Data
		}
		// 划词场景，暂时只有 rs，需要将rs的errmsg设置到tplData中
		if (*request)[constant.SCENE] == constant.CPAGE_HIGHLIGHT && backend == "rs" {
			rsErrno = v.Errno
		}
	}

	// 请求后端错误，并且都没结果时，返回错误
	if backendErr && len(universalTplData) == 0 {
		this.handleErr("-2000", "server error")
		return
	}

	tplData := this.defaultTpl()
	if (*request)[constant.SCENE] == constant.CPAGE_HIGHLIGHT {
		// 划词场景，需要将rs的errmsg设置到tplData中，端上会根据 Errno 区分是否需要划词，不为 0 不划词
		// 同时前端需要 data 的名字为data 而不是 RS，在这里做一次转换。
		tplData.Data = universalTplData["rs"]
		tplData.Errno = rsErrno
		if tplData.Errno == "100" {
			tplData.Errmsg = "no highlight recall"
		} else if tplData.Errno == "200" {
			tplData.Errmsg = "blacklist url"
		}

	} else {
		tplData.Data = universalTplData
	}

	this.response(tplData)

	return
}

// 根据nodeName获取不同的 pageSrv
func (c *CPageUniversal) getTargetData(nodeName string) (his.HisResponse, error) {
	pageName := "his" + "-" + nodeName
	pageSrv, err := his.NewPager(pageName, c.ctx)
	if err != nil {
		return his.HisResponse{}, err
	}

	pageSrv.Execute()
	c.addNotice(pageName, "1")

	// target指定的后端执行错误
	originData, ok := pageSrv.GetTplOriginData().(his.HisListErr)
	if ok && originData.Errno != "0" {
		return his.HisResponse{}, errors.New("execute err")
	}

	v, ok := pageSrv.GetTplOriginData().(his.HisResponse)
	if !ok {
		return his.HisResponse{}, errors.New("GetTplOriginData err")
	}

	return v, nil
}

// 处理错误，返回错误信息
func (c *CPageUniversal) handleErr(errno string, msg string) {
	res := Response{
		Errno:     errno,
		Errmsg:    msg,
		Requestid: c.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	c.setErr()
	c.response(res)
}

// 初始化对端协议的结构
func (this *CPageUniversal) defaultTpl() Response {
	ret := Response{
		Errno:     "0",
		Errmsg:    "",
		Requestid: this.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	return ret
}

func (this *CPageUniversal) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	this.setTplByteData(tplByte)
}

func (this *CPageUniversal) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &CPageUniversal{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("cpage-universal", &CPageUniversal{})
}
