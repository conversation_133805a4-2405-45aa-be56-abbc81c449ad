package twoinone

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/bfenetworks/bfe/bfe_module"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/proto"
	"icode.baidu.com/baidu/gdp/codec/cbrotli/cbrotli"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"

	"icode.baidu.com/baidu/searchbox/go-suggest/idl"
	"icode.baidu.com/baidu/searchbox/go-suggest/idl/csmixed"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	data "icode.baidu.com/baidu/searchbox/go-suggest/models/service/data/twoinone"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/sug"
	gosug_modules "icode.baidu.com/baidu/searchbox/go-suggest/modules"
	"icode.baidu.com/baidu/searchbox/go-suggest/strategyer"
)

type ConfRoute struct {
	His   []string `toml:"his"`
	Error string   `toml:"error"`
	Sug   struct {
		Default string   `toml:"__default"`
		Sug     string   `toml:"sug"`
		Open    []string `toml:"open"`
	} `toml:"sug"`
}

type responseErr struct {
	Errno     string `json:"errno"`
	Errmsg    string `json:"errmsg"`
	Requestid string `json:"requestid"`
}

var confRoute ConfRoute

type TwoInOne struct {
	BasePage
	delimitedMsgBuf *bytes.Buffer
	chunkIndex      int
	searchClient    *data.SeachClient

	gzipWriter  *gzip.Writer
	brWriter    *cbrotli.Writer
	compressBuf *bytes.Buffer

	// 判断是否命中各种过滤策略
	straFilter bool
}

// 主要执行的函数入口
func (this *TwoInOne) Execute() {
	// 对压缩 writer 释放资源
	defer this.release()
	this.addNotice("TwoInOne", "1")

	w := this.ctx.Context.Writer // 获取http response write hander, 用于chunk数据返回
	this.sendHeader(&w)

	request := this.getRequest()
	adaption := this.getAdaption()
	ctl := (*request)["ctl"]
	action := (*request)["action"]

	this.searchClient.ParseRequest()

	pageSrvName := CreateSugPageSrv(*adaption, action, ctl)
	this.ctx.AddNotice("pageSrvName", pageSrvName)
	this.ctx.TimerStart("sugCost")
	pageSrc, err := sug.NewPager(pageSrvName, this.ctx)
	if err != nil {
		this.sendErrorSugResponse(&w, "9999", "server sug error")
		return
	}
	this.addNotice("erheyiSample", this.searchClient.Mode)

	// 是否需要请求sug预测服务
	needPredict := false
	// 判断是否预取
	prefetchControl := this.prefetchControl()

	if prefetchControl && (this.searchClient.Mode == "PREDICTSUG" || this.searchClient.Mode == "FORCE") {
		needPredict = true
		this.addNotice("needPresearch", "1")
	}
	pageSrc.SetNeedPredict(needPredict)

	// sug处理逻辑， 第一个pb包返回
	pageSrc.Execute()
	// this.addNotice("finalres", fmt.Sprintf("%s", pageSrc.GetTplData()))

	// 判断是否进行预取, event_type为0不进行预取
	hasMore := 1
	// 预测模块返回的数据
	presearchData := pageSrc.GetPresearchData()
	if !prefetchControl ||
		(this.searchClient.Mode == "COMPARE" && (*request)["event_type"] != "2") ||
		(this.searchClient.Mode == "UNKOWN" && (*request)["event_type"] != "2") ||
		((*request)["event_type"] != "2" && presearchData.Query == "") ||
		(*request)["event_type"] == "0" {
		hasMore = 0
	}

	// 强制预取
	if (*request)["event_type"] == "2" {
		// 主sug, 并且没被策略过滤时生效
		if action == "" && !this.straFilter {
			presearchData.Query = (*request)["query"]
			hasMore = 1
		} else {
			hasMore = 0
		}
	}

	// 返回给端混合协议类型打点
	if common.IsUseCSMIXED(request) {
		this.ctx.AddNotice("CSMixed", "1")
	}
	if osbranch, ok := (*request)["osbranch"]; ok {
		this.ctx.AddNotice("osbranch", osbranch)
	}

	// mode为force时，不发送sug包
	if this.searchClient.Mode != "FORCE" {
		_, err = this.sendResponse(
			&w,
			uint32(this.chunkIndex),
			1,
			uint32(hasMore),
			csmixed.StatusVal_OK,
			csmixed.TypeVal_SUG,
			[]byte(pageSrc.GetTplData()),
		)
		if err != nil {
			this.warning("sugSendResponseFailed", err.Error())
			return
		}
	}

	this.addNotice("sugCost", fmt.Sprintf("%.2f", this.ctx.TimerSince("sugCost").Seconds()*1000))

	// 不需要预取，直接返回
	if hasMore != 1 {
		return
	}

	// 执行预取请求
	this.executePresearch(w, presearchData)
}

// 执行预取请求
func (this *TwoInOne) executePresearch(w gin.ResponseWriter, presearchData sug.PresearchType) {
	request := this.getRequest()
	this.ctx.TimerStart("vuiCost")
	this.ctx.TimerStart("firstChunkCost")

	// 发送请求
	resp, err := this.searchClient.Do(presearchData.Query)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil || resp.StatusCode != 200 {
		this.sendResponse(
			&w,
			uint32(this.chunkIndex),
			1,
			0,
			csmixed.StatusVal_FATAL_ERR,
			csmixed.TypeVal_SEARCH_HEADER,
			[]byte(""),
		)
		this.ctx.AddNotice("requestPrNginxFailed", 1)
		return
	}

	// header pb包
	osbranch := (*this.getRequest())["osbranch"]
	headerMap := common.HttpHeaderConvert(osbranch, &resp.Header)
	if bdsvrtm := resp.Header.Get("Bdsvrtm"); bdsvrtm != "" {
		this.addNotice("Bdsvrtm", bdsvrtm)
	}
	if _, ok := headerMap["X-Protocol"]; !ok {
		headerMap["X-Protocol"] = resp.Proto
	}
	header_str, _ := json.Marshal(headerMap)
	this.addNotice("responseHeader", string(header_str))

	// 发送预取的header数据
	_, err = this.sendResponse(
		&w,
		uint32(this.chunkIndex),
		1,
		1,
		csmixed.StatusVal_OK,
		csmixed.TypeVal_SEARCH_HEADER,
		header_str,
	)
	common.DumpData(this.ctx, "2shoubai.nginx_header", header_str)

	if err != nil {
		this.warning("sendPrefetchHeaderErr", err.Error())
		return
	}

	var ori_bytes = 0
	var compressed_bytes = 0
	var chunkSize = 0
	var UnCompressTotal float64

	headBufSize := 256 * 1024 // 按照256k申请

	headBuf := make([]byte, headBufSize) // head包的buffer
	getHead := false
	headCnt := 0
	headLen := 0
	headFlush := false // 是否已经给端返回head首包

	// 128k
	packageSize := common.TwoInOneConfMem.PackageSize
	if (*request)["cksize"] != "" {
		cksize, err := strconv.Atoi((*request)["cksize"])
		if err == nil && cksize > 0 {
			packageSize = cksize * 1024
		}
	}

	var brReader *cbrotli.Reader
	var gzipReader *gzip.Reader
	var gzipReadErr error
	useBrReader := false

	// 使用 br 对预取的返回数据解压缩
	if resp.Header.Get("Content-Encoding") == "br" {
		this.ctx.AddNotice("presearchResEncoding", "br")
		useBrReader = true
		brReader = cbrotli.NewReader(resp.Body)
		defer brReader.Close()
	} else {
		this.ctx.AddNotice("presearchResEncoding", "gzip")
		gzipReader, gzipReadErr = gzip.NewReader(resp.Body)
		if gzipReadErr != nil {
			this.warning("gzipReadErrInitError", gzipReadErr.Error())
			this.sendSearchResultErr(&w)
			return
		}
		defer gzipReader.Close()
	}

	// 处理 混合协议 格式的预取数据
	if this.isCSMixedType(resp.Header.Get("Content-Type")) {
		this.sendPresearchData(&w, brReader, gzipReader, useBrReader, packageSize)
		return
	}

	// 处理 html 格式的预取数据
	for {
		chunkBodyLen := 0
		var chunkErr error
		buf := make([]byte, packageSize)
		headCnt = headCnt + 1

		this.ctx.TimerStart("UnCompressCost")
		this.ctx.TimerStart("findHeadCompressCost")
		if !headFlush {
			getHead = strings.Index(string(headBuf), "</head>") != -1
		}

		// head包收包完毕之后才发送，不按照size控制
		if !getHead {
			if useBrReader {
				chunkBodyLen, chunkErr = brReader.Read(buf)
			} else {
				chunkBodyLen, chunkErr = gzipReader.Read(buf)
			}
		}
		if headFlush == false {
			this.ctx.AddNotice(fmt.Sprintf("headCnt_%d_%d_%d", headCnt, chunkBodyLen, headBufSize-headLen), headLen)
		}
		if getHead == false && chunkErr == nil {
			if (headBufSize - headLen) < chunkBodyLen {
				this.warning("searchbody_is_to_large", "1")
				this.sendSearchResultErr(&w)
				break
			}
			copy(headBuf[headLen:], buf[:chunkBodyLen])
			headLen = chunkBodyLen + headLen
			continue
		} else if getHead == true && headFlush == false && chunkErr == nil {
			// 已经获取到完整的head数据
			buf = headBuf
			chunkBodyLen = headLen
			getHeadCost := this.ctx.TimerSince("findHeadCompressCost").Seconds() * 1000
			this.ctx.AddNotice(fmt.Sprintf("%d_getHeadCost", headCnt), getHeadCost)
		}

		// head包按照整包输出后，content包按照size大小控制
		if headFlush == true {
			if useBrReader {
				chunkBodyLen, chunkErr = this.readAtLeast(brReader, buf, packageSize)
			} else {
				chunkBodyLen, chunkErr = this.readAtLeast(gzipReader, buf, packageSize)
			}
		}

		UnCompressCost := this.ctx.TimerSince("UnCompressCost").Seconds() * 1000
		this.ctx.AddNotice(fmt.Sprintf("%d_IndexReadCost", this.chunkIndex), UnCompressCost)
		UnCompressTotal += UnCompressCost

		is_finstream := 0
		if chunkErr != nil && chunkBodyLen == 0 {
			// 数据有异常
			if chunkErr != io.EOF {
				this.addNotice("chunkEnd", chunkErr.Error())
				this.sendSearchResultErr(&w)
				break
			}

			this.ctx.AddNotice("chunkBodyLen", chunkBodyLen)
			is_finstream = 1
			_, err = this.sendResponse(
				&w,
				uint32(this.chunkIndex),
				uint32(is_finstream),
				0,
				csmixed.StatusVal_OK,
				csmixed.TypeVal_A_PAGE,
				[]byte(""),
			)
			if err != nil {
				this.warning("finalSearchSendFailed", err.Error())
			}
			break
		}

		searchPbChunkLen, err := this.sendResponse(
			&w,
			uint32(this.chunkIndex),
			uint32(is_finstream),
			0,
			csmixed.StatusVal_OK,
			csmixed.TypeVal_A_PAGE,
			buf[:chunkBodyLen],
		)
		if err != nil {
			this.warning(fmt.Sprintf("%d_Index_SearchSendFailed", this.chunkIndex), err.Error())
		}

		ori_bytes += searchPbChunkLen
		compressed_bytes += len(this.compressBuf.Bytes())
		this.ctx.AddNotice(fmt.Sprintf("%d Index_Pb", this.chunkIndex-1), searchPbChunkLen)
		this.ctx.AddNotice(fmt.Sprintf("%d Index", this.chunkIndex-1), chunkBodyLen)

		// 首包返回后进行打点
		if !headFlush {
			this.ctx.AddNotice("firstChunkCost", this.ctx.TimerSince("firstChunkCost").Seconds()*1000)
			this.ctx.AddNotice("headFlushCnt", headCnt)
			headFlush = true
		}

		chunkSize += chunkBodyLen
		buf = nil
	}

	this.ctx.AddNotice("vuiCost", this.ctx.TimerSince("vuiCost").Seconds()*1000)
	this.ctx.AddNotice("uncompressCostTotal", UnCompressTotal)
	this.ctx.AddNotice("chunkBytes", chunkSize)
	this.ctx.AddNotice("ori_bytes", ori_bytes)
	this.ctx.AddNotice("compressed_bytes", compressed_bytes)
}

/**
 * @desc 从reader读取至少 min bytes到buf， buf保证申请到足够的空间大小
 */
func (this *TwoInOne) readAtLeast(r io.Reader, buf []byte, min int) (n int, err error) {
	if len(buf) < min {
		return 0, io.ErrShortBuffer
	}
	for n < min && err == nil {
		var nn int
		nn, err = r.Read(buf[n:])
		n += nn
	}
	if err != nil && err != io.EOF {
		this.ctx.AddNotice("readAtLeast error", err.Error())
	}

	if n >= min {
		err = nil
	} else if n > 0 && err == io.EOF {
		err = io.ErrUnexpectedEOF
	}
	return
}

/**
 * @desc 初始化
 *
 */
func (this *TwoInOne) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &TwoInOne{}
	target.ctx = ctx
	searchClient := data.NewSearchClient(ctx)
	target.searchClient = searchClient

	var delimitedMsgBuf bytes.Buffer
	var compressBuf bytes.Buffer

	if common.IsEnableBr(ctx) {
		ctx.AddNotice("Content-Encoding", "br")
		target.brWriter = cbrotli.NewWriter(&compressBuf, cbrotli.WriterOptions{
			Quality: 3,
		})
	} else {
		ctx.AddNotice("Content-Encoding", "gzip")
		gzipWriter, gzipWriteErr := gzip.NewWriterLevel(&compressBuf, 1)
		if gzipWriteErr != nil {
			ctx.AddNotice("gzipWriterInitErr", gzipWriteErr.Error())
			return target, gzipWriteErr
		}
		target.gzipWriter = gzipWriter
	}

	target.delimitedMsgBuf = &delimitedMsgBuf
	target.chunkIndex = 0
	target.compressBuf = &compressBuf

	gosug_modules.ModPrison.SetCtx(ctx)
	return target, nil
}

func (this *TwoInOne) prefetchControl() bool {
	event_type := (*this.getRequest())["event_type"]
	prefetchSwitch := sug.GetPrefetch()
	this.ctx.AddNotice("prefetchSwitch", prefetchSwitch == "on")
	// 开关关闭，并且不是强制预取时，不进行后续处理
	// 是为了在强制预取时，执行后续的过滤策略
	if prefetchSwitch != "on" && event_type != "2" {
		return false
	}

	osbranch := (*this.getRequest())["osbranch"]
	prefeth_strategy := common.TwoInOneConfMem.PrefetchStragtegy

	val, ok := prefeth_strategy[osbranch]
	if ok && this.searchClient.Mode != "FORCE" {
		this.ctx.AddNotice("prefetchStrategy", val)
		for _, stategyName := range val {
			dataInput := []byte{}
			strageyObj := strategyer.NewStrategyer(stategyName, this.ctx, dataInput, nil)
			if strageyObj == nil {
				this.ctx.AddNotice(fmt.Sprintf("%s_is_nil", stategyName), 1)
				continue
			}

			strategyRet := strageyObj.Execute(dataInput)
			// 当前请求被策略过滤
			if strategyRet {
				this.straFilter = true
				this.ctx.AddNotice(fmt.Sprintf("%s", stategyName), strategyRet)
				return false
			}
		}
	}

	// modpresion 实例级别限流
	processRet, _ := gosug_modules.ModPrison.PrisonHandler("wise", this.ctx.Request)
	this.ctx.AddNotice("mod_prisonRet", processRet)
	if processRet != bfe_module.BfeHandlerGoOn {
		this.ctx.AddNotice("prefetchReqLimitHit", 1)
		return false
	}
	return true
}

/**
 * @desc 初始化资源的释放
 *
 */
func (this *TwoInOne) release() {
	this.compressBuf.Reset()

	if common.IsEnableBr(this.ctx) {
		this.brWriter.Close()
	} else {
		this.gzipWriter.Close()
	}

	w := this.ctx.Context.Writer
	w.Write(this.compressBuf.Bytes())
	w.(http.Flusher).Flush()
}

// 判断预取返回数据是否使用混合协议
func (this *TwoInOne) isCSMixedType(ContentType string) bool {
	if strings.Contains(ContentType, "application/x-protobuffer") {
		this.ctx.AddNotice("presearchResType", "CSMixed")
		return true
	}
	return false
}

// 发送混合协议类型的预取数据
func (this *TwoInOne) sendPresearchData(w *gin.ResponseWriter, brReader *cbrotli.Reader, gzipReader *gzip.Reader, useBrReader bool, packageSize int) {
	totalLen := 0
	compressed_bytes := 0
	// 记录当前读到了第几个包
	chunkCnt := 0

	var chunkLen int
	var readErr error
	buf := make([]byte, packageSize)

	for {
		if useBrReader {
			chunkLen, readErr = brReader.Read(buf)
		} else {
			chunkLen, readErr = gzipReader.Read(buf)
		}

		if chunkLen > 0 {
			this.sendChunkData(w, buf[:chunkLen])
		}

		if readErr != nil {
			// 收数据过程有异常
			if readErr != io.EOF {
				this.sendSearchResultErr(w)
				this.ctx.AddNotice("cs_chunk_err", readErr.Error())
			}

			this.ctx.AddNotice("cs_chunk_end_len", chunkLen)
			break
		}

		if chunkLen == 0 {
			this.ctx.AddNotice("cs_chunk_len_zero", chunkCnt)
			this.sendSearchResultErr(w)
			break
		}

		if chunkCnt == 0 {
			this.ctx.AddNotice("cs_firstChunkCost", this.ctx.TimerSince("firstChunkCost").Seconds()*1000)
		}
		chunkCnt++
		totalLen += chunkLen
		compressed_bytes += len(this.compressBuf.Bytes())
	}

	this.ctx.AddNotice("cs_vuiCost", this.ctx.TimerSince("vuiCost").Seconds()*1000)
	this.ctx.AddNotice("cs_compressed_bytes", compressed_bytes)
	this.ctx.AddNotice("cs_ori_bytes", totalLen)
	this.ctx.AddNotice("cs_chunk_num", chunkCnt)
}

// 发送混合协议的chunk包
func (this *TwoInOne) sendChunkData(w *gin.ResponseWriter, resBytes []byte) {
	this.compressBuf.Reset()
	if common.IsEnableBr(this.ctx) {
		common.BrCompress(this.brWriter, this.compressBuf, resBytes)
	} else {
		common.GzipCompress(this.gzipWriter, this.compressBuf, resBytes)
	}
	(*w).Write(this.compressBuf.Bytes())
	(*w).(http.Flusher).Flush()
	this.chunkIndex++
}

/**
 * @desc 发送chunk包
 *
 */
func (this *TwoInOne) sendResponse(w *gin.ResponseWriter, packId uint32, isFin uint32, hasMore uint32, csStatus csmixed.StatusVal, csResType csmixed.TypeVal, responseBytes []byte) (int, error) {
	// this.ctx.TimerStart("pbMarshaCost")
	var err error
	var out []byte
	var responseType idl.TypeVal

	// 使用CS混合协议打包数据
	if common.IsUseCSMIXED(this.getRequest()) {
		csPbRes := this.searchClient.MakeCSMixedResponse(packId, isFin, hasMore, csStatus, csResType, responseBytes)
		out, err = proto.Marshal(csPbRes)
	} else {
		status := idl.StatusVal(int32(csStatus))

		// 将cs协议的type转换为二合一协议type
		switch csResType {
		case csmixed.TypeVal_SUG:
			responseType = idl.TypeVal_SUG
		case csmixed.TypeVal_SEARCH_HEADER:
			responseType = idl.TypeVal_SEARCH_HEADER
		case csmixed.TypeVal_A_PAGE:
			responseType = idl.TypeVal_SEARCH_BODY
		default:
			return 0, fmt.Errorf("idl not have type: %d", int32(csResType))
		}

		headerPbResonse := this.searchClient.MakeTwoInOneResponse(packId, isFin, hasMore, status, responseType, responseBytes)
		out, err = proto.Marshal(headerPbResonse)
	}

	if err != nil {
		this.warning(fmt.Sprintf("%d_Index_SearchSendFailed", packId), err.Error())
		return len(out), err
	}
	// pbMarshaCost := this.ctx.TimerSince("pbMarshaCost").Seconds() *1000
	// this.ctx.AddNotice(fmt.Sprintf("%d_Index_pbMarshaCost", packId), pbMarshaCost)

	this.delimitedMsgBuf.Reset()
	this.compressBuf.Reset()
	common.WriteProtoBufDelimited(this.delimitedMsgBuf, out)

	// this.ctx.TimerStart("gzipCompressCost")
	if common.IsEnableBr(this.ctx) {
		common.BrCompress(this.brWriter, this.compressBuf, this.delimitedMsgBuf.Bytes())
	} else {
		common.GzipCompress(this.gzipWriter, this.compressBuf, this.delimitedMsgBuf.Bytes())
	}
	// gzipCost := this.ctx.TimerSince("gzipCompressCost").Seconds() * 1000
	// this.ctx.AddNotice(fmt.Sprintf("%d_Index_GzipCompressCost", packId), gzipCost)
	(*w).Write(this.compressBuf.Bytes())
	(*w).(http.Flusher).Flush()
	this.chunkIndex++

	return len(out), nil
}

/**
 * @desc 发送二合一header
 *
 */
func (this *TwoInOne) sendHeader(w *gin.ResponseWriter) {
	header := (*w).Header()
	header.Set("Transfer-Encoding", "chunked")
	header.Set("Content-Type", "application/x-protobuffer;charset=utf-8")
	header.Set("Content-Encoding", common.GetCompressName(this.ctx))
	header.Set("X-Accel-Buffering", "no")
	header.Set("X-TwoInoneId", this.getLogidStr())
	(*w).WriteHeader(http.StatusOK)
}

/**
 * @desc 错误响应发送
 *
 */
func (this *TwoInOne) sendErrorSugResponse(w *gin.ResponseWriter, errno string, errmsg string) {
	ret := responseErr{
		Errno:     errno,
		Errmsg:    errmsg,
		Requestid: this.getLogidStr(),
	}
	tplByte, _ := json.Marshal(ret)
	//this.addNotice("finalres", fmt.Sprintf("%s", tplByte))
	this.sendResponse(w, uint32(this.chunkIndex), 1, 0, csmixed.StatusVal_OK, csmixed.TypeVal_SUG, tplByte)
}

// 发送预取结果错误
func (this *TwoInOne) sendSearchResultErr(w *gin.ResponseWriter) {
	this.sendResponse(
		w,
		uint32(this.chunkIndex),
		1,
		0,
		csmixed.StatusVal_EXP_SEARCH_RESULT_ERR,
		csmixed.TypeVal_A_PAGE,
		[]byte(""),
	)
}

/**
 * @desc 获取sug处理对象
 */
func CreateSugPageSrv(adaption map[string]string, action string, ctl string) string {
	// 普通sug
	if action == "sug" || action == "" {
		platform := adaption["platform"]
		return "sug" + "-" + platform
	}
	// 判断是否在六合sug 配置白名单内
	for _, nodeName := range confRoute.Sug.Open {
		if nodeName == action {
			return "sug" + "-" + "open"
		}
	}
	// action有值但不在白名单内，交由union处理
	return "sug" + "-" + "union"
}

/**
 * @desc 读取配置
 *
 */
func init() {
	filePath := filepath.Join(env.ConfRootPath(), "route.toml")
	toml.DecodeFile(filePath, &confRoute)
	Register("twoinone", &TwoInOne{})
}
