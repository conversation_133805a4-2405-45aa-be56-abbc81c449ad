package ups

import (
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data/ups"
)

type GetUPS struct {
	BasePage
}

type Params struct {
	Target string `json:"target"`
}

func (g *GetUPS) Execute() {
	request := g.getRequest()
	tplData := g.defaultTpl()

	// 未登录，不算做错误，不计入err[1]
	if !common.IsLogin(request) {
		tplData.Errno = "1001"
		tplData.Errmsg = "not login"
		g.response(tplData)
		return
	}

	// 获取请求参数
	targetArr, err := g.fetchTargetArr((*request)["data"])
	if err != nil {
		g.ctx.AddNotice("fetchTargetErr", err.Error())
		g.handleErr("2001", err.Error(), err)
		return
	}

	dataSrv := ups.NewGetSrv(g.ctx)
	err = dataSrv.GetResponse((*request)[constant.SESSION_UID], targetArr)
	if err != nil {
		g.ctx.AddNotice("pageSrvErr", err.Error())
		g.handleErr("3001", "ral request fail", err)
		return
	}

	tplData.Data = dataSrv.GetUPSData()
	g.response(tplData)

	return
}

// 解析请求参数，获取target数组
func (g *GetUPS) fetchTargetArr(dataParams string) ([]string, error) {
	var reqData Params
	err := json.Unmarshal([]byte(dataParams), &reqData)
	if err != nil || reqData.Target == "" {
		return nil, errors.New("params[data] is missing")
	}

	// 获取target参数
	var targetArr []string
	err = json.Unmarshal([]byte(reqData.Target), &targetArr)
	if err != nil || len(targetArr) == 0 {
		return nil, errors.New("params[target] is missing")

	}

	return targetArr, nil
}

// 处理错误，返回错误信息
func (g *GetUPS) handleErr(errno string, msg string, err error) {
	res := Response{
		Errno:     errno,
		Errmsg:    msg,
		Requestid: g.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	// 没找到任何开关值时，不计入统计错误
	if err != ups.ErrEmpty {
		g.setErr()
	}
	g.response(res)
}

// 初始化对端协议的结构
func (g *GetUPS) defaultTpl() Response {
	ret := Response{
		Errno:     "0",
		Errmsg:    "",
		Requestid: g.getLogidStr(),
		Data:      map[string]interface{}{},
	}
	return ret
}

func (g *GetUPS) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	g.setTplByteData(tplByte)
}

func (g *GetUPS) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &GetUPS{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("ups-get", &GetUPS{})
}
