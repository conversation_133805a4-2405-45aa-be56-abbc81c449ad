package ups

import (
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/data/ups"
)

type SetUPS struct {
	BasePage
}

// 传入的data参数结构
type setBodyParams struct {
	PersonalSwitch string `json:"personalSwitch"`
	SugStoreSet    string `json:"sugStoreSet"`
}

func (s *SetUPS) Execute() {
	request := s.getRequest()
	tplData := s.defaultTpl()

	// 未登录，不算做错误，不计入err[1]
	if !common.IsLogin(request) {
		tplData.Errno = "1001"
		tplData.Errmsg = "not login"
		s.response(tplData)
		return
	}

	// 检查请求参数是否合法
	reqData, err := s.getDataParams((*request)["data"])
	if err != nil {
		s.ctx.AddNotice("fetchTargetErr", err.Error())
		s.handleErr("2001", err.Error())
		return
	}

	dataSrv := ups.NewSetSrv(s.ctx)
	err = dataSrv.GetResponse((*request)[constant.SESSION_UID], reqData)
	if err != nil {
		s.ctx.AddNotice("pageSrvErr", err.Error())
		s.handleErr("3001", "ral request fail")
		return
	}

	s.response(tplData)

	return
}

// 检查输入参数是否合法
func (s *SetUPS) getDataParams(data string) (map[string]string, error) {
	var reqData map[string]string
	err := json.Unmarshal([]byte(data), &reqData)
	if err != nil || len(reqData) == 0 {
		return nil, errors.New("params[data] is missing")
	}

	return reqData, nil
}

// 处理错误，返回错误信息
func (s *SetUPS) handleErr(errno string, msg string) {
	res := Response{
		Errno:     errno,
		Errmsg:    msg,
		Requestid: s.getLogidStr(),
		Data:      map[string]interface{}{},
	}

	s.setErr()
	s.response(res)
}

// 初始化对端协议的结构
func (s *SetUPS) defaultTpl() Response {
	ret := Response{
		Errno:     "0",
		Errmsg:    "",
		Requestid: s.getLogidStr(),
		Data:      map[string]interface{}{},
	}
	return ret
}

func (s *SetUPS) response(tpl interface{}) {
	tplByte, _ := json.Marshal(tpl)
	s.setTplByteData(tplByte)
}

func (s *SetUPS) newSelf(ctx *gdp.WebContext) (Pager, error) {
	target := &SetUPS{}
	target.ctx = ctx
	return target, nil
}

func init() {
	Register("ups-set", &SetUPS{})
}
