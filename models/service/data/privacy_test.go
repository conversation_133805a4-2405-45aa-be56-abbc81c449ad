package data

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewHISPrive(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewHISPrive ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := HISPrive{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*HISPrive
	}{
		{
			"TestNewHISPrive",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewHISPrive(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewHISPrive, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*HISPrive))
	}
	ag.Run()
}

func TestHISPrive_SetHISResponse(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}
	type args struct {
		uid    string
		status string
		target string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: SetHISResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		err := this.SetHISResponse(tt.args.uid, tt.args.status, tt.args.target)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHISPrive_SetHISResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHISPrive_BuildHISRequest(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}
	type args struct {
		uid string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildHISRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.BuildHISRequest(tt.args.uid)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISPrive_BuildHISRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(ral.HTTPRequest))
	}
	ag.Run()
}

func TestHISPrive_BuildHisSetRequest(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}
	type args struct {
		uid    string
		status string
		target string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildHisSetRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	rs := HisPriveRALResponse{}
	requestral := ral.HTTPRequest{
		Method:    "GET",
		Path:      "ups/api/addtips",
		Converter: ral.FORMConverter,
		Ctx:       ctx,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		{
			"TestHISPrive_BuildHisSetRequest",
			fields{
				baseData,
				rs,
			},
			args{
				"22233344",
				"test",
				"target",
			},
			Want{
				requestral,
				ut.ShouldNotBeNil,
			},
		},
	}

	for k, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.BuildHisSetRequest(tt.args.uid, tt.args.status, tt.args.target)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISPrive_BuildHisSetRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(ral.HTTPRequest))
	}
	ag.Run()
}

func TestHISPrive_ParseHISResponse(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}
	type args struct {
		response httpResp
		target   string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHISResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	es := HisPriveRALResponse{}
	hrs := httpResp{}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestHISPrive_ParseHISResponse",
			fields{
				baseData,
				es,
			},
			args{
				hrs,
				"target",
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		err := this.ParseHISResponse(tt.args.response, tt.args.target)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHISPrive_ParseHISResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHISPrive_ParseHISSetResponse(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}
	type args struct {
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHISSetResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	es := HisPriveRALResponse{}
	hrs := httpResp{}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestHISPrive_ParseHISSetResponse",
			fields{
				baseData,
				es,
			},
			args{
				hrs,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		err := this.ParseHISSetResponse(tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHISPrive_ParseHISSetResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHISPrive_setHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}
	type args struct {
		r HisPriveRALResponse
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		this.setHISTplData(tt.args.r)
	}
	ag.Run()
}

func TestHISPrive_GetHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisPriveRALResponse
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisPriveRALResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISPrive{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.GetHISTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISPrive_GetHISTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisPriveRALResponse))
	}
	ag.Run()
}
