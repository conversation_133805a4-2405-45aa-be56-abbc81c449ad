package data

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

const (
	AgentServer   = "agent_server"
	FromNormalSug = "1"
	FromAgentSug  = "2"
)

type AIAgent struct {
	BaseData
	TplData AgentSugResp
}

type AgentSugResp struct {
	Errno  string                   `json:"err_no"`
	Errmsg string                   `json:"err_msg"`
	LogID  string                   `json:"log_id"`
	Data   []map[string]interface{} `json:"data"`
}

type AgentServerResp struct {
	Code   int                      `json:"code"`
	Status string                   `json:"status"`
	RID    string                   `json:"rid"`
	Data   []map[string]interface{} `json:"data"`
}

func NewAIAgent(ctx *gdp.WebContext) *AIAgent {
	dataSrv := AIAgent{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// 获取推荐Response
func (r *AIAgent) GetAgentSugResponse(query string, from string) error {
	req, err := r.buildAgentSugRequest(query, from)
	if req == nil && err.Error() == "less than min length" {
		r.TplData = AgentSugResp{
			Errno: "0",
			LogID: r.ctx.GetLogID(),
		}
		return nil
	}
	common.DumpData(r.ctx, "2ai_agent", req)
	if err != nil {
		return err
	}
	resp := rawResp{}
	err = ral.Ral(AgentServer, *req, &resp, ral.RAWConverter)
	if err != nil {
		return err
	}

	var agentResp AgentServerResp
	err = json.Unmarshal(resp.Body, &agentResp)
	if err != nil {
		return err
	}
	return r.parseResponse(agentResp)
}

// 解析推荐Response
func (r *AIAgent) parseResponse(res AgentServerResp) error {
	// 请求成功透传下游内容
	r.TplData = AgentSugResp{
		Errno:  strconv.Itoa(res.Code),
		Errmsg: res.Status,
		Data:   res.Data,
		LogID:  r.ctx.GetLogID(),
	}
	return nil
}

// 构建推荐http请求
func (r *AIAgent) buildAgentSugRequest(query string, from string) (*ral.HTTPRequest, error) {
	querystring := fmt.Sprintf("agent/sug/list?sug_query=%s", url.QueryEscape(query))

	// 在推荐面板上的agent展示场景下，需要读取配置的max/min参数
	if from == FromAgentSug {
		// 推荐面板上的智能体推荐下线
		return nil, errors.New("less than min length")
		// common.GetAgentConf()
		// if utf8.RuneCountInString(query) < common.AgentConfig.QueryMinLen {
		// 	return nil, errors.New("less than min length")
		// }
		// if common.AgentConfig.AgentMinNum >= 0 &&
		// 	common.AgentConfig.AgentMaxNum >= common.AgentConfig.AgentMinNum {
		// 	querystring += fmt.Sprintf("&agent_min_num=%d&agent_max_num=%d", common.AgentConfig.AgentMinNum, common.AgentConfig.AgentMaxNum)
		// }
	}

	// 传递cookie
	cs := r.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}

	// 构建http请求
	req := &ral.HTTPRequest{
		Header: map[string][]string{
			"Cookie": {cstr},
		},
		Method:    "GET",
		Path:      querystring,
		Converter: ral.JSONConverter,
	}
	return req, nil
}
