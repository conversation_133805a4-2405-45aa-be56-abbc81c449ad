// nolint
package data

import (
	"errors"
	"net/http"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

func TestGetPrefetchRecResponse(t *testing.T) {
	// 构造 test redis
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()
	common.RedisClient = testRedis.Client()

	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(ral.Ral, func(serviceName string, request interface{}, response interface{}, ct ral.ConverterType) (err error) {
		mockRes := response.(*httpResp1)
		mockRes.Raw = []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":0,"result":[{"srcid":1000718,"displaydata_json":"{\"retno\":0,\"msg\":\"\",\"data\":{\"title\":\"test\",\"bold_title\":\"bold\",\"item\":[{\"text\":\"item1\",\"sa\":\"sa1\",\"rsf\":1,\"pos\":1}]}}"}]}`)
		return nil
	})

	tests := []struct {
		name          string
		mp            *MegafoilParams
		megafoilData  *ClickContentParams
		expectedError string
	}{
		{
			name:          "nil megafoilParams",
			mp:            nil,
			megafoilData:  nil,
			expectedError: "get megafoilParams is nil",
		},
		{
			name:          "empty megafoilData",
			mp:            &MegafoilParams{},
			megafoilData:  nil,
			expectedError: "get megafoil resData is empty",
		},
		{
			name:         "success",
			mp:           &MegafoilParams{Word: "test", CUID: "456", Plid: "789", CardPos: "1", SubPos: "2"},
			megafoilData: &ClickContentParams{Content: "test", URL: "test", Pos: int32(1), Title: "test", Srcid: uint32(12345), Type: int32(0)},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createGetWebContext()
			cgr := NewClickGetRec(ctx)

			err := cgr.GetPrefetchRecResponse(tt.mp, tt.megafoilData)
			if tt.expectedError != "" {
				assert.EqualError(t, err, tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, cgr.resData)
			}
		})
	}
}

func TestClickGetRec_buildRequest(t *testing.T) {
	tests := []struct {
		name      string
		setupFunc func() (*ClickGetRec, *MegafoilParams, *ClickContentParams)
		expectReq *ral.HTTPRequest
	}{
		{
			name: "basic request",
			setupFunc: func() (*ClickGetRec, *MegafoilParams, *ClickContentParams) {
				ctx := createGetWebContext()
				cgr := NewClickGetRec(ctx)
				mp := &MegafoilParams{
					Word: "test",
					CUID: "456",
				}
				megafoilData := &ClickContentParams{Content: "test", URL: "test", Pos: int32(1), Title: "test", Srcid: uint32(12345), Type: int32(0)}
				return cgr, mp, megafoilData
			},
			expectReq: &ral.HTTPRequest{
				Method:    "POST",
				Path:      reqRecPath,
				Converter: ral.JSONConverter,
				Ctx:       createGetWebContext(),
			},
		},
		{
			name: "empty megafoil data",
			setupFunc: func() (*ClickGetRec, *MegafoilParams, *ClickContentParams) {
				ctx := createGetWebContext()
				cgr := NewClickGetRec(ctx)
				mp := &MegafoilParams{
					Word: "test",
					CUID: "456",
				}
				megafoilData := &ClickContentParams{}
				return cgr, mp, megafoilData
			},
			expectReq: &ral.HTTPRequest{
				Method:    "POST",
				Path:      reqRecPath,
				Converter: ral.JSONConverter,
				Ctx:       createGetWebContext(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cgr, mp, megafoilData := tt.setupFunc()
			req := cgr.buildRequest(mp, megafoilData)

			assert.Equal(t, tt.expectReq.Method, req.Method)
			assert.Equal(t, tt.expectReq.Path, req.Path)
			assert.Equal(t, tt.expectReq.Converter, req.Converter)

			if tt.name == "basic request" {
				assert.Contains(t, req.Body, `"originquery":"test"`)
				assert.Contains(t, req.Body, `"title":"test"`)
			} else {
				assert.Contains(t, req.Body, `"title":""`)
			}
		})
	}
}

func TestGetRecParseResponse(t *testing.T) {
	// 构造 test redis
	testRedis := &redis.Testing{}
	testRedis.Start()
	defer testRedis.Stop()
	common.RedisClient = testRedis.Client()

	tests := []struct {
		name        string
		response    httpResp1
		mp          *MegafoilParams
		expectedErr error
	}{
		{
			name: "invalid json response",
			response: httpResp1{
				Raw: []byte("invalid json"),
			},
			expectedErr: errors.New(""),
		},
		{
			name: "result code error",
			response: httpResp1{
				Raw: []byte(`{"resultcode":1,"resultnum":0,"cs_log_id":0,"result":[]}`),
			},
			expectedErr: errors.New("result code error: 1"),
		},
		{
			name: "empty result",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":0,"cs_log_id":0,"result":[]}`),
			},
			expectedErr: ErrClickRec,
		},
		{
			name: "invalid display data json",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":0,"result":[{"srcid":1000718,"displaydata_json":"invalid"}]}`),
			},
			expectedErr: errors.New(""),
		},
		{
			name: "retno error",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":0,"result":[{"srcid":1000718,"displaydata_json":"{\"retno\":1,\"msg\":\"error\",\"data\":{}}"}]}`),
			},
			expectedErr: errors.New("get rec retno error: error"),
		},
		{
			name: "success with redis",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":0,"result":[{"srcid":1000718,"displaydata_json":"{\"retno\":0,\"msg\":\"\",\"data\":{\"title\":\"test\",\"bold_title\":\"bold\",\"item\":[{\"text\":\"item1\",\"sa\":\"sa1\",\"rsf\":1,\"pos\":1}]}}"}]}`),
			},
			mp: &MegafoilParams{
				Plid:    "plid",
				CardPos: "cardpos",
				SubPos:  "subpos",
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createGetWebContext()
			cgr := NewClickGetRec(ctx)
			cgr.resData = make(map[string]interface{})

			err := cgr.parseResponse(tt.mp, tt.response)
			if tt.expectedErr != nil {
				assert.NotNil(t, err)
				if tt.expectedErr.Error() != "" {
					assert.Contains(t, err.Error(), tt.expectedErr.Error())
				}
			} else {
				assert.Nil(t, err)
				assert.NotEmpty(t, cgr.resData)
			}
		})
	}
}

func TestClickGetRec_GetTplData(t *testing.T) {
	tests := []struct {
		name       string
		setupFunc  func() *ClickGetRec
		expectData map[string]interface{}
	}{
		{
			name: "empty resData",
			setupFunc: func() *ClickGetRec {
				ctx := createGetWebContext()
				cgr := NewClickGetRec(ctx)
				return cgr
			},
			expectData: make(map[string]interface{}),
		},
		{
			name: "non-empty resData",
			setupFunc: func() *ClickGetRec {
				ctx := createGetWebContext()
				cgr := NewClickGetRec(ctx)
				cgr.resData = map[string]interface{}{"key": "value"}
				return cgr
			},
			expectData: map[string]interface{}{"key": "value"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cgr := tt.setupFunc()
			assert.Equal(t, tt.expectData, cgr.GetTplData())
		})
	}
}

func TestNewClickGetRec(t *testing.T) {
	tests := []struct {
		name       string
		setupFunc  func() *gdp.WebContext
		expectData map[string]interface{}
	}{
		{
			name: "empty context",
			setupFunc: func() *gdp.WebContext {
				return createGetWebContext()
			},
			expectData: make(map[string]interface{}),
		},
		{
			name: "context with cookie",
			setupFunc: func() *gdp.WebContext {
				ctx := createGetWebContext()
				ctx.Request.AddCookie(&http.Cookie{Name: "test", Value: "value"})
				return ctx
			},
			expectData: make(map[string]interface{}),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setupFunc()
			cgr := NewClickGetRec(ctx)
			assert.Equal(t, ctx, cgr.ctx)
			assert.Equal(t, tt.expectData, cgr.resData)
		})
	}
}

func TestGetSid(t *testing.T) {
	tests := []struct {
		name     string
		cookie   string
		header   string
		expected []int32
	}{
		{
			name:     "empty",
			cookie:   "",
			expected: []int32{},
		},
		{
			name:     "from cookie",
			cookie:   "123_456_789",
			expected: []int32{123, 456, 789},
		},
		{
			name:     "invalid values",
			cookie:   "abc_def_123",
			expected: []int32{123},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createGetWebContext()
			if tt.cookie != "" {
				ctx.Request.AddCookie(&http.Cookie{Name: "H_WISE_SIDS", Value: tt.cookie})
			}
			cgr := NewClickGetRec(ctx)
			result := cgr.getSid()
			assert.Equal(t, tt.expected, result)
		})
	}
}
