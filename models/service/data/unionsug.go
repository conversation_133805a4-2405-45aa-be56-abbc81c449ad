//http://wiki.baidu.com/pages/viewpage.action?pageId=590990038
//http://wiki.baidu.com/pages/viewpage.action?pageId=576601410

package data

import (
	"encoding/json"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

// const OpenSug_SERVER = "opensug_public"
const UnionSugNew_SERVER = "searchbox_sug_new"

type UnionSugNew struct {
	BaseData
	opensugTplData Wisesugnew_Response
	CurResponse    resp
}

func NewUnionSugNew(ctx *gdp.WebContext) *UnionSugNew {
	dataSrv := UnionSugNew{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// /////////////////////////////////////////////////
// 打包、请求、解码于一体的single请求方法
func (this *UnionSugNew) GetUnionSugResponse(query string) error {
	requestrals := this.BuildUnionSugRequest(query)
	common.DumpData(this.ctx, "2unionsug", requestrals)

	var response = httpResp{}
	err := ral.Ral(UnionSugNew_SERVER, *requestrals, &response, ral.JSONConverter)

	//this.addNotice("ralres", string(response.Raw))
	if len(response.Raw) == 0 && response.Head.StatusCode == 200 {
		return nil
	}

	if err != nil {
		return err
	}
	return this.ParseOpenSugResponse(query, response)
}
func (this *UnionSugNew) BuildUnionSugRequest(query string) *ral.HTTPRequest {
	request := this.getRequest()
	adaption := this.getAdaption()

	values := url.Values{}
	values.Set("prod", "union_shoubai")
	values.Set("wd", query)
	values.Set("useat", "0") //直达号项目以下线 此处固定0
	pqy := "-"
	if v, ok := (*request)["pq"]; ok {
		pqy = v
	}
	values.Set("preqy", pqy)
	pqt := "-"
	if v, ok := (*request)["pt"]; ok {
		pqt = v
	}
	values.Set("preqt", pqt)
	os := "2"
	if (*adaption)["isiOS"] == "1" {
		os = "1"
	}
	values.Set("os", os)
	values.Set("net", common.GetNetwork((*request)["network"]))

	//wise请求固定参数
	values.Set("cfrom", "searchbox")
	values.Set("from", "wise_web") //不用wise_web可能不出直达
	values.Set("ie", "utf-8")
	values.Set("action", "opensearch")
	values.Set("sbox_cip", this.ctx.ClientIP())
	sboxuid := "-"
	if v, ok := (*this.getRequest())["uid"]; ok {
		sboxuid = v
	}
	values.Set("sbox_uid", sboxuid)
	//values.Set("sbox_branch", (*request)["osbranch"])
	//添加cs_log_id用于串联日志,2020.3.5
	values.Set("cs_log_id", this.getLogidStr())

	//透传sug_mode、pwd字段给sug server, 2020.05.26
	if v, ok := (*this.getRequest())["sug_mode"]; ok {
		values.Set("sugmode", v)
	}

	if v, ok := (*this.getRequest())["pwd"]; ok {
		values.Set("pwd", v)
	}

	if v, ok := (*request)["data"]; ok {
		m, _ := url.QueryUnescape(v)
		var reqdata map[string]string
		decodeErr := json.Unmarshal([]byte(m), &reqdata)
		swanHis := reqdata["swan_his"]
		decodeRet, err := base64.Deocde(swanHis, 0)
		if decodeErr == nil && err == nil {
			dataqueryStr, _ := url.QueryUnescape(string(decodeRet))
			values.Set("app_his", dataqueryStr)
		} else {
			this.addNotice("swanErr", err.Error())
		}
	}

	querystring := values.Encode()

	//避免encode bdid 此处再单独处理
	if v, ok := (*this.getRequest())["bdid"]; ok {
		arr := strings.Split(v, ":")
		querystring += "&baiduid=" + arr[0]
	} else {
		querystring += "&baiduid=-"
	}

	sids := this.ctx.GetString(constant.HWiseSIDs)

	//cookie再过滤 大cookie此处删除
	cs := this.getHTTPRequest().Cookies()

	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cv.Name == "H_WISE_SIDS" && sids != "" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	if sids != "" {
		cstr += " H_WISE_SIDS=" + sids
	}

	//基本的请求封装
	/*header := map[string]string{
		"Host":       "m.baidu.com",
		"User-Agent": this.getHttpRequest().UserAgent(),
		"Cookie":     cstr,
	}*/
	header := map[string][]string{
		//"Host": {"m.baidu.com"},
		"User-Agent": {this.getHTTPRequest().UserAgent()},
		"Cookie":     {cstr},
	}
	requestral := &ral.HTTPRequest{
		Header:    header,
		Method:    "GET",
		Path:      "singularity.api.sug.SugService/query?" + querystring,
		Converter: "form",
		LogID:     this.getLogidStr(),
		Ctx:       this.ctx,
	}
	/*requestInfo := goral.RequestInfo{
		PathInfo: "/sugrec?" + querystring,
		Header:   header,
		Server:   WISESUGNEW_SERVER,
		Logid:    this.getLogidStr(),
	}*/
	return requestral
}

func (this *UnionSugNew) ParseOpenSugResponse(query string, response httpResp) error {
	resData, err := this.decodeUnionSugResponse(response.Raw)
	if err != nil {
		if !this.isJsonpRet(string(response.Raw)) {
			return err
		}
		//被终止返回jsonp时不论是否有数据都一律当做查询失败处理
		//后续如有需求 此处不进行支持 让opensug来修正
		resData = Wisesugnew_Response{
			Q: query,
		}
	}
	this.setUnionsugTplData(resData)
	return nil
}

func (this *UnionSugNew) decodeUnionSugResponse(jsonstring []byte) (Wisesugnew_Response, error) {
	data := Wisesugnew_Response{}
	if string(jsonstring) == "{}" {
		return data, nil
	}
	err_ral := json.Unmarshal(jsonstring, &data)
	//err_ral = errors.New("sss")
	if err_ral != nil {
		return data, err_ral
	}
	//jsonstring

	/*if decodeErr := json.Unmarshal([]byte(jsonstring), &data); decodeErr != nil {
		return Wisesugnew_Response{}, decodeErr
	}*/
	return data, nil
}

// 粗暴的判断jsonp方法
func (this *UnionSugNew) isJsonpRet(body string) bool {
	return strings.Contains(body, "window.baidu.sug")
}

func (this *UnionSugNew) setUnionsugTplData(r Wisesugnew_Response) {
	this.opensugTplData = r
}

func (this *UnionSugNew) GetUnionsugTplData() Wisesugnew_Response {
	return this.opensugTplData
}
