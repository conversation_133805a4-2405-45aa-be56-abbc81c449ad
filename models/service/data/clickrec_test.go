package data

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"gopkg.in/go-playground/assert.v1"
	"icode.baidu.com/baidu/gdp/gdp/ral"
)

func TestCheckReqParams(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(checkBlackList, true)
	defer patch.Reset()
	tests := []struct {
		name       string
		reqParams  map[string]string
		wantParams *ClickRecParams
		wantErr    error
	}{
		{
			name:       "nil reqParams",
			reqParams:  nil,
			wantParams: &ClickRecParams{},
			wantErr:    errors.New("reqData is nil"),
		},
		{
			name:       "empty reqData",
			reqParams:  map[string]string{"data": ""},
			wantParams: &ClickRecParams{},
			wantErr:    errors.New("reqData is nil"),
		},
		{
			name:       "invalid json",
			reqParams:  map[string]string{"data": "invalid_json"},
			wantParams: &ClickRecParams{},
			wantErr:    errors.New("reqData json Unmarshal fail"),
		},
		{
			name:       "empty request_type",
			reqParams:  map[string]string{"data": `{"url": "http://example.com"}`},
			wantParams: &ClickRecParams{URL: "http://example.com"},
			wantErr:    errors.New("req request_type is nil"),
		},
		{
			name:       "empty url",
			reqParams:  map[string]string{"data": `{"request_type": "get_rec"}`},
			wantParams: &ClickRecParams{RequestType: "get_rec"},
			wantErr:    errors.New("get_rec req url is nil"),
		},

		{
			name:       "get_rec empty word",
			reqParams:  map[string]string{"data": `{"request_type": "get_rec", "url": "http://example.com"}`},
			wantParams: &ClickRecParams{RequestType: "get_rec", URL: "http://example.com"},
			wantErr:    errors.New("get_rec req word is nil"),
		},
		{
			name:       "get_rec empty paragraph",
			reqParams:  map[string]string{"data": `{"request_type": "get_rec", "url": "http://example.com", "word": "test"}`},
			wantParams: &ClickRecParams{RequestType: "get_rec", URL: "http://example.com", Word: "test"},
			wantErr:    errors.New("get_rec req paragraph is nil"),
		},
		{
			name:       "detect_rec success",
			reqParams:  map[string]string{"data": `{"request_type": "detect_rec", "url": "http://example.com", "rec_id": 123}`},
			wantParams: &ClickRecParams{RequestType: "detect_rec", URL: "http://example.com", RecID: int64(123), Word: "FIRST_GUIDE"},
			wantErr:    nil,
		},
		{
			name: "success get_rec",
			reqParams: map[string]string{"data": `{"request_type": "get_rec", "url": "http://example.com", 
			"word": "test", "paragraph": "test_paragraph", "rec_id": 123}`},
			wantParams: &ClickRecParams{RequestType: "get_rec", URL: "http://example.com", Word: "test", Paragraph: "test_paragraph", RecID: int64(123)},
			wantErr:    nil,
		},
		{
			name:       "illegal request_type",
			reqParams:  map[string]string{"data": `{"request_type": "illegal_type", "url": "http://example.com", "rec_id": 123}`},
			wantParams: &ClickRecParams{RequestType: "illegal_type", URL: "http://example.com", RecID: int64(123)},
			wantErr:    errors.New("req request_type illegal: illegal_type"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotParams, gotErr := checkReqParams(&tt.reqParams)
			if gotParams.RequestType != tt.wantParams.RequestType || gotParams.URL != tt.wantParams.URL ||
				gotParams.Word != tt.wantParams.Word || gotParams.Paragraph != tt.wantParams.Paragraph || gotParams.RecID != tt.wantParams.RecID {
				t.Errorf("checkReqParams() gotParams = %v, want %v", gotParams, tt.wantParams)
			}
			if gotErr != nil && gotErr.Error() != tt.wantErr.Error() {
				t.Errorf("checkReqParams() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
		})
	}
	// 重置recID
	recID = 0
}

func TestCreateLink(t *testing.T) {
	tests := []struct {
		word string
		sa   string
		want string
	}{
		{"test", "rsv", "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dtest%26sa%3Drsv"},
		{"hello", "world", "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dhello%26sa%3Dworld"},
		{"", "", "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3D%26sa%3D"},
	}

	for _, tt := range tests {
		recSA = tt.sa
		got := createLink(tt.word)
		if got != tt.want {
			t.Errorf("createLink(%q, %q) = %q, want %q", tt.word, tt.sa, got, tt.want)
		}
	}
	// 重置recID
	recID = 0
}

func TestBuildClickRecRequest(t *testing.T) {
	ctx := createGetWebContext()
	tests := []struct {
		name     string
		crp      *ClickRecParams
		wantBody map[string]interface{}
	}{
		{
			name: "detect_rec request",
			crp: &ClickRecParams{
				URL:         "http://example.com",
				Word:        "test_word",
				RequestType: "detect_rec",
			},
		},
		{
			name: "get_rec request with rec_id",
			crp: &ClickRecParams{
				URL:         "http://example.com",
				Word:        "test_word",
				Paragraph:   "test_paragraph",
				RequestType: getRec,
			},
		},
		{
			name: "get_rec request with rec_id",
			crp: &ClickRecParams{
				URL:         "http://example.com",
				Word:        "test_word",
				Paragraph:   "test_paragraph",
				RecID:       123,
				RequestType: getRec,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ClickRec{
				BaseData: BaseData{
					ctx: ctx,
				},
			}
			reqType = tt.crp.RequestType
			gotReq := c.BuildClickRecRequest(tt.crp)
			var gotBody map[string]interface{}
			_ = json.Unmarshal([]byte(gotReq.Body.(string)), &gotBody)
			assert.Equal(t, "http://example.com", gotBody["content_req_url"])
			assert.Equal(t, "test_word", gotBody["originquery"])
		})
	}
	// 重置recID
	recID = 0
}

func TestParseGetRecResponse(t *testing.T) {
	ctx := createGetWebContext()
	tests := []struct {
		name    string
		rsData  map[string]interface{}
		wantErr error
		wantRes map[string]interface{}
	}{
		{
			name: "no_rec",
			rsData: map[string]interface{}{
				"has_rec": "0",
			},
			wantErr: ErrClickRec,
			wantRes: map[string]interface{}{
				"has_rec":        "0",
				"rec_info":       []map[string]interface{}{},
				"pannel_title":   "",
				"rec_sa":         "",
				"highlight_mode": "",
				"rec_id":         int64(0),
			},
		},
		{
			name: "has_rec_with_rec_sa",
			rsData: map[string]interface{}{
				"has_rec":        "1",
				"rec_sa":         "test_sa",
				"highlight_mode": "1",
				"rec_query":      []interface{}{"test_query"},
			},
			wantErr: nil,
			wantRes: map[string]interface{}{
				"has_rec":        "1",
				"rec_sa":         "test_sa",
				"highlight_mode": "1",
				"rec_info": []map[string]interface{}{
					{
						"query":      "test_query",
						"uri_search": "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dtest_query%26sa%3Dtest_sa",
					},
				},
				"rec_id":       recID,
				"pannel_title": pannelTitle,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ClickRec{
				BaseData: BaseData{
					ctx: ctx,
				},
				resData: make(map[string]interface{}),
			}
			err := c.parseGetRecResponse(tt.rsData)
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, tt.wantRes, c.resData)
		})
	}
	// 重置recID
	recID = 0
}

func TestParseDetectRecResponse(t *testing.T) {
	ctx := createGetWebContext()
	tests := []struct {
		name    string
		rsData  map[string]interface{}
		wantErr error
		wantRes map[string]interface{}
	}{
		{
			name: "no_rec",
			rsData: map[string]interface{}{
				"has_rec": "0",
			},
			wantErr: ErrClickRec,
			wantRes: map[string]interface{}{
				"has_rec": "0",
				"key":     "",
				"context": "",
				"rec_id":  int64(0),
			},
		},
		{
			name: "has_rec_with_match_key_and_context",
			rsData: map[string]interface{}{
				"has_rec":   "1",
				"match_key": "test_key",
				"context":   "test_context",
			},
			wantErr: nil,
			wantRes: map[string]interface{}{
				"has_rec": "1",
				"key":     "test_key",
				"context": "test_context",
				"rec_id":  int64(0),
			},
		},
		{
			name: "has_rec_without_match_key_and_context",
			rsData: map[string]interface{}{
				"has_rec": "1",
			},
			wantErr: nil,
			wantRes: map[string]interface{}{
				"has_rec": "1",
				"rec_id":  int64(0),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ClickRec{
				BaseData: BaseData{
					ctx: ctx,
				},
				resData: make(map[string]interface{}),
			}
			err := c.parseDetectRecResponse(tt.rsData)
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, tt.wantRes, c.resData)
		})
	}
	// 重置recID
	recID = 0
}

func TestClickRec_GetTplData(t *testing.T) {
	tests := []struct {
		name    string
		resData map[string]interface{}
		want    map[string]interface{}
	}{
		{
			name:    "empty map",
			resData: map[string]interface{}{},
			want:    map[string]interface{}{},
		},
		{
			name:    "non-empty map",
			resData: map[string]interface{}{"key1": "value1", "key2": 2},
			want:    map[string]interface{}{"key1": "value1", "key2": 2},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cr := &ClickRec{
				resData: tt.resData,
			}
			got := cr.GetTplData()
			if !compareMaps(got, tt.want) {
				t.Errorf("ClickRec.GetTplData() = %v, want %v", got, tt.want)
			}
		})
	}
	// 重置recID
	recID = 0
}

func compareMaps(a, b map[string]interface{}) bool {
	if len(a) != len(b) {
		return false
	}
	for k, v := range a {
		if b[k] != v {
			return false
		}
	}
	return true
}

func TestNewClickRec(t *testing.T) {
	ctx := createGetWebCtx()
	cr := NewClickRec(ctx)
	if cr.ctx != ctx {
		t.Errorf("NewClickRec() ctx = %v, want %v", cr.ctx, ctx)
	}
}

func TestParseResponse(t *testing.T) {
	ctx := createGetWebContext()
	tests := []struct {
		name     string
		response httpResp1
		wantErr  error
		wantRes  map[string]interface{}
		reqType  string
	}{
		{
			name: "successful_detect_rec_response",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":123456789,
				"result":[{"srcid":1000545,"displaydata_json":
				"{\"rs\":{\"has_rec\":\"1\",\"match_key\":\"test_key\",\"context\":\"test_context\"}}"}]}`),
			},
			wantErr: nil,
			wantRes: map[string]interface{}{
				"has_rec": "1",
				"key":     "test_key",
				"context": "test_context",
				"rec_id":  int64(0),
			},
			reqType: detectRec,
		},
		{
			name: "successful_get_rec_response",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":123456789,
				"result":[{"srcid":1000545,"displaydata_json":
				"{\"rs\":{\"has_rec\":\"1\",\"rec_sa\":\"test_sa\",\"highlight_mode\":\"1\",\"rec_query\":[\"query1\",\"query2\"]}}"}]}`),
			},
			wantErr: nil,
			wantRes: map[string]interface{}{
				"has_rec":        "1",
				"rec_info":       []map[string]interface{}{{"query": "query1", "uri_search": 
				"baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dquery1%26sa%3Dtest_sa"}, {"query": "query2", "uri_search": 
				"baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3Dquery2%26sa%3Dtest_sa"}},
				"pannel_title":   "你可能想搜",
				"rec_sa":         "test_sa",
				"highlight_mode": "1",
				"rec_id":         int64(0),
			},
			reqType: getRec,
		},
		{
			name: "resultcode_error",
			response: httpResp1{
				Raw: []byte(`{"resultcode":1,"resultnum":1,"cs_log_id":123456789,
				"result":[{"srcid":1000545,"displaydata_json":
				"{\"rs\":{\"has_rec\":1,\"match_key\":\"test_key\",\"context\":\"test_context\"}}"}]}`),
			},
			wantErr: errors.New("ResultCode error: 1"),
			wantRes: nil,
			reqType: detectRec,
		},
		{
			name: "no_result",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":0,"cs_log_id":123456789,"result":[]}`),
			},
			wantErr: ErrClickRec,
			wantRes: nil,
			reqType: detectRec,
		},
		{
			name: "data_rs_nil",
			response: httpResp1{
				Raw: []byte(`{"resultcode":0,"resultnum":1,"cs_log_id":123456789,"result":[{"srcid":1000545,"displaydata_json":"{\"rs\":null}"}]}`),
			},
			wantErr: errors.New("data Rs is nil"),
			wantRes: nil,
			reqType: detectRec,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ClickRec{
				BaseData: BaseData{
					ctx: ctx,
				},
				resData: make(map[string]interface{}),
			}
			reqType = tt.reqType
			err := c.parseResponse(tt.response)
			assert.Equal(t, tt.wantErr, err)
			if tt.wantRes != nil {
				assert.Equal(t, tt.wantRes, c.resData)
			}
		})
	}
	// 重置recID
	recID = 0
}

func TestGetClickRecResponse(t *testing.T) {
	ctx := createGetWebContext()
	patch := gomonkey.ApplyFuncReturn(checkBlackList, true)
	patch.ApplyFuncReturn(ral.Ral, nil)
	patch.ApplyFuncReturn(json.Unmarshal, nil)
	crp := ClickRecParams{
		URL:         "https://test.com",
		Word:        "test_word",
		RecID:       123456789,
		Paragraph:   "test_paragraph",
		RequestType: detectRec,
	}
	patch.ApplyFuncReturn(checkReqParams, &crp, nil)
	defer patch.Reset()
	tests := []struct {
		name      string
		reqParams map[string]string
		wantErr   error
		wantRes   map[string]interface{}
		reqType   string
	}{
		{
			name: "successful_detect_rec_response",
			reqParams: map[string]string{
				"data": `{"url":"https://test.com","request_type":"detect_rec"}`,
			},
			wantErr: ErrClickRec,
			wantRes: map[string]interface{}{
				"has_rec": "1",
				"key":     "test_key",
				"context": "test_context",
			},
			reqType: detectRec,
		},
		{
			name: "successful_get_rec_response",
			reqParams: map[string]string{
				"data": `{"url":"https://test.com","request_type":"get_rec"}`,
			},
			wantErr: ErrClickRec,
			wantRes: map[string]interface{}{
				"has_rec": "1",
				"key":     "test_key",
				"context": "test_context",
			},
			reqType: getRec,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewClickRec(ctx)
			reqType = tt.reqType
			err := c.GetClickRecResponse(&tt.reqParams)
			assert.Equal(t, tt.wantErr, err)
		})
	}
}

func TestRemoveEmojis(t *testing.T) {
	tests := []struct {
		name string
		text string
		want string
	}{
		{
			name: "no emoji",
			text: "hello world",
			want: "hello world",
		},
		{
			name: "with emoji",
			text: "hello 😊 world",
			want: "hello  world",
		},
		{
			name: "only emoji",
			text: "😊😂🤣",
			want: "",
		},
		{
			name: "mixed emoji and text",
			text: "测试📱🌏🚅🇫🇯🌍🌎🌏🇨🇳文字🚀",
			want: "测试文字",
		},
		{
			name: "empty string",
			text: "",
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveEmojis(tt.text); got != tt.want {
				t.Errorf("RemoveEmojis() = %v, want %v", got, tt.want)
			}
		})
	}
}
