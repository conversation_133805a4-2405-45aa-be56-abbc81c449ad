package data

import (
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"testing"

	"bou.ke/monkey"
	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

var (
	httpRspBody *httptest.ResponseRecorder
)

func createGetWebCtx() *gdp.WebContext {
	httpRspBody1 = httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	req, _ := http.NewRequest("GET", `/suggest?`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "1.1.1.1"),
	}
	return wc
}

func TestAIAgent_buildAgentSugRequest(t *testing.T) {
	type fields struct {
		BaseData BaseData
		TplData  AgentSugResp
	}
	type args struct {
		query string
		from  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ral.HTTPRequest
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			fields: fields{
				BaseData: BaseData{
					ctx: createGetWebContext(),
				},
			},
			args: args{
				query: "你好呀",
				from:  "2",
			},
			want: nil,
			wantErr: true,
		},
		{
			name: "test2",
			fields: fields{
				BaseData: BaseData{
					ctx: createGetWebContext(),
				},
			},
			args: args{
				query: "你好",
				from:  "2",
			},
			want:    nil,
			wantErr: true,
		},
	}

	p := monkey.Patch(common.GetAgentConf, func() {
		common.AgentConfig = common.AgentConf{
			AgentMinNum: 2,
			AgentMaxNum: 5,
			QueryMinLen: 3,
		}
	})
	defer p.Unpatch()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &AIAgent{
				BaseData: tt.fields.BaseData,
				TplData:  tt.fields.TplData,
			}
			got, err := r.buildAgentSugRequest(tt.args.query, tt.args.from)
			if (err != nil) != tt.wantErr {
				t.Errorf("AIAgent.buildAgentSugRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AIAgent.buildAgentSugRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAIAgent_GetAgentSugResponse(t *testing.T) {
	type fields struct {
		BaseData BaseData
		TplData  AgentSugResp
	}
	type args struct {
		query string
		from  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				query: "n",
				from:  "2",
			},
			fields: fields{
				BaseData: BaseData{
					ctx: createGetWebContext(),
				},
				TplData: AgentSugResp{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &AIAgent{
				BaseData: tt.fields.BaseData,
				TplData:  tt.fields.TplData,
			}
			if err := r.GetAgentSugResponse(tt.args.query, tt.args.from); (err != nil) != tt.wantErr {
				t.Errorf("AIAgent.GetAgentSugResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
