package data

import (
	"encoding/json"
	"hash/crc32"
	"net/url"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/gdp/ral"
)

const IP_POI_SERVER = "ip2poi"

type IP2PoiResponse struct {
	ErrNo   int           `json:"ErrNo"`
	Msg     string        `json:"Msg"`
	Cost    string        `json:"Cost"`
	Payload payloadStruct `json:"Payload"`
}

type payloadStruct struct {
	Poi PoiStruct `json:"Poi"`
}

type PoiStruct struct {
	Country            string `json:"Country"`
	Province           string `json:"Province"`
	City               string `json:"City"`
	County             string `json:"County"`
	ISP                string `json:"ISP"`
	CountryConfidence  int    `json:"CountryConfidence"`
	ProvinceConfidence int    `json:"ProvinceConfidence"`
	CityConfidence     int    `json:"CityConfidence"`
	CountyConfidence   int    `json:"CountyConfidence"`
	ISPConfidence      int    `json:"ISPConfidence"`
}

func IP2Poi(userIP string) (address map[string]string, ralMsg map[string]string) {
	address = map[string]string{
		"from":     "",
		"country":  "",
		"province": "",
		"city":     "",
	}
	ralMsg = map[string]string{
		"errMsg":   "",
		"ralErrNo": "",
	}
	if userIP == "" {
		ralMsg["errMsg"] = "userIp is empty"
		return
	}

	ak := "GaxLZVKgk0URpgMiPP42yuem6AWGyNvN"
	sk := "xbKKae8DsYbgimZCz6nxEXSwQ08ePIqF"
	ts := strconv.FormatInt(int64(time.Now().Unix()), 10)
	tk := strconv.FormatInt(int64(crc32.ChecksumIEEE([]byte(ak+ts+sk))), 10)
	payload := map[string]string{
		"IP": userIP,
	}
	payload_json, _ := json.Marshal(payload)

	query := map[string]string{
		"module":  "go-sug",
		"from":    "sug",
		"ak":      ak,
		"ts":      ts,
		"tk":      tk,
		"payload": string(payload_json),
	}

	body := map[string]string{
		"payload": string(payload_json),
	}
	query_string := url.Values{}
	for k, v := range query {
		query_string.Add(k, v)
	}

	//    header := map[string][]string{
	//        "Host": {"sandbox.baidu-int.com"},
	//    }

	requestral := &ral.HTTPRequest{
		//        Header:     header,
		Method:    "POST",
		Path:      "ip2poi?" + query_string.Encode(),
		Body:      body,
		Converter: ral.FORMConverter,
	}

	var response = httpResp1{}
	err := ral.Ral(IP_POI_SERVER, *requestral, &response, ral.FORMConverter)

	if err != nil {
		ralMsg["errMsg"] = err.Error()
		return
	}

	var response_parse IP2PoiResponse
	if json.Unmarshal(response.Raw, &response_parse) != nil {
		ralMsg["errMsg"] = "json.unmarshal response fail"
		return
	}

	if response_parse.ErrNo != 0 {
		ralMsg["ralErrNo"] = strconv.Itoa(response_parse.ErrNo)
		return
	}
	if response_parse.Payload.Poi.Country == "None" || response_parse.Payload.Poi.Province == "None" || response_parse.Payload.Poi.City == "None" {
		ralMsg["errMsg"] = string(response.Raw)
		return
	}

	address["from"] = "IP"
	address["country"] = response_parse.Payload.Poi.Country
	address["province"] = response_parse.Payload.Poi.Province
	address["city"] = response_parse.Payload.Poi.City
	return
}
