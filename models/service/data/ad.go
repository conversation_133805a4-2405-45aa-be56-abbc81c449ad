package data

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/axgle/mahonia"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

const (
	// AFD服务名称.
	AFDServer = "afd_server"
)

type Ad struct {
	JumpURL    string `json:"jump_url"`
	ExtraParam string `json:"extra_param"`
	ShowURL    string `json:"show_url"`
	ClickURL   string `json:"click_url"`
}

type AdGuess struct {
	Ad    Ad     `json:"ad"`
	Text  string `json:"text"`
	Sa    string `json:"sa"`
	Tag   string `json:"tag"`
	Icon  string `json:"icon"`
	Color int    `json:"color"`
}

// 用于解析广告material中JSON数据的结构体
type AdMaterial struct {
	Query          string      `json:"query"`
	JumpURL        string      `json:"jumpurl"`
	Name           string      `json:"name"`
	Plugins        []AdPlugin  `json:"plugins"`
	ShowURL        string      `json:"show_url"`
	ClickURL       string      `json:"click_url"`
	LpType         string      `json:"lp_type"`
	LpDemo         string      `json:"lp_demo"`
	Sa             string      `json:"sa"`
	Global         interface{} `json:"global"`
	Title          string      `json:"title"`
	VshowMonitor   int         `json:"vshow_monitor"`
	FloorIndexFlag int         `json:"floor_index_flag"`
}

type AdPlugin struct {
	Val      string `json:"val"`
	Key      string `json:"key"`
	Selected bool   `json:"selected"`
}

func ExtractRecAdMaterial(adResp AFDResp) []AdGuess {
	adGuesses := make([]AdGuess, 0, 3)

	for _, adItem := range adResp.Data.Ad {
		for _, adInfo := range adItem.AdInfo {
			for _, material := range adInfo.Material {
				if material.Info != "" {
					var adMaterial AdMaterial
					if err := json.Unmarshal([]byte(material.Info), &adMaterial); err == nil {
						// 优先使用 Title 字段，如果 Title 为空，则尝试使用 Query 字段
						text := adMaterial.Title
						if text == "" {
							text = adMaterial.Query
						}

						// 如果有文本内容，则添加广告
						if text != "" {
							adGuesses = append(adGuesses, AdGuess{
								Ad: Ad{
									JumpURL:    adMaterial.JumpURL,
									ExtraParam: getAFDReportedParam(adInfo.Extra),
									ShowURL:    adMaterial.ShowURL,
									ClickURL:   adMaterial.ClickURL,
								},
								Text:  text,
								Sa:    adMaterial.Sa,
								Tag:   "",
								Icon:  "",
								Color: 0,
							})
						}
					}
				}
			}
		}
	}

	return adGuesses
}

// AFD 反作弊数据结构
type AFD struct {
	BaseData
	TplData AFDResp
}

// AFD接口响应结构
type AFDResp struct {
	Errno  string  `json:"err_no"`
	Errmsg string  `json:"err_msg"`
	LogID  string  `json:"log_id"`
	Data   AFDData `json:"data"`
}

// AFD数据结构
type AFDData struct {
	Ad    []AFDAdItem `json:"ad"`
	ReqID string      `json:"reqId"`
}

// AFD广告项结构
type AFDAdItem struct {
	LocCode string      `json:"locCode"`
	AdInfo  []AFDAdInfo `json:"adInfo"`
}

// AFD广告信息结构
type AFDAdInfo struct {
	Material   []AFDMaterial `json:"material"`
	ID         string        `json:"id"`
	Extra      []AFDExtra    `json:"extra"`
	Advisible  int           `json:"advisible"`
	ProductID  int           `json:"productId"`
	ModuleType int           `json:"moduleType"`
	ClientType int           `json:"clientType"`
	ExtInfo    string        `json:"ext_info"`
}

// AFD素材结构
type AFDMaterial struct {
	ID   string `json:"id"`
	Info string `json:"info"`
}

// AFD额外信息结构
type AFDExtra struct {
	K string `json:"k"`
	V string `json:"v"`
}

func getAFDReportedParam(afdExtras []AFDExtra) string {
	for _, extra := range afdExtras {
		if extra.K == "extraParam" {
			return extra.V
		}
	}
	return ""
}

// AFD服务器响应结构
type AFDServerResp struct {
	Errno  int    `json:"errno"`
	Errmsg string `json:"errmsg"`
	Res    struct {
		ImTimeSign int         `json:"imTimeSign"`
		ReqID      string      `json:"reqId"`
		Ad         []AFDAdItem `json:"ad"`
	} `json:"res"`
}

// 创建新的AFD实例
func NewAFD(ctx *gdp.WebContext) *AFD {
	dataSrv := AFD{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// 获取AFD响应
func (r *AFD) GetAFDResponse(query string, params map[string]string, adaption map[string]string, userPrivacyInfo common.UserPrivacyInfo) error {
	// 直接使用参数构建请求
	req, err := r.buildAFDRequest(query, params, adaption, userPrivacyInfo)
	common.DumpData(r.ctx, "afd_entry", req)
	if err != nil {
		return err
	}

	resp := rawResp{}
	err = ral.Ral(AFDServer, *req, &resp, ral.RAWConverter)
	if err != nil {
		return err
	}

	var afdResp AFDServerResp
	err = json.Unmarshal(resp.Body, &afdResp)
	if err != nil {
		return err
	}
	r.addNotice("afd_success", "1")
	return r.parseResponse(afdResp)
}

// 解析AFD响应
func (r *AFD) parseResponse(res AFDServerResp) error {
	// 请求成功透传下游内容
	r.TplData = AFDResp{
		Errno:  strconv.Itoa(res.Errno),
		Errmsg: res.Errmsg,
		LogID:  r.ctx.GetLogID(),
		Data: AFDData{
			Ad:    res.Res.Ad,
			ReqID: res.Res.ReqID,
		},
	}
	return nil
}

// 构建AFD请求
func (r *AFD) buildAFDRequest(
	_ string,
	params map[string]string,
	adaption map[string]string,
	userPrivacyInfo common.UserPrivacyInfo,
) (*ral.HTTPRequest, error) {
	// 传递cookie
	cs := r.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	dataquery := params["data"]
	if dataquery == "" {
		return nil, errors.New("request body: data is empty")
	}
	// 解析 dataquery 中的 guessData，提取 sa 前缀为 igh_12345_pinpai 的猜搜词
	var guessWord string
	if dataquery != "" {
		var reqdata map[string]interface{}
		if err := json.Unmarshal([]byte(dataquery), &reqdata); err == nil {
			// 参考hisRec.go中的解析方式
			if guessdata, ok := reqdata["guessdata"].(string); ok && guessdata != "" {
				decodeRet, _ := base64.Deocde(guessdata, 0)
				var guessDataList []map[string]interface{}
				if err := json.Unmarshal([]byte(decodeRet), &guessDataList); err == nil {
					// 寻找符合条件的猜搜词
					for _, item := range guessDataList {
						if sa, ok := item["sa"].(string); ok && strings.HasPrefix(sa, "igh_12345_pinpai") {
							if word, ok := item["text"].(string); ok && word != "" {
								guessWord = word
								break
							}
						}
					}
				}
			}
		}
	}
	// 将参数转换为表单编码格式
	values := url.Values{}

	values.Add("qe", mahonia.NewEncoder("gbk").ConvertString(guessWord))
	// 直接硬编码设置默认值
	if params["osbranch"] == "i0" {
		values.Add("pid", "1744193300495") // iOS默认
		values.Add("apna", "com.baidu.BaiduMobile")
	} else if params["osbranch"] == "a0" {
		values.Add("pid", "1744193300926") // Android默认
		values.Add("apna", "com.baidu.searchbox")
	}
	values.Add("ac", "3")        // 默认广告数量
	values.Add("idc", env.IDC()) // 添加IDC

	// 直接使用传入的UserPrivacyInfo结构体
	// 添加位置信息
	if userPrivacyInfo.CookieLocation.Merca.MerX != 0 && userPrivacyInfo.CookieLocation.Merca.MerY != 0 {
		values.Add("coot", "bd09mc")
		values.Add("lgt", fmt.Sprintf("%f", userPrivacyInfo.CookieLocation.Merca.MerX))
		values.Add("lat", fmt.Sprintf("%f", userPrivacyInfo.CookieLocation.Merca.MerY))
	}
	ext, _ := json.Marshal([]map[string]string{
		{"k": "mac", "v": userPrivacyInfo.MacAddr},
		{"k": "encoded_ua_new", "v": url.QueryEscape(r.getHTTPRequest().UserAgent())},
		{"k": "oaid_v", "v": userPrivacyInfo.OAID},
		{"k": "honor_oaid", "v": userPrivacyInfo.HornorOAID},
	})
	values.Add("ext", string(ext))
	values.Add("ct", "2")                     // 默认客户端类型APP=2
	values.Add("is_https", "1")               // 默认https
	values.Add("from", params["from"])        // from
	values.Add("cfrom", params["cfrom"])      // cfrom
	values.Add("ver", adaption["bd_version"]) // app版本
	puidDecode, _ := base64.Deocde(params["puid"], 0)
	values.Add("uid", string(puidDecode)) // uid
	bdid, _ := r.ctx.Cookie("BAIDUID_BFESS")
	if bdid != "" {
		values.Add("bdid", bdid)
	}
	values.Add("cuid", params["uid"])
	values.Add("mod", userPrivacyInfo.Model)
	values.Add("ov", userPrivacyInfo.OsVer)
	values.Add("ua", params["ua"])
	values.Add("fmt", "json") // 默认返回json
	values.Add("ip", r.ctx.ClientIP())
	values.Add("eid", r.getEid())
	values.Add("imei", userPrivacyInfo.IMEI)
	values.Add("idfa", userPrivacyInfo.IDFA)
	values.Add("st", "2") // 默认sourceType=2
	values.Add("nt", "1") // 默认流量类型1
	values.Add("fc", "1") // 默认刷新次数1

	// 扩展字段映射
	extMap := make(map[string]string)

	// 添加扩展参数
	if len(extMap) > 0 {
		if extBytes, err := json.Marshal(extMap); err == nil {
			values.Set("ext", string(extBytes))
		}
	}

	// 构建http请求
	req := &ral.HTTPRequest{
		Header: map[string][]string{
			"Cookie":       {cstr},
			"Host":         {"afd.baidu.com"},
			"Content-Type": {"application/x-www-form-urlencoded"},
		},
		Method:    http.MethodPost,
		Path:      "/afd/entry",
		Converter: ral.FORMConverter,
		Body:      values,
		LogID:     r.ctx.GetLogID(),
		Ctx:       r.ctx,
	}
	common.DumpData(r.ctx, "afd_entry", req)
	r.addNotice("afd_success", "0")
	return req, nil
}

// 不区分大小写的字符串包含判断
func containsIgnoreCase(s, substr string) bool {
	s = strings.ToLower(s)
	substr = strings.ToLower(substr)
	return strings.Contains(s, substr)
}

// 获取cookie中的手百 sid转换为 AFD 需要的 eid（用逗号分割的 sid）
func (r *AFD) getEid() string {
	cookie, err := r.ctx.Cookie("CS_W_SIDS")
	if err != nil {
		return ""
	}
	ssid := strings.Split(cookie, "-")

	return strings.Join(ssid, ",")
}
