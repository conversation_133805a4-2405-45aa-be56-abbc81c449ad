package data

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"strconv"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type BaseData struct {
	ctx *gdp.WebContext
}

type NamedHTTPReq struct {
	Name string
	*ral.HTTPRequest
}

type NamedHTTPResp struct {
	Name     string
	respBody string
}

func (b *BaseData) getRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := b.ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (b *BaseData) getAdaption() *map[string]string {
	var adaption map[string]string
	if val, exists := b.ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	return &adaption
}

func (b *BaseData) getHTTPRequest() *http.Request {
	return b.ctx.Context.Request
}

func (b *BaseData) addNotice(key, value string) {
	b.ctx.AddNotice(key, value)
}

func (b *BaseData) getLogidStr() string {

	return b.ctx.GetLogID()
}

func (b *BaseData) getLogid() uint64 {

	logidstr := b.ctx.GetLogID()
	logid64, _ := strconv.Atoi(logidstr)
	return uint64(logid64)

}

// 获取csrf修复需要的token参数 http://wiki.baidu.com/pages/viewpage.action?pageId=885836521
func (b *BaseData) getHisToken() string {
	bduss, err := b.ctx.Cookie("BDUSS")
	if err != nil {
		bduss = ""
		b.addNotice("bduss_empty", err.Error())
	}

	return fmt.Sprintf("%x", md5.Sum([]byte(bduss+"hWAumdbNQBXT22FDNYRzFKIAJIpLsMJj")))
}
