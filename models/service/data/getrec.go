package data

import (
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type ClickGetRec struct {
	BaseData
	resData map[string]interface{}
}

type ClickGetRecResponse struct {
	ResultCode int32  `json:"resultcode"`
	ResultNum  uint32 `json:"resultnum"`
	CsLogID    uint64 `json:"cs_log_id"`
	Result     []struct {
		SrcID           int32  `json:"srcid"`
		DisplayDataJSON string `json:"displaydata_json"`
	} `json:"result"`
}

type RecDisplayData struct {
	RetNo int    `json:"retno"`
	Msg   string `json:"msg"`
	Data  struct {
		Title     string `json:"title"`
		BoldTitle string `json:"bold_title"`
		Item      []struct {
			Text string `json:"text"`
			SA   string `json:"sa"`
			RSF  int32  `json:"rsf"`
			POS  int32  `json:"pos"`
		} `json:"item"`
	} `json:"data"`
}

const (
	recCaller         = "touchrec_gosug"
	reqRecPath        = "singularity.api.us_simple.UsSimpleService/query"
	recToken   uint32 = 24353528
	recSrcID   int32  = 1000718
	recServer         = "click_rec"
	sourceSa          = "search_content_gen"
)

func (c *ClickGetRec) GetPrefetchRecResponse(mp *MegafoilParams, megafoilData *ClickContentParams) error {
	if mp == nil {
		return errors.New("get megafoilParams is nil")
	}
	if megafoilData == nil {
		return errors.New("get megafoil resData is empty")
	}
	// 封装数据，构建用于请求的HTTPRequest
	reqGetRec := c.buildRequest(mp, megafoilData)
	common.DumpData(c.ctx, "2GetPrefetchRec", reqGetRec)
	// 接收请求结果的数据集
	response := httpResp1{}
	// 发起请求
	if err := ral.Ral(recServer, *reqGetRec, &response, ral.JSONConverter); err != nil {
		return err
	}
	return c.parseResponse(mp, response)
}

func (c *ClickGetRec) buildRequest(mp *MegafoilParams, megafoilData *ClickContentParams) *ral.HTTPRequest {
	body := map[string]interface{}{
		"originquery": mp.Word,
		"token":       recToken,
		"caller":      recCaller,
		"cuid":        mp.CUID,
		"sid":         c.getSid(),
		"srcarr": []map[string]interface{}{
			{
				"srcid": recSrcID,
			},
		},
		"source_for_sa": sourceSa,
		"cs_log_id":     c.getLogid(),
		"click_content": *megafoilData,
	}

	bodyFormByte, _ := json.Marshal(body)
	req := &ral.HTTPRequest{
		Method:    "POST",
		Path:      reqRecPath,
		Body:      string(bodyFormByte),
		Converter: ral.JSONConverter,
		LogID:     c.getLogidStr(),
		Ctx:       c.ctx,
	}
	return req
}

func (c *ClickGetRec) parseResponse(mp *MegafoilParams, response httpResp1) error {
	// 解析获得的json格式请求结果，放在ralRespnse中
	ralRespnse := ClickGetRecResponse{}
	if err := json.Unmarshal([]byte(response.Raw), &ralRespnse); err != nil {
		return err
	}
	if ralRespnse.ResultCode != 0 {
		c.addNotice("getRecCode", "-1")
		return errors.New("result code error: " + strconv.FormatInt(int64(ralRespnse.ResultCode), 10))
	}
	if len(ralRespnse.Result) == 0 {
		return ErrClickRec
	}
	for _, res := range ralRespnse.Result {
		if res.SrcID != recSrcID {
			continue
		}
		data := RecDisplayData{}
		if err := json.Unmarshal([]byte(res.DisplayDataJSON), &data); err != nil {
			return err
		}
		if data.RetNo != 0 {
			c.addNotice("getRecNo", "-1")
			return errors.New("get rec retno error: " + data.Msg)
		}
		if len(data.Data.Title) > 0 {
			c.resData["guideText"] = data.Data.Title
		}
		if len(data.Data.BoldTitle) > 0 {
			c.resData["guideHiglight"] = data.Data.BoldTitle
		}
		if len(data.Data.Item) > 0 {
			c.resData["list"] = data.Data.Item
		}
	}
	// 存在redis中
	if len(c.resData) > 0 {
		resByte, jErr := json.Marshal(c.resData)
		if jErr != nil {
			return errors.New("json resData for redis error: " + jErr.Error())
		}
		client := common.RedisClient
		if client == nil {
			return errors.New("redis client is nil")
		}
		// 获取redis key
		curKey, keyErr := common.GetClickRecKey(mp.Plid, mp.CardPos, mp.SubPos)
		if keyErr != nil {
			return errors.New("get click rec key error: " + keyErr.Error())
		}
		// 获取redis key  10分过期
		_, clientErr := client.Set(c.ctx.StdContext(), curKey, string(resByte), time.Minute*time.Duration(10)).Result()
		if clientErr != nil {
			return errors.New("getrec set data error: " + clientErr.Error())
		}
	}
	return nil
}

func (c *ClickGetRec) GetTplData() map[string]interface{} {
	return c.resData
}

func NewClickGetRec(ctx *gdp.WebContext) *ClickGetRec {
	c := ClickGetRec{}
	c.ctx = ctx
	c.resData = make(map[string]interface{})
	return &c
}

// 获取sid
func (c *ClickGetRec) getSid() []int32 {
	sidstr := []string{}
	bdsid, err := c.ctx.Cookie("H_WISE_SIDS")
	if err == nil && bdsid != "" {
		sidstr = strings.Split(bdsid, "_")
	}

	sids := c.ctx.GetString(constant.HWiseSIDs)
	if sids != "" {
		sidstr = strings.Split(sids, "_")
	}

	sid := []int32{}
	for _, str := range sidstr {
		strInt, err := strconv.Atoi(str)
		if err == nil {
			sid = append(sid, int32(strInt))
		}
	}
	return sid
}
