package data

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/pbrpc"
	"icode.baidu.com/baidu/searchbox/go-suggest/idl/megafoil"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type Megafoil struct {
	BaseData
	resData ClickContentParams
}

type MegafoilParams struct {
	Word     string `json:"word"`
	Plid     string `json:"plid"`
	CUID     string `json:"cuid"`
	SubPos   string `json:"subpos"`
	CardPos  string `json:"card_pos"`
	Abstract string `json:"abstract"`
}

type ClickContentParams struct {
	Content string `json:"content"`
	URL     string `json:"url"`
	Pos     int32  `json:"pos"`
	Title   string `json:"title"`
	Srcid   uint32 `json:"srcid"`
	Type    int32  `json:"type"`
}

const (
	serverName    = "megafoil"
	megafoilToken = "go-sug"
	userName      = "go-sug"
	product       = "SUG"
)

func (m *Megafoil) GetMegafoilResponse(reqParams *map[string]string) (*MegafoilParams, error) {
	// 请求必要参数校验
	mp, reqErr := m.checkReqParams(reqParams)
	if reqErr != nil {
		return mp, reqErr
	}
	// 构建请求
	request := m.BuildRequest(mp)
	common.DumpData(m.ctx, "Megafoil", request)
	ralRes := &pbrpc.RalResponse{Data: &megafoil.MegafoilResponseV2{}}
	// 发起请求
	if err := ral.RAL(m.ctx.StdContext(), serverName, request, ralRes); err != nil {
		return mp, err
	}
	// 处理响应
	return mp, m.parseResponse(mp, ralRes)
}

func (m *Megafoil) BuildRequest(mp *MegafoilParams) *pbrpc.RalRequest {
	dataType := uint32(megafoil.DataType_disp)
	requestKeys := []*megafoil.RequestKeys{
		{
			RequestType: megafoil.RequestType_megafoil_data.Enum(),
			DataType:    &dataType,
			SubKey: []*megafoil.SubKeyType{
				{
					KeyType: []byte("QID"),
					Key:     []byte(mp.Plid),
				},
			},
		},
	}
	userInfo := &megafoil.UserInfo{
		UserName: []byte(userName),
		Token:    []byte(megafoilToken),
		Product:  []byte(product),
	}

	reqData := &megafoil.MegafoilRequestV2{
		Keys:     requestKeys,
		LogId:    []byte(mp.Plid),
		UserInfo: userInfo,
	}

	ralReq := pbrpc.NewRalRequest("MegafoilService", "get_v2", reqData, nil)
	return ralReq
}

func (m *Megafoil) parseResponse(mp *MegafoilParams, res *pbrpc.RalResponse) error {
	if res.ErrorCode != 0 {
		return errors.New(res.ErrorText)
	}
	respData, ok := res.Data.(*megafoil.MegafoilResponseV2)
	if !ok {
		return errors.New("get MegafoilResponseV2 failed")
	}
	if len(respData.Response) == 0 {
		return errors.New("respData Response empty")
	}
	megafoilDataList := respData.Response[0].MegafoilData
	if len(megafoilDataList) == 0 {
		return errors.New("megafoilDataList empty")
	}
	megafoilData := megafoilDataList[0]
	if megafoilData.DisplayData == nil || len(megafoilData.DisplayData.DisplayUnits) == 0 {
		return errors.New("displayData empty")
	}
	displayData := megafoilData.DisplayData.DisplayUnits[0]
	if len(displayData.Items) == 0 {
		return errors.New("displayData Items empty")
	}
	itemData := displayData.Items
	cardPosInt, _ := strconv.Atoi(mp.CardPos)
	if cardPosInt <= 0 || cardPosInt > len(itemData) {
		return fmt.Errorf("cardPos:%v invalid", mp.CardPos)
	}
	// 构造请求策略数据
	itemVal := itemData[cardPosInt-1]
	mr := ClickContentParams{}
	if itemVal.Srcid != nil {
		mr.Srcid = *itemVal.Srcid
	}
	// subPos > 0 表示点击子链，默认为0
	subPosInt, _ := strconv.Atoi(mp.SubPos)
	if subPosInt > 0 {
		if len(itemVal.SubResult) == 0 {
			return errors.New("subResult empty")
		}
		subResult := itemVal.SubResult[0]
		if len(subResult.SubLink) == 0 {
			return errors.New("subLink empty")
		}
		if subPosInt > len(subResult.SubLink) {
			return fmt.Errorf("subPos:%v invalid", mp.SubPos)
		}
		// 组装结果
		subItemVal := subResult.SubLink[subPosInt-1]
		if subItemVal == nil {
			return errors.New("subItemVal empty")
		}
		mr.Pos = int32(subPosInt)
		if targetURL := subItemVal.GetUrl(); len(targetURL) > 0 {
			mr.URL = string(targetURL)
		}
		if title := subItemVal.GetTitle(); len(title) > 0 {
			mr.Title = string(title)
		}
		// 升级proto增加子链摘要字段后处理
		if abstract := subItemVal.GetAbstract(); len(abstract) > 0 {
			mr.Content = string(abstract)
			mr.Type = 0
		} else {
			mr.Content = mp.Abstract
			mr.Type = 1
		}
	} else {
		// 点击整卡结果
		mr.Pos = int32(cardPosInt)
		offset := itemVal.GetOffsetInfo()
		if offset == nil {
			return errors.New("offset info empty")
		}
		if targetURL := offset.GetTargetUrl(); len(targetURL) > 0 {
			mr.URL = string(targetURL)
		}
		if title := offset.GetTitle(); len(title) > 0 {
			mr.Title = string(title)
		}
		if summary := offset.GetSummary(); len(summary) > 0 {
			mr.Content = string(summary)
			mr.Type = 0
		} else {
			mr.Content = mp.Abstract
			mr.Type = 1
		}
	}
	m.resData = mr
	return nil
}

func (m *Megafoil) GetTplData() *ClickContentParams {
	return &m.resData
}

func (m *Megafoil) checkReqParams(reqParams *map[string]string) (*MegafoilParams, error) {
	// 参数校验
	mp := MegafoilParams{}
	reqData := (*reqParams)["data"]
	if reqData == "" {
		return &mp, errors.New("reqData is nil")
	}
	jsonErr := json.Unmarshal([]byte(reqData), &mp)
	if jsonErr != nil {
		return &mp, errors.New("reqData json Unmarshal fail")
	}
	// 校验必须字段,subpos可选字段
	if mp.Plid == "" {
		return &mp, errors.New("req Plid is nil")
	}
	if mp.CardPos == "" {
		return &mp, errors.New("req CardPos is nil")
	}
	if mp.Word == "" {
		return &mp, errors.New("req Word is nil")
	}
	if uid := (*reqParams)["uid"]; len(uid) > 0 {
		mp.CUID = (*reqParams)["uid"]
	} else if uid := (*reqParams)["_uid"]; len(uid) > 0 {
		mp.CUID = (*reqParams)["_uid"]
	}
	return &mp, nil
}

func NewMegafoil(ctx *gdp.WebContext) *Megafoil {
	m := Megafoil{}
	m.ctx = ctx
	return &m
}
