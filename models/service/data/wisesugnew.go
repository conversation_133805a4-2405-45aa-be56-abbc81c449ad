// http://wiki.baidu.com/pages/viewpage.action?pageId=590990038
// http://wiki.baidu.com/pages/viewpage.action?pageId=576601410
package data

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const WISESUGNEW_SERVER = "searchbox_sug_new"

type Wisesugnew struct {
	BaseData
	wisesugTplData    Wisesugnew_Response
	CurResponse       resp
	NeedPredictSug    bool
	wisesugRalResp    chRawHTTPResp
	predictsugTplData PresearchType_Response
}

type httpResp struct {
	Head    ral.HTTPHead
	Body    interface{}
	Raw     []byte
	TagHead ral.HTTPHead `ral:"head"`
	TagBody interface{}  `ral:"body"`
	TagRaw  []byte       `ral:"raw"`
}

type rawResp struct {
	Head ral.HTTPHead
	Body []byte
}

type chRawHTTPResp struct {
	Name string
	Resp rawResp
	Err  error
}

type ChRawHTTPResp chRawHTTPResp

type resp struct {
	Head http.Header `json:"header"`
	Body string      `json:"body"`
	Err  error       `json:"err"`
	Name string      `json:"name"`
}

// ral下游返回的结构
type Wisesugnew_Response struct {
	Q         string            `json:"q"`           // 请求命中与检索使用的query
	P         bool              `json:"p"`           // 暂时不知道是什么
	G         []Wisesugnew_Type `json:"g"`           // 主SUG列表
	Slid      string            `json:"slid"`        // 透传slid
	GMot      []Wisesugnew_Type `json:"g_mot"`       // sug回框推荐词
	GMotStyle int               `json:"g_mot_style"` // sug特殊样式，1:猜你想搜，2:搜索历史
	GMotTitle string            `json:"g_mot_title"` // sug中显示的title名称
	// OriLid    string            `json:"ori_lid"`     // 透传多轮对话session_id
}

type PresearchType_Response struct {
	Presearch string `json:"presearch"` // 是否预取
}

type Wisesugnew_Type struct {
	Type string                 `json:"type"`
	Sa   string                 `json:"sa"`
	Q    string                 `json:"q"`
	Info map[string]interface{} `json:"info"`
}

func NewWiseSugNew(ctx *gdp.WebContext) *Wisesugnew {
	dataSrv := Wisesugnew{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// /////////////////////////////////////////////////
// 打包、请求、解码于一体的single请求方法
func (this *Wisesugnew) GetWiseSugResponse(query string) (error, string) {
	var mutiRequestInfo map[string]*ral.HTTPRequest
	request := this.getRequest()
	premode := (*request)["premode"]

	if this.NeedPredictSug == true {
		this.ctx.TimerStart("toPretch")
		mutiRequestInfo = map[string]*ral.HTTPRequest{
			"sug":          this.BuildWiseSugRequest(query),
			"presearchsug": this.BuildPreSearchSugRequest(query),
		}
	} else {
		mutiRequestInfo = map[string]*ral.HTTPRequest{
			"sug": this.BuildWiseSugRequest(query),
		}
	}

	// premode=force时，不请求sug后端
	if premode == "force" {
		delete(mutiRequestInfo, "sug")
	}

	num := len(mutiRequestInfo)
	mutiResponseChan := make(chan chRawHTTPResp, num)
	this.MutiRequest(mutiRequestInfo, mutiResponseChan)

	// 使用for从channel取数据
	for chData := range mutiResponseChan {
		// 此处需要后续优化一下 不要以bns的名字取 最好自定义tag。。。
		switch chData.Name {
		case "sug":
			this.wisesugRalResp = chData
			// this.addNotice("ralres", string(chData.Resp.Body))
		case "presearchsug":
			presearchsugResp, err := this.ParsePresearchSugData(chData)
			if err != nil {
				this.addNotice("presearchsugralParseErr", err.Error())
			}
			this.addNotice("toPretch", fmt.Sprintf("%.2f", this.ctx.TimerSince("toPretch").Seconds()*1000))
			this.predictsugTplData = presearchsugResp
			// this.addNotice("presearchsugralres", string(chData.Resp.Body))
		default:
			continue // 暂时无default处理逻辑
		}
	}

	// 大搜对某些敏感词屏蔽例如:包含 89 的query，此时返回的StatusCode为200，内容长度为0。这种case返回sug列表为空
	if this.wisesugRalResp.Err != nil {
		return this.wisesugRalResp.Err, string(this.wisesugRalResp.Resp.Body)
	}

	// 没请求sug时，不用解析sug数据
	if _, ok := mutiRequestInfo["sug"]; !ok {
		return nil, ""
	}

	return this.ParseWiseSugResponse(query, this.wisesugRalResp.Resp), string(this.wisesugRalResp.Resp.Body)
}

var requestral *ral.HTTPRequest

func (this *Wisesugnew) BuildWiseSugRequest(query string) *ral.HTTPRequest {
	request := this.getRequest()
	adaption := this.getAdaption()

	values := url.Values{}
	values.Set("prod", "baiduapp_sug")
	values.Set("wd", query)
	values.Set("useat", "0") // 直达号项目以下线 此处固定0
	pqy := "-"
	if v, ok := (*request)["pq"]; ok {
		pqy = v
	}
	values.Set("preqy", pqy)
	pqt := "-"
	if v, ok := (*request)["pt"]; ok {
		pqt = v
	}
	values.Set("preqt", pqt)
	os := "2"
	if (*adaption)["isiOS"] == "1" {
		os = "1"
	}

	// sug 需要lid,所以增加
	if v, ok := (*request)["lid"]; ok {
		values.Set("lid", v)
	}

	isChatSearch := common.IsChatSearch(request)
	if isChatSearch {
		values.Set("is_simple_sug", "1")
		// 对话式搜索需要传入ori_lid
		if v, ok := (*request)["ori_lid"]; ok {
			values.Set("ori_lid", v)
		}
	}

	values.Set("os", os)
	values.Set("net", common.GetNetwork((*request)["network"]))

	// wise请求固定参数
	values.Set("cfrom", "searchbox")
	values.Set("from", "wise_web") // 不用wise_web可能不出直达
	values.Set("ie", "utf-8")
	values.Set("action", "opensearch")
	values.Set("sbox_cip", this.ctx.ClientIP())
	sboxuid := "-"
	if v, ok := (*this.getRequest())["uid"]; ok {
		sboxuid = v
	}
	values.Set("sbox_uid", sboxuid)
	values.Set("sbox_branch", (*request)["osbranch"])
	values.Set("sugid", (*request)["sugid"])
	values.Set("device_time", (*request)["device_time"])
	// 添加cs_log_id用于串联日志,2020.3.5
	values.Set("cs_log_id", this.getLogidStr())

	// 透传sug_mode、pwd字段给sug server, 2020.05.26
	if v, ok := (*request)["sug_mode"]; ok {
		values.Set("sugmode", v)
	}

	if v, ok := (*request)["pwd"]; ok {
		values.Set("pwd", v)
	}

	if v, ok := (*request)["data"]; ok {
		m, _ := url.QueryUnescape(v)

		var reqdata map[string]string
		decodeErr := json.Unmarshal([]byte(m), &reqdata)
		if decodeErr != nil {
			this.addNotice("jsonDecodeError", decodeErr.Error())
		} else {
			// swan_data
			swanHis := reqdata["swan_his"]
			if swanHis != "" {
				decodeRet, err := base64.Deocde(swanHis, 0)
				if err == nil {
					dataqueryStr, _ := url.QueryUnescape(string(decodeRet))
					values.Set("app_his", dataqueryStr)
				} else {
					this.addNotice("swanDecodeErr", err.Error())
				}
			}

			// hisdata
			hisData := reqdata["his"]
			if hisData != "" {
				decodeRet, err := base64.Deocde(hisData, 0)
				if err == nil {
					hisDataStr := strings.Replace(string(decodeRet), "\n", "", -2)
					values.Set("his", hisDataStr)
				} else {
					this.addNotice("hisDataDecodeErr", err.Error())
				}
			}

			seDup := reqdata["seDup"]
			values.Set("se_dup", seDup)
		}
	}

	// 未登录或错误时，传"0"
	uid, ok := (*request)[constant.SESSION_UID]
	if !ok {
		uid = "0"
	}
	values.Set("uid", uid)

	hot_launch, ok := (*request)["hot_launch"]
	if ok {
		values.Set("hot_launch", hot_launch)
	}

	// 是否打开无痕模式
	incognito_mode := this.ctx.GetHeader("incognito-mode")
	if incognito_mode != "" {
		values.Set("incognito_mode", incognito_mode)
	}

	// 是否打开隐私模式
	private_mode := this.ctx.GetHeader("private-mode")
	if private_mode != "" {
		values.Set("private_mode", private_mode)
	}

	// 个性化开关
	personal_switch := this.ctx.GetHeader("personal-switch")
	if personal_switch != "" {
		values.Set("personal_switch", personal_switch)
	}

	querystring := values.Encode()
	// 避免encode bdid 此处再单独处理
	if v, ok := (*this.getRequest())["bdid"]; ok {
		arr := strings.Split(v, ":")
		querystring += "&baiduid=" + arr[0]
	} else {
		querystring += "&baiduid=-"
	}

	sids := this.ctx.GetString("HWiseSids")

	// cookie再过滤 大cookie此处删除
	cs := this.getHTTPRequest().Cookies()
	cstr := ""

	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		} else if cv.Name == "g_attr" {
			// 如果g_attr不可用，删除
			if len(common.ParseGAttr(cv.Value)) == 0 {
				continue
			}
		}
		if cv.Name == "H_WISE_SIDS" && sids != "" {
			continue
		}

		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	if sids != "" {
		cstr += " H_WISE_SIDS=" + sids
	}

	header := map[string][]string{
		"User-Agent": {this.getHTTPRequest().UserAgent()},
		"Cookie":     {cstr},
	}

	requestral = &ral.HTTPRequest{
		Header:    header,
		Method:    "GET",
		Path:      "singularity.api.sug.SugService/query?" + querystring,
		Converter: "string",
		LogID:     this.getLogidStr(),
		Ctx:       this.ctx,
	}

	common.DumpData(this.ctx, "2sugService.sug", requestral)
	return requestral
}

func (this *Wisesugnew) BuildPreSearchSugRequest(query string) *ral.HTTPRequest {
	query_prefix := "json=1&p=3&from=wise_web&prod=yq&ie=utf-8&pre=2&sp=9"
	var retriveKeys = []string{
		"wise_csor", "sugid", "rsv_sug", "rsv_pq", "oq", "t_samp",
		"pre_input_num", "delete_num", "pre_tlist", "isdelete",
	}
	values := url.Values{}
	request := this.getRequest()
	for _, field := range retriveKeys {
		if v, ok := (*request)[field]; ok {
			values.Set(field, v)
		}
	}

	if (*request)["premode"] == "force" {
		values.Set("premode", "force")
	}

	sids := this.ctx.GetString(constant.HWiseSIDs)

	// cookie再过滤 大cookie此处删除
	cs := this.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cv.Name == "H_WISE_SIDS" && sids != "" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	if sids != "" {
		cstr += " H_WISE_SIDS=" + sids
	}
	header := map[string][]string{
		// "Host":       {"m.baidu.com"},
		"User-Agent": {this.getHTTPRequest().UserAgent()},
		"Cookie":     {cstr},
	}

	querystring_sufix := values.Encode()
	querystring := fmt.Sprintf("singularity.api.sug.SugService/query?%s&wd=%s&%s", query_prefix, query, querystring_sufix)
	// this.addNotice("predictquerystring", querystring)
	requestral := &ral.HTTPRequest{
		Header:    header,
		Method:    "GET",
		Path:      querystring,
		Converter: "string",
		LogID:     this.getLogidStr(),
		Ctx:       this.ctx,
	}

	common.DumpData(this.ctx, "2sugService.presearch", requestral)
	return requestral
}

func (sug *Wisesugnew) MutiRequest(rs map[string]*ral.HTTPRequest, ch chan chRawHTTPResp) {
	var wg sync.WaitGroup
	for key, r := range rs {
		wg.Add(1)
		go func(k string, req *ral.HTTPRequest, c chan chRawHTTPResp) {
			defer wg.Done()
			cResp := chRawHTTPResp{}
			cResp.Name = k
			response := rawResp{}
			var err error
			err = ral.Ral(WISESUGNEW_SERVER, *req, &response, ral.RAWConverter)

			cResp.Err = err
			cResp.Resp = response
			c <- cResp
		}(key, r, ch)
	}
	wg.Wait()
	close(ch)
}

func (this *Wisesugnew) ParseWiseSugResponse(query string, response rawResp) error {
	resData, err := this.decodeWiseSugResponse(response.Body)
	if err != nil {
		if !this.isJsonpRet(string(response.Body)) {
			return err
		}
		// 被终止返回jsonp时不论是否有数据都一律当做查询失败处理
		// 后续如有需求 此处不进行支持 让opensug来修正
		resData = Wisesugnew_Response{
			Q: query,
		}
	}
	this.setWisesugTplData(resData)
	return nil
}

func (sug *Wisesugnew) ParsePresearchSugData(response chRawHTTPResp) (PresearchType_Response, error) {
	data := PresearchType_Response{}
	if response.Err != nil {
		return data, response.Err
	}

	err := json.Unmarshal(response.Resp.Body, &data)

	if err != nil {
		return data, err
	}
	sug.predictsugTplData = data
	return data, nil
}

func (this *Wisesugnew) decodeWiseSugResponse(jsonstring []byte) (Wisesugnew_Response, error) {
	data := Wisesugnew_Response{}
	err_ral := json.Unmarshal(jsonstring, &data)

	// err_ral = errors.New("sss")
	if err_ral != nil {
		return data, err_ral
	}
	// jsonstring

	/*if decodeErr := json.Unmarshal([]byte(jsonstring), &data); decodeErr != nil {
		return Wisesugnew_Response{}, decodeErr
	}*/
	return data, nil
}

// 粗暴的判断jsonp方法
func (this *Wisesugnew) isJsonpRet(body string) bool {
	return strings.Contains(body, "window.baidu.sug")
}

func (this *Wisesugnew) setWisesugTplData(r Wisesugnew_Response) {
	this.wisesugTplData = r
}

func (this *Wisesugnew) GetWisesugTplData() Wisesugnew_Response {
	return this.wisesugTplData
}

func (this *Wisesugnew) GetPresearchSugData() PresearchType_Response {
	return this.predictsugTplData
}
