package data

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"time"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/utils/runtimeinfo"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const REC_SERVER = "sug_his_rec"

const (
	BackendRecVideo = "rec_video"
	BackendRec      = "rec"
)

const (
	NewUserHisSrcid = 1000535
)

type Rec struct {
	BaseData
	hisTplData map[string]TypeDispaly
}

type DataParms struct {
	Hisdata            []string
	HisSearchData      []map[string]interface{}
	Boxdata            []map[string]interface{}
	Guessdata          []map[string]interface{}
	Guessfeedbackquery []string
	Showquerys         []string
	Eventtype          string
	Eventbox           string
	Eventquery         string
	Eventchannel       string
	Eventfrom          string
	Puid               string
	Sids               []int
	Guessshowrownum    float64
	PrivateMode        int
	ClientName         string
	ContentInfo        map[string]interface{}
	UnderBox           []map[string]interface{}
	EventHisFrom       string
	BoxShowQuery       string
	TodayFirst         int
	DisplayWidth       int
	RecVideoInfo       int
	ColdLaunchTime     string
	VidRec             []map[string]string // content_info中传入的视频推荐词
	VidCtl             map[string]int      // 视频推荐词控制信息
	VidRecSa           string              // 视频推荐词sa
	NeedNewUserHis     bool
	PrivacyString      string // 用户隐私字段
	RecMaxReturn       int
	TextCount          int //设备可展现最大中文字符数
	IntelligentHisType int // 智能模式历史类型
}

const (
	RecPageHisFirstPage      = "first_page"
	RecPageHisTopicSubscribe = "subscribe_page"
)

func NewRec(ctx *gdp.WebContext) *Rec {
	dataSrv := Rec{}
	dataSrv.ctx = ctx
	return &dataSrv
}

type httpResp1 struct {
	Head ral.HTTPHead
	Raw  []byte
}

func (r *Rec) GetRecResponse(reqparams, adpterprams *map[string]string, dataParams DataParms) error {
	requests, _ := r.BuildRequest(reqparams, adpterprams, dataParams)
	multiResponseChan := make(chan ChRawHTTPResp, len(requests))
	r.MultiRequest(requests, multiResponseChan)
	return r.ParseHISResponse(multiResponseChan)
}

func (r *Rec) BuildRequest(reqparams, adpterprams *map[string]string, dataParams DataParms) ([]NamedHTTPReq, error) {
	// 荣耀白牌暂时屏蔽搜索发现数据
	if (*reqparams)[constant.RongyaoBaipai] == "1" && common.TwoInOneConfMem.CloseGuess == 1 {
		r.addNotice("cancel_request_rec", "1")
		return nil, nil
	}

	requests := r.buildRecRequest(reqparams, adpterprams, dataParams)
	return requests, nil
	// r.addNotice("videoInfoBranch", strconv.Itoa(GetVideoInfoBranch(dataParams)))
}

// nolint:gocyclo
func (r *Rec) buildRecRequest(reparams, adptparams *map[string]string, dataParams DataParms) []NamedHTTPReq {
	os := "iphone"

	if (*adptparams)["isAndroid"] == "1" {
		os = "android"
	}
	// trick逻辑，百度视频版rec推荐的版本号请求时候修改到版本11.26
	// todo 删掉@maolinghua  @rd:lifubo pm:lumingtai
	osbranch := (*reparams)["osbranch"]
	bd_version := (*adptparams)["bd_version"]
	if osbranch == "i6" || osbranch == "a6" {
		bd_version = "*********"
	}
	if (osbranch == "i3" || osbranch == "a2" || osbranch == "i7" || osbranch == "a7") && (*reparams)["fv"] != "" {
		bd_version = (*reparams)["fv"]
	}
	realOsbranch := (*reparams)["osbranch"]
	if (*reparams)["realOsbranch"] != "" {
		realOsbranch = (*reparams)["realOsbranch"]
	}

	cip := (*reparams)["cip"]
	// 打点观察，线上是否存在cip参数
	if cip == "" {
		cip = r.ctx.ClientIP()
	} else {
		r.addNotice("rec_cip", cip)
	}

	ip := net.ParseIP(cip)
	useripv6 := ""
	if ip.To16() != nil {
		useripv6 = ip.To16().String()
	}

	clientIP := r.ctx.ClientIP()
	address, errMsg := IP2Poi(clientIP)
	errMsgByte, err := json.Marshal(errMsg)
	if err == nil {
		r.addNotice("ip2poi_errMsg", string(errMsgByte))
	}

	wisePm, errck := r.ctx.Cookie("WISE_HIS_PM")
	if errck != nil {
		wisePm = "1"
	}
	bdsid, _ := r.ctx.Cookie("CS_W_SIDS")
	if bdsid == "" {
		bdsid = r.ctx.Request.Form.Get("sid")
	}
	var brand string
	var osVer string
	var model string
	var cookieLocation common.CookieLocation
	userPrivacyInfo, _ := r.ctx.Get("userPrivacyInfo")
	if userPrivacyInfo != nil {
		userPrivacyInfo, _ := userPrivacyInfo.(*common.UserPrivacyInfo)
		if os == "iphone" {
			brand = "apple"
		} else {
			brand = userPrivacyInfo.Manu
		}
		osVer = userPrivacyInfo.OsVer
		model = userPrivacyInfo.Model
		cookieLocation = userPrivacyInfo.CookieLocation
	}

	network := (*reparams)["network"]
	fontSize, _ := r.ctx.Cookie("fontsize")
	fontSizeFloat, _ := strconv.ParseFloat(fontSize, 64)

	incidental := map[string]interface{}{
		"sid":                  bdsid,
		"puid":                 dataParams.Puid,
		"os":                   os,
		"osbranch":             osbranch,
		"app_version":          bd_version,
		"event_type":           dataParams.Eventtype,
		"event_query":          dataParams.Eventquery,
		"event_box":            dataParams.Eventbox,
		"event_channel":        dataParams.Eventchannel,
		"event_from":           dataParams.Eventfrom,
		"hisdata":              dataParams.Hisdata,
		"his_search_data":      dataParams.HisSearchData,
		"boxdata":              dataParams.Boxdata,
		"guessdata":            dataParams.Guessdata,
		"showquerys":           dataParams.Showquerys,
		"guess_show_rownum":    dataParams.Guessshowrownum,
		"guess_feedback_query": dataParams.Guessfeedbackquery,
		"content_info":         dataParams.ContentInfo,
		"under_box":            dataParams.UnderBox,
		"address":              address,
		"wise_pm":              wisePm,
		"event_his_from":       dataParams.EventHisFrom,
		"box_show_query":       dataParams.BoxShowQuery,
		"today_first":          dataParams.TodayFirst,
		"display_width":        dataParams.DisplayWidth,
		"font_size":            fontSizeFloat,
		"text_count":           dataParams.TextCount,
		"intelligent_his_type": dataParams.IntelligentHisType,
		"mobile_phone_model":   model,
		"mobile_phone_version": osVer,
		"mobile_phone_network": network,
		"mobile_phone_brand":   brand,
		"cookie_location":      cookieLocation,
	}

	sids := r.ctx.GetString(constant.HWiseSIDs)
	if sids != "" {
		incidental["search_sids"] = sids
	}

	gAttr, err := r.ctx.Cookie("g_attr")
	if err == nil && gAttr != "" {
		parsedValue := common.ParseGAttr(gAttr)
		// 新增或付费渠道调起用户
		if r.isSelectedUser(parsedValue) {
			incidental["is_callup"] = 1
		}
		incidental["g_attr"] = gAttr
	}

	if version.Compare("12.12", bd_version, "<=") {
		incidental["private_mode"] = dataParams.PrivateMode
	}
	bdenvmode, err := r.ctx.Cookie("bdenvmode")
	if err == nil {
		incidental["bdenvmode"] = bdenvmode
	}

	if dataParams.ColdLaunchTime != "" {
		incidental["cold_launch_time"] = dataParams.ColdLaunchTime
	}

	// 是否打开无痕模式
	if mode := r.ctx.GetHeader("incognito-mode"); mode != "" {
		if v, err := strconv.Atoi(mode); err == nil {
			incidental["incognito_mode"] = v
			r.addNotice("incognito_mode", mode)
		}
	}
	// 个性化推荐开关
	if mode := r.ctx.GetHeader("personal-switch"); mode != "" {
		if v, err := strconv.Atoi(mode); err == nil {
			incidental["person_rec"] = v
			r.addNotice("person_rec", mode)
		}
	}
	// 智能化开关
	if mode := r.ctx.GetHeader("user-intelligent-mode"); mode != "" {
		if v, err := strconv.Atoi(mode); err == nil {
			incidental["user_intelligent_mode"] = v
		}
	}

	// 本次仅接入安卓端
	if osbranch == "a0" {
		// 主版：端上下发隐私字段时，使用端上传的字段；否则请求隐私mapping服务，获取隐私字段
		if dataParams.PrivacyString != "" {
			decodeOaid, err := common.NewBase32Decode(dataParams.PrivacyString)
			if err != nil {
				r.addNotice("decodeOaidErr", fmt.Sprintf("encode oaid: %s, err: %s", dataParams.PrivacyString, err.Error()))
			} else {
				incidental["oaid"] = string(decodeOaid)
			}
		} else {
			if userPrivacyInfo, ok := r.ctx.Get("userPrivacyInfo"); ok {
				userPrivacyInfo, _ := userPrivacyInfo.(*common.UserPrivacyInfo)
				incidental["oaid"] = userPrivacyInfo.OAID
			}
		}
	}
	bodyByte, _ := json.Marshal(incidental)
	token, srcid, caller := handleParams(osbranch, bd_version, os)
	srcArr := r.createSrcArr(reparams, srcid, bodyByte, dataParams, bd_version, realOsbranch, address)

	// 12.3版本解决rec不同请求时机获取box和guess问题，减少rec服务的开销
	// 方案wiki: http://wiki.baidu.com/pages/viewpage.action?pageId=1267910053
	// 该 Map 本意是为了解决需要并发RAL 请求下游 Aries
	// 通过请求 body 里的 srcArr 数组新增资源号的方式也可以在一次请求中返回多个资源，但是最与 502 和503请求（空框和猜搜）架构不同意一个 RAL 请求两个资源号
	// 如果需要新增并发的 RAL 请求请在下方的 eventTypeScridMap对应的场景中新增新的资源号。
	eventTypeScridMap := map[string][]int{
		"change": {28386},
		// 怀疑这里有历史遗留问题，之前的代码可能存在依赖 bug 运行的可能, 原本这里没有 exchange 但是客户端猜你喜欢的刷新按钮会传递 exchange
		"exchange":                 {28386},
		"search":                   {1000503},
		"hissug_refresh":           {1000502},
		"home_auto_change_hint":    {1000502}, // 首页预置词自动轮播，轮播到快没词的时候发起的rec请求
		"homepagerefresh":          {1000502},
		"home_refresh_change_hint": {1000502}, // 首页刷新触发预置词请求（轮播实验下）
		"videobar":                 {1000502},
		"feedback":                 {1000503},
		"hissug":                   {1000503},
		"hissug_with_box":          {1000502, 1000503},
		"cpage_error":              {1000503}, // c页面错误页
		"cpage_block":              {1000503}, // 政府指令屏蔽
		"cpage_dead":               {1000503}, // 协议死链
		"cpage_medium_risk":        {1000503}, // 中风险弹窗
		"cpage_high_risk":          {1000503}, // 高风险弹窗
		"novel_detail":             {1000503},
		"novel_reader":             {1000503},
		"sug_session":              {1000510},
		"pull_to_refresh":          {1000502, 1000503}, // 下拉刷新
	}

	r.addNotice("rec_event_type", dataParams.Eventtype)

	requestRals := make([]NamedHTTPReq, 0, 3)
	allSrcArrStr := make([]byte, 0, 1024)
	recSrcIDs := eventTypeScridMap[dataParams.Eventtype]
	// 做一个兜底，和原先的代码保持一致。
	if len(recSrcIDs) == 0 {
		recSrcIDs = []int{28386}
	}
	for i, ID := range recSrcIDs {
		// 保证如果需要并发请求下游，通过 srcArr资源号新增的请求只请求一次。
		if i > 0 {
			srcArr = srcArr[0:1]
		}
		if version.Compare("12.20", bd_version, "<=") && dataParams.Eventtype == "change" {
			ID = 1000502
		}

		// 替换搜索发现资源号
		if version.Compare("12.3", bd_version, "<=") && ID != 0 {
			srcArr[0]["srcid"] = ID
		}
		recallSrcid := ""
		for _, one := range srcArr {
			v, ok := one["srcid"].(int)
			if !ok {
				continue
			}
			if v == 1000503 {
				r.processAIToolData(incidental)
				bodyByte, _ := json.Marshal(incidental)
				one["incidental_json"] = string(bodyByte)
			}

			recallSrcid += strconv.Itoa(v) + "_"
			allSrcArrStr = append(allSrcArrStr, []byte(strconv.Itoa(v))...)
			allSrcArrStr = append(allSrcArrStr, []byte("_")...)
		}
		r.addNotice("recall_srcids_"+strconv.Itoa(i), strings.TrimRight(recallSrcid, "_"))
		bodyForm := map[string]interface{}{
			"originquery": (*reparams)["uid"],
			"queryid64":   r.getLogid(),
			"cs_log_id":   r.getLogid(),
			"token":       token,
			"caller":      caller,
			"useripv6":    useripv6,
			"user_ip":     cip,
			"sid":         dataParams.Sids,
			"ssid":        strings.Split(bdsid, "-"),
			"cuid":        (*reparams)["uid"],
			"srcarr":      srcArr,
		}

		tbundle := (*reparams)["tbundle"]
		if clientName := getClientName(srcArr, osbranch, dataParams.Eventtype, tbundle); clientName != "" {
			bodyForm["ClientName"] = clientName
		} else if dataParams.ClientName != "" {
			bodyForm["ClientName"] = dataParams.ClientName
		} else {
			bodyForm["ClientName"] = "unknown"
		}

		if dataParams.ClientName == "sug_session_box" {
			bodyForm["originquery"] = dataParams.Eventquery
		}
		bodyFormByte, _ := json.Marshal(bodyForm)
		// r.addNotice("ralBodyParam", string(bodyFormByte))
		requestRal := &ral.HTTPRequest{
			Method:    "POST",
			Path:      "singularity.api.us_simple.UsSimpleService/query",
			Body:      string(bodyFormByte),
			Converter: ral.JSONConverter,
			LogID:     r.getLogidStr(),
			Ctx:       r.ctx,
		}
		requestRals = append(requestRals, NamedHTTPReq{BackendRec, requestRal})
		common.DumpData(r.ctx, "2"+BackendRec+strconv.Itoa(ID), requestRal)
	}
	dumpMap := map[string]string{
		"allSrcID": string(allSrcArrStr),
	}
	dumpStr, err := json.Marshal(dumpMap)
	if err != nil {
		r.addNotice("allSrcIDErr", err.Error())
	}
	common.DumpData(r.ctx, "2"+BackendRec+"_allSrcArr", dumpStr)

	if GetVideoInfoBranch(dataParams) != 0 && (*reparams)["intelligent_mode"] != "1" {
		requestVideo := r.buildVideoRequest(reparams, adptparams, dataParams)
		if requestVideo != nil {
			requestRals = append(requestRals, NamedHTTPReq{BackendRecVideo, requestVideo})
			common.DumpData(r.ctx, "2"+BackendRecVideo, requestVideo)
		}
	}
	return requestRals
}

func (r *Rec) isTargetEventType(eventType string) bool {
	switch eventType {
	case "hissug", "hissug_with_box", "exchange":
		return true
	default:
		return false
	}
}

func (r *Rec) checkYunyingCondition(bdVersion, osBranch string, dataParams DataParms, reqParam *map[string]string) (bool, string) {
	if !(r.isTargetEventType(dataParams.Eventtype) && !common.IsAudit(r.ctx) && (*reqParam)[constant.RongyaoBaipai] != "1") {
		return false, ""
	}
	if version.Compare(bdVersion, "13.77", ">=") && (osBranch == "i0" || osBranch == "a0") {
		return true, "main"
	}

	if (osBranch == "a2" || osBranch == "i3") && version.Compare(bdVersion, "6.56", ">=") {
		return true, "lite"
	}
	return false, ""
}

// 构造下游 RAL 请求 body 中需要的 srcArr 参数，这些资源号对应的资源会在一次请求中返回。
func (r *Rec) createSrcArr(reqParam *map[string]string, srcID int, bodyByte []byte,
	dataParams DataParms, bdVersion string, osBranch string, address map[string]string) []map[string]interface{} {
	srcArr := make([]map[string]interface{}, 0, 3)
	srcArr = append(srcArr, map[string]interface{}{
		"srcid":           srcID,
		"incidental_json": string(bodyByte),
	})

	// 框下推荐词
	if (*reqParam)["rec_session"] == "1" {
		srcArr = append(srcArr, map[string]interface{}{
			"srcid":           1000513,
			"incidental_json": string(bodyByte),
		})
	}

	// 新户his页二期优化，新增搜索精选服务：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/MYFIkxNl2o/G89EI77DsJ/18l2jj04R1Gd8T
	// 原先1000535服务已经不使用，本次新增服务复用原先1000535通路
	if dataParams.NeedNewUserHis {
		srcArr = append(srcArr, map[string]interface{}{
			"srcid":           NewUserHisSrcid,
			"incidental_json": string(bodyByte),
		})
	}

	// 增加请求his页红包运营资源号,控制在hissug场景只请求一次1000539
	ok, app := r.checkYunyingCondition(bdVersion, osBranch, dataParams, reqParam)
	if ok && app == "main" {
		srcArr = append(srcArr, map[string]interface{}{
			"srcid":           1000539,
			"incidental_json": getHisYunyingJson(r.ctx, *reqParam, address, common.GetSampleValues(r.ctx, "his_yunying", []string{}), app),
		})
	} else if ok && app == "lite" {
		srcArr = append(srcArr, map[string]interface{}{
			"srcid":           1000539,
			"incidental_json": getHisYunyingJson(r.ctx, *reqParam, address, common.GetSampleValues(r.ctx, "his_yunying_lite", []string{}), app),
		})
	}
	return srcArr
}

type RecRALResponse struct {
	Result []typeResult `json:"result"`
}

type typeResult struct {
	Displaydatajson string `json:"displaydata_json"`
	Srcid           int    `json:"srcid"`
}

type TypeDispaly struct {
	Retno     int                    `json:"retno"`
	Data      map[string]interface{} `json:"data"`
	QueryList []interface{}          `json:"query_list"`
}

func (r *Rec) ParseHISResponse(ch chan ChRawHTTPResp) error {
	responses := make([]NamedHTTPResp, 0, len(ch))

	for chData := range ch {
		switch chData.Name {
		case BackendRec:
			// 不可丢的下游，有错误时，整体接口错误
			if chData.Err != nil {
				return chData.Err
			}
			responses = append(responses, NamedHTTPResp{Name: BackendRec, respBody: string(chData.Resp.Body)})
			// r.addNotice("rec_response", rec_response)
		case BackendRecVideo:
			// 可丢的下游，有错误时，video_response=""
			if chData.Err != nil {
				responses = append(responses, NamedHTTPResp{Name: BackendRecVideo, respBody: ""})
				r.addNotice("rec_video_err", chData.Err.Error())
			} else {
				responses = append(responses, NamedHTTPResp{Name: BackendRecVideo, respBody: string(chData.Resp.Body)})
			}
		default:
			continue
		}
	}
	// 添加打印堆栈信息
	defer func() {
		if err := recover(); err != nil {
			stack := string(runtimeinfo.Stack(3))
			panicMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, stack)
			r.addNotice("panic_errMsg", panicMsg)
			respStr := ""
			for _, v := range responses {
				respStr += v.respBody + "\n"
			}
			r.addNotice("response_for_json", respStr)
		}
	}()

	typedispalyMap := map[string]TypeDispaly{}

	for _, response := range responses {
		if response.Name == BackendRecVideo {
			if response.respBody == "" {
				continue
			}
			r.addNotice("recVideoResponse", response.respBody)
		}

		var resStruct RecRALResponse
		if err := json.Unmarshal([]byte(response.respBody), &resStruct); err != nil {
			return err
		}

		if len(resStruct.Result) < 1 {
			if response.Name == BackendRecVideo {
				continue
			}
			return errors.New("ral response err, " + response.respBody)
		}

		for _, v := range resStruct.Result {
			var typedispaly TypeDispaly
			if err := json.Unmarshal([]byte(v.Displaydatajson), &typedispaly); err != nil {
				return err
			}
			if ac, ok := typedispaly.Data["activity_config"].(map[string]interface{}); ok {
				adaptions := r.getAdaption()
				replaceAtmosphereColorForTemp(ac, *adaptions)
			}
			typedispalyMap[strconv.Itoa(v.Srcid)] = typedispaly
		}
	}

	r.setHISTplData(typedispalyMap)
	return nil
}

func replaceAtmosphereColorForTemp(ac map[string]interface{}, adaptions map[string]string) {
	bdVersion := adaptions["bd_version"]
	if version.Compare(bdVersion, "15.21", ">=") {
		if data, ok := ac["data"].(map[string]interface{}); ok {
			if _, ok := data["hispage_atmosphere_day_up_gradientcolor"].(string); ok {
				data["hispage_atmosphere_day_up_gradientcolor"] = "#00FFFFFF"
			}
			if _, ok := data["hispage_atmosphere_day_down_gradientcolor"]; ok {
				data["hispage_atmosphere_day_down_gradientcolor"] = "#00FFFFFF"
			}
		}
	}
}

func (r *Rec) setHISTplData(data map[string]TypeDispaly) {
	r.hisTplData = data
}

func (r *Rec) GetHISTplData() map[string]TypeDispaly {
	return r.hisTplData
}

// 判断是否为新增或付费渠道调起用户
func (r *Rec) isSelectedUser(parsedValue map[string]string) bool {
	if len(parsedValue) == 0 {
		return false
	}

	nowTime := time.Now().Unix()

	// 判断新增用户在N天内
	var newUserExp int64 = 7 * 24 * 3600
	if parsedValue["ut"] == "1" && parsedValue["at"] != "" {
		atime, err := strconv.ParseInt(parsedValue["at"], 10, 64)

		// 转换成北京时间的时间戳，计算北京时间当天0点，再换算成0时区的时间戳
		// 当天24点内，算作第一天
		end := (atime+8*3600)/86400*86400 - 8*3600 + newUserExp
		if err == nil && atime < nowTime && nowTime < end {
			return true
		}
	}

	// 判断付费调取用户在X天内
	var payChannelExp int64 = 7 * 24 * 3600
	if parsedValue["d8t"] != "" {
		d8time, err := strconv.ParseInt(parsedValue["d8t"], 10, 64)

		end := (d8time+8*3600)/86400*86400 - 8*3600 + payChannelExp
		if err == nil && d8time < nowTime && nowTime < end {
			return true
		}
	}

	return false
}

func handleParams(osBranch string, bdVersion string, os string) (token int, srcid int, caller string) {
	if (version.Compare("11.16", bdVersion, "<=") && os == "android") || (version.Compare("11.17", bdVersion, "<=") && (os == "iphone")) {
		token, srcid, caller = 11111189, 28386, "base_baiduboxapp"
	} else {
		token, srcid, caller = 11111189, 1000502, "base_baiduboxapp"
	}
	return
}

func getClientName(srcarr []map[string]interface{}, osbranch string, eventType string, tbundle string) string {
	if tbundle == "tomasBasics" {
		return "changting_box"
	}
	res := make([]string, 0)
	clientName := ""
	eventTypeTag := map[string]string{
		"videobar": "videobar", // 视频底bar顶部热词
	}

	for _, one := range srcarr {
		srcid, ok := one["srcid"].(int)
		if !ok {
			return ""
		}

		// osbranch参考http://wiki.baidu.com/pages/viewpage.action?pageId=97550490
		switch osbranch {
		case "a0":
			clientName = "a_main"
		case "i0":
			clientName = "i_main"
		case "a2":
			clientName = "a_lite"
		case "i3":
			clientName = "i_lite"
		case "a7":
			clientName = "a_big"
		case "i7":
			clientName = "i_big"
		default:
			return ""
		}

		if v, ok := eventTypeTag[eventType]; ok {
			clientName += "_" + v
		} else {
			switch srcid {
			case 1000502: // 空框热词
				clientName += "_box"
			case 1000503: // 搜索发现
				clientName += "_guess"
			case 1000510: // sug框下推荐词
				clientName += "_sug_session"
			case 1000513: // his框下推荐词
				clientName += "_under_box"
			case 1000539: // his页运营位
				clientName += "_his_yunying"
			default:
				return ""
			}
		}
		res = append(res, clientName)
	}

	return strings.Join(res, ",")
}

// 获取1000539 hisYunying 服务的 incidental_json 数据
func getHisYunyingJson(ctx *gdp.WebContext, reparams map[string]string, address map[string]string, values []string, app string) string {
	incidental := map[string]interface{}{
		"address": address,
	}

	if reparams["cuid_mapping"] == "1" {
		incidental["cuid_mapping"] = 1
	} else {
		incidental["cuid_mapping"] = 0
	}

	// 当命中实验时, 下发sample.toml中配置的值; 否则传1, 通过cuid获取活动配置
	incidental["user_pack_id"] = "1"

	var userPackArray []string

	if len(values) != 0 {
		for _, v := range values {
			if !strings.Contains(v, "|") {
				userPackArray = append(userPackArray, v)
			} else if randomID := getRandomID(v); randomID != "" {
				userPackArray = append(userPackArray, randomID)
			}
		}
	} else {
		if app == "main" {
			// 推全按照switch配置选择召回的活动id
			userPackID := SelectCampaign(common.SwitchConfMem.Campaigns)
			if userPackID != "" {
				ids := strings.Split(userPackID, ",")
				userPackArray = append(userPackArray, ids...)
			}
		} else if app == "lite" {
			userPackID := SelectCampaign(common.SwitchConfMem.CampaignsLite)
			if userPackID != "" {
				ids := strings.Split(userPackID, ",")
				userPackArray = append(userPackArray, ids...)
			}
		}
	}

	cityCode := getCityCode(ctx)
	blackCity := common.SwitchConfMem.HisYunyingBlackCity

	// 特定城市下，下发his_yunying_preview中的配置
	if blackCity[cityCode] == 1 || blackCity[address["province"]] == 1 || blackCity[address["city"]] == 1 {
		incidental["user_pack_id"] = common.GetSampleValue(ctx, "his_yunying_preview", "120")
	} else {
		incidental["user_pack_id"] = strings.Join(userPackArray, ",")
	}
	ctx.AddNotice("user_pack_id", incidental["user_pack_id"])
	bodyByte, err := json.Marshal(incidental)
	if err != nil {
		return ""
	}
	return string(bodyByte)
}

func SelectCampaign(campaigns []common.Campaign) string {
	rand.Seed(time.Now().UnixNano())
	r := rand.Float64()
	acc := 0.0
	for _, c := range campaigns {
		acc += c.RecallProbability
		if r < acc {
			return c.ID
		}
	}
	return ""
}

// 根据BAIDULOCNEW中获取citycode
func getCityCode(ctx *gdp.WebContext) string {
	locNew, err := ctx.Cookie("BAIDULOCNEW")
	if err == nil && locNew != "" {
		// 毫秒时间戳
		now := time.Now().Unix() * 1000

		locNewArr := strings.Split(locNew, "_")
		if len(locNewArr) == 6 {
			cityCode := locNewArr[3]
			timeInt, err := strconv.ParseInt(locNewArr[4], 10, 64)
			// 判断时间戳是否在15分钟内有效
			if cityCode != "" && err == nil && now-timeInt < 15*60*1000 {
				return cityCode
			}
		}
	}

	return ""
}

// 根据权重 随机返回id, 格式为"111,112,113|20,20,60"
func getRandomID(value string) string {
	parts := strings.Split(value, "|")
	if len(parts) != 2 {
		return ""
	}
	options := strings.Split(parts[0], ",")
	weightStrings := strings.Split(parts[1], ",")
	if len(options) != len(weightStrings) {
		return ""
	}

	weights := make([]int, len(weightStrings))
	totalWeight := 0

	// 将权重字符串解析为整数
	for i, w := range weightStrings {
		weight, err := strconv.Atoi(w)
		if err != nil {
			return ""
		}
		weights[i] = weight
		totalWeight += weight
	}

	if totalWeight != 100 {
		return ""
	}

	// 生成随机数
	rand.Seed(time.Now().UnixNano())
	randomValue := rand.Intn(totalWeight)

	// 根据权重选择
	for i, weight := range weights {
		if randomValue < weight {
			return options[i]
		}
		randomValue -= weight
	}

	return ""
}

// processAIToolData 处理AI工具数据，从context中获取aiToolTypes并构造aiToolData
// 当srcid为1000503时调用此函数来设置AI工具相关数据
func (r *Rec) processAIToolData(incidental map[string]interface{}) {
	if aiToolTypes, ok := r.ctx.Get("aiToolTypes"); ok {
		if aiToolTypesSlice, ok := aiToolTypes.([]string); ok {
			aiToolData := make([]map[string]string, 0, len(aiToolTypesSlice))
			for _, aiToolType := range aiToolTypesSlice {
				aiToolData = append(aiToolData, map[string]string{
					"type": aiToolType,
					"text": aiToolType,
				})
			}
			incidental["ai_tool_data"] = aiToolData
		}
	}
}
