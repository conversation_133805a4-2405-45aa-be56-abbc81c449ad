package data

import (
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestBaseData_getRequest(t *testing.T) {
	type fields struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //*middle.RequestParams
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &BaseData{
			ctx: tt.fields.ctx,
		}
		got := this.getRequest()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBaseData_getRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*map[string]string))
	}
	ag.Run()
}

func TestBaseData_getAdaption(t *testing.T) {
	type fields struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getAdaption ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //*middle.AdaptionParams
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &BaseData{
			ctx: tt.fields.ctx,
		}
		got := this.getAdaption()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBaseData_getAdaption, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*map[string]string))
	}
	ag.Run()
}

func TestBaseData_getHttpRequest(t *testing.T) {
	type fields struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getHttpRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //*http.Request
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &BaseData{
			ctx: tt.fields.ctx,
		}
		got := this.getHTTPRequest()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBaseData_getHttpRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*http.Request))
	}
	ag.Run()
}

func TestBaseData_addNotice(t *testing.T) {
	type fields struct {
		ctx *gdp.WebContext
	}
	type args struct {
		key   string
		value string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: addNotice ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &BaseData{
			ctx: tt.fields.ctx,
		}
		this.addNotice(tt.args.key, tt.args.value)
	}
	ag.Run()
}

func TestBaseData_getLogidStr(t *testing.T) {
	type fields struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getLogidStr ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //string
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &BaseData{
			ctx: tt.fields.ctx,
		}
		got := this.getLogidStr()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBaseData_getLogidStr, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestBaseData_getHisToken(t *testing.T) {
	type Want struct {
		Value  string
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getHisToken ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		want          Want
	}{
		{
			"getHisToken test",
			Want{
				"fb8d3da5b566c13fb125187b6dda738b",
				ut.ShouldEqual,
			},
		},
	}

	this := &BaseData{
		ctx: createGetWebContext(),
	}

	for k, tt := range tests {
		token := this.getHisToken()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestBaseData_getHisToken, Result Index:0 Value Compare", token, tt.want.Assert, tt.want.Value)
	}
	ag.Run()
}

var (
	httpRspBody1 *httptest.ResponseRecorder
)

func createGetWebContext() *gdp.WebContext {
	httpRspBody1 = httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	//构造Request                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           //获取Gin提供的Context，WebContext包括gin.Context
	//req, _ := http.NewRequest("GET", `/suggest?query=be&cfrom=1099a&from=1099a&network=1_0&osbranch=i0&osname=baiduboxapp&puid=_a2K8jun28e6A&service=bdbox&ua=750_1334_iphone_11.3.6.10_0&uid=D21DBA569DE01513BC23847AF4C5A42E79D82E372OHFAOKEQMR&ut=iPhone8%2C1_11.3&zid=Y-KKDc8Yxi1YpH87_ea1q4kNX-dP0ZqQV_fDqPEQ1L6KtISkxgbT03ZhuHzRGtj5sqai5JIOURiBbj_no65zMWg&bdid=E7101D946E3509D10F03306AC256BAF4%3AFG%3D1&wise_csor=2&v=3`, nil)
	req, _ := http.NewRequest("GET", `/suggest?ctl=sug&query=sug&v=3&voi=0&wise_csor=3&lid=15625789587437782785&service=bdbox&uid=gi2kig8zvujjaHtH_uS3a0OH-a_Aa2a20uvg8lu_vuKaLiqlB&from=757b&ua=_a-qi4ujvfg4NE65I5me6NN0v8oNu2I4_hvDhSdqNqqqB&ut=09STI0tc2iyqPXiDzuD58gIVQMlykSO6A&osname=baiduboxapp&osbranch=a0&pkgname=com.baidu.searchbox&network=1_0&cfrom=757b&ctv=2&cen=uid_ua_ut&typeid=0&puid=_u-oi_aq-ieNdqqqB&zid=9BE28306D28D2E866398511E0D675B075987B4E95332AF1B834DA4A197B8DE&pq=%E5%8F%82%E6%95%B0&pt=1562567664`, nil)
	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "*******"),
	}
	return wc
}
