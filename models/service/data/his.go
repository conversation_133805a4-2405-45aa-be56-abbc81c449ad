package data

import (
	"encoding/json"
	"net/url"
	"path/filepath"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

const HIS_SERVER = "bdbox_hsug"

type HIS struct {
	BaseData
	hisTplData  HisRALResponse
	directToHis bool
}

type DataType struct {
	Query string `json:"query"`
	Tag   string `json:"tag"`
	URL   string `json:"url"`
	Type  string `json:"type"`
}

type HisRALResponse struct {
	Msg    string `json:"msg"`
	Status string `json:"status"`
}

type ConfMapping struct {
	Tag2His map[string]string `toml:"tag2his"`
	His2Tag map[string]string `toml:"his2tag"`
}

func NewHIS(ctx *gdp.WebContext, directToHis bool) *HIS {
	dataSrv := HIS{}
	dataSrv.ctx = ctx
	dataSrv.directToHis = directToHis
	return &dataSrv
}
func (this *HIS) GetHISResponse(reqdata DataType, wisehispm string) error {
	requestrals := this.BuildHISRequest(reqdata, wisehispm)
	common.DumpData(this.ctx, "2his", requestrals)

	var response = httpResp{}
	err := ral.Ral(HIS_SERVER, *requestrals, &response, ral.JSONConverter)
	// this.addNotice("ralres", string(response.Raw))
	if err != nil {
		return err
	}
	return this.ParseHISResponse(reqdata.Query, response)
}

func (this *HIS) BuildHISRequest(reqdata DataType, wisehispm string) *ral.HTTPRequest {
	var confTypeMapping ConfMapping
	query := reqdata.Query
	filePath := filepath.Join(env.ConfRootPath(), "histype.toml")
	_, err := toml.DecodeFile(filePath, &confTypeMapping)
	if err != nil {
		this.addNotice("mappingconfReadError", "1")
	}
	bodyForm := map[string]string{
		"qr":   query,
		"ts":   strconv.FormatInt(time.Now().Unix(), 10),
		"src":  "2",
		"st":   wisehispm,
		"pn":   "1",
		"f":    "1",
		"tn":   "",
		"type": "0",
		"url":  "",
	}

	if this.directToHis && err == nil && reqdata.Tag != "" && reqdata.URL != "" && confTypeMapping.Tag2His[reqdata.Tag] != "" {
		bodyForm["type"] = confTypeMapping.Tag2His[reqdata.Tag]
		bodyForm["url"] = reqdata.URL
	}

	//bodybyte,_ := json.Marshal(bodyForm)
	values := url.Values{}
	values.Set("query", query)
	values.Set("_slog", this.getLogidStr())
	values.Set("token", this.getHisToken())
	cs := this.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	requestrals := &ral.HTTPRequest{
		Header: map[string][]string{
			"Cookie":       {cstr},
			"Host":         {"m.baidu.com"},
			"Content-Type": {"application/x-www-form-urlencoded"},
		},
		Method: "POST",
		Body:   bodyForm,
		//Path:        "new_hsug/data/write",
		Path:        "hisproxy/api/write",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       this.getLogidStr(),
		Ctx:         this.ctx,
	}

	return requestrals
}
func (this *HIS) ParseHISResponse(query string, response httpResp) error {

	var resStruct HisRALResponse
	if err := json.Unmarshal([]byte(response.Raw), &resStruct); err != nil {
		return err
	}
	this.setHISTplData(resStruct)
	return nil
}

func (this *HIS) setHISTplData(r HisRALResponse) {
	this.hisTplData = r
}

func (this *HIS) GetHISTplData() HisRALResponse {
	return this.hisTplData
}
