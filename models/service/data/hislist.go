package data

import (
	"encoding/json"
	"sync"

	"errors"
	"fmt"
	"path/filepath"
	"strconv"

	"net/url"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const HISLISTSERVER = "searchbox_his_new"

type HISLIST struct {
	BaseData
	hisPriTplData  map[string]HisPriveRALResponse
	hisListTplData HisListResponse
	hisListRalData rawResp
	hisPrivRalData rawResp
}
type HisList_Request_Error struct {
	HisList error
	HisPriv error
}

func (e HisList_Request_Error) Error() string {
	return e.HisList.Error() + " " + e.HisPriv.Error()
}

func (e HisList_Request_Error) IsHisListErr() bool {
	return e.HisList != nil
}

func (e HisList_Request_Error) IsHisPrivErr() bool {
	return e.HisPriv != nil
}

type HisListRalResponse struct {
	Errno       int                 `json:"err_no"`
	Errmsg      string              `json:"errmsg"`
	G           []typeG             `json:"g"`
	Histitle    string              `json:"his_title"`
	Ext         map[string]string   `json:"ext"`
	FrequentHis []map[string]string `json:"frequent_his"` //his常用词
	Hasmore     int                 `json:"has_more"`
	QueryId     string              `json:"queryid"`
}
type typeG struct {
	Type  string      `json:"type"`
	Sa    string      `json:"sa"`
	Q     string      `json:"q"`
	Ext   interface{} `json:"ext"`
	St    typeSt      `json:"st"`
	Dtype string      `json:"d_type"`
	Durl  string      `json:"d_url"`
}
type typeSt struct {
	Src string `json:"src"`
}

type HisListResponse struct {
	Errno       int                 `json:"errno"`
	Errmsg      string              `json:"errmsg"`
	Data        []typeData          `json:"data"`
	Ext         map[string]string   `json:"ext"`
	FrequentHis []map[string]string `json:"frequent_his"` //his常用词
	Hasmore     int                 `json:"has_more"`
	QueryId     string              `json:"queryid"`
}
type typeData struct {
	Type  string      `json:"type"`
	Vals  []string    `json:"vals"`
	Ext   interface{} `json:"ext"`
	Pos   int         `json:"pos"`
	Dtype string      `json:"d_type"`
}

var isRecom = true

func NewHISLIST(ctx *gdp.WebContext) *HISLIST {
	dataSrv := HISLIST{}
	dataSrv.ctx = ctx
	return &dataSrv
}
func (h *HISLIST) GetResponse(reqparams, adpterprams *map[string]string, uid string) error {
	requests, _ := h.BuildRequest(reqparams, adpterprams, uid)
	mutiResponseChan := make(chan ChRawHTTPResp, len(requests))
	h.MultiRequest(requests, mutiResponseChan)
	return h.ParseHisListResponse(mutiResponseChan)
}

func (h *HISLIST) BuildRequest(reqparams, adpterprams *map[string]string, uid string) ([]NamedHTTPReq, error) {
	return []NamedHTTPReq{
		{
			HISLISTSERVER,
			h.BuildHisListRequest(reqparams, adpterprams, uid),
		}, {
			HIS_PRIVE_SERVER,
			h.BuildPrivRequest(reqparams, adpterprams, uid),
		},
	}, nil
}

func (h *HISLIST) ParseHisListResponse(ch chan ChRawHTTPResp) error {
	//使用for从channel取数据 会比自己定义channle长度读取靠谱一些
	var hislist, hispriv error
	for chData := range ch {
		//此处需要后续优化一下 不要以bns的名字取 最好自定义tag。。。
		switch chData.Name {
		case HISLISTSERVER:
			h.hisListRalData = chData.Resp
			// h.addNotice("hisralres", string(chData.Resp.Body))
			hislist = h.ParseHisListRalResponse(chData)
		case HIS_PRIVE_SERVER:
			h.hisPrivRalData = chData.Resp
			// h.addNotice("hispriralres", string(chData.Resp.Body))
			hispriv = h.ParsehisPrivRalResponse(chData)
		default:
			continue //不太可能default
		}
	}
	return HisList_Request_Error{
		HisList: hislist,
		HisPriv: hispriv,
	}
}

func (h *HISLIST) ParseHisListRalResponse(chRes ChRawHTTPResp) error {
	if chRes.Err != nil {
		return chRes.Err
	}
	var hisListRalResponse HisListRalResponse
	if err := json.Unmarshal([]byte(chRes.Resp.Body), &hisListRalResponse); err != nil {
		return err
	}

	var confTypeMapping ConfMapping
	filePath := filepath.Join(env.ConfRootPath(), "histype.toml")
	_, err := toml.DecodeFile(filePath, &confTypeMapping)
	if err != nil {
		h.addNotice("mappingconfReadError", "1")
	}

	var hisListResponse HisListResponse

	hisListResponse.Errno = hisListRalResponse.Errno
	hisListResponse.Errmsg = hisListRalResponse.Errmsg
	hisListResponse.QueryId = hisListRalResponse.QueryId
	hisListResponse.Data = []typeData{}
	if len(hisListRalResponse.G) == 0 {
		h.setHISListTplData(hisListResponse)
		return nil
	}
	for i, val := range hisListRalResponse.G {
		var item = typeData{}
		if val.Type == "his_normal" {
			item.Type = "0"
		}
		if val.Type == "his_img" {
			item.Type = "18"
		}
		if val.Type == "rec" {
			item.Type = "20"
		}
		if val.Type == "news" {
			item.Type = "21"
		}
		item.Pos = i + 1
		item.Vals = []string{val.Q}
		if item.Type == "18" {
			if val.St.Src != "" {
				item.Vals = append(item.Vals, val.St.Src)
			}
		}

		if val.Dtype != "" && val.Durl != "" && len(val.Durl) <= 1024 && confTypeMapping.His2Tag[val.Dtype] != "" {
			item.Vals = append(item.Vals, confTypeMapping.His2Tag[val.Dtype])
			item.Vals = append(item.Vals, val.Durl)
		}

		// 微频道
		if val.Dtype == "17" {
			item.Dtype = val.Dtype
		}
		// 笔记
		if val.Dtype == "5" {
			item.Dtype = val.Dtype
		}

		if val.Ext != nil {
			item.Ext = val.Ext
		} else {
			item.Ext = ""
		}
		hisListResponse.Data = append(hisListResponse.Data, item)
	}
	hisListResponse.Ext = make(map[string]string)
	if hisListRalResponse.Ext != nil {
		hisListResponse.Ext = hisListRalResponse.Ext
	}
	if hisListRalResponse.Histitle != "" {
		hisListResponse.Ext["hot_header_title"] = hisListRalResponse.Histitle
	} else {
		hisListResponse.Ext["hot_header_title"] = ""
	}
	hisListResponse.FrequentHis = hisListRalResponse.FrequentHis

	if hisListRalResponse.Hasmore != 0 {
		hisListResponse.Hasmore = 1
	} else {
		hisListResponse.Hasmore = 0
	}
	h.setHISListTplData(hisListResponse)
	return nil
}
func (h *HISLIST) ParsehisPrivRalResponse(chRes ChRawHTTPResp) error {
	if chRes.Err != nil {
		return chRes.Err
	}

	var hisPrivResponse privRALResponse
	if err := json.Unmarshal([]byte(chRes.Resp.Body), &hisPrivResponse); err != nil {
		return err
	}

	errno := hisPrivResponse.Errno
	if errno != 0 {
		errmsg := "request datasrv upskv error:" + string(chRes.Resp.Body)
		return errors.New(errmsg)
	}

	hisPriRes := map[string]HisPriveRALResponse{}
	targetArr := []string{"switchinfo", constant.PersonalSwitch}

	for _, target := range targetArr {
		one := sugStoreSet{}

		if target == "switchinfo" {
			one = hisPrivResponse.SugStoreSet
		} else if target == constant.PersonalSwitch {
			one = hisPrivResponse.PersonalSwitch
		} else {
			continue
		}

		status := ""
		switch one.Value.(type) {
		case float64:
			status = strconv.FormatFloat(one.Value.(float64), 'f', -1, 64)
		case string:
			status = fmt.Sprintf("%s", one.Value)
		}

		var onePriRes HisPriveRALResponse
		if status == "0" || status == "1" {
			onePriRes.Status = status
			str := ""
			switch one.Utime.(type) {
			case float64:
				str = strconv.FormatFloat(one.Utime.(float64), 'f', -1, 64)
			case string:
				str = fmt.Sprintf("%s", one.Utime)
			}
			onePriRes.Timestamp = str
		}

		hisPriRes[target] = onePriRes
	}

	h.setHISPriTplData(hisPriRes)
	return nil
}
func (h *HISLIST) MultiRequest(rs []NamedHTTPReq, ch chan ChRawHTTPResp) {
	var wg sync.WaitGroup
	for _, r := range rs {
		wg.Add(1)
		go func(k string, req *ral.HTTPRequest, c chan ChRawHTTPResp) {
			defer wg.Done()
			cResp := ChRawHTTPResp{}
			cResp.Name = k
			response := rawResp{}
			err := ral.Ral(k, *req, &response, ral.RAWConverter)
			cResp.Err = err
			cResp.Resp = response
			c <- cResp
		}(r.Name, r.HTTPRequest, ch)
	}

	wg.Wait()
	close(ch)
}

func (h *HISLIST) BuildHisListRequest(reqparams, adpterprams *map[string]string, uid string) *ral.HTTPRequest {
	values := url.Values{}
	values.Set("type", "1")
	values.Set("cuid", (*reqparams)["uid"])
	values.Set("pid", uid)
	values.Set("from", "kuang")
	values.Set("rf", "2")
	values.Set("hisdata", h.ctx.Request.FormValue("hisdata"))
	values.Set("ua", (*reqparams)["ua"])

	// hislist最大返回条数
	if v, ok := (*reqparams)[constant.MAX_RETURN_NUM]; ok {
		values.Set("mrn", v)
	}

	bdsid, _ := h.ctx.Cookie("CS_W_SIDS")
	if bdsid != "" {
		values.Set("sid", bdsid)
	} else {
		values.Set("sid", h.ctx.Request.Form.Get("sid"))
	}

	sids, errck := h.ctx.Cookie("H_WISE_SIDS")
	if errck != nil || sids == "" {
		sids = ""
	}

	val := h.ctx.GetString(constant.HWiseSIDs)
	if val != "" {
		sids = val
	}

	values.Set("ksid", sids)
	bdid, errdb := h.ctx.Cookie("BAIDUID")
	if errdb != nil || bdid == "" {
		bdid = ""
	} else {
		bdid = bdid[:32]
	}
	values.Set("baiduid", bdid)
	//his侧 需要lid,所以增加
	if v, ok := (*reqparams)["lid"]; ok {
		values.Set("lid", v)
	}
	emptField := h.ctx.Request.FormValue("empty_field")
	if emptField != "" {
		values.Set("kuang_style_sid", emptField)
	}
	bdVersion := (*adpterprams)["bd_version"]
	dpi, _ := strconv.ParseFloat((*adpterprams)["device_dpi"], 64)
	isAnd := (*adpterprams)["isAndroid"]
	resWidth, _ := strconv.Atoi((*adpterprams)["resolution_width"])
	if isRecom && version.Compare("7.1", bdVersion, "<=") {
		if (isAnd == "1" && dpi > 1.5) || (isAnd == "0" && resWidth > 640) {
			values.Set("type", "5")
		}
	}
	if version.Compare("7.6", bdVersion, "<=") {
		values.Set("pic", "1")
	}
	if (*reqparams)["osbranch"] == "a2" && version.Compare("2.4", bdVersion, "<=") {
		values.Set("pic", "1")
	}
	values.Set("prod", "baiduapp_his")

	//添加cs_log_id用于串联日志,2020.3.5
	values.Set("cs_log_id", h.getLogidStr())

	requestral := ral.HTTPRequest{
		//Header:      map[string][]string{"Host": {"m.baidu.com"}},
		Method:      "GET",
		Path:        "singularity.api.sug.SugService/query",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       h.getLogidStr(),
		Ctx:         h.ctx,
	}

	common.DumpData(h.ctx, "2hislist", requestral)
	return &requestral
}
func (h *HISLIST) BuildPrivRequest(reqparams, adpterprams *map[string]string, uid string) *ral.HTTPRequest {
	_ = reqparams
	_ = adpterprams
	values := url.Values{}
	values.Set("product", "ps")
	values.Set("uid", uid)
	values.Set("logid", h.getLogidStr())
	values.Set("from", "gosug")
	requestral := ral.HTTPRequest{
		Method:      "GET",
		Path:        "ups/api/gettips",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       h.getLogidStr(),
		Ctx:         h.ctx,
	}

	common.DumpData(h.ctx, "2getups", requestral)
	return &requestral
}
func (h *HISLIST) ParseHISResponse(query string, response httpResp) error {
	_ = query
	var resStruct HisRALResponse
	return json.Unmarshal([]byte(response.Raw), &resStruct)
}

func (h *HISLIST) setHISPriTplData(r map[string]HisPriveRALResponse) {
	h.hisPriTplData = r
}

func (h *HISLIST) GetHISPriTplData() map[string]HisPriveRALResponse {
	return h.hisPriTplData
}
func (h *HISLIST) setHISListTplData(r HisListResponse) {
	h.hisListTplData = r
}

func (h *HISLIST) GetHISListTplData() HisListResponse {
	return h.hisListTplData
}
