package data

import (
	"encoding/json"

	"errors"
	"fmt"
	"strconv"

	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

const HIS_PRIVE_SERVER = "bdbox_upskv"

type HISPrive struct {
	BaseData
	hisTplData HisPriveRALResponse
}

// addtips返回结构
type hisPrivResponse struct {
	Errno int `json:"errno"`
}
type HisPriveRALResponse struct {
	Timestamp interface{} `json:"timestamp"`
	Status    interface{} `json:"status"`
}

// gettips返回结构
type privRALResponse struct {
	SugStoreSet    sugStoreSet `json:"sugStoreSet"`    // 记录历史
	SePerSwitch    sugStoreSet `json:"sePerSwitch"`    // 用户收藏历史
	PersonalSwitch sugStoreSet `json:"personalSwitch"` // 个性化开关

	Errno int `json:"errno"`
}

type sugStoreSet struct {
	Value  interface{} `json:"value"`
	Utime  interface{} `json:"utime"`
	ErrMsg string      `json:"ErrMsg"`
}

func NewHISPrive(ctx *gdp.WebContext) *HISPrive {
	dataSrv := HISPrive{}
	dataSrv.ctx = ctx
	return &dataSrv
}
func (this *HISPrive) GetHISResponse(uid string, target string) error {
	requestrals := this.BuildHISRequest(uid)
	common.DumpData(this.ctx, "2getups", requestrals)

	var response = httpResp{}
	err := ral.Ral(HIS_PRIVE_SERVER, *requestrals, &response, ral.JSONConverter)
	// this.addNotice("ralres", string(response.Raw))
	if err != nil {
		return err
	}
	return this.ParseHISResponse(response, target)
}

func (this *HISPrive) SetHISResponse(uid string, status string, target string) error {
	requestrals := this.BuildHisSetRequest(uid, status, target)
	common.DumpData(this.ctx, "2addups", requestrals)

	var response = httpResp{}
	err := ral.Ral(HIS_PRIVE_SERVER, *requestrals, &response, ral.JSONConverter)
	// this.addNotice("ralres", string(response.Raw))
	if err != nil {
		return err
	}
	return this.ParseHISSetResponse(response)
}

func (this *HISPrive) BuildHISRequest(uid string) *ral.HTTPRequest {
	values := url.Values{}
	values.Set("product", "ps")
	values.Set("uid", uid)
	values.Set("logid", this.getLogidStr())
	values.Set("from", "gosug")
	requestral := &ral.HTTPRequest{
		Method:      "GET",
		Path:        "ups/api/gettips",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       this.getLogidStr(),
		Ctx:         this.ctx,
	}
	return requestral
}
func (this *HISPrive) BuildHisSetRequest(uid string, status string, target string) *ral.HTTPRequest {
	sugStoreSet := map[string]string{
		"sugStoreSet": status,
	}
	if target != "" && target == "sePerSwitch" {
		sugStoreSet = map[string]string{
			"sePerSwitch": status,
		}
	}
	setByte, _ := json.Marshal(sugStoreSet)
	values := url.Values{}
	values.Set("product", "ps")
	values.Set("uid", uid)
	values.Set("logid", this.getLogidStr())
	values.Set("tips", string(setByte))
	values.Set("from", "gosug")
	requestral := &ral.HTTPRequest{
		Method:      "GET",
		Path:        "ups/api/addtips",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       this.getLogidStr(),
		Ctx:         this.ctx,
	}
	return requestral
}

// 解析gettips结果
func (this *HISPrive) ParseHISResponse(response httpResp, target string) error {
	var hisPrivResponse privRALResponse
	if err := json.Unmarshal([]byte(response.Raw), &hisPrivResponse); err != nil {
		return err
	}
	sugStoreSet := hisPrivResponse.SugStoreSet
	if target == "sePerSwitch" {
		sugStoreSet = hisPrivResponse.SePerSwitch
	}
	errno := hisPrivResponse.Errno
	if errno != 0 {
		errmsg := "request datasrv upskv error"
		return errors.New(errmsg)
	}

	var hisPriRes HisPriveRALResponse
	status := ""
	switch sugStoreSet.Value.(type) {
	case float64:
		status = strconv.FormatFloat(sugStoreSet.Value.(float64), 'f', -1, 64)
	case string:
		status = fmt.Sprintf("%s", sugStoreSet.Value)
	}

	if status == "0" || status == "1" {
		hisPriRes.Status = status
		str := ""
		switch sugStoreSet.Utime.(type) {
		case float64:
			str = strconv.FormatFloat(sugStoreSet.Utime.(float64), 'f', -1, 64)
		case string:
			str = fmt.Sprintf("%s", sugStoreSet.Utime)
		}
		hisPriRes.Timestamp = str
		this.setHISTplData(hisPriRes)
		return nil
	}
	//this.setHISTplData(resStruct)
	//errmsg := "request datasrv upskv error:" + string(response.Raw)
	return nil
}

// 解析addtips的结果
func (this *HISPrive) ParseHISSetResponse(response httpResp) error {

	var hisPrivRes hisPrivResponse
	if err := json.Unmarshal([]byte(response.Raw), &hisPrivRes); err != nil {
		return err
	}

	errno := hisPrivRes.Errno
	if errno != 0 {
		errmsg := "request datasrv upskv error"
		return errors.New(errmsg)
	}
	return nil
}

func (this *HISPrive) CheckToken(timestamp string, target string, token string) bool {
	checkStr := timestamp
	checkStr += target
	checkStr += "hissugprimode"
	this.addNotice("requestToken", token)
	this.addNotice("originStr", checkStr)
	localToken := base64.Encode([]byte(checkStr), 0)
	this.addNotice("localToken", localToken)
	if localToken != token {
		return false
	}
	return true
}

func (this *HISPrive) setHISTplData(r HisPriveRALResponse) {
	this.hisTplData = r
}

func (this *HISPrive) GetHISTplData() HisPriveRALResponse {
	return this.hisTplData
}
