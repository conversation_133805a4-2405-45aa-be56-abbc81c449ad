// nolint
package data

import (
	"context"
	"net/http"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/require"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/pbrpc"
	"icode.baidu.com/baidu/searchbox/go-suggest/idl/megafoil"
)

func TestMegafoil_GetMegafoilResponse(t *testing.T) {
	// 设置mock服务

	// ral.RAL func(ctx context.Context, name interface{}, req ral.Request, resp ral.Response, opts ...ral.ROption) error
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(ral.RAL, func(ctx context.Context, name interface{}, req ral.Request, resp ral.Response, opts ...ral.ROption) error {
		mockRes := resp.(*pbrpc.RalResponse)
		mockRes.Data = &megafoil.MegafoilResponseV2{
			Response: []*megafoil.MegafoilResponseV1{
				{
					MegafoilData: []*megafoil.MegafoilData{
						{
							DisplayData: &megafoil.DisplayData{
								DisplayUnits: []*megafoil.DisplayUnit{
									{
										Items: []*megafoil.MergeItem{
											{
												Srcid: uint32Ptr(123),
												SubResult: []*megafoil.SubBlock{
													{
														SubLink: []*megafoil.SubLink{
															{
																Url:   []byte("http://test.com"),
																Title: []byte("test title"),
															},
														},
													},
												},
												OffsetInfo: &megafoil.Offset{
													TargetUrl: []byte("http://main.com"),
													Title:     []byte("main title"),
													Summary:   []byte("main summary"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}
		return nil
	})

	// 测试用例
	tests := []struct {
		name      string
		reqParams map[string]string
		wantErr   bool
		errMsg    string
	}{
		{
			name: "success case - main card",
			reqParams: map[string]string{
				"data": `{
					"word": "test",
					"plid": "123",
					"cuid": "456",
					"user_id": 789,
					"subpos": "0",
					"card_pos": "1",
					"abstract": "test abstract"
				}`,
			},
			wantErr: false,
		},
		{
			name: "success case - sub link",
			reqParams: map[string]string{
				"data": `{
					"word": "test",
					"plid": "123",
					"cuid": "456",
					"user_id": 789,
					"subpos": "1",
					"card_pos": "1",
					"abstract": "test abstract"
				}`,
			},
			wantErr: false,
		},
		{
			name: "invalid card pos",
			reqParams: map[string]string{
				"data": `{
					"word": "test",
					"plid": "123",
					"cuid": "456",
					"user_id": 789,
					"subpos": "0",
					"card_pos": "2",
					"abstract": "test abstract"
				}`,
			},
			wantErr: true,
			errMsg:  "cardPos:2 invalid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createGetWebContext()
			m := NewMegafoil(ctx)

			mp, err := m.GetMegafoilResponse(&tt.reqParams)
			if tt.wantErr {
				require.Error(t, err)
				if tt.errMsg != "" {
					require.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				require.NoError(t, err)
				require.NotNil(t, mp)
				require.NotEmpty(t, m.resData)
			}
		})
	}
}

func TestMegafoil_parseResponse(t *testing.T) {
	tests := []struct {
		name     string
		mp       *MegafoilParams
		res      *pbrpc.RalResponse
		wantErr  bool
		errMsg   string
		wantData ClickContentParams
	}{
		{
			name: "success case - main card",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{
														Srcid: uint32Ptr(1),
														OffsetInfo: &megafoil.Offset{
															TargetUrl: []byte("http://test.com"),
															Title:     []byte("test title"),
															Summary:   []byte("test summary"),
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr:  false,
			wantData: ClickContentParams{Content: "test summary", URL: "http://test.com", Pos: 1, Title: "test title", Srcid: 1, Type: 0},
		},
		{
			name: "success case - sub card",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "1",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{
														Srcid: uint32Ptr(1),
														SubResult: []*megafoil.SubBlock{
															{
																SubLink: []*megafoil.SubLink{
																	{
																		Url:   []byte("http://sub.test.com"),
																		Title: []byte("sub test title"),
																	},
																},
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr:  false,
			wantData: ClickContentParams{Content: "test abstract", URL: "http://sub.test.com", Pos: 1, Title: "sub test title", Srcid: 1, Type: 1},
		},
		{
			name: "error case - response error",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 1,
				ErrorText: "test error",
			},
			wantErr: true,
			errMsg:  "test error",
		},
		{
			name: "error case - invalid response type",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data:      &megafoil.MegafoilResponse{},
			},
			wantErr: true,
			errMsg:  "get MegafoilResponseV2 failed",
		},
		{
			name: "error case - empty response",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{},
				},
			},
			wantErr: true,
			errMsg:  "respData Response empty",
		},
		{
			name: "error case - empty megafoil data",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "megafoilDataList empty",
		},
		{
			name: "error case - empty display data",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: nil,
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "displayData empty",
		},
		{
			name: "error case - empty items",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "displayData Items empty",
		},
		{
			name: "error case - invalid card pos",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "0",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "cardPos:0 invalid",
		},
		{
			name: "error case - empty sub result",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "1",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{
														SubResult: []*megafoil.SubBlock{},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "subResult empty",
		},
		{
			name: "error case - empty sub link",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "1",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{
														SubResult: []*megafoil.SubBlock{
															{
																SubLink: []*megafoil.SubLink{},
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "subLink empty",
		},
		{
			name: "error case - invalid sub pos",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "2",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{
														SubResult: []*megafoil.SubBlock{
															{
																SubLink: []*megafoil.SubLink{
																	{},
																},
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "subPos:2 invalid",
		},
		{
			name: "error case - empty offset info",
			mp: &MegafoilParams{
				Word:     "test",
				Plid:     "123",
				CUID:     "456",
				SubPos:   "0",
				CardPos:  "1",
				Abstract: "test abstract",
			},
			res: &pbrpc.RalResponse{
				ErrorCode: 0,
				Data: &megafoil.MegafoilResponseV2{
					Response: []*megafoil.MegafoilResponseV1{
						{
							MegafoilData: []*megafoil.MegafoilData{
								{
									DisplayData: &megafoil.DisplayData{
										DisplayUnits: []*megafoil.DisplayUnit{
											{
												Items: []*megafoil.MergeItem{
													{
														OffsetInfo: nil,
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "offset info empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Megafoil{}
			err := m.parseResponse(tt.mp, tt.res)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseResponse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err.Error() != tt.errMsg {
				t.Errorf("parseResponse() error = %v, wantErrMsg %v", err.Error(), tt.errMsg)
			}
			if !tt.wantErr && m.resData != tt.wantData {
				t.Errorf("parseResponse() resData = %v, wantData %v", m.resData, tt.wantData)
			}
		})
	}
}

func TestMegafoil_BuildRequest(t *testing.T) {
	tests := []struct {
		name   string
		params *MegafoilParams
		want   *pbrpc.RalRequest
	}{
		{
			name: "normal request",
			params: &MegafoilParams{
				Plid: "test_plid",
			},
			want: pbrpc.NewRalRequest(
				"MegafoilService",
				"get_v2",
				&megafoil.MegafoilRequestV2{
					Keys: []*megafoil.RequestKeys{
						{
							RequestType: megafoil.RequestType_megafoil_data.Enum(),
							DataType:    uint32Ptr(uint32(megafoil.DataType_disp)),
							SubKey: []*megafoil.SubKeyType{
								{
									KeyType: []byte("QID"),
									Key:     []byte("test_plid"),
								},
							},
						},
					},
					LogId: []byte("test_plid"),
					UserInfo: &megafoil.UserInfo{
						UserName: []byte(userName),
						Token:    []byte(megafoilToken),
						Product:  []byte(product),
					},
				},
				nil,
			),
		},
		{
			name: "empty plid",
			params: &MegafoilParams{
				Plid: "",
			},
			want: pbrpc.NewRalRequest(
				"MegafoilService",
				"get_v2",
				&megafoil.MegafoilRequestV2{
					Keys: []*megafoil.RequestKeys{
						{
							RequestType: megafoil.RequestType_megafoil_data.Enum(),
							DataType:    uint32Ptr(uint32(megafoil.DataType_disp)),
							SubKey: []*megafoil.SubKeyType{
								{
									KeyType: []byte("QID"),
									Key:     []byte(""),
								},
							},
						},
					},
					LogId: []byte(""),
					UserInfo: &megafoil.UserInfo{
						UserName: []byte(userName),
						Token:    []byte(megafoilToken),
						Product:  []byte(product),
					},
				},
				nil,
			),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Megafoil{}
			got := m.BuildRequest(tt.params)
			if got.Service != tt.want.Service {
				t.Errorf("BuildRequest() Service = %v, want %v", got.Service, tt.want.Service)
			}
			// Compare request data fields
			gotData := got.Data.(*megafoil.MegafoilRequestV2)
			wantData := tt.want.Data.(*megafoil.MegafoilRequestV2)
			if string(gotData.LogId) != string(wantData.LogId) {
				t.Errorf("BuildRequest() LogId = %v, want %v", string(gotData.LogId), string(wantData.LogId))
			}
			if len(gotData.Keys) != len(wantData.Keys) {
				t.Errorf("BuildRequest() Keys length = %v, want %v", len(gotData.Keys), len(wantData.Keys))
			}
		})
	}
}

func uint32Ptr(u uint32) *uint32 {
	return &u
}

func TestMegafoil_GetTplData(t *testing.T) {
	ccp1 := ClickContentParams{}
	ccp2 := ClickContentParams{Content: "test summary", URL: "http://test.com", Pos: 1, Title: "test title", Srcid: 1, Type: 0}
	tests := []struct {
		name    string
		resData ClickContentParams
		want    ClickContentParams
	}{
		{
			name:    "empty data",
			resData: ccp1,
			want:    ccp1,
		},
		{
			name:    "normal data",
			resData: ccp2,
			want:    ccp2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Megafoil{
				resData: tt.resData,
			}
			if got := m.GetTplData(); *got != tt.want {
				t.Errorf("Megafoil.GetTplData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewMegafoil(t *testing.T) {
	tests := []struct {
		name    string
		ctx     *gdp.WebContext
		wantNil bool
	}{
		{
			name:    "with context",
			ctx:     &gdp.WebContext{},
			wantNil: false,
		},
		{
			name:    "nil context",
			ctx:     nil,
			wantNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewMegafoil(tt.ctx)
			if (got == nil) != tt.wantNil {
				t.Errorf("NewMegafoil() = %v, want nil? %v", got, tt.wantNil)
			}
			if got != nil && got.ctx != tt.ctx {
				t.Errorf("NewMegafoil().ctx = %v, want %v", got.ctx, tt.ctx)
			}
		})
	}
}

func TestMegafoil_checkReqParams(t *testing.T) {
	tests := []struct {
		name      string
		reqParams map[string]string
		wantErr   bool
		errMsg    string
	}{
		{
			name: "valid params",
			reqParams: map[string]string{
				"data": `{
					"word": "test",
					"plid": "123",
					"subpos": "1",
					"card_pos": "2",
					"abstract": "test abstract"
				}`,
			},
			wantErr: false,
		},
		{
			name: "empty data",
			reqParams: map[string]string{
				"data": "",
			},
			wantErr: true,
			errMsg:  "reqData is nil",
		},
		{
			name: "invalid json",
			reqParams: map[string]string{
				"data": "{invalid json}",
			},
			wantErr: true,
			errMsg:  "reqData json Unmarshal fail",
		},
		{
			name: "missing plid",
			reqParams: map[string]string{
				"data": `{
						"word": "test",
						"card_pos": "2",
						"abstract": "test abstract"
					}`,
			},
			wantErr: true,
			errMsg:  "req Plid is nil",
		},
		{
			name: "missing card_pos",
			reqParams: map[string]string{
				"data": `{
						"word": "test",
						"plid": "123",
						"abstract": "test abstract"
					}`,
			},
			wantErr: true,
			errMsg:  "req CardPos is nil",
		},
		{
			name: "missing word",
			reqParams: map[string]string{
				"data": `{
						"plid": "123",
						"card_pos": "2",
						"abstract": "test abstract"
					}`,
			},
			wantErr: true,
			errMsg:  "req Word is nil",
		},
	}

	ctx := createGetWebContext()
	ctx.Request.AddCookie(&http.Cookie{Name: "BAIDUCUID", Value: "lavla0aHBtg5uvtQ_uSEu_8Uvagku2u2gu-_u_a6Bagku2iS_aSV8Y8vWPJW9QPcabVmA"})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMegafoil(ctx)
			_, err := m.checkReqParams(&(tt.reqParams))
			if (err != nil) != tt.wantErr {
				t.Errorf("checkReqParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err.Error() != tt.errMsg {
				t.Errorf("checkReqParams() error = %v, wantErrMsg %v", err.Error(), tt.errMsg)
			}
		})
	}
}
