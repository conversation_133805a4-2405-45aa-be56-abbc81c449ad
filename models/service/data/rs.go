package data

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const (
	RsServer = "sug_his_rec"
	// ERR_CPAGE 和 CPAGE_TOP 使用这个资源号
	ErrCPageSrcID = 1000528
	// TUWEN_PUSH 使用这个资源号
	TuWenPushSrcID = 28685
	// fixme: HIGHLIGHT 使用这个资源号, 尚未分配
	HighlightSrcID = 1000538
	// CPAGE_TOP 使用这个资源号
	CPageTopSrcID = 1000528
)

var srcID = map[string]int{
	constant.ERR_CPAGE:       ErrCPageSrcID,
	constant.TUWEN_PUSH:      TuWenPushSrcID,
	constant.CPAGE_HIGHLIGHT: HighlightSrcID,
	constant.CPAGE_TOP:       CPageTopSrcID,
}

var EMPTY_ERR = errors.New("not recall")

// 端上传递的data
type RSDataParams struct {
	RSMaxReturn int    `json:"rs_max_return"`
	Query       string `json:"query"`
	URL         string `json:"url"`
	CUID        string `json:"cuid"`
	EventType   string `json:"event_type"`
	Title       string `json:"title"`
}

type RS struct {
	BaseData
	hisTplData map[string]interface{}
}

func NewRS(ctx *gdp.WebContext) *RS {
	dataSrv := RS{}
	dataSrv.ctx = ctx
	return &dataSrv
}

func (r *RS) GetResponse(reqParams, adapterParams *map[string]string, reqData RSDataParams) error {
	request, err := r.buildRequest(reqParams, adapterParams, reqData)
	if err != nil {
		return err
	}
	common.DumpData(r.ctx, "2rs", request)

	response := httpResp1{}
	if err = ral.Ral(RsServer, *request, &response, ral.JSONConverter); err != nil {
		return err
	}

	if err := r.parseResponse(response); err != nil {
		return err
	}

	return nil
}

func (r *RS) buildRequest(reqParams, _ *map[string]string, reqData RSDataParams) (*ral.HTTPRequest, error) {
	scene := (*reqParams)[constant.SCENE]

	srcArr := []map[string]interface{}{
		{
			"srcid": srcID[scene],
		},
	}
	// 获取搜索sid
	sidStr, _ := r.ctx.Cookie("H_WISE_SIDS")
	var sids []int
	for _, id := range strings.Split(sidStr, "_") {
		sidInt, _ := strconv.Atoi(id)
		sids = append(sids, sidInt)
	}
	bodyForm := map[string]interface{}{
		"originquery":       reqData.Query,
		"content_req_url":   reqData.URL,   // 落地页url
		"content_req_title": reqData.Title, // 落地页标题
		"cuid":              reqData.CUID,
		"caller":            "content_rs_gosug",
		"token":             24353503,
		"srcarr":            srcArr,
		"sid":               sids,
		"cs_log_id":         r.getLogid(),
	}
	// 划词场景，需要将originquery设置为划词URL（架构要求）
	if scene == constant.CPAGE_HIGHLIGHT {
		bodyForm["originquery"] = reqData.URL
		bodyForm["caller"] = "highseek_gosug"
		bodyForm["token"] = 24353525
	}
	bodyFormByte, _ := json.Marshal(bodyForm)
	r.addNotice("ralBodyParam", string(bodyFormByte))

	requestRAL := &ral.HTTPRequest{
		Method:    http.MethodPost,
		Path:      "singularity.api.us_simple.UsSimpleService/query",
		Body:      string(bodyFormByte),
		Converter: ral.JSONConverter,
		LogID:     r.getLogidStr(),
		Ctx:       r.ctx,
	}

	return requestRAL, nil
}

// 策略返回的结构
type ralRsResponse struct {
	ResultCode int          `json:"resultcode"`
	Result     []typeResult `json:"result"`
}

type rsResult struct {
	RS interface{} `json:"rs"`
}
type highlightResultItem struct {
	Key       string `json:"key"`
	Context   string `json:"context"`
	URISearch string `json:"uri_search"`
	Index     int    `json:"index"`
}

// 划词场景返回的结构
type highlightResult struct {
	RS []highlightResultItem `json:"rs"`
}

func (r *RS) parseResponse(response httpResp1) error {
	ralResp := ralRsResponse{}
	if err := json.Unmarshal([]byte(response.Raw), &ralResp); err != nil {
		return err
	}

	if ralResp.ResultCode != 0 {
		return errors.New("rs ResultCode err")
	}

	// 策略未召回，不算做错误
	if len(ralResp.Result) == 0 {
		r.addNotice("rsEmpty", "1")
		return EMPTY_ERR
	}

	tplData := map[string]interface{}{}
	for _, v := range ralResp.Result {
		if v.Srcid == TuWenPushSrcID || v.Srcid == ErrCPageSrcID {
			rs := rsResult{}
			if err := json.Unmarshal([]byte(v.Displaydatajson), &rs); err != nil {
				return err
			}
			tplData["rslist"] = r.filterRS(rs.RS)
		} else if v.Srcid == HighlightSrcID {
			rs := highlightResult{}
			if err := json.Unmarshal([]byte(v.Displaydatajson), &rs); err != nil {
				return err
			}
			tplData["data"] = r.filterHighlightRS(rs.RS)
			recallNum := len(rs.RS)
			tplData["recall_num"] = recallNum

			// 处理 recall_num 为 0 的情况
			if recallNum == 0 {
				// 添加日志
				r.addNotice("highlightEmpty", "1")
				return EMPTY_ERR
			}
		}
	}

	if len(tplData) == 0 {
		errmsg := "rs response empty"
		return errors.New(errmsg)
	}

	r.setHISTplData(tplData)
	return nil
}

// 对rs数据过滤处理
func (r *RS) filterRS(rs interface{}) interface{} {
	request := r.getRequest()
	dataquery := (*request)["data"]

	dataStruct := RSDataParams{}
	_ = json.Unmarshal([]byte(dataquery), &dataStruct)

	rsList, ok := rs.([]interface{})
	if !ok {
		return rs
	}
	if (*request)[constant.SCENE] == constant.CPAGE_TOP {
		rsList = r.BlackListFilter(rsList)
	}
	for i, v := range rsList {
		rsMap, ok := v.(map[string]interface{})
		if !ok {
			continue
		}
		delete(rsMap, "feature")
		delete(rsMap, "score")

		// c页面错误页
		prefix := "re_dl_se_error"

		if (*request)[constant.SCENE] == constant.TUWEN_PUSH { // push图文落地页
			prefix = "gh_landpage_tuwenpush"
		} else if (*request)[constant.SCENE] == constant.CPAGE_TOP { // c页面顶部框
			prefix = "ctopbar"
		} else if dataStruct.EventType == "novel_detail" {
			prefix = "re_dl_se_error_novel_detail_rs"
		} else if dataStruct.EventType == "novel_reader" {
			prefix = "re_dl_se_error_novel_reader_rs"
		}
		rsMap["sa"] = prefix + "_" + strconv.Itoa(i)
	}

	// 根据RSMaxReturn参数对list截断
	if dataStruct.RSMaxReturn > 0 && dataStruct.RSMaxReturn < len(rsList) {
		rsList = rsList[:dataStruct.RSMaxReturn]
	}

	if len(rsList) > 0 {
		r.addNotice("errPageRSNum", strconv.Itoa(len(rsList)))
	}
	if filterNum, ok := r.ctx.Get("rongyao_blacklist_filter"); ok {
		r.addNotice("rongyao_blacklist_filter", fmt.Sprintf("%v", filterNum))
	}
	return rsList
}

func (r *RS) setHISTplData(tplData map[string]interface{}) {
	r.hisTplData = tplData
}

func (r *RS) GetHISTplData() map[string]interface{} {
	return r.hisTplData
}

func (r *RS) BlackListFilter(data []interface{}) []interface{} {
	// 从上下文中获取过滤计数并转换为 int 类型
	filterCount := 0
	if v, ok := r.ctx.Get("rongyao_blacklist_filter"); ok {
		if count, ok := v.(int); ok {
			filterCount = count
		}
	}

	// 在函数返回前将最新的计数值写回上下文
	defer func() {
		r.ctx.Set("rongyao_blacklist_filter", filterCount)
	}()

	// 如果不需要过滤，则直接返回原始数据
	if (*r.getRequest())[constant.RongyaoBaipai] != "1" {
		return data
	}

	// 定义一个新的切片用于存储过滤后的结果
	filteredData := make([]interface{}, 0, len(data))
	for _, v := range data {
		// 尝试将每个元素转换为 map[string]interface{}
		item, ok := v.(map[string]interface{})
		if !ok {
			filteredData = append(filteredData, v)
			continue
		}

		// 检查 item 中是否存在 "term" 字段
		termVal, exists := item["term"]
		if !exists {
			filteredData = append(filteredData, v)
			continue
		}

		// 判断 "term" 字段是否为字符串
		strVal, ok := termVal.(string)
		if !ok {
			filteredData = append(filteredData, v)
			continue
		}

		// 如果匹配黑名单规则，则更新计数并跳过该项
		if background.RongYao.IsAccurateMatch(strVal) || background.RongYao.IsFuzzyMatch(strVal) {
			filterCount++
			continue
		}

		// 不满足过滤条件则保留该项
		filteredData = append(filteredData, v)
	}

	return filteredData
}

func (r *RS) filterHighlightRS(items []highlightResultItem) []map[string]interface{} {
	request := r.getRequest()
	dataquery := (*request)["data"]

	dataStruct := RSDataParams{}
	_ = json.Unmarshal([]byte(dataquery), &dataStruct)
	// 按照 index 排序
	sort.Slice(items, func(i, j int) bool {
		return items[i].Index < items[j].Index
	})

	// 限制返回数量
	if len(items) > dataStruct.RSMaxReturn {
		items = items[:dataStruct.RSMaxReturn]
	}

	// 转换为不包含 index 的 map 切片
	// 改为使用 schema 字段拼接，废弃 uri_search 字段
	result := make([]map[string]interface{}, 0, len(items))
	for _, item := range items {
		m := make(map[string]interface{})
		m["key"] = item.Key
		m["context"] = item.Context
		m["uri_search"] = "https://m.baidu.com/s?word=" + url.QueryEscape(item.Key) + "&sa=scribe_h5landingpage"
		result = append(result, m)
	}

	return result
}
