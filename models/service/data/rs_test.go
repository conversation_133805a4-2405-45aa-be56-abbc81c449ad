package data

import (
	"encoding/json"
	"net/http"
	"net/url"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestFilterRS(t *testing.T) {
	ctx := createGetWebContext()
	base := BaseData{
		ctx: ctx,
	}

	h := RS{
		BaseData: base,
	}
	result := []interface{}{
		map[string]interface{}{},
	}

	//novel_detail
	h.ctx.Set(constant.REQPARAMS, map[string]string{
		"data": "{\"event_type\":\"novel_detail\"}",
	})
	h.filterRS(result)
	retMap, _ := result[0].(map[string]interface{})
	assert.Equal(t, retMap["sa"], "re_dl_se_error_novel_detail_rs_0")

	// novel_reader
	h.ctx.Set(constant.REQPARAMS, map[string]string{
		"data": "{\"event_type\":\"novel_reader\"}",
	})
	result = []interface{}{
		map[string]interface{}{},
	}
	h.filterRS(result)
	retMap, _ = result[0].(map[string]interface{})
	assert.Equal(t, retMap["sa"], "re_dl_se_error_novel_reader_rs_0")

}

func TestBuildRequest(t *testing.T) {
	ctx := createGetWebContext()
	base := BaseData{
		ctx: ctx,
	}

	r := RS{
		BaseData: base,
	}
	// 创建请求参数
	reqParams := map[string]string{
		constant.SCENE: constant.TUWEN_PUSH,
	}

	reqData := RSDataParams{
		RSMaxReturn: 10,
		Query:       "test query",
		URL:         "http://test.url",
		CUID:        "test-cuid",
		EventType:   "test-event-type",
		Title:       "test title",
	}

	// 调用 buildRequest 方法
	requestRAL, err := r.buildRequest(&reqParams, nil, reqData)

	// 断言没有错误返回
	assert.NoError(t, err)

	// 断言返回的请求对象的内容
	assert.Equal(t, http.MethodPost, requestRAL.Method)
	assert.Equal(t, "singularity.api.us_simple.UsSimpleService/query", requestRAL.Path)
	assert.Equal(t, r.ctx, requestRAL.Ctx)

	// 断言请求体
	var bodyForm map[string]interface{}
	err = json.Unmarshal([]byte((requestRAL.Body).(string)), &bodyForm)
	assert.NoError(t, err)

	// 检查请求体的内容
	assert.Equal(t, reqData.Query, bodyForm["originquery"])
	assert.Equal(t, reqData.URL, bodyForm["content_req_url"])
	assert.Equal(t, reqData.Title, bodyForm["content_req_title"])
	assert.Equal(t, reqData.CUID, bodyForm["cuid"])
	assert.Equal(t, "content_rs_gosug", bodyForm["caller"])
	assert.Equal(t, float64(24353503), bodyForm["token"])

	// 检查 srcarr 的内容
	srcarr, ok := bodyForm["srcarr"].([]interface{})
	assert.True(t, ok)
	assert.Len(t, srcarr, 1)
}

func TestRS_ParseResponse(t *testing.T) {
	mockReturnValue := []interface{}{map[string]interface{}{"test": "mocked_data"}}
	patch := gomonkey.NewPatches()
	defer patch.Reset()
	patch.ApplyFuncReturn((*RS).filterRS, mockReturnValue)
	tests := []struct {
		name     string
		response httpResp1
		wantErr  bool
		errMsg   string
	}{
		{
			name: "valid response with TUWENPUSH_SRCID",
			response: httpResp1{
				Raw: []byte(`{
                    "resultcode": 0,
                    "result": [
                        {
                            "srcid": 28685,
                            "displaydata_json": "{\"rs\":[{\"test\":\"data\"}]}"
                        }
                    ]
                }`),
			},
			wantErr: false,
		},
		{
			name: "valid response with ERRCPAGE_SRCID",
			response: httpResp1{
				Raw: []byte(`{
                    "resultcode": 0,
                    "result": [
                        {
                            "srcid": 1000528,
                            "displaydata_json": "{\"rs\":[{\"test\":\"data\"}]}"
                        }
                    ]
                }`),
			},
			wantErr: false,
		},
		{
			name: "empty result",
			response: httpResp1{
				Raw: []byte(`{
                    "resultcode": 0,
                    "result": []
                }`),
			},
			wantErr: true,
			errMsg:  "EMPTY_ERR",
		},
		{
			name: "invalid result code",
			response: httpResp1{
				Raw: []byte(`{
                    "resultcode": 1,
                    "result": []
                }`),
			},
			wantErr: true,
			errMsg:  "rs ResultCode err",
		},
		{
			name: "invalid json in displaydata_json",
			response: httpResp1{
				Raw: []byte(`{
                    "resultcode": 0,
                    "result": [
                        {
                            "srcid": 28685,
                            "displaydata_json": "invalid json"
                        }
                    ]
                }`),
			},
			wantErr: true,
			errMsg:  "invalid character 'i' looking for beginning of value",
		},
		{
			name: "invalid response json",
			response: httpResp1{
				Raw: []byte(`invalid json`),
			},
			wantErr: true,
			errMsg:  "invalid character 'i' looking for beginning of value",
		},
		{
			name: "no matching srcid",
			response: httpResp1{
				Raw: []byte(`{
                    "resultcode": 0,
                    "result": [
                        {
                            "srcid": 99999,
                            "displaydata_json": "{\"rs\":[{\"test\":\"data\"}]}"
                        }
                    ]
                }`),
			},
			wantErr: true,
			errMsg:  "rs response empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RS{}
			r.ctx = createGetWebContext()

			err := r.parseResponse(tt.response)

			if tt.wantErr {
				if err == nil {
					t.Errorf("parseResponse() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if err.Error() != tt.errMsg && err.Error() != "not recall" {
					t.Errorf("parseResponse() error = %v, wantErr %v", err, tt.errMsg)
				}
				return
			}

			if err != nil {
				t.Errorf("parseResponse() unexpected error = %v", err)
			}
		})
	}
}

func TestRSBlackListFilter(t *testing.T) {
	// -------------------------------
	// 1. 使用 mock 对象设置测试环境
	// -------------------------------
	// 创建 mock 实例并设置为全局变量
	mockBlacklist, _, _ := background.SetupMockForTest()

	// 设置黑名单数据
	mockBlacklist.AddAccurate("forbidden")
	mockBlacklist.AddFuzzy("evil")
	// 添加交叉模糊匹配测试数据
	mockBlacklist.AddFuzzy("cross;fuzzy;match")

	// -------------------------------
	// 2. 构造测试用的 RS 实例和测试数据
	// -------------------------------
	// 模拟请求，设置 constant.RongyaoBaipai 对应的值为 "1"，表示需要过滤
	ctx := createGetWebContext()
	ctx.Set(constant.REQPARAMS, map[string]string{
		constant.RongyaoBaipai: "1",
	})
	base := BaseData{
		ctx: ctx,
	}

	rs := RS{
		BaseData: base,
	}

	// 构造输入数据：
	// - 第一项：map 中 "term": "forbidden" 被精确匹配，应被过滤
	// - 第二项：map 中 "term": "this text contains evil" 被模糊匹配，应被过滤
	// - 第三项：map 中 "term": "this contains cross and fuzzy and match words" 被交叉模糊匹配，应被过滤
	// - 第四项：map 中 "term": "this contains cross and fuzzy but no match" 包含部分交叉词但不完全匹配，应保留
	// - 第五项：map 中 "term": "good" 不在黑名单中，应保留
	// - 第六项：map 中没有 "term" 字段，应保留
	// - 第七项：非 map 类型，应保留
	data := []interface{}{
		map[string]interface{}{"term": "forbidden"},
		map[string]interface{}{"term": "this text contains evil"},
		map[string]interface{}{"term": "this contains cross and fuzzy and match words"},
		map[string]interface{}{"term": "this contains cross and fuzzy but "},
		map[string]interface{}{"term": "good"},
		map[string]interface{}{"other": "value"},
		"non-map data",
	}

	// -------------------------------
	// 3. 调用过滤函数并验证结果
	// -------------------------------
	filtered := rs.BlackListFilter(data)

	// 预期结果：第一、二和三项被过滤掉，剩下第四、五、六和七项
	expected := []interface{}{
		map[string]interface{}{"term": "this contains cross and fuzzy but "},
		map[string]interface{}{"term": "good"},
		map[string]interface{}{"other": "value"},
		"non-map data",
	}

	assert.Equal(t, expected, filtered)
}

func TestFilterHighlightRS(t *testing.T) {
	// 创建测试上下文
	ctx := createGetWebContext()
	base := BaseData{
		ctx: ctx,
	}

	r := RS{
		BaseData: base,
	}

	// 设置请求参数
	r.ctx.Set(constant.REQPARAMS, map[string]string{
		"data": `{"rs_max_return": 2, "query": "测试查询", "url": "http://test.url", "cuid": "test-cuid", "event_type": "highlight", "title": "测试标题"}`,
	})

	// 创建测试数据
	testItems := []highlightResultItem{
		{
			Key:     "测试关键词1",
			Context: "这是上下文1",
			Index:   3, // 设置不同的Index以测试排序
		},
		{
			Key:     "测试关键词2",
			Context: "这是上下文2",
			Index:   1,
		},
		{
			Key:     "测试关键词3",
			Context: "这是上下文3",
			Index:   2,
		},
	}

	// 调用测试函数
	result := r.filterHighlightRS(testItems)

	// 验证结果
	assert.Equal(t, 2, len(result), "应该只返回两个结果项（受rs_max_return限制）")

	// 验证排序是否正确（按Index升序）
	assert.Equal(t, "测试关键词2", result[0]["key"], "第一项应该是Index为1的项")
	assert.Equal(t, "测试关键词3", result[1]["key"], "第二项应该是Index为2的项")

	// 验证返回的map中不包含Index字段
	_, hasIndex := result[0]["index"]
	assert.False(t, hasIndex, "结果不应包含Index字段")

	// 验证其他字段正确映射
	assert.Equal(t, "这是上下文2", result[0]["context"], "context字段应正确映射")
	expectedURI := "https://m.baidu.com/s?word=" + url.QueryEscape("测试关键词2") + "&sa=scribe_h5landingpage"
	assert.Equal(t, expectedURI, result[0]["uri_search"], "uri_search字段应正确拼接")

	// 测试空输入情况
	emptyResult := r.filterHighlightRS([]highlightResultItem{})
	assert.Equal(t, 0, len(emptyResult), "空输入应该返回空结果")

	// 测试rs_max_return大于输入项数量的情况
	r.ctx.Set(constant.REQPARAMS, map[string]string{
		"data": `{"rs_max_return": 10, "query": "测试查询"}`,
	})
	fullResult := r.filterHighlightRS(testItems)
	assert.Equal(t, 3, len(fullResult), "当rs_max_return大于输入项数量时，应返回所有输入项")
}
