package data

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/utils/runtimeinfo"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const LEADERBOARD_SERVER = "leaderboard"

var oldBoardMap = map[string]bool{
	"realtime":      true,
	"gaokao":        true,
	"olympic":       true,
	"local_board":   true,
	"livelihood":    true,
	"finance":       true,
	"novel":         true,
	"movie":         true,
	"teleplay":      true,
	"phrase":        true,
	"election_news": true,
}

var boardLayoutMap = map[string]string{
	"realtime":          "hot_search_item",
	"gaokao":            "hot_search_item",
	"olympic":           "hot_search_item",
	"local_board":       "hot_search_item",
	"livelihood":        "hot_search_item",
	"finance":           "hot_search_item",
	"novel":             "hot_search_content",
	"movie":             "hot_search_content",
	"teleplay":          "hot_search_content",
	"phrase":            "hot_search_joke",
	"challenge":         "hot_search_content_btn",
	"car":               "hot_search_content",
	"travel_board_b":    "hot_search_content",
	"sports":            "hot_search_item",
	"new_entertainment": "hot_search_item",
	"games":             "hot_search_item",
	"internation_news":  "hot_search_item",
	"health":            "hot_search_item",
	"character":         "hot_search_item",
	"election_news":     "hot_search_item",
}

var boardKeyMap = map[string]string{
	"热搜榜":     "realtime",
	"民生榜":     "livelihood",
	"财经榜":     "finance",
	"热梗榜":     "phrase",
	"小说榜":     "novel",
	"电影榜":     "movie",
	"电视剧榜":    "teleplay",
	"挑战榜":     "challenge",
	"汽车榜":     "car",
	"旅游榜":     "travel_board_b",
	"体育榜":     "sports",
	"文娱榜":     "new_entertainment",
	"国际榜":     "internation_news",
	"健康榜":     "health",
	"人物榜":     "character",
	"游戏榜":     "games",
	"选举资讯榜":   "election_news",
	"gaokao":  "gaokao",
	"olympic": "olympic",
}
var tagImageMap = map[string]string{
	"5": "https://gips2.baidu.com/it/u=*********,**********&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_54",
	"4": "https://gips1.baidu.com/it/u=*********,*********&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_54",
	"3": "https://gips2.baidu.com/it/u=**********,**********&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_54",
	"1": "https://gips2.baidu.com/it/u=**********,**********&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_54",
}

type BoardRec struct {
	BaseData
	hisTplData    []interface{}
	cityName      string
	boardChannel  string
	IncogniteMode string
	PersonRec     string
}

type BodyParams struct {
	EntranceSa string `json:"entrance_sa"`
}

func NewBoardRec(ctx *gdp.WebContext) *BoardRec {
	dataSrv := BoardRec{}
	dataSrv.ctx = ctx
	return &dataSrv
}

type httpBoardRecResp struct {
	Head ral.HTTPHead
	Raw  []byte
}

func (b *BoardRec) GetResponse(reqparams, adpterprams *map[string]string, uid string) error {
	requests, err := b.BuildRequest(reqparams, adpterprams, uid)
	if err != nil {
		return err
	}
	multiResponseChan := make(chan ChRawHTTPResp, len(requests))
	b.MultiRequest(requests, multiResponseChan)
	return b.ParseResponse(multiResponseChan)
}

func (b *BoardRec) BuildRequest(reqparams, adpterprams *map[string]string, uid string) ([]NamedHTTPReq, error) {
	osbranch := (*reqparams)[constant.OSBRANCH]
	scene := (*reqparams)[constant.SCENE]
	_ = adpterprams
	_ = uid
	if osbranch == "" {
		return nil, errors.New("osbranch is empty")
	}

	b.IncogniteMode = b.ctx.GetHeader("incognito-mode")
	b.PersonRec = b.ctx.GetHeader("personal-switch")

	params, header := b.getParams(osbranch, scene)
	b.boardChannel = params.Get("from")
	b.addNotice("boardChannel", b.boardChannel)

	// 未获取到from时，不请求下游
	if b.boardChannel == "" {
		return nil, errors.New("boardChannel is empty")
	}

	request := ral.HTTPRequest{
		Method:      http.MethodGet,
		Path:        "/api/board",
		QueryParams: params,
		Header:      header,
		Converter:   ral.JSONConverter,
		Ctx:         b.ctx,
	}

	common.DumpData(b.ctx, "2leaderboard", request)
	return []NamedHTTPReq{{LEADERBOARD_SERVER, &request}}, nil
}

func (b *BoardRec) setHISTplData(r []interface{}) {
	b.hisTplData = r
}

func (b *BoardRec) GetHISTplData() []interface{} {
	return b.hisTplData
}

func (b *BoardRec) getParams(osbranch, scene string) (url.Values, map[string][]string) {
	from := ""
	key := ""
	sid := ""
	csid := ""
	params := url.Values{}
	header := map[string][]string{}

	request := b.getRequest()
	cuid := (*request)["uid"]

	// 解析cookie
	if HSids, err := b.ctx.Request.Cookie("H_WISE_SIDS"); err == nil {
		sid = HSids.Value
	}
	if CSids, err := b.ctx.Request.Cookie("CS_W_SIDS"); err == nil {
		csid = CSids.Value
	}

	adaptions := b.getAdaption()
	bdVersion := (*adaptions)["bd_version"]

	b.addNotice("boardSwitch", osbranch+scene)

	// 根据矩阵标识和scene请求场景，选择不同热榜from配置
	switch osbranch + scene {
	case "a0" + constant.TUWEN_PUSH, "i0" + constant.TUWEN_PUSH:
		from = constant.TUWEN_PUSH
		key = "bq1lcqpx1pg2cdqc"

		header["User-Agent"] = []string{b.getHTTPRequest().UserAgent()}
		params.Set("tab", "realtime")
	case "a0" + constant.ERR_CPAGE, "i0" + constant.ERR_CPAGE, "a2" + constant.ERR_CPAGE, "i3" + constant.ERR_CPAGE:
		from = constant.ERR_CPAGE
		key = "ecm3lf16vasxz7gp"
	case "a0" + constant.INCOGNITO, "i0" + constant.INCOGNITO:
		from = "his_incognito"
		key = "na5wap1c9lq1v51t"
		params.Set("tab", "realtime")
		params.Set("sid", sid)
		params.Set("csid", csid)
		params.Set("cuid", cuid)
	case "a2" + constant.INCOGNITO, "i3" + constant.INCOGNITO: // lite无痕模式
		if version.Compare(bdVersion, "6.25", ">=") {
			from = "his_incognito"
			key = "na5wap1c9lq1v51t"
			params.Set("tab", "realtime")
		}
	case "a0", "i0": // 主版
		from = "his"
		key = "a6af50bb2f174db3"
		params.Set("sid", sid)
		params.Set("csid", csid)
		params.Set("cuid", cuid)
		b.cityName = b.getCityName()
		params.Set("city_name", b.cityName)
	case "a2", "i3":
		// lite>=6.26的版本走主版逻辑
		if version.Compare(bdVersion, "6.26", ">=") {
			from = "his"
			key = "a6af50bb2f174db3"
			params.Set("sid", sid)
			params.Set("csid", csid)
			params.Set("cuid", cuid)
			b.cityName = b.getCityName()
			params.Set("city_name", b.cityName)
		} else {
			from = "his_lite"
			key = "5242f74d9012d164"
			b.cityName = b.getCityName()
			params.Set("city_name", b.cityName)
		}
	case "a7", "i7":
		// big>=2.31的版本走主版逻辑
		if version.Compare(bdVersion, "2.31", ">=") {
			from = "his"
			key = "a6af50bb2f174db3"
			params.Set("sid", sid)
			params.Set("csid", csid)
			params.Set("cuid", cuid)
			b.cityName = b.getCityName()
			params.Set("city_name", b.cityName)
		} else {
			from = "his_big"
			key = "fcd311ef37c3123c"
		}
	}

	// 未获取到from时，直接返回
	if from == "" {
		return params, header
	}

	localIP := common.GetLocalIp()
	nowTime := strconv.FormatInt(time.Now().Unix(), 10)

	md5 := fmt.Sprintf("%x", md5.Sum([]byte(from+nowTime+localIP+key)))
	auth := string(md5[7]) + string(md5[3]) + string(md5[17]) + string(md5[13]) + string(md5[1]) + string(md5[21])

	params.Set("from", from)
	params.Set("auth", auth)
	params.Set("clientip", localIP)
	params.Set("time", nowTime)
	params.Set("platform", "wise")

	return params, header
}

// leaderboard返回数据结构
type leaderBoardStruct struct {
	Success bool `json:"success"`
	Data    struct {
		Cards []interface{} `json:"cards"`
	} `json:"data"`
	Error struct {
		Message string `json:"message"`
	} `json:"error"`
}

func (b *BoardRec) ParseResponse(ch chan ChRawHTTPResp) error {
	response := <-ch
	return b.parseLeaderboard(response.Resp)
}

// 解析leaderboard数据
func (b *BoardRec) parseLeaderboard(response rawResp) error {
	reqparam := b.getRequest()
	osbranch := (*reqparam)[constant.OSBRANCH]
	adaptions := b.getAdaption()
	bdVersion := (*adaptions)["bd_version"]
	bodyParams := BodyParams{}
	_ = json.Unmarshal([]byte((*reqparam)["data"]), &bodyParams)
	// 添加打印堆栈信息
	defer func() {
		if err := recover(); err != nil {
			stack := string(runtimeinfo.Stack(3))
			panicMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, stack)
			b.addNotice("panic_errMsg", panicMsg)
			b.addNotice("response_for_json", string(response.Body))
		}
	}()

	leaderBoardResponse := leaderBoardStruct{}
	if err := json.Unmarshal([]byte(response.Body), &leaderBoardResponse); err != nil {
		return err
	}
	if !leaderBoardResponse.Success {
		return errors.New(leaderBoardResponse.Error.Message)
	}
	cards := make([]interface{}, 0, len(leaderBoardResponse.Data.Cards))
	if version.Compare(bdVersion, "13.72.0.0", ">=") {
		for _, item := range leaderBoardResponse.Data.Cards {
			v, ok := item.(map[string]interface{})
			if !ok {
				continue
			}

			content, ok := v["content"].([]interface{})
			if !ok {
				continue
			}

			if topItems, ok := v["topContent"].([]interface{}); ok && len(topItems) > 0 {
				content = append(topItems, content...)
				delete(v, "topContent")
			}

			boardKey, _ := v["boardKey"].(string)
			delete(v, "boardKey")
			if boardKey == "" {
				// 兜底旧数据协议不存在 boardkey 的情况
				tabName, _ := v["text"].(string)
				boardKey, _ = boardKeyMap[tabName]
			}
			if boardKey == "" {
				// 跳过 boardKey 不存在的榜单
				continue
			}
			v["board_key"] = boardKey

			// 对content中的show,index字段转换
			for i := range content {
				one, ok := content[i].(map[string]interface{})
				if !ok {
					continue
				}
				show, ok := one["show"].([]interface{})
				if !ok {
					continue
				}
				if len(show) == 0 {
					content[i].(map[string]interface{})["show"] = ""
				} else {
					content[i].(map[string]interface{})["show"] = show[0]
				}
				floatIndex, ok := one["index"].(float64)
				if !ok {
					continue
				}
				content[i].(map[string]interface{})["index"] = fmt.Sprint(floatIndex)
				b.modifyItemUrl(one, i, boardKey, bodyParams)
				if tagStyle, ok := getTagStyle(one, bdVersion, osbranch); ok {
					one["tag_style"] = tagStyle
				}
				// leaderboard协议转换
				if buttonInfo, ok := one["RightButtonInfo"].(map[string]interface{}); ok {
					one["btn_title"], _ = buttonInfo["title"].(string)
					one["btn_title_color"], _ = buttonInfo["color"].(string)
					one["btn_title_night_color"], _ = buttonInfo["nightColor"].(string)
					one["btn_title_dark_color"], _ = buttonInfo["darkColor"].(string)
					one["btn_title_bg_color"], _ = buttonInfo["bgColor"].(string)
					one["btn_title_bg_night_color"], _ = buttonInfo["bgNightColor"].(string)
					one["btn_title_bg_dark_color"], _ = buttonInfo["bgDarkColor"].(string)
					one["btn_cmd"], _ = buttonInfo["cmd"].(string)
					delete(one, "RightButtonInfo")
				}
				if imgWidthIOS, ok := one["imgWidthIOS"].(float64); ok {
					one["img_tpl_w_ios"] = imgWidthIOS
					delete(one, "imgWidthIOS")
				}
				if imgWidthAnd, ok := one["imgWidthAnd"].(float64); ok {
					one["img_tpl_w_and"] = imgWidthAnd
					delete(one, "imgWidthAnd")
				}
				if imgRatio, ok := one["imgRatio"].(float64); ok {
					one["w_h_ratio"] = imgRatio
					delete(one, "imgRatio")
				}
				if _, ok1 := one["layout"]; !ok1 {
					layout, ok2 := boardLayoutMap[boardKey]
					if !ok2 {
						continue
					}
					one["layout"] = layout
				}

			}
			boardStyle := make(map[string]string, 9)
			if boardInfo, ok := v["boardInfo"].(map[string]interface{}); ok {
				boardStyle["title_color"], _ = boardInfo["titleColor"].(string)
				boardStyle["title_color_night"], _ = boardInfo["titleColorNight"].(string)
				boardStyle["title_color_dark"], _ = boardInfo["titleColorDark"].(string)
				boardStyle["background_start_color"], _ = boardInfo["backgroundStartColor"].(string)
				boardStyle["background_start_color_night"], _ = boardInfo["backgroundStartColorNight"].(string)
				boardStyle["background_start_color_dark"], _ = boardInfo["backgroundStartColorDark"].(string)
				boardStyle["background_end_color"], _ = boardInfo["backgroundEndColor"].(string)
				boardStyle["background_end_color_night"], _ = boardInfo["backgroundEndColorNight"].(string)
				boardStyle["background_end_color_dark"], _ = boardInfo["backgroundEndColorDark"].(string)
				v["board_img"], _ = boardInfo["boardIcon"].(string)
			}

			v["board_style"] = boardStyle
			delete(v, "boardInfo")
			// content字段重命名为items
			v["items"] = content
			delete(v, "content")

			// 本地榜名称改写
			text, ok := v["text"].(string)
			if ok && text == "本地榜" {
				b.addNotice("showLocalBoard", b.cityName)
				v["text"] = b.cityName + "榜"
			}
			cards = append(cards, v)
			if len(cards) == 0 {
				return errors.New("leaderboard error")
			}
		}
	} else {
		for _, item := range leaderBoardResponse.Data.Cards {
			v, ok := item.(map[string]interface{})
			// 删除旧版本不需要的字段
			delete(v, "boardKey")
			delete(v, "boardInfo")
			if !ok {
				return errors.New("leaderboard cards error")
			}

			content, ok := v["content"].([]interface{})
			if !ok {
				return errors.New("leaderboard content error")
			}

			if topItems, ok := v["topContent"].([]interface{}); ok && len(topItems) > 0 {
				content = append(topItems, content...)
				delete(v, "topContent")
			}

			tabName, _ := v["text"].(string)
			boardKey, _ := boardKeyMap[tabName]
			if !oldBoardMap[boardKey] {
				// 旧版本跳过新增榜单, 避免兼容性问题
				continue
			}
			v["board_key"] = boardKey

			// lite版无痕模式，moreAppUrl使用h5链接
			if common.GetAppBranch(osbranch) == common.LITE_APP && b.boardChannel == "his_incognito" {
				v["moreAppUrl"] = v["moreUrl"]
			}

			// 对content中的show,index字段转换
			for i := range content {
				one, ok := content[i].(map[string]interface{})
				// 删除旧版本不需要的字段
				delete(one, "RightButtonInfo")
				delete(one, "imgWidthIOS")
				delete(one, "imgWidthAnd")
				delete(one, "imgRatio")
				if !ok {
					return errors.New("leaderboard one content error")
				}

				show, ok := one["show"].([]interface{})
				if !ok {
					return errors.New("leaderboard show error")
				}

				if len(show) == 0 {
					content[i].(map[string]interface{})["show"] = ""
				} else {
					content[i].(map[string]interface{})["show"] = show[0]
				}

				floatIndex, ok := one["index"].(float64)
				if !ok {
					return errors.New("leaderboard index error")
				}
				content[i].(map[string]interface{})["index"] = fmt.Sprint(floatIndex)

				if b.boardChannel == "his" {
					b.modifyItemUrl(one, i, boardKey, bodyParams)
					one["layout"] = boardLayoutMap[boardKey]
					if tagStyle, ok := getTagStyle(one, bdVersion, osbranch); ok {
						one["tag_style"] = tagStyle
					}
				} else if b.boardChannel == "his_incognito" {
					b.modifyItemUrl(one, i, boardKey, bodyParams)
				}
			}

			// content字段重命名为items
			v["items"] = content
			delete(v, "content")

			// 本地榜名称改写
			text, ok := v["text"].(string)
			if ok && text == "本地榜" {
				b.addNotice("showLocalBoard", b.cityName)
				v["text"] = b.cityName + "榜"
			} else if ok && text == "gaokao" {
				v["text"] = "高考榜"
			} else if ok && text == "olympic" {
				v["text"] = "奥运榜"
			}

			cards = append(cards, v)
		}
	}
	b.setHISTplData(cards)
	return nil
}

// 修改sa及url
func (b *BoardRec) modifyItemUrl(one map[string]interface{}, i int, boardKey string, bodyParams BodyParams) {
	// 点击sa
	oldSa := getSaFromUrl(one["url"])
	// 拼接无痕和个性化推荐信息
	extSa := ""
	if b.PersonRec == "0" {
		extSa += "_prec"
	}
	if b.IncogniteMode == "1" {
		extSa += "_inc"
	}

	if bodyParams.EntranceSa != "" {
		extSa += "_" + bodyParams.EntranceSa
	}

	reqParam := b.getRequest()
	if reqParam != nil && (*reqParam)["intelligent_mode"] == "1" {
		extSa += "_aimode"
	}

	newSa := oldSa + extSa + "_" + fmt.Sprint(i+1)

	// 奥运榜特殊修改下sa
	if boardKey == "olympic" {
		newSa = "fyb_hp_olympic_news_his" + extSa + "_" + fmt.Sprint(i+1)
	}

	// 展现sa
	one["sa"] = newSa

	// 修改点击跳转sa
	if oldSa != "" {
		if _, ok := one["url"].(string); ok {
			one["url"] = strings.ReplaceAll(one["url"].(string), oldSa, newSa)
		}
		if _, ok := one["appUrl"].(string); ok {
			one["appUrl"] = strings.ReplaceAll(one["appUrl"].(string), oldSa, newSa)
		}
	}
}

func getSaFromUrl(v interface{}) string {
	rawURL, ok := v.(string)
	if !ok || rawURL == "" {
		return ""
	}
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}

	return parsedURL.Query().Get("sa")
}

// 城市信息不合法时，不需要本地榜单，返回0
func (b *BoardRec) getCityName() string {
	locNew, _ := b.ctx.Cookie("BAIDULOCNEW")
	loc, _ := b.ctx.Cookie("BAIDULOC")

	// 毫秒时间戳
	nowTime := time.Now().Unix() * 1000

	var locTimeNew int64 = 0
	locCityCodeNew := ""
	if locNew != "" {
		locNewArr := strings.Split(locNew, "_")
		if len(locNewArr) == 6 {
			locCityCodeNew = locNewArr[3]
			tmpTime, err := strconv.ParseInt(locNewArr[4], 10, 64)
			if err == nil && tmpTime < nowTime {
				locTimeNew = tmpTime
			}
		}
	}

	var locTime int64 = 0
	locCityCode := ""
	if loc != "" {
		locArr := strings.Split(loc, "_")
		if len(locArr) == 5 {
			locCityCode = locArr[3]
			tmpTime, err := strconv.ParseInt(locArr[4], 10, 64)
			if err == nil && tmpTime < nowTime {
				locTime = tmpTime
			}
		}
	}

	cityCode := ""
	var timeout int64 = 15 * 60 * 1000

	// 使用时间戳最新的数据
	if locTimeNew >= locTime && nowTime-locTimeNew <= timeout {
		cityCode = locCityCodeNew
	} else if nowTime-locTime <= timeout {
		cityCode = locCityCode
	}

	cityName := ""
	var ok bool
	if cityCode != "" {
		// 通过cityCode解析
		b.addNotice("getCityName", "cityCode")
		cityName, ok = common.BoardCity[cityCode]
		if !ok {
			return "0"
		}
	} else {
		// 通过ip2poi解析
		b.addNotice("getCityName", "ip2poi")
		clientIP := b.ctx.ClientIP()
		address, _ := IP2Poi(clientIP)

		cityName = address["city"]
		if cityName == "" {
			return "0"
		}
	}

	return cityName
}

func (b *BoardRec) GetBoardChannel() string {
	return b.boardChannel
}

// 这里只是为了和 List 和 Rec 保持一致，实际上没有进行并发请求
func (b *BoardRec) MultiRequest(rs []NamedHTTPReq, ch chan ChRawHTTPResp) {
	if len(rs) == 0 {
		close(ch)
		return
	}
	cResp := ChRawHTTPResp{}
	cResp.Name = rs[0].Name
	response := rawResp{}
	err := ral.Ral(LEADERBOARD_SERVER, *(rs[0].HTTPRequest), &response, ral.RAWConverter)

	cResp.Err = err
	cResp.Resp = response
	ch <- cResp
	close(ch)
}

// getTagStyle 构造标签，直播中标签为 Pac 动态图标资源，优先级大于新热沸爆, 注意这个函数中会删除原有的 tag，请不要重复调用导致 tag_style消失
func getTagStyle(one map[string]interface{}, bdVersion string, osbranch string) (tagStyle map[string]interface{}, ok bool) {
	tagStyle = make(map[string]interface{})
	// 仅有 13.79 版本后才有直播中标签，如果后续还要添加新的 pac 资源标签需要注意修改
	if liveTagDay, ok := one["resourceDayTag"].(map[string]interface{}); ok &&
		version.Compare(bdVersion, "13.79.0.0", ">=") &&
		(osbranch == "a0" || osbranch == "i0") {
		tagStyle["tag_type"] = 4
		tagStyle["pag_url"] = liveTagDay["label_image_pag"]

		liveTagDark, _ := one["resourceDarkTag"].(map[string]interface{})
		tagStyle["pag_url_dark"] = liveTagDark["label_image_pag"]

		liveTagNight, _ := one["resourceNightTag"].(map[string]interface{})
		tagStyle["pag_url_night"] = liveTagNight["label_image_pag"]

		tagStyle["width_and"], tagStyle["height_and"], tagStyle["width_ios"], tagStyle["height_ios"], ok = ceilWidthAndHeight(liveTagDay)
		if !ok {
			return nil, false
		}
		delete(one, "resourceDayTag")
		delete(one, "resourceNightTag")
		delete(one, "resourceDarkTag")
		return tagStyle, true
	} else if hotTag, ok := one["hotTag"].(string); ok {
		if imgURL, has := tagImageMap[hotTag]; has {
			tagStyle["image"] = imgURL
			tagStyle["w_h_ratio"] = 0.89
			tagStyle["tag_type"] = 2
			return tagStyle, true
		}
	}
	return nil, false
}

func ceilWidthAndHeight(liveTag map[string]interface{}) (widthAndroid, heightAndroid, widthIOS, heightIOS int, ok bool) {
	ok = true
	heightStr, _ := liveTag["height"].(string)
	height, _ := strconv.ParseFloat(heightStr, 64)
	widthStr, _ := liveTag["width"].(string)
	width, _ := strconv.ParseFloat(widthStr, 64)

	if height == 0 || width == 0 {
		ok = false
		return
	}
	widthAndroid = int(math.Ceil(width * 0.87 / 3.0))
	heightAndroid = int(math.Ceil(height * 0.87 / 3.0))
	widthIOS = int(math.Ceil(width / 3.0))
	heightIOS = int(math.Ceil(height / 3.0))
	return
}
