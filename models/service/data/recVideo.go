package data

import (
	"encoding/json"
	"net/http"
	"sync"

	"icode.baidu.com/baidu/gdp/gdp/ral"
)

// 获取命中了rec_video的哪个实验分支
// 若没命中实验，返回0
func GetVideoInfoBranch(dataParams DataParms) int {
	return dataParams.RecVideoInfo
}

// 构造rec_video请求
func (r *Rec) buildVideoRequest(reparams, adptparams *map[string]string, dataParams DataParms) *ral.HTTPRequest {
	if len(dataParams.ContentInfo) == 0 {
		return nil
	}

	// nid是必须参数
	nid, ok := dataParams.ContentInfo["nid"]
	if !ok {
		return nil
	}

	ua_os := "iphone"
	if (*adptparams)["isAndroid"] == "1" {
		ua_os = "android"
	}

	device := map[string]interface{}{
		"user_agent": map[string]string{
			"ua_os": ua_os,
		},
	}

	incidental := map[string]interface{}{
		"device": device,
	}

	incidental_str, _ := json.<PERSON>(incidental)

	srcArr := make([]map[string]interface{}, 0)
	srcArr = append(srcArr, map[string]interface{}{
		"srcid":           1000514,
		"incidental_data": string(incidental_str),
	})

	bodyForm := map[string]interface{}{
		"originquery": nid,
		"caller":      "content_rs_gosug",
		"ClientName":  "content_rs_gosug",
		"token":       24353503,
		"srcarr":      srcArr,
		"cs_log_id":   r.getLogid(),
		"cuid":        (*reparams)["uid"],
	}

	bodyFormByte, err := json.Marshal(bodyForm)
	if err != nil {
		return nil
	}

	r.addNotice("recVideoRalData", string(bodyFormByte))
	requestral := &ral.HTTPRequest{
		Method:    http.MethodPost,
		Path:      "singularity.api.us_simple.UsSimpleService/query",
		Body:      string(bodyFormByte),
		Converter: ral.JSONConverter,
		LogID:     r.getLogidStr(),
		Ctx:       r.ctx,
	}

	return requestral
}

func (r *Rec) MultiRequest(rs []NamedHTTPReq, ch chan ChRawHTTPResp) {
	var wg sync.WaitGroup
	for _, r := range rs {
		wg.Add(1)
		go func(k string, req *ral.HTTPRequest, c chan ChRawHTTPResp) {
			defer wg.Done()
			cResp := ChRawHTTPResp{}
			cResp.Name = k
			response := rawResp{}
			var err error
			err = ral.Ral(REC_SERVER, *req, &response, ral.RAWConverter)

			cResp.Err = err
			cResp.Resp = response
			c <- cResp
		}(r.Name, r.HTTPRequest, ch)
	}
	wg.Wait()
	close(ch)
}
