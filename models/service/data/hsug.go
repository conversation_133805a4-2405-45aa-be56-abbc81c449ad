package data

import (
	"encoding/json"
	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type HSUG struct {
	BaseData
	hisTplData HisRALResponse
}

func NewHSUG(ctx *gdp.WebContext) *HSUG {
	dataSrv := HSUG{}
	dataSrv.ctx = ctx
	return &dataSrv
}
func (this *HSUG) GetHSUGResponse() error {
	requestrals := this.BuildHSUGRequest()
	common.DumpData(this.ctx, "2hsug", requestrals)

	var response = httpResp{}
	err := ral.Ral(HIS_SERVER, *requestrals, &response, ral.JSONConverter)
	if err != nil {
		return err
	}
	return this.ParseHISResponse(response)
}
func (this *HSUG) GetHSugSdelResponse(query string) error {
	requestrals := this.BuildHSugSdelRequest(query)
	common.DumpData(this.ctx, "2hsug.sdel", requestrals)

	var response = httpResp{}
	err := ral.Ral(HIS_SERVER, *requestrals, &response, ral.JSONConverter)
	if err != nil {
		return err
	}
	return this.ParseHISResponse(response)
}

func (this *HSUG) BuildHSUGRequest() *ral.HTTPRequest {
	values := url.Values{}
	values.Set("token", this.getHisToken())

	cs := this.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	requestrals := &ral.HTTPRequest{
		Header: map[string][]string{
			"Cookie": {cstr},
			"Host":   {"m.baidu.com"},
		},
		Method:      "GET",
		Path:        "hisproxy/api/deleteall",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       this.getLogidStr(),
		Ctx:         this.ctx,
	}

	return requestrals
}

func (this *HSUG) BuildHSugSdelRequest(query string) *ral.HTTPRequest {
	values := url.Values{}
	values.Set("query", query)
	values.Set("_slog", this.getLogidStr())
	values.Set("token", this.getHisToken())

	cs := this.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	requestral := &ral.HTTPRequest{
		Header: map[string][]string{
			"Cookie": {cstr},
			"Host":   {"m.baidu.com"},
		},
		Method:      "GET",
		Path:        "hisproxy/api/delete",
		Converter:   ral.FORMConverter,
		QueryParams: values,
		LogID:       this.getLogidStr(),
		Ctx:         this.ctx,
	}

	return requestral
}
func (this *HSUG) ParseHISResponse(response httpResp) error {

	var resStruct HisRALResponse
	if err := json.Unmarshal([]byte(response.Raw), &resStruct); err != nil {
		return err
	}
	this.setHISTplData(resStruct)
	return nil
}

func (this *HSUG) setHISTplData(r HisRALResponse) {
	this.hisTplData = r
}

func (this *HSUG) GetHISTplData() HisRALResponse {
	return this.hisTplData
}
