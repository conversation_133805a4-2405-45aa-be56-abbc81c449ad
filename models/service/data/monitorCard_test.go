// nolint
package data

import (
	"net/http"
	"net/url"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestMonitorCard_buildRequest(t *testing.T) {
	// Setup test context
	ctx := createGetWebContext()

	// Create MonitorCard instance
	card := NewMonitorCard(ctx)
	card.TplData = make(map[string]interface{})

	// Test case
	uid := "test_uid123"
	expectedParams := url.Values{}
	expectedParams.Set("cuid", uid)

	// Execute
	httpReq := card.buildRequest(uid)

	// Verify
	assert.Equal(t, http.MethodGet, httpReq.Method)
	assert.Equal(t, "/gsearch/his_monitor/show_entry?"+expectedParams.Encode(), httpReq.Path)
	assert.Equal(t, expectedParams, httpReq.QueryParams)
	assert.Equal(t, ral.JSONConverter, httpReq.Converter)
	assert.Equal(t, ctx, httpReq.Ctx)
}

func TestNewMonitorCard(t *testing.T) {
	// Setup test context
	ctx := &gdp.WebContext{}

	// Execute
	card := NewMonitorCard(ctx)

	// Verify
	assert.NotNil(t, card)
	assert.Equal(t, ctx, card.ctx)
	assert.NotNil(t, card.TplData)
	assert.Empty(t, card.TplData)
}

func TestMonitorCard_parseResponse(t *testing.T) {
	tests := []struct {
		name     string
		res      MonitorRes
		wantErr  bool
		checkTpl bool
	}{
		{
			name: "normal case",
			res: MonitorRes{
				Title:   "test title",
				CanShow: 1,
				JumpURL: "http://test.com",
			},
			wantErr:  false,
			checkTpl: true,
		},
		{
			name: "canShow not 1",
			res: MonitorRes{
				Title:   "test title",
				CanShow: 0,
				JumpURL: "http://test.com",
			},
			wantErr:  true,
			checkTpl: false,
		},
		{
			name: "empty title",
			res: MonitorRes{
				Title:   "",
				CanShow: 1,
				JumpURL: "http://test.com",
			},
			wantErr:  true,
			checkTpl: false,
		},
		{
			name: "empty JumpURL",
			res: MonitorRes{
				Title:   "test title",
				CanShow: 1,
				JumpURL: "",
			},
			wantErr:  true,
			checkTpl: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			card := &MonitorCard{
				TplData: make(map[string]interface{}),
			}

			err := card.parseResponse(tt.res)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			if tt.checkTpl {
				assert.Equal(t, tt.res.Title, card.TplData["text"])
				assert.Equal(t, map[string]interface{}{"link": tt.res.JumpURL}, card.TplData["survey"])
				assert.Equal(t, map[string]interface{}{
					"image":       daytimeImage,
					"night_image": nightImage,
					"dark_image":  darkImage,
					"tag_type":    2,
					"w_h_ratio":   1.375,
				}, card.TplData["tag_style"])
				assert.Equal(t, monitorCardSa, card.TplData["sa"])
			}
		})
	}
}

func TestMonitorCard_GetResponse(t *testing.T) {
	// Setup mock server
	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFunc(ral.Ral, func(serviceName string, request interface{}, response interface{}, ct ral.ConverterType) (err error) {
		mockRes := response.(*rawResp)
		mockRes.Body = []byte(`{"canShow":1,"jumpUrl":"http://test.com","title":"test title"}`)
		return nil
	})

	tests := []struct {
		name    string
		uid     string
		wantErr bool
	}{
		{
			name:    "normal case",
			uid:     "test_uid",
			wantErr: false,
		},
		{
			name:    "empty uid",
			uid:     "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createGetWebContext()
			card := NewMonitorCard(ctx)
			card.TplData = make(map[string]interface{})

			err := card.GetResponse(tt.uid)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, "test title", card.TplData["text"])
			assert.Equal(t, map[string]interface{}{"link": "http://test.com"}, card.TplData["survey"])
		})
	}
}

func TestMonitorCard_isFromFeedScene(t *testing.T) {
	tests := []struct {
		name     string
		data     string
		expected bool
	}{
		{
			name:     "from feed scene",
			data:     `{"content_info":"{\"vid_rec\":[1]}"}`,
			expected: true,
		},
		{
			name:     "not from feed scene - empty vid_rec",
			data:     `{"content_info":"{\"vid_rec\":[]}"}`,
			expected: false,
		},
		{
			name:     "not from feed scene - no vid_rec",
			data:     `{"content_info":"{}"}`,
			expected: false,
		},
		{
			name:     "invalid content info",
			data:     `{"content_info":"invalid json"}`,
			expected: false,
		},
		{
			name:     "empty content info",
			data:     `{"content_info":""}`,
			expected: false,
		},
		{
			name:     "no content info",
			data:     `{}`,
			expected: false,
		},
		{
			name:     "empty data",
			data:     "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createGetWebContext()
			card := NewMonitorCard(ctx)

			if tt.data != "" {
				card.BaseData.ctx.Set(constant.REQPARAMS, map[string]string{
					"data": tt.data,
				})
			}

			result := card.isFromFeedScene()
			assert.Equal(t, tt.expected, result)
		})
	}
}
