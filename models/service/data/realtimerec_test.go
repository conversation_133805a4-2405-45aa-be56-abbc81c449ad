package data

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// 创建带ua的ctx
func createRealtimeRecContext() *gdp.WebContext {
	httpRspBody1 = httptest.NewRecorder()
	c, _ := gin.CreateTestContext(httpRspBody1)
	req, _ := http.NewRequest("GET", `https://www.baidu.com`, nil)
	req.Header.Set("User-Agent", "baiduboxapp/13.63.0.10")
	c.Request = req
	return &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "1.1.1.1"),
	}
}

func TestRealtimeRec_IsBdBox(t *testing.T) {
	ctx := createRealtimeRecContext()
	r := &RealtimeRec{}
	r.ctx = ctx
	assert.True(t, r.IsBdbox())
}

func TestRealtimeRec_parseResponse(_ *testing.T) {
	ctx := createRealtimeRecContext()
	r := &RealtimeRec{}
	r.ctx = ctx
	res := RecResp{
		ResultCode: 0,
		ResultNum:  1,
		ContentResult: []*ContentRes{
			{
				SrcID: 0,
				TermList: []Term{
					{
						Word: "123",
						Sa:   "123",
					},
				},
			},
		},
	}
	_ = r.parseResponse(res)
}
