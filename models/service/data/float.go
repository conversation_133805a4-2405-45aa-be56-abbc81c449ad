package data

import (
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Float struct {
	BaseData
	hisTplData map[string]FloatTerm
}

type FloatTerm struct {
	Title           string `json:"title"`
	Query           string `json:"query"`
	Content         string `json:"content"`
	Sa              string `json:"sa"`
	IconImage       string `json:"icon_img"`
	IconImageWidth  int    `json:"icon_img_width"`
	IconImageHeight int    `json:"icon_img_height"`
}

// 策略返回的结构
type ralFloatRespnse struct {
	ResultCode int32  `json:"resultcode"`
	ResultNum  uint32 `json:"resultnum"`
	CsLogID    uint64 `json:"cs_log_id"`
	Result     []struct {
		SrcID           int    `json:"srcid"`
		DisplayDataJSON string `json:"displaydata_json"`
	} `json:"result"`
}

const (
	FloatServer = "sug_his_rec"
	// 主动问浮层结果的资源号
	FloatSrcID int = 1000532
)

var ErrFloat = errors.New("not recall")

// 获得一个实例指针
func NewFloat(ctx *gdp.WebContext) *Float {
	dataSrv := Float{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// 将数据整合封装到*ral.HTTPRequest中
func (f *Float) BuildFloatRequest(reparams *map[string]string) *ral.HTTPRequest {

	// 未登录或错误时，传"0"
	userIDStr, ok := (*reparams)[constant.SESSION_UID]
	userID, err := strconv.Atoi(userIDStr)
	if !ok || err != nil {
		userID = 0
	}

	// 获得用户IPv4地址
	cip := f.ctx.ClientIP()

	// 构建参数srcarr
	srcarr := make([](map[string]interface{}), 1)
	srcarr[0] = map[string]interface{}{
		"srcid": FloatSrcID,
	}

	sidstr := []string{}
	bdsid, err := f.ctx.Cookie("H_WISE_SIDS")
	if err == nil && bdsid != "" {
		sidstr = strings.Split(bdsid, "_")
	}

	sids := f.ctx.GetString(constant.HWiseSIDs)
	if sids != "" {
		sidstr = strings.Split(sids, "_")
	}

	sid := []int{}
	for _, str := range sidstr {
		strInt, err := strconv.Atoi(str)
		if err == nil {
			sid = append(sid, strInt)
		}

	}

	ssid := []string{}
	arrssid, err := f.ctx.Cookie("CS_W_SIDS")
	if err == nil && arrssid != "" {
		ssid = strings.Split(arrssid, "-")
	}

	bodyForm := map[string]interface{}{
		"originquery": "",
		"queryid64":   f.getLogid(),
		"token":       24353513,
		"caller":      "gosug",
		"user_ip":     cip,
		"sid":         sid,
		"ssid":        ssid,
		"cuid":        (*reparams)["uid"],
		"ClientName":  "gosug",
		"user_id":     userID,
		"srcarr":      srcarr,
		"cs_log_id":   f.getLogid(),
	}
	bodyFormByte, _ := json.Marshal(bodyForm)
	requestral := &ral.HTTPRequest{
		Method:    "POST",
		Path:      "singularity.api.us_simple.UsSimpleService/query",
		Body:      string(bodyFormByte),
		Converter: ral.JSONConverter,
		LogID:     f.getLogidStr(),
		Ctx:       f.ctx,
	}

	return requestral
}

// 发起ral请求，获得端返回的数据
func (f *Float) GetFloatResponse(reqparams *map[string]string) error {
	// 封装数据，构建用于请求的HTTPRequest
	requestFloat := f.BuildFloatRequest(reqparams)
	common.DumpData(f.ctx, "2float", requestFloat)
	// 接收请求结果的数据集
	responseFloat := httpResp1{}
	// 发起请求
	if err := ral.Ral(FloatServer, *requestFloat, &responseFloat, ral.JSONConverter); err != nil {
		return err
	}
	return f.parseResponse(responseFloat)
}

// 解析返回的数据
func (f *Float) parseResponse(response httpResp1) error {

	// 解析获得的json格式请求结果，放在ralRespnse中
	ralRespnse := ralFloatRespnse{}
	if err := json.Unmarshal([]byte(response.Raw), &ralRespnse); err != nil {
		return err
	}
	// 结果码不正确
	if ralRespnse.ResultCode != 0 {
		errmsg := "float ResultCode err"
		return errors.New(errmsg)
	}

	// 策略未召回，不算做错误
	if len(ralRespnse.Result) == 0 {
		f.addNotice("floatEmpty", "1")
		return ErrFloat
	}

	// 循环解析结果，将数据转存为map
	typeDispalyMap := make(map[string]FloatTerm)
	for _, v := range ralRespnse.Result {
		if v.SrcID == FloatSrcID {

			var dataTerm struct {
				Rs []FloatTerm `json:"rs"`
			}
			if err := json.Unmarshal([]byte(v.DisplayDataJSON), &dataTerm); err != nil {
				return err
			}
			if len(dataTerm.Rs) == 0 {
				continue
			}
			typeDispalyMap[strconv.Itoa(v.SrcID)] = dataTerm.Rs[0] // 结果只有1个，在第0位
		}
	}
	if len(typeDispalyMap) == 0 {
		errmsg := "float response empty"
		return errors.New(errmsg)
	}

	// 解析后的结果放在成员属性中
	f.setHISTplData(typeDispalyMap)
	return nil
}

// 将解析后的结果放在成员属性hisTplData
func (f *Float) setHISTplData(r map[string]FloatTerm) {
	f.hisTplData = r
}

// 从成员属性hisTplData获得解析后的结果
func (f *Float) GetHISTplData() map[string]FloatTerm {
	return f.hisTplData
}
