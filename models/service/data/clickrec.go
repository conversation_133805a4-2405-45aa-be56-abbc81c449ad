package data

import (
	"encoding/json"
	"errors"
	"net/url"
	"strconv"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type ClickRec struct {
	BaseData
	resData map[string]interface{}
}

type ClickRecParams struct {
	URL         string `json:"url"`
	Word        string `json:"word"`
	RecID       int64  `json:"rec_id"`
	Paragraph   string `json:"paragraph"`
	RequestType string `json:"request_type"`
}

type ClickRecRespnse struct {
	ResultCode int32  `json:"resultcode"`
	ResultNum  uint32 `json:"resultnum"`
	CsLogID    uint64 `json:"cs_log_id"`
	Result     []struct {
		SrcID           int    `json:"srcid"`
		DisplayDataJSON string `json:"displaydata_json"`
	} `json:"result"`
}

type DisplayData struct {
	Rs map[string]interface{} `json:"rs"`
}

var (
	reqType string
	recSA   string
	recID   int64
)

const (
	caller             = "touchrec_gosug"
	getRec             = "get_rec"
	reqPath            = "singularity.api.us_simple.UsSimpleService/query"
	detectRec          = "detect_rec"
	firstWord          = "FIRST_GUIDE"
	token          int = 24353528
	clickRecSrcID  int = 1000545
	clickRecServer     = "sug_his_rec"
	pannelTitle        = "你可能想搜"
)

var ErrClickRec = errors.New("not recall")

func (c *ClickRec) GetClickRecResponse(reqParams *map[string]string) error {
	// 请求必要参数校验
	crp, reqErr := checkReqParams(reqParams)
	if reqErr != nil {
		return reqErr
	}
	// 封装数据，构建用于请求的HTTPRequest
	requestClickRec := c.BuildClickRecRequest(crp)
	common.DumpData(c.ctx, "2ClickRec", requestClickRec)
	// 接收请求结果的数据集
	responseClickRec := httpResp1{}
	// 发起请求
	if err := ral.Ral(clickRecServer, *requestClickRec, &responseClickRec, ral.JSONConverter); err != nil {
		return err
	}
	return c.parseResponse(responseClickRec)
}

func (c *ClickRec) BuildClickRecRequest(crp *ClickRecParams) *ral.HTTPRequest {
	body := map[string]interface{}{
		"originquery":     crp.Word,
		"content_req_url": crp.URL,
		"token":           token,
		"caller":          caller,
		"srcarr": []map[string]interface{}{
			{
				"srcid": clickRecSrcID,
			},
		},
		"cs_log_id": c.getLogid(),
	}
	if reqType == getRec {
		body["content_req_extend"] = crp.Paragraph
	}
	bodyFormByte, _ := json.Marshal(body)
	req := &ral.HTTPRequest{
		Method:    "POST",
		Path:      reqPath,
		Body:      string(bodyFormByte),
		Converter: ral.JSONConverter,
		LogID:     c.getLogidStr(),
		Ctx:       c.ctx,
	}
	return req
}

func (c *ClickRec) parseResponse(response httpResp1) error {
	// 解析获得的json格式请求结果，放在ralRespnse中
	ralRespnse := ClickRecRespnse{}
	if err := json.Unmarshal([]byte(response.Raw), &ralRespnse); err != nil {
		return err
	}
	if ralRespnse.ResultCode != 0 {
		return errors.New("ResultCode error: " + strconv.FormatInt(int64(ralRespnse.ResultCode), 10))
	}
	if len(ralRespnse.Result) == 0 {
		return ErrClickRec
	}
	for _, res := range ralRespnse.Result {
		if res.SrcID != clickRecSrcID {
			continue
		}
		data := DisplayData{}
		if err := json.Unmarshal([]byte(res.DisplayDataJSON), &data); err != nil {
			return err
		}
		if data.Rs == nil {
			return errors.New("data Rs is nil")
		}
		// 区分第一次、第二次请求分别处理
		if reqType == detectRec {
			if err := c.parseDetectRecResponse(data.Rs); err != nil {
				return err
			}
		} else if reqType == getRec {
			if err := c.parseGetRecResponse(data.Rs); err != nil {
				return err
			}
		}
	}
	return nil
}

func (c *ClickRec) GetTplData() map[string]interface{} {
	return c.resData
}

func NewClickRec(ctx *gdp.WebContext) *ClickRec {
	c := ClickRec{}
	c.ctx = ctx
	c.resData = make(map[string]interface{})
	return &c
}

func checkReqParams(reqParams *map[string]string) (*ClickRecParams, error) {
	// 参数校验
	crp := ClickRecParams{}
	reqData := (*reqParams)["data"]
	if reqData == "" {
		return &crp, errors.New("reqData is nil")
	}
	jsonErr := json.Unmarshal([]byte(reqData), &crp)
	if jsonErr != nil {
		return &crp, errors.New("reqData json Unmarshal fail")
	}
	requestType := crp.RequestType
	if requestType == "" {
		return &crp, errors.New("req request_type is nil")
	}
	if crp.URL == "" {
		return &crp, errors.New(requestType + " req url is nil")
	}
	// 请求黑名单校验 url + 站点
	if !checkBlackList(crp.URL) {
		return &crp, errors.New("url check fail: " + crp.URL)
	}
	if requestType == getRec {
		if crp.Word == "" {
			return &crp, errors.New(requestType + " req word is nil")
		}
		if crp.Paragraph == "" {
			return &crp, errors.New(requestType + " req paragraph is nil")
		}
	} else if requestType == detectRec {
		crp.Word = firstWord
	} else {
		return &crp, errors.New("req request_type illegal: " + requestType)
	}
	// 更新全局变量
	reqType = requestType
	recID = crp.RecID
	return &crp, nil
}

func (c *ClickRec) parseDetectRecResponse(rsData map[string]interface{}) error {
	hasRec, _ := rsData["has_rec"].(string)
	if hasRec == "" || hasRec == "0" {
		// 策略未覆盖返回默认结果
		c.resData = map[string]interface{}{
			"has_rec": "0",
			"key":     "",
			"context": "",
			"rec_id":  recID,
		}
		return ErrClickRec
	}
	c.resData["has_rec"] = hasRec
	c.resData["rec_id"] = recID
	if matchKey, ok := rsData["match_key"].(string); ok {
		c.resData["key"] = matchKey
	}
	if context, ok := rsData["context"].(string); ok {
		c.resData["context"] = context
	}
	return nil
}

func (c *ClickRec) parseGetRecResponse(rsData map[string]interface{}) error {
	hasRec, _ := rsData["has_rec"].(string)
	if hasRec == "" || hasRec == "0" {
		// 策略未覆盖返回默认结果
		c.resData = map[string]interface{}{
			"has_rec":        "0",
			"rec_info":       []map[string]interface{}{},
			"pannel_title":   "",
			"rec_sa":         "",
			"highlight_mode": "",
			"rec_id":         recID,
		}
		return ErrClickRec
	}
	if recSa, ok := rsData["rec_sa"].(string); ok {
		c.resData["rec_sa"] = recSa
		recSA = recSa
	}
	if mode, ok := rsData["highlight_mode"].(string); ok {
		c.resData["highlight_mode"] = mode
	}
	if recQuery, ok := rsData["rec_query"].([]interface{}); ok {
		recInfo := []map[string]interface{}{}
		for _, v := range recQuery {
			word, _ := v.(string)
			if word == "" {
				continue
			}
			vInfo := map[string]interface{}{
				"query":      word,
				"uri_search": createLink(word),
			}
			recInfo = append(recInfo, vInfo)
		}
		c.resData["rec_info"] = recInfo
	}
	c.resData["has_rec"] = hasRec
	c.resData["rec_id"] = recID
	c.resData["pannel_title"] = pannelTitle
	return nil
}

func createLink(word string) string {
	word = RemoveEmojis(word)
	linkURL := "https://m.baidu.com/s?word=" + url.QueryEscape(word) + "&sa=" + recSA
	return "baiduboxapp://v1/browser/open?url=" + url.QueryEscape(linkURL)
}

func checkBlackList(link string) bool {
	u, err := url.Parse(link)
	if err != nil {
		return false
	}
	if background.CPageClickRec.IsHostMatch(u.Host) || background.CPageClickRec.IsURLMatch(link) {
		return false
	}

	return true
}

// 过滤掉表情符号的函数
func RemoveEmojis(text string) string {
	var result []rune
	for _, r := range text {
		if !IsEmoji(r) {
			result = append(result, r)
		}
	}
	return string(result)
}

// 判断字符是否是表情符号
func IsEmoji(r rune) bool {
	// 使用 Unicode 码点范围来判断是否为表情符号
	return (r >= 0x1F600 && r <= 0x1F64F) || // Emoticons
		(r >= 0x1F300 && r <= 0x1F5FF) || // Miscellaneous Symbols and Pictographs
		(r >= 0x1F680 && r <= 0x1F6FF) || // Transport and Map Symbols
		(r >= 0x1F700 && r <= 0x1F77F) || // Alchemical Symbols
		(r >= 0x1F780 && r <= 0x1F7FF) || // Geometric Shapes Extended
		(r >= 0x1F800 && r <= 0x1F8FF) || // Supplemental Arrows-C
		(r >= 0x1FA00 && r <= 0x1FA6F) || // Chess Symbols
		(r >= 0x1FA70 && r <= 0x1FAFF) || // Symbols and Pictographs Extended-A
		(r >= 0x2600 && r <= 0x26FF) || // Miscellaneous Symbols
		(r >= 0x2700 && r <= 0x27BF) || // Dingbats
		(r >= 0xFE00 && r <= 0xFE0F) || // Variation Selectors
		(r >= 0x1F900 && r <= 0x1F9FF) || // Supplemental Symbols and Pictographs
		(r >= 0x1F1E6 && r <= 0x1F1FF) // Flags (iOS)
}
