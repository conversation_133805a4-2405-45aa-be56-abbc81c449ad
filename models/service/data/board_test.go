package data

import (
	"encoding/json"
	"net/url"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"gopkg.in/go-playground/assert.v1"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

func TestGetSaFromUrl(t *testing.T) {
	v := "https://m.baidu.com/s?word=%E5%9B%BD%E5%AE%B6%E4%BD%93%E8%82%B2%E6%80%BB%E5%B1%80%E5%8E%9F%E5%B" +
		"1%80%E9%95%BF%E8%8B%9F%E4%BB%B2%E6%96%87%E8%A2%AB%E6%9F%A5&sa=fyb_realtime_his_incognito_prec_inc_1&from=his_incognito"
	assert.Equal(t, getSaFromUrl(v), "fyb_realtime_his_incognito_prec_inc_1")

	v = ""
	assert.Equal(t, getSaFromUrl(v), "")

	v = "asdfdaf"
	assert.Equal(t, getSaFromUrl(v), "")
}

func TestModifyItemUrl(t *testing.T) {
	one := map[string]interface{}{
		"url": "https://m.baidu.com/s?word=%E5%9B%BD%E5&sa=fyb_hp_news_his_incognito",
		"appUrl": "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3D%25E4%25B9%25A0%25E8%25BE%25B7%25E4%25BC%259A%25E8%" +
			"25B0%2588%26sa%3Dfyb_hp_news_his_incognito%26from%3Dhis",
	}
	b := BoardRec{
		PersonRec:     "0",
		IncogniteMode: "1",
	}
	b.ctx = createGetWebContext()
	reqParam := map[string]string{
		"intelligent_mode": "0",
	}
	b.ctx.Set(constant.REQPARAMS, reqParam)
	bodyParams := BodyParams{
		EntranceSa: "ho",
	}
	b.modifyItemUrl(one, 0, "", bodyParams)
	assert.Equal(t, one["url"], "https://m.baidu.com/s?word=%E5%9B%BD%E5&sa=fyb_hp_news_his_incognito_prec_inc_ho_1")
	assert.Equal(t, one["appUrl"], "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3D%25E4%25B9%25A0%25E8%25BE%25B"+
		"7%25E4%25BC%259A%25E8%25B0%2588%26sa%3Dfyb_hp_news_his_incognito_prec_inc_ho_1%26from%3Dhis")
	assert.Equal(t, one["sa"], "fyb_hp_news_his_incognito_prec_inc_ho_1")
	reqParam["intelligent_mode"] = "1"
	// 奥运榜
	b.modifyItemUrl(one, 0, "olympic", bodyParams)
	assert.Equal(t, one["url"], "https://m.baidu.com/s?word=%E5%9B%BD%E5&sa=fyb_hp_olympic_news_his_prec_inc_ho_aimode_1")
	assert.Equal(t, one["appUrl"], "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fm.baidu.com%2Fs%3Fword%3D%25E4%25B9%25A0%25E8%25BE%25B"+
		"7%25E4%25BC%259A%25E8%25B0%2588%26sa%3Dfyb_hp_olympic_news_his_prec_inc_ho_aimode_1%26from%3Dhis")
	assert.Equal(t, one["sa"], "fyb_hp_olympic_news_his_prec_inc_ho_aimode_1")

}

func TestGetParams(t *testing.T) {
	b := BoardRec{}
	b.ctx = createGetWebContext()

	// 大字版低版本
	adaption := make(map[string]string)
	adaption["bd_version"] = "2.30"
	b.ctx.Set(constant.ADAPARAMS, adaption)
	values, _ := b.getParams("a7", "")
	assert.Equal(t, values.Get("from"), "his_big")

	// 大字版2.31版本
	adaption["bd_version"] = "2.31"
	b.ctx.Set(constant.ADAPARAMS, adaption)
	values, _ = b.getParams("a7", "")
	assert.Equal(t, values.Get("from"), "his")
}

func TestBoardNewFunc(t *testing.T) {
	_ = t
	b := BoardRec{}
	b.ctx = createGetWebContext()
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFuncReturn(ral.Ral, nil)
	b.MultiRequest([]NamedHTTPReq{{Name: "123", HTTPRequest: &protocol.HTTPRequest{}}}, make(chan ChRawHTTPResp, 1))
	reqParams, adpterParams := make(map[string]string), make(map[string]string)
	_ = b.GetResponse(&reqParams, &adpterParams, "12")
	reqParams[constant.OSBRANCH] = "12.12"
	_ = b.GetResponse(&reqParams, &adpterParams, "12")
	patches.ApplyFuncReturn((url.Values).Get, "123")
	_ = b.GetResponse(&reqParams, &adpterParams, "12")

}

func TestParseLeaderBoard(t *testing.T) {
	b := BoardRec{}
	b.ctx = createGetWebContext()
	// 旧热榜协议
	body := `
	{
		"success": true,
		"data": {
			"cards": [
				{
					"boardInfo": {
						"boardIcon": "https://search-operate.cdn.bcebos.com/42e626c5469cc4cd3b91131bcfe53d44.png",
						"titleColor": "#FFFF4545",
						"titleColorNight": "#FF802222",
						"TitleColorDark": "#FFFF4545",
						"backgroundStartColor": "#05FF4545",
						"backgroundStartColorNight": "#05802222",
						"BackgroundStartColorDark": "#05FF4545",
						"backgroundEndColor": "#08FF4545",
						"backgroundEndColorNight": "#08802222",
						"backgroundEndColorDark": "#08FF4545"
					},
				
					"component": "hotList",
					"content": [
						{
							"appUrl": "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fwww.baidu.com%2Fs%3Fwd%3D%25E5%2590%2589%25E6%259E%2597%25E4%25B8%2580%25E6%259C%25BA%25E5%2585%25B3%25E5%258D%2595%25E4%25BD%258D%25E5%25A4%25A7%25E6%25A5%25BC%25E5%258F%2591%25E7%2594%259F%25E5%259D%258D%25E5%25A1%258C%26sa%3Dfyb_hp_livelihood_his%26rsv_dl%3Dfyb_hp_livelihood_his%26from%3Dhis",
							"desc": "",
							"hotChange": "same",
							"hotScore": "2578",
							"hotTag": "0",
							"img": "",
							"index": 19,
							"show": [],
							"url": "https://www.baidu.com/s?wd=%E5%90%89%E6%9E%97%E4%B8%80%E6%9C%BA%E5%85%B3%E5%8D%95%E4%BD%8D%E5%A4%A7%E6%A5%BC%E5%8F%91%E7%94%9F%E5%9D%8D%E5%A1%8C&sa=fyb_hp_livelihood_his&rsv_dl=fyb_hp_livelihood_his&from=his",
							"word": "吉林一机关单位大楼发生坍塌"
						}
					],
					"moreUrl": "https://top.baidu.com/board?tab=livelihood&sa=fyb_livelihood_his",
					"text": "民生榜",
					"topContent": null
				},
				{
					"boardInfo": {
						"boardIcon": "https://search-operate.cdn.bcebos.com/42e626c5469cc4cd3b91131bcfe53d44.png",
						"titleColor": "#FFFF4545",
						"titleColorNight": "#FF802222",
						"TitleColorDark": "#FFFF4545",
						"backgroundStartColor": "#05FF4545",
						"backgroundStartColorNight": "#05802222",
						"BackgroundStartColorDark": "#05FF4545",
						"backgroundEndColor": "#08FF4545",
						"backgroundEndColorNight": "#08802222",
						"backgroundEndColorDark": "#08FF4545"
					},
					"boardKey": "challenge",
					"component": "textImgListVerticalSmall",
					"content": [
						{
							"RightButtonInfo": {
								"position": "",
								"title": "去参与",
								"color": "#FFFF3333",
								"nightColor": "#FFFF3333",
								"darkColor": "#FFFF3333",
								"bgColor": "#0DFF3333",
								"bgNightColor": "#0DFF3333",
								"bgDarkColor": "#26FF3333",
								"cmd": "baiduboxapp://v6/ugc/publish?upgrade=1&params=%7B%22timer_count%22%3A%20%5B%223%22%2C%20%2210%22%5D%2C%20%22music_pageurl%22%3A%20%22https%3A//sv.baidu.com/feedvideoui/view/videomusic%22%2C%20%22topic_pageurl%22%3A%20%22baiduboxapp%3A//v1/easybrowse/open%3Fnewbrowser%3D1%26url%3Dhttps%253A%252F%252Fmbd.baidu.com%252Fwebpage%253Ftype%253Dtopic%2526action%253Dsearch%2526from%253Dugctopic%22%2C%20%22placeholder%22%3A%20%22%5Cu70b9%5Cu51fb%5Cu8fd9%5Cu91cc%5Cuff0c%5Cu5206%5Cu4eab%5Cu65b0%5Cu9c9c%5Cu4e8b%5Cu2026%22%2C%20%22publishType%22%3A%203%2C%20%22ai_style_id%22%3A%20%22a0a281f5-3d56-440a-b508-be5e4e896f37%22%2C%20%22ugcCallback%22%3A%20%22home_ugc_callback_publish_success%22%2C%20%22at_pageurl%22%3A%20%22baiduboxapp%3A//v1/easybrowse/open%3Fnewbrowser%3D1%26url%3Dhttps%253A%252F%252Fmbd.baidu.com%252Fwebpage%253Ftype%253Dtopic%2526action%253Dat%2526from%253Dugctopic%22%2C%20%22source_type%22%3A%200%2C%20%22record_type%22%3A%20%221%22%2C%20%22source_from%22%3A%20%22video_tiaozhan%22%2C%20%22display_scene%22%3A%20%22video_tiaozhan%22%2C%20%22poi%22%3A%20%7B%22auto_locate%22%3A%20%221%22%7D%2C%20%22duration%22%3A%20%7B%22max%22%3A%2020%2C%20%22min%22%3A%203%7D%2C%20%22camera_buttons%22%3A%20%5B%22timer%22%2C%20%22speed%22%2C%20%22face%22%2C%20%22filter%22%2C%20%22sticker%22%2C%20%22music%22%5D%2C%20%22asyncUpload%22%3A%201%2C%20%22show_toast%22%3A%20%223%22%2C%20%22tiaozhanInfo%22%3A%20%7B%22style%22%3A%20%222%22%2C%20%22bottomText%22%3A%20%22%5Cu52a0%5Cu5165%5Cu6311%5Cu6218%22%2C%20%22title%22%3A%20%22%5Cu4f60%5Cu597d%20%5Cu971c%5Cu964d%22%2C%20%22bottomIcon%22%3A%20%22https%3A//b.bdstatic.com/searchbox/image/gcp/20230919/2819488169.png%22%2C%20%22bottomTextColor%22%3A%20%22%23FD503E%22%2C%20%22banner%22%3A%20%7B%22title%22%3A%20%22%5Cu4f60%5Cu597d%20%5Cu971c%5Cu964d%22%2C%20%22icon%22%3A%20%22https%3A//b.bdstatic.com/searchbox/image/gcp/20230919/2819488169.png%22%7D%2C%20%22id%22%3A%20%2217255065557508%22%7D%7D"
							},
							"appUrl": "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fwww.baidu.com%2Fs%3Fwd%3DAI%25E5%258F%25A4%25E9%25A3%258E%25E6%25B0%25B4%25E5%25A2%25A8%25E7%2594%25BB%26sa%3Dfyb_hp_challenge_his%26rsv_dl%3Dfyb_hp_challenge_his%26from%3Dhis",
							"desc": "",
							"hotChange": "same",
							"hotScore": "3",
							"hotTag": "0",
							"img": "http://pics5.baidu.com/feed/1f178a82b9014a903080c3b7ff4c011cb21beecc.jpeg?token=ac974b7f2cde2cd7dac74b65301a7147",
							"imgRatio": 1,
							"imgWidthAnd": 49,
							"imgWidthIOS": 57,
							"index": 9,
							"layout": "hot_search_content_btn",
							"positionType": 0,
							"rightComponentType": "btn",
							"show": [
								"25055人在看"
							],
							"url": "https://www.baidu.com/s?wd=AI%E5%8F%A4%E9%A3%8E%E6%B0%B4%E5%A2%A8%E7%94%BB&sa=fyb_hp_challenge_his&rsv_dl=fyb_hp_challenge_his&from=his",
							"word": "AI古风水墨画"
						}
					],
					"moreUrl": "https://top.baidu.com/board?tab=challenge&sa=fyb_challenge_his",
					"text": "挑战榜",
					"topContent": [
						{
							"appUrl": "baiduboxapp://v1/browser/open?url=https%3A%2F%2Fwww.baidu.com%2Fs%3Fwd%3D%25E4%25B9%25A0%25E8%25BF%2591%25E5%25B9%25B3%25E5%259C%25A8%25E9%2587%2591%25E7%25A0%2596%252B%25E9%25A2%2586%25E5%25AF%25BC%25E4%25BA%25BA%25E5%25AF%25B9%25E8%25AF%259D%25E4%25BC%259A%25E7%259A%2584%25E8%25AE%25B2%25E8%25AF%259D%26sa%3Dfyb_hp_news_his%26rsv_dl%3Dfyb_hp_news_his%26from%3Dhis",
							"desc": "当地时间10月24日，国家主席习近平在喀山会展中心出席“金砖+”领导人对话会，并发表题为《汇聚“全球南方”磅礴力量 共同推动构建人类命运共同体》的重要讲话。",
							"expression": "",
							"hotChange": "same",
							"hotScore": "4940037",
							"hotTag": "1",
							"img": "https://fyb-2.cdn.bcebos.com/hotboard_image/87bf9aa0421fb52de4b1fb5b1f317725",
							"index": 0,
							"positionType": 1,
							"show": [],
							"url": "https://www.baidu.com/s?wd=%E4%B9%A0%E8%BF%91%E5%B9%B3%E5%9C%A8%E9%87%91%E7%A0%96%2B%E9%A2%86%E5%AF%BC%E4%BA%BA%E5%AF%B9%E8%AF%9D%E4%BC%9A%E7%9A%84%E8%AE%B2%E8%AF%9D&sa=fyb_hp_news_his&rsv_dl=fyb_hp_news_his&from=his",
							"word": "习近平在金砖+领导人对话会的讲话"
						}
					]
				}
			]
		}
	}`
	err := b.parseLeaderboard(rawResp{Body: []byte(body)})
	assert.Equal(t, err, nil)
	adaption := make(map[string]string)
	adaption["bd_version"] = "13.72.0.0"
	b.ctx.Set(constant.ADAPARAMS, adaption)
	err = b.parseLeaderboard(rawResp{Body: []byte(body)})
	assert.Equal(t, err, nil)
}

func TestGetTagStyle(t *testing.T) {
	dataHotTag := `
	{
		"desc": "",
		"expression": "",
		"hotChange": "same",
		"hotScore": "6122229",
		"hotTag": "3",
		"img": "https://fyb-1.cdn.bcebos.com/fyb/de6163834f53ca92c1273fff98ac9078.jpeg",
		"index": 18,
		"positionType": 0,
		"resourceDarkTag": null,
		"resourceDayTag": null,
		"resourceNightTag": null,
		"show": [],
		"word": "女子举报丈夫开车看美女 交警回应"
	}`
	dataLiveTag := `
	{
		"desc": "",
		"expression": "",
		"hotChange": "same",
		"hotScore": "7181996",
		"hotTag": "0",
		"img": "https://fyb-1.cdn.bcebos.com/fyb/444846eeb217ede8c838d13ae8c9c9b0.jpg",
		"index": 8,
		"positionType": 0,
		"resourceDarkTag": {
			"label_image": "https://b.bdstatic.com/searchbox/image/gcp/20241205/339702131.gif",
			"height": "54",
			"width": "161",
			"label_position": "right",
			"label_image_pag": "https://b.bdstatic.com/searchbox/image/gcp/20241205/1875888742.pag"
		},
		"resourceDayTag": {
			"label_image": "https://b.bdstatic.com/searchbox/image/gcp/20241205/4201351533.gif",
			"height": "54",
			"width": "161",
			"label_position": "right",
			"label_image_pag": "https://b.bdstatic.com/searchbox/image/gcp/20241205/1553347553.pag"
		},
		"resourceNightTag": {
			"label_image": "https://b.bdstatic.com/searchbox/image/gcp/20241205/684036407.gif",
			"height": "54",
			"width": "161",
			"label_position": "right",
			"label_image_pag": "https://b.bdstatic.com/searchbox/image/gcp/20241205/1937799934.pag"
		},
		"show": [],
		"word": "#美女老外穿汉服打卡黄山云海#"
	}`

	oneCard := map[string]interface{}{}
	_ = json.Unmarshal([]byte(dataHotTag), &oneCard)

	tagHot := map[string]interface{}{
		"image":     "https://gips2.baidu.com/it/u=2781706582,3786794527&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f48_54",
		"tag_type":  2,
		"w_h_ratio": 0.89,
	}
	tagStyle, _ := getTagStyle(oneCard, "*********", "a0")
	assert.Equal(t, tagStyle, tagHot)
	tagLive := map[string]interface{}{
		"tag_type":      4,
		"pag_url":       "https://b.bdstatic.com/searchbox/image/gcp/20241205/1553347553.pag",
		"pag_url_dark":  "https://b.bdstatic.com/searchbox/image/gcp/20241205/1875888742.pag",
		"pag_url_night": "https://b.bdstatic.com/searchbox/image/gcp/20241205/1937799934.pag",
		"width_and":     47,
		"width_ios":     54,
		"height_ios":    18,
		"height_and":    16,
	}
	_ = json.Unmarshal([]byte(dataLiveTag), &oneCard)
	tagStyle, _ = getTagStyle(oneCard, "*********", "a0")
	assert.Equal(t, tagStyle, tagLive)
}
