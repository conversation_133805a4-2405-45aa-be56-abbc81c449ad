package data

import (
	"encoding/json"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const (
	RealtimeRecServer = "realtime_rec"
	Caller            = "gosug"
	Token             = uint32(24353513)
)

const (
	TextSearchRec = "1"
	IMGSearchRec  = "2"
	AfterClkRec   = "3"
)

var (
	VaildReqType = []string{TextSearchRec, IMGSearchRec, AfterClkRec}
	srcForSA     = map[string]string{
		TextSearchRec: "easy_search_rs_text",
		IMGSearchRec:  "easy_search_rs_image",
		AfterClkRec:   "easy_search_clk",
	}
)

type RealtimeRec struct {
	BaseData
	RecTplData RealtimeRecResp
}

type RealtimeRecRequest struct {
	Query        string `json:"query"`
	ReqType      string `json:"rec_type"`
	Cuid         string `json:"cuid"`
	Sid          string `json:"sid"`
	Lid          string `json:"lid"`
	OriLid       string `json:"ori_lid"`
	PromKey      string `json:"prom_key"`
	CpageUrl     string `json:"cpage_url"`
	CpageContent string `json:"cpage_content"`
	ResultIndex  int32  `json:"result_index"`
	Sign         string `json:"sign"`
	Viscate      string `json:"viscate"`
	Guess        string `json:"guessword"`
	IsMllm       int    `json:"isMllm"`
	OcrResult    string `json:"ocrResult"`
}

type RealtimeRecResp struct {
	Errno  string  `json:"err_no"`
	Errmsg string  `json:"err_msg"`
	LogID  string  `json:"log_id"`
	Data   RecData `json:"data"`
}

type RecData struct {
	Suggestion SuggestionData `json:"suggestion"`
}

type SuggestionData struct {
	SugList []SugItem `json:"suglist"`
}

type SugItem struct {
	Qtype int    `json:"qtype"`
	Pos   int    `json:"pos"`
	Word  string `json:"word"`
	Sa    string `json:"sa"`
}

type RecResp struct {
	ResultCode    int           `json:"resultcode"`
	ResultNum     int           `json:"resultnum"`
	CsLogId       uint64        `json:"cs_log_id"`
	ContentResult []*ContentRes `json:"content_result"`
}

type ContentRes struct {
	SrcID    int    `json:"srcid"`
	TermList []Term `json:"term_list"`
}

type Term struct {
	Pos     int32  `json:"pos"`
	Word    string `json:"term"`
	Feature string `json:"feature"`
	Sa      string `json:"sa"`
}

func NewRealtimeRec(ctx *gdp.WebContext) *RealtimeRec {
	dataSrv := RealtimeRec{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// 新简搜才能走这条通路请求推荐
func (r *RealtimeRec) IsChatSearch() bool {
	ua := r.getHTTPRequest().UserAgent()
	return strings.Contains(ua, "ChatSearch")
}

func (r *RealtimeRec) IsBdbox() bool {
	ua := r.getHTTPRequest().UserAgent()
	return strings.Contains(ua, "baiduboxapp")
}

// 获取推荐Response
func (r *RealtimeRec) GetRTRecResponse(reqParams *map[string]string, reqData *RealtimeRecRequest) error {
	req, err := r.buildRTRecRequest(reqParams, reqData)
	common.DumpData(r.ctx, "2realtime_rec", req)

	if err != nil {
		return err
	}
	resp := rawResp{}
	err = ral.Ral(RealtimeRecServer, *req, &resp, ral.RAWConverter)
	if err != nil {
		return err
	}

	var recResp RecResp
	err = json.Unmarshal(resp.Body, &recResp)
	if err != nil {
		return err
	}
	return r.parseResponse(recResp)
}

// 解析推荐Response
func (r *RealtimeRec) parseResponse(res RecResp) error {
	// 请求推荐失败
	if res.ResultCode == -1 {
		r.RecTplData = RealtimeRecResp{
			Errno:  "1006",
			Errmsg: "rec ral error",
			LogID:  r.ctx.GetLogID(),
		}
		return nil
	}

	// 请求成功
	r.RecTplData = RealtimeRecResp{
		Errno:  "0",
		Errmsg: "success",
		LogID:  r.ctx.GetLogID(),
	}

	// 无推荐结果
	if res.ResultNum == 0 {
		return nil
	}

	var sugList []SugItem

	blankSugNum := 0
	// 有推荐结果
	for _, resItem := range res.ContentResult {
		for _, item := range resItem.TermList {
			sugList = append(sugList, SugItem{
				Qtype: 0,
				Pos:   resItem.SrcID,
				Word:  item.Word,
				Sa:    item.Sa,
			})
		}
		// 只统计空框推荐
		if resItem.SrcID == 0 {
			blankSugNum = len(resItem.TermList)
		}
	}
	if r.IsBdbox() {
		r.addNotice("blank_sug_num", strconv.Itoa(blankSugNum))
	}
	r.RecTplData.Data.Suggestion.SugList = sugList
	return nil
}

// 构建推荐http请求
func (r *RealtimeRec) buildRTRecRequest(request *map[string]string, req *RealtimeRecRequest) (*ral.HTTPRequest, error) {
	reqType := req.ReqType

	uid := (*request)[constant.SESSION_UID]
	userID, err := strconv.ParseInt(uid, 10, 64)

	if err != nil {
		userID = 0
	}

	lid, err := strconv.ParseUint(req.Lid, 10, 64)
	if err != nil {
		return nil, err
	}

	srcArr := []map[string]interface{}{
		{
			"srcid": 1000533,
		},
	}

	// 点后推srcid
	if reqType == AfterClkRec {
		srcArr[0]["srcid"] = 1000534
	}

	// 获取sid，本次应该没有
	sid := r.getSid()

	bodyForm := map[string]interface{}{
		"originquery":   req.Query,
		"caller":        Caller,
		"token":         Token,
		"user_id":       userID,
		"cuid":          (*request)["uid"],
		"sid":           sid,
		"queryid64":     lid,
		"cs_log_id":     lid,
		"ori_lid":       req.OriLid,
		"prom_key":      req.PromKey,
		"source_for_sa": srcForSA[reqType],
		"srcarr":        srcArr,
	}

	// 图搜额外参数
	if reqType == IMGSearchRec {
		bodyForm["image_info"] = map[string]interface{}{
			"sign":       req.Sign,
			"viscate":    req.Viscate,
			"guess":      req.Guess,
			"is_mllm":    req.IsMllm,
			"ocr_result": req.OcrResult,
		}
	}

	// 点后推额外参数
	if reqType == AfterClkRec {
		CpageContent := req.CpageContent
		if len(req.CpageContent) > 20000 {
			CpageContent = req.CpageContent[:20000]
		}
		bodyForm["content"] = CpageContent
		bodyForm["content_req_url"] = req.CpageUrl
		bodyForm["result_index"] = req.ResultIndex
	}

	bodyFromByte, err := json.Marshal(bodyForm)

	if err != nil {
		return nil, err
	}

	// 构建http请求
	req1 := &ral.HTTPRequest{
		Method:    "POST",
		Path:      "singularity.api.us_simple.UsSimpleService/query",
		Body:      string(bodyFromByte),
		Converter: ral.JSONConverter,
		LogID:     r.ctx.GetLogID(),
		Ctx:       r.ctx,
	}
	return req1, nil
}

// 获取sid
func (r *RealtimeRec) getSid() []int {
	sidstr := []string{}
	bdsid, err := r.ctx.Cookie("H_WISE_SIDS")
	if err == nil && bdsid != "" {
		sidstr = strings.Split(bdsid, "_")
	}

	sids := r.ctx.GetString(constant.HWiseSIDs)
	if sids != "" {
		sidstr = strings.Split(sids, "_")
	}

	sid := []int{}
	for _, str := range sidstr {
		strInt, err := strconv.Atoi(str)
		if err == nil {
			sid = append(sid, strInt)
		}
	}
	return sid
}
