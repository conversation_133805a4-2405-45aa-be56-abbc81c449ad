//http://wiki.baidu.com/pages/viewpage.action?pageId=590990038
//http://wiki.baidu.com/pages/viewpage.action?pageId=576601410

package data

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewOpenSugNew(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewOpenSugNew ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := OpenSugNew{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*OpenSugNew
	}{
		{
			"TestNewOpenSugNew",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewOpenSugNew(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewOpenSugNew, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*OpenSugNew))
	}
	ag.Run()
}

func TestOpenSugNew_BuildOpenSugRequest(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		query string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildOpenSugRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &OpenSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.BuildOpenSugRequest(tt.args.query)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestOpenSugNew_BuildOpenSugRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(ral.HTTPRequest))
	}
	ag.Run()
}

func TestOpenSugNew_ParseOpenSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		query    string
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseOpenSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	es := Wisesugnew_Response{}
	rs := resp{}
	hrs := httpResp{}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestOpenSugNew_ParseOpenSugResponse",
			fields{
				baseData,
				es,
				rs,
			},
			args{
				"test",
				hrs,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &OpenSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		err := this.ParseOpenSugResponse(tt.args.query, tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestOpenSugNew_ParseOpenSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestOpenSugNew_decodeOpenSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		jsonstring []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: decodeOpenSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Wisesugnew_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &OpenSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got, err := this.decodeOpenSugResponse(tt.args.jsonstring)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestOpenSugNew_decodeOpenSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestOpenSugNew_decodeOpenSugResponse, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesugnew_Response))
	}
	ag.Run()
}

func TestOpenSugNew_setOpensugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		r Wisesugnew_Response
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setOpensugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &OpenSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		this.setOpensugTplData(tt.args.r)
	}
	ag.Run()
}

func TestOpenSugNew_GetOpensugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetOpensugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Wisesugnew_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &OpenSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.GetOpensugTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestOpenSugNew_GetOpensugTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesugnew_Response))
	}
	ag.Run()
}
