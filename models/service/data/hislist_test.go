package data

import (
	"errors"
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewHISLIST(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewHISLIST ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := HISLIST{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*HISLIST
	}{
		{
			"TestNewHISLIST",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewHISLIST(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewHISLIST, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*HISLIST))
	}
	ag.Run()
}

func TestHISLIST_ParseHisListRalResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		chRes ChRawHTTPResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHisListRalResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		err := this.ParseHisListRalResponse(tt.args.chRes)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_ParseHisListRalResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHISLIST_ParsehisPrivRalResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		chRes ChRawHTTPResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParsehisPrivRalResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	rs := map[string]HisPriveRALResponse{}
	rs1 := HisListResponse{}
	hrs := rawResp{}
	crs := ChRawHTTPResp{}

	crs1 := ChRawHTTPResp{}
	crs1.Err = errors.New("test")

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestHISLIST_ParsehisPrivRalResponse",
			fields{
				baseData,
				rs,
				rs1,
				hrs,
				hrs,
			},
			args{
				crs,
			},
			true,
		},
		{
			"TestHISLIST_ParsehisPrivRalResponse",
			fields{
				baseData,
				rs,
				rs1,
				hrs,
				hrs,
			},
			args{
				crs1,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		err := this.ParsehisPrivRalResponse(tt.args.chRes)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_ParsehisPrivRalResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHISLIST_MutiRequest(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		rs []NamedHTTPReq
		ch chan ChRawHTTPResp
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: MutiRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		this.MultiRequest(tt.args.rs, tt.args.ch)
	}
	ag.Run()
}

func TestHISLIST_BuildHisListRequest(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	hisPriTplData := map[string]HisPriveRALResponse{}
	hisListTplData := HisListResponse{}
	hisListRalData := rawResp{}
	hisPrivRalData := rawResp{}

	mockParams := make(map[string]string)
	mockParams["lid"] = "15632471522506912039"
	mockParams["pt"] = "1562567664"
	mockParams["osbranch"] = "a0"
	mockParams["uid"] = "gi2kig8zvujjaHtH_uS3a0OH-a_Aa2a20uvg8lu_vuKaLiqlB"

	mockAdaption := make(map[string]string)
	mockAdaption["bd_version"] = "11.12.0.0"
	mockAdaption["device_dpi"] = "3"
	mockAdaption["isAndroid"] = "1"
	mockAdaption["resolution_width"] = "1080"
	mockUid := "1092508088"
	type args struct {
		mockParams   *map[string]string
		mockAdaption *map[string]string
		mockUid      string
	}
	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}
	values := map[string][]string{
		"lid":       {"15632471522506912039"},
		"cuid":      {"gi2kig8zvujjaHtH_uS3a0OH-a_Aa2a20uvg8lu_vuKaLiqlB"},
		"from":      {"kuang"},
		"pid":       {"1092508088"},
		"cs_log_id": {"1"},
		"rf":        {"2"},
		"baiduid":   {""},
		"hisdata":   {""},
		"ksid":      {""},
		"pic":       {"1"},
		"prod":      {"baiduapp_his"},
		"sid":       {""},
		"type":      {"5"},
		"ua":        {""},
	}

	v := ral.HTTPRequest{
		Header:      map[string][]string(nil),
		Method:      "GET",
		Path:        "singularity.api.sug.SugService/query",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       "1",
		Ctx:         ctx,
	}
	utInst := ut.New(t, "Unit tests for FUNCTION: BuildHisListRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		{
			"TestWisesugnew_BuildWiseSugRequest",
			fields{
				baseData,
				hisPriTplData,
				hisListTplData,
				hisListRalData,
				hisPrivRalData,
			},
			args{
				&mockParams,
				&mockAdaption,
				mockUid,
			},
			Want{&v, ut.ShouldResemble},
		},
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		got := this.BuildHisListRequest(tt.args.mockParams, tt.args.mockAdaption, tt.args.mockUid)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_BuildHisListRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*ral.HTTPRequest))
	}
	ag.Run()
}

func TestHISLIST_BuildPrivRequest(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		reqparams   *map[string]string
		adpterprams *map[string]string
		uid         string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildPrivRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		got := this.BuildPrivRequest(tt.args.reqparams, tt.args.adpterprams, tt.args.uid)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_BuildPrivRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(ral.HTTPRequest))
	}
	ag.Run()
}

func TestHISLIST_ParseHISResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		query    string
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHISResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		err := this.ParseHISResponse(tt.args.query, tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_ParseHISResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHISLIST_setHISPriTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		r map[string]HisPriveRALResponse
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setHISPriTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		this.setHISPriTplData(tt.args.r)
	}
	ag.Run()
}

func TestHISLIST_GetHISPriTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetHISPriTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisPriveRALResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		got := this.GetHISPriTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_GetHISPriTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisPriveRALResponse))
	}
	ag.Run()
}

func TestHISLIST_setHISListTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}
	type args struct {
		r HisListResponse
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setHISListTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		this.setHISListTplData(tt.args.r)
	}
	ag.Run()
}

func TestHISLIST_GetHISListTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		hisPriTplData  map[string]HisPriveRALResponse
		hisListTplData HisListResponse
		hisListRalData rawResp
		hisPrivRalData rawResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetHISListTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisListResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HISLIST{
			BaseData:       tt.fields.BaseData,
			hisPriTplData:  tt.fields.hisPriTplData,
			hisListTplData: tt.fields.hisListTplData,
			hisListRalData: tt.fields.hisListRalData,
			hisPrivRalData: tt.fields.hisPrivRalData,
		}
		got := this.GetHISListTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHISLIST_GetHISListTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisListResponse))
	}
	ag.Run()
}

func TestHisListNewFunc(t *testing.T) {
	_ = t
	h := &HISLIST{}
	h.ctx = createGetWebContext()
	// 测试方法调用
	h.setHISPriTplData(nil)
	h.GetHISPriTplData()
	h.setHISListTplData(HisListResponse{})
	h.GetHISListTplData()
	_ = h.ParseHISResponse("", httpResp{})
	h.BuildPrivRequest(nil, nil, "111")
	h.MultiRequest([]NamedHTTPReq{{HTTPRequest: &protocol.HTTPRequest{}}}, make(chan ChRawHTTPResp, 1))
	ch := make(chan ChRawHTTPResp, 2)
	ch <- ChRawHTTPResp{Err: errors.New("123"), Name: HISLISTSERVER}
	ch <- ChRawHTTPResp{Err: errors.New("123"), Name: HIS_PRIVE_SERVER}
	close(ch)
	_ = h.ParseHisListResponse(ch)
	_, _ = h.BuildRequest(new(map[string]string), new(map[string]string), "12")
	_ = h.GetResponse(new(map[string]string), new(map[string]string), "12")
}
