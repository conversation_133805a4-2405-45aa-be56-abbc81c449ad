package data

import (
	"encoding/json"
	"errors"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

const WISESUG_SERVER = "searchbox_sug_new"
const WISESUG_EXT_NUM = 10
const WISESUG_LIST_NUM = 10

type Wisesug struct {
	BaseData
	wisesugTplData Wisesug_Response
	CurResponse    resp
}

type Wisesug_Response struct {
	Query   string
	Suglist []string
	Extend  []string
}

type Swan_Sug struct {
	Flag            string
	Type            string
	VersionControl  VersionCtr
	Value           interface{}
	VersionControl1 VersionCtr
	Value1          interface{}
}

type Swan_Sug_Res struct {
	Flag  string      `json:"flag"`
	Value interface{} `json:"value"`
}

type VersionCtr map[string]VersionMinMax

type Swan_Sug_Arr []Swan_Sug

type Swan_Sug_Res_Arr []Swan_Sug_Res

type VersionMinMax struct {
	Min string
	Max string
}

type Novel_Sug struct {
	Flag  string
	Value Novel_Value
}

type Novel_Sug_Arr []Novel_Sug

type Novel_Value struct {
	Title_main      string
	Category        string
	Tag             string
	Desc            string
	Img             string
	Bookid          string
	Author          string
	Ch_0_cid        string
	Ch_0_title      string
	Ch_1_cid        string
	Ch_1_title      string
	Content_type    string
	Ex_content_type string
	Finish_stat     string
	Chapter_all_num string
}

func NewWiseSug(ctx *gdp.WebContext) *Wisesug {
	dataSrv := Wisesug{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// /////////////////////////////////////////////////
// 打包、请求、解码于一体的single请求方法
func (this *Wisesug) GetWiseSugResponse(query string) error {
	requestrals := this.BuildWiseSugRequest(query)
	common.DumpData(this.ctx, "2wisesug", requestrals)

	var response = httpResp{}
	err := ral.Ral(WISESUG_SERVER, *requestrals, &response, ral.JSONConverter)
	//this.addNotice("ralres", string(response.Raw))
	if err != nil {
		return err
	}

	return this.ParseWiseSugResponse(query, response)
}

// 打包请求wisesug的请求结构
func (this *Wisesug) BuildWiseSugRequest(query string) *ral.HTTPRequest {
	request := this.getRequest()
	adaption := this.getAdaption()

	values := url.Values{}
	values.Set("prod", "baiduapp_sug")
	values.Set("wd", query)
	values.Set("useat", "0") // 直达号项目以下线 此处固定0
	pqy := "-"
	if v, ok := (*request)["pq"]; ok {
		pqy = v
	}
	values.Set("preqy", pqy)
	pqt := "-"
	if v, ok := (*request)["pt"]; ok {
		pqt = v
	}
	values.Set("preqt", pqt)
	os := "2"
	if (*adaption)["isiOS"] == "1" {
		os = "1"
	}
	values.Set("os", os)
	values.Set("net", common.GetNetwork((*request)["network"]))

	// wise请求固定参数
	values.Set("cfrom", "searchbox")
	values.Set("from", "wise_web")
	values.Set("ie", "utf-8")
	values.Set("action", "opensearch")
	values.Set("sbox_cip", this.ctx.ClientIP())
	sboxuid := "-"
	if v, ok := (*this.getRequest())["uid"]; ok {
		sboxuid = v
	}
	values.Set("sbox_uid", sboxuid)
	values.Set("sbox_branch", (*request)["osbranch"])
	// 添加cs_log_id用于串联日志,2020.3.5
	values.Set("cs_log_id", this.getLogidStr())

	// 透传sug_mode、pwd字段给sug server, 2020.05.26
	if v, ok := (*this.getRequest())["sug_mode"]; ok {
		values.Set("sugmode", v)
	}

	if v, ok := (*this.getRequest())["pwd"]; ok {
		values.Set("pwd", v)
	}

	if v, ok := (*request)["data"]; ok {
		m, _ := url.QueryUnescape(v)
		var reqdata map[string]string
		decodeErr := json.Unmarshal([]byte(m), &reqdata)
		swanHis := reqdata["swan_his"]
		decodeRet, err := base64.Deocde(swanHis, 0)
		if decodeErr == nil && err == nil {
			dataqueryStr, _ := url.QueryUnescape(string(decodeRet))
			values.Set("app_his", dataqueryStr)
		} else {
			this.addNotice("swanErr", err.Error())
		}
	}

	querystring := values.Encode()

	// 避免encode bdid 此处再单独处理
	if v, ok := (*this.getRequest())["bdid"]; ok {
		arr := strings.Split(v, ":")
		querystring += "&baiduid=" + arr[0]
	} else {
		querystring += "&baiduid=-"
	}

	sids := this.ctx.GetString(constant.HWiseSIDs)

	// cookie再过滤
	cs := this.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cv.Name == "H_WISE_SIDS" && sids != "" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	if sids != "" {
		cstr += " H_WISE_SIDS=" + sids
	}

	header := map[string][]string{
		// "Host":       {"m.baidu.com"},
		"User-Agent": {this.getHTTPRequest().UserAgent()},
		"Cookie":     {cstr},
	}
	requestral = &ral.HTTPRequest{
		Header:    header,
		Method:    "GET",
		Path:      "singularity.api.sug.SugService/query?" + querystring,
		Converter: "string",
		LogID:     this.getLogidStr(),
		Ctx:       this.ctx,
	}

	return requestral
}

// 带容错模式的解析wisesug请求
func (this *Wisesug) ParseWiseSugResponse(query string, response httpResp) error {
	resData, err := this.decodeWiseSugResponse(response.Raw)
	if err != nil {
		if !this.isJsonpRet(string(response.Raw)) {
			return err
		}
		// 被终止返回jsonp时不论是否有数据都一律当做查询失败处理
		// 后续如有需求 此处不进行支持 让opensug来修正
		resData = Wisesug_Response{
			Query:   query,
			Suglist: []string{},
		}
	}
	this.setWisesugTplData(resData)
	return nil
}

// wisesug结构解码
func (this *Wisesug) decodeWiseSugResponse(jsonstring []byte) (Wisesug_Response, error) {
	// 非常难看的写法 后面有待优化
	// 另：opensug的返回结构本身也比较恶心
	var data interface{}
	if err := json.Unmarshal([]byte(jsonstring), &data); err != nil {
		return Wisesug_Response{}, nil
	}

	ret := Wisesug_Response{
		Query:   "",
		Suglist: []string{},
		Extend:  []string{},
	}
	level1 := data.([]interface{})
	countNum := len(level1)
	if countNum == 0 {
		return ret, errors.New("wisesug response empty json")
	}

	// 第一部分 后端收到的以及用来检索的转码后query
	if countNum >= 1 {
		query, qok := level1[0].(string)
		if !qok {
			return ret, errors.New("wisesug response json err position 0")
		}
		ret.Query = query
	}

	// 第二部分 后端返回的Sug检索结果
	if countNum >= 2 {
		suglist, sok := level1[1].([]interface{})
		if !sok {
			return ret, errors.New("wisesug response json err position 1")
		}
		// sug列表有且最多取10个 目前后端最多也返回10个 若产品有需求需再改动
		var tmp [WISESUG_LIST_NUM]string
		for index, each := range suglist {
			if index >= WISESUG_LIST_NUM {
				break
			}
			tmp[index] = each.(string)
		}
		ret.Suglist = tmp[:min(len(suglist), len(tmp))]
	}

	// 第三部分 特殊Sug的拓展信息
	if countNum >= 3 {
		extdata, tok := level1[2].([]interface{})
		if !tok {
			return ret, errors.New("wisesug response json err position 2")
		}
		// 目前拓展信息不会超过10个 若存在超过10个的情况 需要再确认对方的协议
		var tmp [WISESUG_EXT_NUM]string
		for index, each := range extdata {
			if index >= WISESUG_EXT_NUM {
				break
			}
			tmp[index] = each.(string)
		}
		ret.Extend = tmp[:min(len(extdata), len(tmp))]
	}

	return ret, nil
}

// 粗暴的判断jsonp方法
func (this *Wisesug) isJsonpRet(body string) bool {
	return strings.Contains(body, "window.baidu.sug")
}

func (this *Wisesug) setWisesugTplData(r Wisesug_Response) {
	this.wisesugTplData = r
}

func (this *Wisesug) GetWisesugTplData() Wisesug_Response {
	return this.wisesugTplData
}

// ///////////////////////////////////////////////////////////////
func min(left, right int) int {
	if left > right {
		return right
	}

	return left
}
