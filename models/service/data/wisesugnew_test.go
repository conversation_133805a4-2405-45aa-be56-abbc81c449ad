//http://wiki.baidu.com/pages/viewpage.action?pageId=590990038
//http://wiki.baidu.com/pages/viewpage.action?pageId=576601410

package data

import (
	"reflect"
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewWiseSugNew(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewWiseSugNew ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := Wisesugnew{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*Wisesugnew
	}{
		{
			"TestNewWiseSugNew",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewWiseSugNew(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewWiseSugNew, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*Wisesugnew))
	}
	ag.Run()
}

func TestWisesugnew_ParseWiseSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		query    string
		response rawResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseWiseSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesugnew{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		err := this.ParseWiseSugResponse(tt.args.query, tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesugnew_ParseWiseSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestWisesugnew_decodeWiseSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		jsonstring []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: decodeWiseSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Wisesugnew_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesugnew{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got, err := this.decodeWiseSugResponse(tt.args.jsonstring)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesugnew_decodeWiseSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesugnew_decodeWiseSugResponse, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesugnew_Response))
	}
	ag.Run()
}

func TestWisesugnew_isJsonpRet(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		body string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: isJsonpRet ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesugnew{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.isJsonpRet(tt.args.body)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesugnew_isJsonpRet, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func TestWisesugnew_setWisesugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		r Wisesugnew_Response
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setWisesugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Wisesugnew{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		this.setWisesugTplData(tt.args.r)
	}
	ag.Run()
}

func TestWisesugnew_GetWisesugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesugnew_Response
		CurResponse    resp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetWisesugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Wisesugnew_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesugnew{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.GetWisesugTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesugnew_GetWisesugTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesugnew_Response))
	}
	ag.Run()
}

func TestWisesugnew_MutiRequest(t *testing.T) {
	type fields struct {
		BaseData          BaseData
		wisesugTplData    Wisesugnew_Response
		CurResponse       resp
		NeedPredictSug    bool
		wisesugRalResp    chRawHTTPResp
		predictsugTplData PresearchType_Response
	}
	type args struct {
		rs map[string]*ral.HTTPRequest
		ch chan chRawHTTPResp
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case-1_test_service",
			args: args{
				rs: map[string]*ral.HTTPRequest{
					"test": {
						Method: "GET",
					},
				},
				ch: make(chan chRawHTTPResp, 1),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Wisesugnew{}
			this.MutiRequest(tt.args.rs, tt.args.ch)
		})
	}
}

func TestWisesugnew_ParsePresearchSugData(t *testing.T) {
	type fields struct {
		BaseData          BaseData
		wisesugTplData    Wisesugnew_Response
		CurResponse       resp
		NeedPredictSug    bool
		wisesugRalResp    chRawHTTPResp
		predictsugTplData PresearchType_Response
	}
	type args struct {
		response chRawHTTPResp
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    PresearchType_Response
		wantErr bool
	}{
		{
			name: "case-1_presearch",
			args: args{
				response: chRawHTTPResp{
					Resp: rawResp{
						Body: []byte(`{"presearch":"1"}`),
					},
				},
			},
			want: PresearchType_Response{
				Presearch: "1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &Wisesugnew{}
			got, err := this.ParsePresearchSugData(tt.args.response)
			if (err != nil) != tt.wantErr {
				t.Errorf("Wisesugnew.ParsePresearchSugData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Wisesugnew.ParsePresearchSugData() = %v, want %v", got, tt.want)
			}
		})
	}
}
