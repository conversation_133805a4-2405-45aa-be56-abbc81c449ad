package ups

import (
	"encoding/json"
	"errors"
	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type SetSrv struct {
	BaseData
}

// ups下游返回的结构
type setUPSRalResponse struct {
	PersonalSwitch string `json:"personalSwitch"` // 个性化开关，1-打开个性化，0-关闭个性化
	SugStoreSet    string `json:"sugStoreSet"`    // 搜索历史开关，1-记录，0-不记录
	Errno          int    `json:"errno"`
}

func NewSetSrv(ctx *gdp.WebContext) *SetSrv {
	dataSrv := SetSrv{}
	dataSrv.ctx = ctx
	return &dataSrv
}

func (s *SetSrv) GetResponse(uid string, reqData map[string]string) error {
	request := s.BuildRequest(uid, reqData)
	common.DumpData(s.ctx, "2set_ups_new", request)

	response := httpResp{}
	err := ral.Ral(UPS_SERVER, *request, &response, ral.JSONConverter)
	if err != nil {
		return err
	}

	return s.ParseResponse(response, reqData)
}

func (s *SetSrv) BuildRequest(uid string, reqData map[string]string) *ral.HTTPRequest {
	tipsMap := map[string]string{}
	for k, v := range reqData {
		tipsMap[k] = v
	}
	tips, _ := json.Marshal(tipsMap)

	values := url.Values{}
	values.Set("product", "ps")
	values.Set("from", "gosug")
	values.Set("logid", s.ctx.GetLogID())
	values.Set("uid", uid)
	values.Set("tips", string(tips))

	request := &ral.HTTPRequest{
		Method:      "GET",
		Path:        "ups/api/addtips",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       s.ctx.GetLogID(),
		Ctx:         s.ctx,
	}

	return request
}

func (s *SetSrv) ParseResponse(response httpResp, reqData map[string]string) error {
	ralRes := setUPSRalResponse{}

	err := json.Unmarshal(response.Raw, &ralRes)
	if err != nil {
		return err
	}

	if ralRes.Errno != 0 {
		return errors.New("request addups error")
	}

	// 检查所有值是否都成功，有任一不成功的，返回错误
	for target := range reqData {
		value := ""
		if target == constant.PersonalSwitch {
			value = ralRes.PersonalSwitch
		} else if target == constant.SugStoreSet {
			value = ralRes.SugStoreSet
		} else {
			// 接口暂时只支持上面2个开关
			return errors.New(target + " unsupport")
		}

		if value != SUCCESS {
			return errors.New(target + " return err" + value)
		}
	}

	return nil
}
