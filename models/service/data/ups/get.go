package ups

import (
	"encoding/json"
	"errors"
	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type GetSrv struct {
	BaseData
	upsData map[string]upsRes // 返回给端的数据
}

type upsRes struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
}

// 下游ups返回的结构
type getUPSRalResponse struct {
	PersonalSwitch item `json:"personalSwitch"` // 个性化开关，1-打开个性化，0-关闭个性化
	SugStoreSet    item `json:"sugStoreSet"`    // 搜索历史开关，1-记录，0-不记录
	Errno          int  `json:"errno"`
}

type item struct {
	Value  string `json:"value"`
	Utime  int64  `json:"utime"`
	ErrMsg string `json:"ErrMsg"`
}

// SUCCESS ups返回的状态
const SUCCESS = "SUCCESS"

var ErrEmpty = errors.New("not found any target")

func NewGetSrv(ctx *gdp.WebContext) *GetSrv {
	dataSrv := GetSrv{}
	dataSrv.ctx = ctx
	return &dataSrv
}

func (g *GetSrv) GetResponse(uid string, targetArr []string) error {
	request := g.BuildRequest(uid, targetArr)
	common.DumpData(g.ctx, "2get_ups_new", request)

	response := httpResp{}
	err := ral.Ral(UPS_SERVER, *request, &response, ral.JSONConverter)
	if err != nil {
		return err
	}

	return g.ParseResponse(response, targetArr)
}

func (g *GetSrv) BuildRequest(uid string, targetArr []string) *ral.HTTPRequest {
	values := url.Values{}
	values.Set("product", "ps")
	values.Set("from", "gosug")
	values.Set("logid", g.ctx.GetLogID())
	values.Set("uid", uid)

	requestral := &ral.HTTPRequest{
		Method:      "GET",
		Path:        "ups/api/gettips",
		QueryParams: values,
		Converter:   ral.FORMConverter,
		LogID:       g.ctx.GetLogID(),
		Ctx:         g.ctx,
	}

	return requestral
}

// ParseResponse 解析ral返回的结果
func (g *GetSrv) ParseResponse(response httpResp, targetArr []string) error {
	ralRes := getUPSRalResponse{}
	err := json.Unmarshal(response.Raw, &ralRes)
	if err != nil {
		return err
	}

	if ralRes.Errno != 0 {
		return errors.New("request ups error")
	}

	resData := map[string]upsRes{}
	// 寻找返回数据中的target值
	for _, target := range targetArr {
		one := item{}
		if target == constant.PersonalSwitch {
			one = ralRes.PersonalSwitch
		} else if target == constant.SugStoreSet {
			one = ralRes.SugStoreSet
		} else {
			// 接口暂时只支持上面2个开关
			return errors.New(target + " unsupport")
		}

		// 返回成功时，标识找到了此开关值
		if one.ErrMsg == SUCCESS {
			resData[target] = upsRes{
				Status:    one.Value,
				Timestamp: one.Utime,
			}
		}
	}

	// 所有target开关值都没找到时，返回错误
	if len(resData) == 0 {
		//g.ctx.AddNotice("upsResult", string(response.Raw))
		return ErrEmpty
	}

	g.setUPSData(resData)
	return nil
}

func (g *GetSrv) setUPSData(u map[string]upsRes) {
	g.upsData = u
}

func (g *GetSrv) GetUPSData() map[string]upsRes {
	return g.upsData
}
