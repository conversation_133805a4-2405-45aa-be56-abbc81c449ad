//http://wiki.baidu.com/pages/viewpage.action?pageId=590990038
//http://wiki.baidu.com/pages/viewpage.action?pageId=576601410

package data

import (
	"encoding/json"

	"net/url"
	"path/filepath"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	base64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

const OpenSug_SERVER = "opensug_public"
const OpenSugNew_SERVER = "searchbox_sug_new"

type OpenSugNew struct {
	BaseData
	opensugTplData Wisesugnew_Response
	CurResponse    resp
}

// ral请求时不同的六合sug类型 对应的 prod参数的值
var confProd map[string]string

func init() {
	filePath := filepath.Join(env.ConfRootPath(), "prod.toml")
	_, err := toml.DecodeFile(filePath, &confProd)
	if err != nil {
		panic(err)
	}
}

func NewOpenSugNew(ctx *gdp.WebContext) *OpenSugNew {
	dataSrv := OpenSugNew{}
	dataSrv.ctx = ctx
	return &dataSrv
}

// 获取新opensug请求方法
func (this *OpenSugNew) GetOpenSugResponse(query string) error {
	requestrals := this.BuildOpenSugRequest(query)
	common.DumpData(this.ctx, "2opensug", requestrals)

	var response = httpResp{}
	err := ral.Ral(OpenSugNew_SERVER, *requestrals, &response, ral.JSONConverter)

	//this.addNotice("ralres", string(response.Raw))
	if len(response.Raw) == 0 && response.Head.StatusCode == 200 {
		return nil
	}

	if err != nil {
		return err
	}
	//this.CurResponse = response
	//return nil
	return this.ParseOpenSugResponse(query, response)
}

func (this *OpenSugNew) BuildOpenSugRequest(query string) *ral.HTTPRequest {
	request := this.getRequest()
	adaption := this.getAdaption()

	values := url.Values{}
	prod := (*this.getRequest())["action"]
	var prodfinal string
	//获取当前tab 对应的prod参数值
	for key, value := range confProd {
		if prod == key {
			prodfinal = value
			break
		}
	}
	values.Set("prod", prodfinal)
	values.Set("wd", query)
	values.Set("useat", "0") //直达号项目以下线 此处固定0
	pqy := "-"
	if v, ok := (*request)["pq"]; ok {
		pqy = v
	}
	values.Set("preqy", pqy)
	pqt := "-"
	if v, ok := (*request)["pt"]; ok {
		pqt = v
	}
	values.Set("preqt", pqt)
	os := "2"
	if (*adaption)["isiOS"] == "1" {
		os = "1"
	}
	values.Set("os", os)
	values.Set("net", common.GetNetwork((*request)["network"]))

	//wise请求固定参数
	values.Set("cfrom", "searchbox")
	values.Set("from", "wise_web") //不用wise_web可能不出直达
	values.Set("ie", "utf-8")
	values.Set("action", "opensearch")
	values.Set("sbox_cip", this.ctx.ClientIP())

	sboxuid := "-"
	if v, ok := (*this.getRequest())["uid"]; ok {
		sboxuid = v
	}
	values.Set("sbox_uid", sboxuid)
	//values.Set("sbox_branch", (*request)["osbranch"])
	//添加cs_log_id用于串联日志,2020.3.5
	values.Set("cs_log_id", this.getLogidStr())

	//透传sug_mode、pwd字段给sug server, 2020.05.26
	if v, ok := (*this.getRequest())["sug_mode"]; ok {
		values.Set("sugmode", v)
	}

	if v, ok := (*this.getRequest())["pwd"]; ok {
		values.Set("pwd", v)
	}

	if v, ok := (*request)["data"]; ok {
		m, _ := url.QueryUnescape(v)
		var reqdata map[string]string
		decodeErr := json.Unmarshal([]byte(m), &reqdata)
		swanHis := reqdata["swan_his"]
		decodeRet, err := base64.Deocde(swanHis, 0)
		if decodeErr == nil && err == nil {
			dataqueryStr, _ := url.QueryUnescape(string(decodeRet))
			values.Set("app_his", dataqueryStr)
		} else {
			this.addNotice("swanErr", err.Error())
		}
	}

	querystring := values.Encode()

	//避免encode bdid 此处再单独处理
	if v, ok := (*this.getRequest())["bdid"]; ok {
		arr := strings.Split(v, ":")
		querystring += "&baiduid=" + arr[0]
	} else {
		querystring += "&baiduid=-"
	}

	sids := this.ctx.GetString(constant.HWiseSIDs)

	//cookie再过滤 大cookie此处删除
	cs := this.getHTTPRequest().Cookies()

	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cv.Name == "H_WISE_SIDS" && sids != "" {
			continue
		}

		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}
	if sids != "" {
		cstr += " H_WISE_SIDS=" + sids
	}

	header := map[string][]string{
		//"Host": {"m.baidu.com"},
		"User-Agent": {this.getHTTPRequest().UserAgent()},
		"Cookie":     {cstr},
	}
	requestral := &ral.HTTPRequest{
		Header:    header,
		Method:    "GET",
		Path:      "singularity.api.sug.SugService/query?" + querystring,
		Converter: "form",
		LogID:     this.getLogidStr(),
		Ctx:       this.ctx,
	}
	return requestral
}

func (this *OpenSugNew) ParseOpenSugResponse(query string, response httpResp) error {

	resData, err := this.decodeOpenSugResponse(response.Raw)
	if err != nil {
		return err
	}
	this.setOpensugTplData(resData)
	return nil
}

func (this *OpenSugNew) decodeOpenSugResponse(jsonstring []byte) (Wisesugnew_Response, error) {
	data := Wisesugnew_Response{}
	err_ral := json.Unmarshal(jsonstring, &data)
	if err_ral != nil {
		return data, err_ral
	}
	return data, nil
}

func (this *OpenSugNew) setOpensugTplData(r Wisesugnew_Response) {
	this.opensugTplData = r
}

func (this *OpenSugNew) GetOpensugTplData() Wisesugnew_Response {
	return this.opensugTplData
}
