package data

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestHIS_BuildHISRequest(t *testing.T) {
	type args struct {
		reqdata   DataType
		wisehispm string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildHISRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //ral.HTTPRequest
	}{
		{
			"TestHIS_BuildHISRequest",
			args{
				DataType{},
				"test",
			},
			Want{
				&ral.HTTPRequest{},
				ut.ShouldNotEqual,
			},
		},
	}

	this := &HIS{
		BaseData: BaseData{
			ctx: createGetWebContext(),
		},
	}

	for k, tt := range tests {
		request := this.BuildHISRequest(tt.args.reqdata, tt.args.wisehispm)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHIS_BuildHISRequest, Result Index:0 Value Compare", request, tt.want.Assert, tt.want.Value.(*ral.HTTPRequest))
	}
	ag.Run()
}

func TestHIS_ParseHISResponse(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisRALResponse
	}
	type args struct {
		query    string
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHISResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	es := HisRALResponse{}
	baseData := BaseData{
		ctx: ctx,
	}
	var response = httpResp{}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestHIS_ParseHISResponse",
			fields{
				baseData,
				es,
			},
			args{
				"test",
				response,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &HIS{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		err := this.ParseHISResponse(tt.args.query, tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHIS_ParseHISResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHIS_setHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisRALResponse
	}
	type args struct {
		r HisRALResponse
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HIS{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		this.setHISTplData(tt.args.r)
	}
	ag.Run()
}

func TestHIS_GetHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisRALResponse
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisRALResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HIS{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.GetHISTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHIS_GetHISTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisRALResponse))
	}
	ag.Run()
}
