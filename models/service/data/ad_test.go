package data

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

func TestNewAFD(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewAFD")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := AFD{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
	}{
		{
			"TestNewAFD",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewAFD(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewAFD, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*AFD))
	}
	ag.Run()
}

func TestExtractRecAdMaterial(t *testing.T) {
	type args struct {
		adResp AFDResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ExtractRecAdMaterial")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// 创建测试用的 AFDResp 对象
	adResp := AFDResp{
		Errno:  "0",
		Errmsg: "",
		LogID:  "12345",
		Data: AFDData{
			Ad: []AFDAdItem{
				{
					LocCode: "sug",
					AdInfo: []AFDAdInfo{
						{
							ID: "ad1",
							Material: []AFDMaterial{
								{
									ID: "material1",
									Info: `{
										"title": "测试广告词",
										"jumpurl": "https://example.com",
										"sa": "sa_test",
										"show_url": "https://show.example.com",
										"click_url": "https://click.example.com"
									}`,
								},
							},
							Extra: []AFDExtra{
								{
									K: "extraParam",
									V: "extraValue",
								},
							},
						},
					},
				},
			},
			ReqID: "req123",
		},
	}

	expectedAdGuess := []AdGuess{
		{
			Ad: Ad{
				JumpURL:    "https://example.com",
				ExtraParam: "extraValue",
				ShowURL:    "https://show.example.com",
				ClickURL:   "https://click.example.com",
			},
			Text:  "测试广告词",
			Sa:    "sa_test",
			Tag:   "",
			Icon:  "",
			Color: 0,
		},
	}

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
	}{
		{
			"TestExtractRecAdMaterial",
			args{
				adResp,
			},
			Want{
				expectedAdGuess,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := ExtractRecAdMaterial(tt.args.adResp)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestExtractRecAdMaterial, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value)
	}
	ag.Run()
}

func TestAFD_GetAFDResponse(t *testing.T) {
	type fields struct {
		BaseData BaseData
		TplData  AFDResp
	}

	type args struct {
		query           string
		params          map[string]string
		adaption        map[string]string
		userPrivacyInfo common.UserPrivacyInfo
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetAFDResponse")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}

	// Mock ral.Ral
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 直接模拟 ral.Ral 返回模拟的响应
	serverRespBytes := []byte(`{
		"errno": 0,
		"errmsg": "success",
		"res": {
			"imTimeSign": 123456789,
			"reqId": "req123",
			"ad": [
				{
					"locCode": "sug",
					"adInfo": [
						{
							"id": "ad1",
							"material": [
								{
									"id": "material1",
									"info": "{\"title\": \"测试广告词\", \"jumpurl\": \"https://example.com\", \"sa\": \"sa_test\", \"show_url\": \"https://show.example.com\", \"click_url\": \"https://click.example.com\"}"
								}
							],
							"extra": [
								{
									"k": "extraParam",
									"v": "extraValue"
								}
							]
						}
					]
				}
			]
		}
	}`)

	patches.ApplyFunc(ral.Ral, func(_ string, _ interface{}, response interface{}, _ ral.ConverterType) error {
		// 将模拟的响应解析为 AFDServerResp 对象
		var resp AFDServerResp
		err := json.Unmarshal(serverRespBytes, &resp)
		if err != nil {
			return err
		}

		// 将解析后的响应复制到传入的 response 引用
		r, ok := response.(*rawResp)
		if ok {
			r.Body = serverRespBytes
			return nil
		}
		return nil
	})

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestAFD_GetAFDResponse",
			fields{
				BaseData: baseData,
				TplData:  AFDResp{},
			},
			args{
				query: "测试查询",
				params: map[string]string{
					"data":     `{"guessdata":"123"}`,
					"osbranch": "a0", // 添加必要的 osbranch 参数避免构建请求时出错
				},
				adaption: map[string]string{
					"bd_version": "13.0.0",
				},
				userPrivacyInfo: common.UserPrivacyInfo{},
			},
			false,
		},
	}

	for k, tt := range tests {
		this := &AFD{
			BaseData: tt.fields.BaseData,
			TplData:  tt.fields.TplData,
		}
		err := this.GetAFDResponse(tt.args.query, tt.args.params, tt.args.adaption, tt.args.userPrivacyInfo)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestAFD_GetAFDResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
	}
	ag.Run()
}

func TestAFD_parseResponse(t *testing.T) {
	type fields struct {
		BaseData BaseData
		TplData  AFDResp
	}

	type args struct {
		res AFDServerResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: parseResponse")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}

	serverResp := AFDServerResp{
		Errno:  0,
		Errmsg: "success",
		Res: struct {
			ImTimeSign int         `json:"imTimeSign"`
			ReqID      string      `json:"reqId"`
			Ad         []AFDAdItem `json:"ad"`
		}{
			ImTimeSign: 123456789,
			ReqID:      "req123",
			Ad: []AFDAdItem{
				{
					LocCode: "sug",
					AdInfo: []AFDAdInfo{
						{
							ID: "ad1",
							Material: []AFDMaterial{
								{
									ID:   "material1",
									Info: `{"title": "测试广告词"}`,
								},
							},
						},
					},
				},
			},
		},
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantTplData   AFDResp
		wantErr       bool
	}{
		{
			"TestAFD_parseResponse",
			fields{
				BaseData: baseData,
				TplData:  AFDResp{},
			},
			args{
				res: serverResp,
			},
			AFDResp{
				Errno:  "0",
				Errmsg: "success",
				LogID:  ctx.GetLogID(),
				Data: AFDData{
					Ad:    serverResp.Res.Ad,
					ReqID: serverResp.Res.ReqID,
				},
			},
			false,
		},
	}

	for k, tt := range tests {
		this := &AFD{
			BaseData: tt.fields.BaseData,
			TplData:  tt.fields.TplData,
		}
		err := this.parseResponse(tt.args.res)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestAFD_parseResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		if !tt.wantErr {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestAFD_parseResponse, Result Value Compare", this.TplData, ut.ShouldResemble, tt.wantTplData)
		}
	}
	ag.Run()
}

func TestAFD_buildAFDRequest(t *testing.T) {
	t.Skip("Skipping test for non-exported method buildAFDRequest")

	// 原代码保留但会被跳过
	type fields struct {
		BaseData BaseData
		TplData  AFDResp
	}

	type args struct {
		query           string
		params          map[string]string
		adaption        map[string]string
		userPrivacyInfo common.UserPrivacyInfo
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: buildAFDRequest")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	// 设置 HTTP 请求
	request, _ := http.NewRequest("GET", "http://example.com", nil)
	request.Header.Set("User-Agent", "Mozilla/5.0")
	ctx.Request = request

	// 添加 cookie
	ctx.Request.AddCookie(&http.Cookie{
		Name:  "CS_W_SIDS",
		Value: "123-456-789",
	})

	baseData := BaseData{
		ctx: ctx,
	}

	// 模拟 UserPrivacyInfo
	userPrivacyInfo := common.UserPrivacyInfo{
		Model:      "iPhone12",
		OsVer:      "iOS15",
		IDFA:       "idfa12345",
		OAID:       "oaid12345",
		MacAddr:    "mac12345",
		IMEI:       "imei12345",
		HornorOAID: "honor12345",
		CookieLocation: common.CookieLocation{
			Merca: common.MercaLocation{
				MerX: 116.123,
				MerY: 39.987,
			},
		},
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		pathContains  string
		wantErr       bool
	}{
		{
			"TestAFD_buildAFDRequest_Android",
			fields{
				BaseData: baseData,
				TplData:  AFDResp{},
			},
			args{
				query: "测试查询",
				params: map[string]string{
					"data":     `{"guessdata":"eyJndWVzc2RhdGEiOiJbeyJ0ZXh0IjoiYnJhbmQiLCJzYSI6ImlnaF8xMjM0NV9waW5wYWkifV0ifQ=="}`,
					"osbranch": "a0",
					"from":     "openbox",
					"cfrom":    "1000539",
					"puid":     "xxx",
					"uid":      "123",
					"ua":       "Mozilla/5.0",
				},
				adaption: map[string]string{
					"bd_version": "13.0.0",
				},
				userPrivacyInfo: userPrivacyInfo,
			},
			"/afd/entry",
			false,
		},
		{
			"TestAFD_buildAFDRequest_iOS",
			fields{
				BaseData: baseData,
				TplData:  AFDResp{},
			},
			args{
				query: "测试查询",
				params: map[string]string{
					"data":     `{"guessdata":"eyJndWVzc2RhdGEiOiJbeyJ0ZXh0IjoiYnJhbmQiLCJzYSI6ImlnaF8xMjM0NV9waW5wYWkifV0ifQ=="}`,
					"osbranch": "i0",
					"from":     "openbox",
					"cfrom":    "1000539",
					"puid":     "xxx",
					"uid":      "123",
					"ua":       "Mozilla/5.0",
				},
				adaption: map[string]string{
					"bd_version": "13.0.0",
				},
				userPrivacyInfo: userPrivacyInfo,
			},
			"/afd/entry",
			false,
		},
		{
			"TestAFD_buildAFDRequest_EmptyData",
			fields{
				BaseData: baseData,
				TplData:  AFDResp{},
			},
			args{
				query: "测试查询",
				params: map[string]string{
					"data":     "",
					"osbranch": "a0",
				},
				adaption:        map[string]string{},
				userPrivacyInfo: userPrivacyInfo,
			},
			"",
			true,
		},
	}

	for k, tt := range tests {
		this := &AFD{
			BaseData: tt.fields.BaseData,
			TplData:  tt.fields.TplData,
		}
		req, err := this.buildAFDRequest(tt.args.query, tt.args.params, tt.args.adaption, tt.args.userPrivacyInfo)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestAFD_buildAFDRequest, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		if !tt.wantErr && req != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestAFD_buildAFDRequest, Path Value Compare", req.Path, ut.ShouldContainSubstring, tt.pathContains)
		}
	}
	ag.Run()
}

func TestAFD_getEid(t *testing.T) {
	type fields struct {
		BaseData BaseData
		TplData  AFDResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getEid")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()

	// 添加测试 cookie
	ctx.Request.AddCookie(&http.Cookie{
		Name:  "CS_W_SIDS",
		Value: "123-456-789",
	})

	baseData := BaseData{
		ctx: ctx,
	}

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want
	}{
		{
			"TestAFD_getEid",
			fields{
				BaseData: baseData,
				TplData:  AFDResp{},
			},
			Want{
				"123,456,789",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		this := &AFD{
			BaseData: tt.fields.BaseData,
			TplData:  tt.fields.TplData,
		}
		got := this.getEid()
		ag.Add(fmt.Sprintf("Test Case Of TestAFD_getEid, Result Index:%d Value Compare", k), got, tt.want.Assert, tt.want.Value)
	}
	ag.Run()
}

func Test_getAFDReportedParam(t *testing.T) {
	type args struct {
		afdExtras []AFDExtra
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: getAFDReportedParam")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
	}{
		{
			"TestGetAFDReportedParam_WithExtraParam",
			args{
				afdExtras: []AFDExtra{
					{
						K: "extraParam",
						V: "testValue",
					},
					{
						K: "otherParam",
						V: "otherValue",
					},
				},
			},
			Want{
				"testValue",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetAFDReportedParam_WithoutExtraParam",
			args{
				afdExtras: []AFDExtra{
					{
						K: "otherParam",
						V: "otherValue",
					},
				},
			},
			Want{
				"",
				ut.ShouldEqual,
			},
		},
		{
			"TestGetAFDReportedParam_EmptyExtras",
			args{
				afdExtras: []AFDExtra{},
			},
			Want{
				"",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := getAFDReportedParam(tt.args.afdExtras)
		ag.Add(fmt.Sprintf("Test Case Of Test_getAFDReportedParam, Result Index:%d Value Compare", k), got, tt.want.Assert, tt.want.Value)
	}
	ag.Run()
}

func Test_containsIgnoreCase(t *testing.T) {
	type args struct {
		s      string
		substr string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: containsIgnoreCase")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
	}{
		{
			"TestContainsIgnoreCase_SameCase",
			args{
				s:      "Hello World",
				substr: "World",
			},
			Want{
				true,
				ut.ShouldEqual,
			},
		},
		{
			"TestContainsIgnoreCase_DifferentCase",
			args{
				s:      "Hello World",
				substr: "world",
			},
			Want{
				true,
				ut.ShouldEqual,
			},
		},
		{
			"TestContainsIgnoreCase_NotContain",
			args{
				s:      "Hello World",
				substr: "Golang",
			},
			Want{
				false,
				ut.ShouldEqual,
			},
		},
		{
			"TestContainsIgnoreCase_EmptySubstr",
			args{
				s:      "Hello World",
				substr: "",
			},
			Want{
				true,
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := containsIgnoreCase(tt.args.s, tt.args.substr)
		ag.Add(fmt.Sprintf("Test Case Of Test_containsIgnoreCase, Result Index:%d Value Compare", k), got, tt.want.Assert, tt.want.Value)
	}
	ag.Run()
}
