package data

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewHSUG(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewHSUG ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := HSUG{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*HSUG
	}{
		{
			"TestNewHSUG",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewHSUG(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewHSUG, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*HSUG))
	}
	ag.Run()
}

func TestHSUG_BuildHSUGRequest(t *testing.T) {
	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildHSUGRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		want          Want
	}{
		{
			"TestHIS_BuildHSUGRequest",
			Want{
				&ral.HTTPRequest{},
				ut.ShouldNotEqual,
			},
		},
	}

	this := &HSUG{
		BaseData: BaseData{
			ctx: createGetWebContext(),
		},
	}

	for k, tt := range tests {
		request := this.BuildHSUGRequest()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHSUG_BuildHSUGRequest, Result Index:0 Value Compare", request, tt.want.Assert, tt.want.Value.(*ral.HTTPRequest))
	}
	ag.Run()
}

func TestHSUG_BuildHSugSdelRequest(t *testing.T) {
	type args struct {
		query string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildHSugSdelRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
	}{
		{
			"TestHIS_BuildHSugSdelRequest",
			args{
				"test",
			},
			Want{
				&ral.HTTPRequest{},
				ut.ShouldNotEqual,
			},
		},
	}

	this := &HSUG{
		BaseData: BaseData{
			ctx: createGetWebContext(),
		},
	}

	for k, tt := range tests {
		request := this.BuildHSugSdelRequest(tt.args.query)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHSUG_BuildHSugSdelRequest, Result Index:0 Value Compare", request, tt.want.Assert, tt.want.Value.(*ral.HTTPRequest))
	}
	ag.Run()
}

func TestHSUG_ParseHISResponse(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisRALResponse
	}
	type args struct {
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHISResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HSUG{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		err := this.ParseHISResponse(tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestHSUG_ParseHISResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestHSUG_setHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisRALResponse
	}
	type args struct {
		r HisRALResponse
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &HSUG{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		this.setHISTplData(tt.args.r)
	}
	ag.Run()
}

func TestHSUG_GetHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData HisRALResponse
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //HisRALResponse
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &HSUG{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.GetHISTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestHSUG_GetHISTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(HisRALResponse))
	}
	ag.Run()
}
