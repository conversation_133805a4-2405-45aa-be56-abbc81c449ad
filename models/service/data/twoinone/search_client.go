package data

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/golang/protobuf/proto"
	version "github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/net/gaddr"
	"icode.baidu.com/baidu/gdp/net/servicer"

	"icode.baidu.com/baidu/searchbox/go-suggest/idl"
	"icode.baidu.com/baidu/searchbox/go-suggest/idl/csmixed"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

var expsid = map[string]string{
	"sug":     "SUGQUERY",
	"predict": "PREDICTSUG",
	"2":       "PREDICTSUG", // 兼容安卓错误
	"force":   "FORCE",      // 强制presearch预取
}

type SeachClient struct {
	BaseData
	isTwoInOne bool   // 是否进行sug&预取二合一
	Mode       string // 二合一模式, 根据实验id选择对应的预取query
	CsWSids    string // sids, 实验id标记
}

func NewSearchClient(ctx *gdp.WebContext) *SeachClient {
	dataSrv := SeachClient{}
	dataSrv.ctx = ctx
	return &dataSrv
}

func (this *SeachClient) BuildSearchRequest(query string) (*http.Request, error) {
	// 获取instance信息
	serviceName := "wisenginx_bfe_https"

	// 命中抽样走http协议
	if common.GetSampleValue(this.ctx, "http_tag", "0") == "1" {
		this.addNotice("http_tag", "1")
		serviceName = "wisenginx_bfe"
	}

	ralCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	srv := servicer.DefaultMapper.Servicer(serviceName)
	if srv == nil {
		err := fmt.Errorf("can't find service %q, may be load failed", serviceName)
		return nil, err
	}
	cnctr := srv.Connector()
	if cnctr == nil {
		return nil, servicer.ErrNoConnector
	}
	addr, err := cnctr.Pick(ralCtx)
	if err != nil {
		return nil, err
	}
	host := addr.String()
	this.addNotice("SearchInstanceIDC", gaddr.RemoteIDC(addr))
	this.addNotice("searchInstanceInfo", host)

	// 构造请求
	request := this.getRequest()
	oriRequest := this.getHttpRequest()

	requestParam := url.Values{}
	requestParam.Set("word", query)
	requestParam.Set("mod", "1")
	requestParam.Set("async", "1")
	requestParam.Set("sp", "9")
	requestParam.Set("pre", "2")
	requestParam.Set("f4s", "1")
	requestParam.Set("yqna", "1") // 预取na标识
	requestParam.Set("tn", "zbios")
	requestParam.Set("bd_page_type", "1")

	var retriveRequestKeys = []string{
		"prerender", "wise_csor", "ant_ct", "from", "pu", "pd", "tn", "sugid", "network",
		"clist", "isid", "rsv_sug4", "rsv_pq", "oq", "rq", "t_samp", "sa",
		"branchname", "appname", "p_nw", "mpv", "p_sv",
	}
	for _, field := range retriveRequestKeys {
		if v, ok := (*request)[field]; ok {
			requestParam.Set(field, v)
		}
	}

	if lid, ok := (*request)["lid"]; ok {
		requestParam.Set("lid", lid)
	} else {
		requestParam.Set("lid", this.getLogidStr())
	}

	querystring := requestParam.Encode()
	searchUrl := fmt.Sprintf("https://%s/s?%s", host, querystring)
	if serviceName == "wisenginx_bfe" {
		searchUrl = fmt.Sprintf("http://%s/s?%s", host, querystring)
	}
	this.addNotice("searchUrl", searchUrl)
	searchReq, err := http.NewRequest("GET", searchUrl, nil)
	if err != nil {
		return nil, err
	}

	searchReq.Header = oriRequest.Header

	// 修复请求BFE的BAIDULOC_BFESS回写问题，手百版本13.45及以上时，需要回写
	adaptions := this.getAdaption()
	bdVersion := (*adaptions)["bd_version"]
	if version.Compare(bdVersion, "13.45", ">=") {
		this.adaptCookieFromBFE(searchReq)
	}

	if searchReq.Header.Get("Clientip") == "" {
		clientip := this.ctx.ClientIP()
		if common.IsIPv6(clientip) {
			searchReq.Header.Set("Clientip6", clientip)
		} else {
			searchReq.Header.Set("Clientip", clientip)
		}
	}
	searchReq.Host = "m.baidu.com"

	if common.IsEnableBr(this.ctx) {
		searchReq.Header.Set("Accept-Encoding", "br,gzip")
	} else {
		searchReq.Header.Set("Accept-Encoding", "gzip")
	}

	searchReq.Header.Set("Connection", "close")
	searchReq.Header.Set("X-Ssl-Header", "1")

	// 请求预取结果的混合协议格式
	if common.IsUseCSMIXED(request) {
		searchReq.Header.Set("Accept", "application/x-protobuffer")
	}

	_, err = json.Marshal(searchReq.Header)
	if err != nil {
		this.warning("searchRequestHeaderJsonErr", err.Error())
	}

	return searchReq, nil
}

func (s *SeachClient) adaptCookieFromBFE(req *http.Request) {
	if req == nil {
		return
	}

	cookieBAIDULOC, errLOC := req.Cookie("BAIDULOC")
	_, errLOCBFESS := req.Cookie("BAIDULOC_BFESS")

	if errLOC == nil && errLOCBFESS != nil {
		req.AddCookie(&http.Cookie{
			Name:  "BAIDULOC_BFESS",
			Value: cookieBAIDULOC.Value,
		})
	}
}

func (this *SeachClient) Do(query string) (resp *http.Response, err error) {
	searchReq, err := this.BuildSearchRequest(query)
	common.DumpData(this.ctx, "2nginx", searchReq)
	if err != nil {
		this.warning("buildSearchRequestErr", err.Error())
		return nil, err
	}

	this.ctx.TimerStart("requestPrNginxCost")
	client := &http.Client{
		Timeout: time.Second * 6,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	resp, respErr := client.Do(searchReq)
	this.addNotice("requestPrNginxCost", fmt.Sprintf("%.2f", this.ctx.TimerSince("requestPrNginxCost").Seconds()*1000))
	if respErr != nil {
		this.warning("requestPrNginxErr", respErr.Error())
		return nil, respErr
	}
	this.ctx.AddNotice("respChunkedMode", resp.TransferEncoding)
	return resp, respErr
}

func (this *SeachClient) MakeTwoInOneResponse(packId uint32, isFin uint32, hasMore uint32, status idl.StatusVal, responseType idl.TypeVal, responseBytes []byte) *idl.SearchResponse {
	encoding := "json"
	if responseType == idl.TypeVal_SEARCH_BODY {
		encoding = "text/html;charset=utf-8"
	}
	searchPbPart := &idl.SearchResultPart{
		FinStream: proto.Uint32(isFin),
		More:      proto.Uint32(hasMore),
		Encoding:  proto.String(encoding),
	}
	searchPbResonse := &idl.SearchResponse{
		TraceId: proto.String(this.getLogidStr()),
		PackId:  proto.Uint32(packId),
		Type:    responseType.Enum(),
		Status:  status.Enum(),
	}

	// 根据responseType填充对应pb字段数据
	switch responseType {
	case idl.TypeVal_SUG:
		searchPbPart.TextData = proto.String(string(responseBytes))
		searchPbResonse.Sug = searchPbPart
	case idl.TypeVal_SEARCH_HEADER:
		searchPbPart.TextData = proto.String(string(responseBytes))
		searchPbResonse.Header = searchPbPart
	case idl.TypeVal_SEARCH_BODY:
		searchPbPart.HtmlData = responseBytes
		searchPbResonse.Result = searchPbPart
	}
	return searchPbResonse
}

// 根据混合协议构造数据
func (this *SeachClient) MakeCSMixedResponse(packId uint32, isFin uint32, hasMore uint32, status csmixed.StatusVal,
	responseType csmixed.TypeVal, resBytes []byte) *csmixed.SearchResponse {
	encoding := "json"
	if responseType == csmixed.TypeVal_A_PAGE {
		encoding = "text/html;charset=utf-8"
	}

	searchPbPart := &csmixed.SearchResultPart{
		FinStream: proto.Uint32(isFin),
		More:      proto.Uint32(hasMore),
		Encoding:  proto.String(encoding),
	}
	res := &csmixed.SearchResponse{
		TraceId: proto.String(this.getLogidStr()),
		PackId:  proto.Uint32(packId),
		Type:    responseType.Enum(),
		Status:  status.Enum(),
	}

	// 根据responseType填充对应pb字段数据
	switch responseType {
	case csmixed.TypeVal_SUG:
		searchPbPart.TextData = proto.String(string(resBytes))
		res.Sug = searchPbPart
	case csmixed.TypeVal_SEARCH_HEADER:
		searchPbPart.TextData = proto.String(string(resBytes))
		res.Header = searchPbPart
	case csmixed.TypeVal_A_PAGE:
		searchPbPart.HtmlData = resBytes
		res.Result = searchPbPart
	}

	return res
}

func (this *SeachClient) ParseRequest() {
	v, ok := (*this.getRequest())["premode"]
	if !ok {
		this.Mode = "UNKOWN"
		return
	}
	premode, ok := expsid[v]
	if !ok {
		this.Mode = "UNKOWN"
	}
	this.Mode = premode

	// 解析cs-w-sids
	// 首先从cookie里面取
	oriRequest := this.getHttpRequest()
	cs_w_sids, err := oriRequest.Cookie("CS_W_SIDS")
	if err != nil { // try to get from header
		cs_w_sids_header := oriRequest.Header.Get("CS_W_SIDS")
		if cs_w_sids_header == "" {
			this.CsWSids = "UNKOWN"
		}
		this.CsWSids = cs_w_sids_header
	} else { // get from cookie
		this.CsWSids = cs_w_sids.Value
	}

	// this.ctx.AddNotice("CS_W_SIDS", this.CsWSids)

	return
}
