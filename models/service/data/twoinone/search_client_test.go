package data

import (
	"bytes"
	"net/http"
	"net/http/httptest"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
)

func createGetWebContext() *gdp.WebContext {
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	bodyio := bytes.NewBuffer([]byte{})
	c.Request, _ = http.NewRequest("GET", `/suggest?query=be`, bodyio)

	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1234", "127.0.0.1"),
	}

	return wc
}
