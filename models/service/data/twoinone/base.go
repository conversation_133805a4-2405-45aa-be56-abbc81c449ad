package data

import (
	"net/http"
	"fmt"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type BaseData struct {
	ctx *gdp.WebContext
}

func (this *BaseData) getRequest() *map[string]string {
	var reqParams map[string]string
	if val, exists := this.ctx.Get(constant.REQPARAMS); exists && val != nil {
		reqParams, _ = val.(map[string]string)
	}
	return &reqParams
}

func (this *BaseData) getAdaption() *map[string]string {
	var adaption map[string]string
	if val, exists := this.ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}
	return &adaption
}

func (this *BaseData) getHttpRequest() *http.Request {
	return this.ctx.Context.Request
}

func (this *BaseData) addNotice(key, value string) {
	this.ctx.AddNotice(key, value)
}

func (this *BaseData) warning(key, value string) {
	this.ctx.Warning(fmt.Errorf("%s:%s", key, value))
}

func (this *BaseData) getLogidStr() string {

	return this.ctx.GetLogID()
}