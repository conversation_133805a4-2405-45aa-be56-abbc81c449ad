package data

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

const (
	monitorCardServer = "his_monitor_server"
	monitorCardSa     = "ai_his_survey"
	daytimeImage      = "https://gips2.baidu.com/it/u=922514866,3403013656&fm=3028&app=3028&f=PNG&fmt=auto&q=97&size=f66_48"
	nightImage        = "https://gips2.baidu.com/it/u=354667471,1221865150&fm=3028&app=3028&f=PNG&fmt=auto&q=97&size=f66_48"
	darkImage         = "https://gips3.baidu.com/it/u=922514866,3403013656&fm=3028&app=3028&f=PNG&fmt=auto&q=97&size=f66_48"
)

type MonitorRes struct {
	Title   string `json:"title"`
	CanShow int    `json:"canShow"`
	JumpURL string `json:"jumpUrl"`
}

type MonitorCard struct {
	BaseData
	TplData map[string]interface{}
}

func NewMonitorCard(ctx *gdp.WebContext) *MonitorCard {
	dataSrv := MonitorCard{}
	dataSrv.ctx = ctx
	dataSrv.TplData = make(map[string]interface{})
	return &dataSrv
}

func (m *MonitorCard) GetResponse(uid string) error {
	if uid == "" {
		return errors.New("uid is empty")
	}
	if m.isFromFeedScene() {
		return errors.New("feed scene not support")
	}
	// 直接使用参数构建请求
	req := m.buildRequest(uid)
	common.DumpData(m.ctx, "HisMonitorCard_entry", req)

	resp := rawResp{}
	err := ral.Ral(monitorCardServer, *req, &resp, ral.RAWConverter)
	if err != nil {
		return err
	}

	resData := MonitorRes{}
	err = json.Unmarshal(resp.Body, &resData)
	if err != nil {
		return err
	}
	return m.parseResponse(resData)
}

func (m *MonitorCard) parseResponse(res MonitorRes) error {
	if res.CanShow != 1 {
		return errors.New("his monitor canShow err")
	}
	if res.Title == "" {
		return errors.New("his monitor response title is empty")
	}
	if res.JumpURL == "" {
		return errors.New("his monitor response JumpURL is empty")
	}
	m.TplData["text"] = res.Title
	m.TplData["survey"] = map[string]interface{}{
		"link": res.JumpURL,
	}
	m.TplData["tag_style"] = map[string]interface{}{
		"image":       daytimeImage,
		"night_image": nightImage,
		"dark_image":  darkImage,
		"tag_type":    2,
		"w_h_ratio":   1.375,
	}
	m.TplData["sa"] = monitorCardSa
	return nil
}

func (m *MonitorCard) buildRequest(uid string) *ral.HTTPRequest {
	// 传递cookie
	cs := m.getHTTPRequest().Cookies()
	cstr := ""
	for _, cv := range cs {
		if cv.Name == "statistics_first_referer" {
			continue
		}
		if cstr != "" {
			cstr += " "
		}
		cstr += cv.String() + ";"
	}

	params := url.Values{}
	params.Set("cuid", uid)

	// 构建http请求
	req := &ral.HTTPRequest{
		Header: map[string][]string{
			"Cookie": {cstr},
		},
		Method:      http.MethodGet,
		Path:        "/gsearch/his_monitor/show_entry?" + params.Encode(),
		QueryParams: params,
		Converter:   ral.JSONConverter,
		Ctx:         m.ctx,
	}
	return req
}

func (m *MonitorCard) isFromFeedScene() bool {
	request := m.getRequest()
	if request == nil {
		return false
	}
	dataQuery := (*request)["data"]
	if dataQuery == "" {
		return false
	}
	reqdata := map[string]interface{}{}
	if err := json.Unmarshal([]byte(dataQuery), &reqdata); err != nil {
		return false
	}
	contentInfo, ok := reqdata["content_info"].(string)
	if !ok || contentInfo == "" {
		return false
	}
	contentdata := map[string]interface{}{}
	if err := json.Unmarshal([]byte(contentInfo), &contentdata); err != nil {
		return false
	}
	if v, exit := contentdata["vid_rec"].([]interface{}); exit && len(v) > 0 {
		return true
	}
	return false
}
