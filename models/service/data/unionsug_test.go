//http://wiki.baidu.com/pages/viewpage.action?pageId=590990038
//http://wiki.baidu.com/pages/viewpage.action?pageId=576601410

package data

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewUnionSugNew(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewUnionSugNew ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := UnionSugNew{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*UnionSugNew
	}{
		{
			"TestNewUnionSugNew",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewUnionSugNew(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewUnionSugNew, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*UnionSugNew))
	}
	ag.Run()
}

func TestUnionSugNew_BuildUnionSugRequest(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		query string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildUnionSugRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &UnionSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.BuildUnionSugRequest(tt.args.query)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestUnionSugNew_BuildUnionSugRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(ral.HTTPRequest))
	}
	ag.Run()
}

func TestUnionSugNew_ParseOpenSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		query    string
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseOpenSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &UnionSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		err := this.ParseOpenSugResponse(tt.args.query, tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestUnionSugNew_ParseOpenSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestUnionSugNew_decodeUnionSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		jsonstring []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: decodeUnionSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Wisesugnew_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &UnionSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got, err := this.decodeUnionSugResponse(tt.args.jsonstring)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestUnionSugNew_decodeUnionSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestUnionSugNew_decodeUnionSugResponse, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesugnew_Response))
	}
	ag.Run()
}

func TestUnionSugNew_isJsonpRet(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		body string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: isJsonpRet ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &UnionSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.isJsonpRet(tt.args.body)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestUnionSugNew_isJsonpRet, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func TestUnionSugNew_setUnionsugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}
	type args struct {
		r Wisesugnew_Response
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setUnionsugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &UnionSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		this.setUnionsugTplData(tt.args.r)
	}
	ag.Run()
}

func TestUnionSugNew_GetUnionsugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		opensugTplData Wisesugnew_Response
		CurResponse    resp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetUnionsugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Wisesugnew_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &UnionSugNew{
			BaseData:       tt.fields.BaseData,
			opensugTplData: tt.fields.opensugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.GetUnionsugTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestUnionSugNew_GetUnionsugTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesugnew_Response))
	}
	ag.Run()
}
