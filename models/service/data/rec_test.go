package data

import (
	"errors"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"gopkg.in/go-playground/assert.v1"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/gdp/ral/protocol"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

func TestNewRec(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewRec ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := Rec{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*Rec
	}{
		{
			"TestNewRec",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewRec(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewRec, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*Rec))
	}
	ag.Run()
}

func TestRec_BuildRecRequest(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData map[string]TypeDispaly
	}
	type args struct {
		reparams   *map[string]string
		adptparams *map[string]string
		dataParams DataParms
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildRecRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	ctx.Request.AddCookie(&http.Cookie{
		Name:     "g_attr",
		Value:    url.QueryEscape("ut_3-at_1715084877-id_1019105u"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	baseData := BaseData{
		ctx: ctx,
	}

	patch := gomonkey.NewPatches()
	defer patch.Reset()
	patch.ApplyFunc(common.GetSampleValue, func(ctx *gdp.WebContext, key string, defaultValue string) string {
		if key == "his_yunying" {
			return "1"
		}
		return defaultValue
	})

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want
	}{
		// TODO: Add test cases.
		{
			"test_NeedNewUserHis",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"cip": "************"},
				adptparams: &map[string]string{"bd_version": "*********"},
				dataParams: DataParms{
					NeedNewUserHis: true,
				},
			},
			Want{
				"1000535",
				ut.ShouldContainSubstring,
			},
		},
		{
			"test_g_attr",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"cip": "************"},
				adptparams: &map[string]string{"bd_version": "*********"},
				dataParams: DataParms{
					Eventtype: "hissug",
				},
			},
			Want{
				"ut_3-at_1715084877-id_1019105u",
				ut.ShouldContainSubstring,
			},
		},
		{
			"test_user_privacy",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams: &map[string]string{
					"cip":      "************",
					"osbranch": "a0",
				},
				adptparams: &map[string]string{"bd_version": "*********"},
				dataParams: DataParms{
					Eventtype:     "hissug",
					PrivacyString: "GI3TKMRQGE3GELLEMUYDMLJUMQ3TGLLBG44DGLJSGVSWEYZSMM3DQOJTGA",
				},
			},
			Want{
				"2752016b-de06-4d73-a783-25ebc2c68930",
				ut.ShouldContainSubstring,
			},
		},
		{
			"test_Tbundle",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"cip": "************", "tbundle": "tomasBasics"},
				adptparams: &map[string]string{"bd_version": "*********"},
				dataParams: DataParms{
					NeedNewUserHis: true,
				},
			},
			Want{
				"1000535",
				ut.ShouldContainSubstring,
			},
		},
		{
			"红包运营资源, 添加成功",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"realOsbranch": "a0"},
				adptparams: &map[string]string{"bd_version": "*********"},
				dataParams: DataParms{
					Eventtype: "hissug",
				},
			},
			Want{
				"1000539",
				ut.ShouldContainSubstring,
			},
		},
		{
			"红包运营资源, 添加失败1",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"realOsbranch": "a0"},
				adptparams: &map[string]string{"bd_version": "*********"},
			},
			Want{
				"1000539",
				ut.ShouldNotContainSubstring,
			},
		},
		{
			"红包运营资源, 添加失败2",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"osbranch": "a10"},
				adptparams: &map[string]string{"bd_version": "*********"},
			},
			Want{
				"1000539",
				ut.ShouldNotContainSubstring,
			},
		},
	}

	for k, tt := range tests {
		this := &Rec{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.buildRecRequest(tt.args.reparams, tt.args.adptparams, tt.args.dataParams)
		ag.Add(fmt.Sprintf(" Test Case Of TestRec_BuildRecRequest, Result Index:%d Value Compare", k), got[0].HTTPRequest.Body, tt.want.Assert, tt.want.Value)
	}
	ag.Run()
}

func TestRec_ParseHISResponse(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData map[string]TypeDispaly
	}
	type args struct {
		response string
		ch       chan ChRawHTTPResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseHISResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Rec{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}

		err := this.ParseHISResponse(tt.args.ch)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestRec_ParseHISResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestRec_setHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData map[string]TypeDispaly
	}
	type args struct {
		r map[string]TypeDispaly
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Rec{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		this.setHISTplData(tt.args.r)
	}
	ag.Run()
}

func TestRec_GetHISTplData(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData map[string]TypeDispaly
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetHISTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //TypeDispaly
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Rec{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.GetHISTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestRec_GetHISTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(TypeDispaly))
	}
	ag.Run()
}

func Test_handleParams(t *testing.T) {
	type args struct {
		osBranch  string
		bdVersion string
		os        string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: handleParams ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"Test_handleParams",
			args{
				"i0",
				"11.15.0.0",
				"android",
			},
		},
	}

	for _, tt := range tests {
		handleParams(tt.args.osBranch, tt.args.bdVersion, tt.args.os)
	}
	ag.Run()
}

func Test_getUserPrivacyInfo(t *testing.T) {
	type args struct {
		cuid string
		ctx  *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: SetUserPrivacyInfo")
	defer utInst.RestoreAll()
	utInst.MockFunc(common.GetUserPrivacyString, MockGetUserPrivacyString)

	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want
	}{
		{
			"Test_SetUserPrivacyInfo_withValidCuid",
			args{
				"E0B83431B171A|V77RVWHHR",
				createGetWebContext(),
			},
			Want{
				&common.UserPrivacyInfo{
					OAID: "123",
				},
				ut.ShouldResemble,
			},
		},
		{
			"Test_SetUserPrivacyInfo_withEmptyCuid",
			args{
				"",
				createGetWebContext(),
			},
			Want{
				nil,
				ut.ShouldBeNil,
			},
		},
		{
			"Test_SetUserPrivacyInfo_withNoPipe",
			args{
				"E0B83431B171A",
				createGetWebContext(),
			},
			Want{
				&common.UserPrivacyInfo{
					OAID: "123",
				},
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		common.SetUserPrivacyInfo(tt.args.cuid, tt.args.ctx)
		userPrivacyInfo, ok := tt.args.ctx.Get("userPrivacyInfo")
		if !ok && tt.want.Value != nil {
			ag.Add(fmt.Sprintf("Test Case Of Test_SetUserPrivacyInfo, Result Index:%d Value Compare", k), nil, tt.want.Assert, tt.want.Value)
		} else {
			ag.Add(fmt.Sprintf("Test Case Of Test_SetUserPrivacyInfo, Result Index:%d Value Compare", k), userPrivacyInfo, tt.want.Assert, tt.want.Value)
		}
	}
	ag.Run()
}

func MockGetUserPrivacyString(_ string, _ *gdp.WebContext) common.UserPrivacyInfo {
	return common.UserPrivacyInfo{
		OAID: "123",
	}
}

func TestRecNewFunc(t *testing.T) {
	_ = t
	r := Rec{}
	r.ctx = createGetWebContext()
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFuncReturn(ral.Ral, nil)
	r.MultiRequest([]NamedHTTPReq{{Name: "123", HTTPRequest: &protocol.HTTPRequest{}}}, make(chan ChRawHTTPResp, 1))
	ch := make(chan ChRawHTTPResp, 2)
	ch <- ChRawHTTPResp{Err: errors.New("123"), Name: BackendRec}
	ch <- ChRawHTTPResp{Err: errors.New("123"), Name: BackendRecVideo}
	close(ch)
	_ = r.ParseHISResponse(ch)
	ch = make(chan ChRawHTTPResp, 2)
	ch <- ChRawHTTPResp{Name: BackendRec}
	ch <- ChRawHTTPResp{Name: BackendRecVideo}
	close(ch)
	_ = r.ParseHISResponse(ch)
	ch = make(chan ChRawHTTPResp, 2)
	ch <- ChRawHTTPResp{Name: BackendRec}
	ch <- ChRawHTTPResp{Err: errors.New("123"), Name: BackendRecVideo}
	close(ch)
	_ = r.ParseHISResponse(ch)
	r.setHISTplData(make(map[string]TypeDispaly))
	r.GetHISTplData()
	_, _ = r.BuildRequest(new(map[string]string), new(map[string]string), DataParms{})
	_ = r.GetRecResponse(new(map[string]string), new(map[string]string), DataParms{})

}

func TestGetHisYunyingJson(t *testing.T) {
	ctx := createGetWebContext()
	common.SwitchConfMem.HisYunyingBlackCity = map[string]int{
		"北京": 1,
	}
	common.SwitchConfMem.Campaigns = []common.Campaign{
		{
			ID:                "1",
			RecallProbability: 1.0,
		},
	}

	address := map[string]string{}
	reqParams := map[string]string{}
	ret := getHisYunyingJson(ctx, reqParams, address, []string{"0"}, "main")
	assert.Equal(t, ret, "{\"address\":{},\"cuid_mapping\":0,\"user_pack_id\":\"0\"}")

	reqParams["cuid_mapping"] = "1"
	ret = getHisYunyingJson(ctx, reqParams, address, []string{"101"}, "main")
	assert.Equal(t, ret, "{\"address\":{},\"cuid_mapping\":1,\"user_pack_id\":\"101\"}")

	// 命中屏蔽城市
	address["province"] = "北京"
	ret = getHisYunyingJson(ctx, reqParams, address, []string{"101"}, "main")
	assert.Equal(t, ret, "{\"address\":{\"province\":\"北京\"},\"cuid_mapping\":1,\"user_pack_id\":\"120\"}")
}

func TestGetCityCode(t *testing.T) {
	// 时间有效
	ctx := createGetWebContext()
	now := time.Now().Unix()*1000 - 1000
	loc := "__100000_131_" + strconv.FormatInt(now, 10) + "_1"
	ctx.Request.AddCookie(&http.Cookie{
		Name:     "BAIDULOCNEW",
		Value:    loc,
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})

	ret := getCityCode(ctx)
	assert.Equal(t, ret, "131")
}

func TestGetRandomID(t *testing.T) {
	value := "100,101|0,100"
	ret := getRandomID(value)
	assert.Equal(t, ret, "101")

	value = "100,101,102|0,100,0"
	ret = getRandomID(value)
	assert.Equal(t, ret, "101")

	value = "100,101,102|0,100"
	ret = getRandomID(value)
	assert.Equal(t, ret, "")

	value = "100,101,102|0,80,0"
	ret = getRandomID(value)
	assert.Equal(t, ret, "")

	value = "100,101,102"
	ret = getRandomID(value)
	assert.Equal(t, ret, "")

	value = "100,101,102|0,100,xxa"
	ret = getRandomID(value)
	assert.Equal(t, ret, "")
}

func TestRec_BuildRequestWithPrivacyInfo(t *testing.T) {
	type fields struct {
		BaseData   BaseData
		hisTplData map[string]TypeDispaly
	}
	type args struct {
		reparams   *map[string]string
		adptparams *map[string]string
		dataParams DataParms
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for Rec_BuildRequest with UserPrivacyInfo")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	// 创建Web上下文
	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}

	// 创建模拟的UserPrivacyInfo对象
	privacyInfo := common.UserPrivacyInfo{
		Model: "Pixel 6",
		OsVer: "Android 12",
		Manu:  "Google",
		CookieLocation: common.CookieLocation{
			Radius:   500,
			CityCode: 131,
			TmMs:     1624562487000,
		},
	}

	// 将UserPrivacyInfo存入上下文
	ctx.Set("userPrivacyInfo", &privacyInfo)

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wants         []Want
	}{
		{
			"test_android_privacy_info",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"cip": "************"},
				adptparams: &map[string]string{"isAndroid": "1", "bd_version": "*********"},
				dataParams: DataParms{
					Eventtype: "hissug",
				},
			},
			[]Want{
				{
					"Google", // 期望android设备使用Manu作为brand
					ut.ShouldContainSubstring,
				},
				{
					"Android 12", // 期望OsVer正确传递
					ut.ShouldContainSubstring,
				},
				{
					"Pixel 6", // 期望Model正确传递
					ut.ShouldContainSubstring,
				},
			},
		},
		{
			"test_iphone_privacy_info",
			fields{
				baseData,
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"cip": "************"},
				adptparams: &map[string]string{"isAndroid": "0", "bd_version": "*********"},
				dataParams: DataParms{
					Eventtype: "hissug",
				},
			},
			[]Want{
				{
					"apple", // 期望iPhone设备使用"apple"作为brand
					ut.ShouldContainSubstring,
				},
				{
					"Android 12", // OsVer依然应该正确传递
					ut.ShouldContainSubstring,
				},
				{
					"Pixel 6", // Model依然应该正确传递
					ut.ShouldContainSubstring,
				},
				{
					"131", // CityCode应该正确传递
					ut.ShouldContainSubstring,
				},
			},
		},
		{
			"test_null_privacy_info",
			fields{
				BaseData{
					ctx: createGetWebContext(), // 创建一个新的上下文，不设置userPrivacyInfo
				},
				make(map[string]TypeDispaly),
			},
			args{
				reparams:   &map[string]string{"cip": "************"},
				adptparams: &map[string]string{"isAndroid": "1", "bd_version": "*********"},
				dataParams: DataParms{
					Eventtype: "hissug",
				},
			},
			[]Want{
				{
					"Google", // 应该不包含，因为没有设置UserPrivacyInfo
					ut.ShouldNotContainSubstring,
				},
				{
					"Android 12", // 应该不包含，因为没有设置UserPrivacyInfo
					ut.ShouldNotContainSubstring,
				},
			},
		},
	}

	for k, tt := range tests {
		this := &Rec{
			BaseData:   tt.fields.BaseData,
			hisTplData: tt.fields.hisTplData,
		}
		got := this.buildRecRequest(tt.args.reparams, tt.args.adptparams, tt.args.dataParams)
		for i, want := range tt.wants {
			ag.Add(fmt.Sprintf("Test Case %d of TestRec_BuildRequestWithPrivacyInfo, Check %d", k, i),
				got[0].HTTPRequest.Body, want.Assert, want.Value)
		}
	}
	ag.Run()
}

func TestReplaceAtmosphereColorForTemp(t *testing.T) {
	type args struct {
		ac        map[string]interface{}
		adaptions map[string]string
	}

	tests := []struct {
		name          string
		args          args
		expectedColor string
		shouldReplace bool
	}{
		{
			name: "版本低于15.21，不应替换",
			args: args{
				ac: map[string]interface{}{
					"data": map[string]interface{}{
						"hispage_atmosphere_day_up_gradientcolor":   "#ABCDEF",
						"hispage_atmosphere_day_down_gradientcolor": "#123456",
					},
				},
				adaptions: map[string]string{
					"bd_version": "15.20",
				},
			},
			expectedColor: "#ABCDEF",
			shouldReplace: false,
		},
		{
			name: "版本等于15.21，应替换",
			args: args{
				ac: map[string]interface{}{
					"data": map[string]interface{}{
						"hispage_atmosphere_day_up_gradientcolor":   "#ABCDEF",
						"hispage_atmosphere_day_down_gradientcolor": "#123456",
					},
				},
				adaptions: map[string]string{
					"bd_version": "15.21",
				},
			},
			expectedColor: "#00FFFFFF",
			shouldReplace: true,
		},
		{
			name: "缺少字段，不应 panic",
			args: args{
				ac: map[string]interface{}{
					"data": map[string]interface{}{}, // 无字段
				},
				adaptions: map[string]string{
					"bd_version": "15.21",
				},
			},
			expectedColor: "", // 不重要
			shouldReplace: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			replaceAtmosphereColorForTemp(tt.args.ac, tt.args.adaptions)

			data := tt.args.ac["data"].(map[string]interface{})

			upColor, upOK := data["hispage_atmosphere_day_up_gradientcolor"].(string)
			downColor, downOK := data["hispage_atmosphere_day_down_gradientcolor"].(string)

			if tt.shouldReplace {
				assert.Equal(t, "#00FFFFFF", upColor)
				assert.Equal(t, "#00FFFFFF", downColor)
			} else {
				if upOK {
					assert.NotEqual(t, "#00FFFFFF", upColor)
				}
				if downOK {
					assert.NotEqual(t, "#00FFFFFF", downColor)
				}
			}
		})
	}
}

// 测试三个header开关功能的单元测试
func TestRec_HeaderSwitchesInBuildRecRequest(t *testing.T) {
	// 创建辅助函数来设置请求头
	createContextWithHeaders := func(headers map[string]string) *gdp.WebContext {
		ctx := createGetWebContext()
		// 设置header
		for key, value := range headers {
			ctx.Request.Header.Set(key, value)
		}
		return ctx
	}

	// 测试用例结构
	testCases := []struct {
		name          string
		headers       map[string]string
		expectStrs    []string
		notExpectStrs []string
	}{
		{
			name:       "测试无痕模式开关 - 值为1",
			headers:    map[string]string{"incognito-mode": "1"},
			expectStrs: []string{`\"incognito_mode\":1`},
		},
		{
			name:       "测试无痕模式开关 - 值为0",
			headers:    map[string]string{"incognito-mode": "0"},
			expectStrs: []string{`\"incognito_mode\":0`},
		},
		{
			name:       "测试个性化推荐开关 - 值为1",
			headers:    map[string]string{"personal-switch": "1"},
			expectStrs: []string{`\"person_rec\":1`},
		},
		{
			name:       "测试个性化推荐开关 - 值为0",
			headers:    map[string]string{"personal-switch": "0"},
			expectStrs: []string{`\"person_rec\":0`},
		},
		{
			name:       "测试智能化开关 - 值为1",
			headers:    map[string]string{"user-intelligent-mode": "1"},
			expectStrs: []string{`\"user_intelligent_mode\":1`},
		},
		{
			name:       "测试智能化开关 - 值为0",
			headers:    map[string]string{"user-intelligent-mode": "0"},
			expectStrs: []string{`\"user_intelligent_mode\":0`},
		},
		{
			name: "测试所有三个开关同时设置",
			headers: map[string]string{
				"incognito-mode":        "1",
				"personal-switch":       "0",
				"user-intelligent-mode": "1",
			},
			expectStrs: []string{
				`\"incognito_mode\":1`,
				`\"person_rec\":0`,
				`\"user_intelligent_mode\":1`,
			},
		},
		{
			name: "测试header值为非数字时不处理",
			headers: map[string]string{
				"incognito-mode":        "invalid",
				"personal-switch":       "abc",
				"user-intelligent-mode": "xyz",
			},
			notExpectStrs: []string{
				`\"incognito_mode\":`,
				`\"person_rec\":`,
				`\"user_intelligent_mode\":`,
			},
		},
		{
			name:    "测试没有设置任何header开关",
			headers: map[string]string{}, // 空的header
			notExpectStrs: []string{
				`\"incognito_mode\":`,
				`\"person_rec\":`,
				`\"user_intelligent_mode\":`,
			},
		},
	}

	// 运行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建带有指定header的上下文
			ctx := createContextWithHeaders(tc.headers)
			baseData := BaseData{ctx: ctx}

			rec := &Rec{
				BaseData:   baseData,
				hisTplData: make(map[string]TypeDispaly),
			}

			// 构建请求参数
			reparams := &map[string]string{"cip": "************"}
			adptparams := &map[string]string{"bd_version": "*********"}
			dataParams := DataParms{Eventtype: "hissug"}

			// 调用被测试的方法
			got := rec.buildRecRequest(reparams, adptparams, dataParams)

			if len(got) == 0 {
				t.Errorf("测试用例 %s: 没有生成请求", tc.name)
				return
			}

			// 获取第一个请求的body进行验证
			requestBody, ok := got[0].HTTPRequest.Body.(string)
			if !ok {
				t.Errorf("测试用例 %s: 无法将请求体转换为字符串类型", tc.name)
				return
			}

			// 验证期望包含的字符串
			for _, expectStr := range tc.expectStrs {
				if !strings.Contains(requestBody, expectStr) {
					t.Errorf("测试用例 %s: 期望包含 '%s', 但在请求体中未找到\n请求体: %s",
						tc.name, expectStr, requestBody)
				}
			}

			// 验证期望不包含的字符串
			for _, notExpectStr := range tc.notExpectStrs {
				if strings.Contains(requestBody, notExpectStr) {
					t.Errorf("测试用例 %s: 不期望包含 '%s', 但在请求体中找到了\n请求体: %s",
						tc.name, notExpectStr, requestBody)
				}
			}
		})
	}
}

// TestRec_processAIToolData 测试processAIToolData函数
func TestRec_processAIToolData(t *testing.T) {
	utInst := ut.New(t, "Unit tests for processAIToolData function")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		name           string
		setupContext   func() *gdp.WebContext
		inputData      map[string]interface{}
		expectedResult map[string]interface{}
		description    string
	}{
		{
			name: "正常情况-有aiToolTypes数据",
			setupContext: func() *gdp.WebContext {
				ctx := createGetWebContext()
				ctx.Set("aiToolTypes", []string{"search", "translate", "calculator"})
				return ctx
			},
			inputData: map[string]interface{}{
				"existing_key": "existing_value",
			},
			expectedResult: map[string]interface{}{
				"existing_key": "existing_value",
				"ai_tool_data": []map[string]string{
					{"type": "search", "text": "search"},
					{"type": "translate", "text": "translate"},
					{"type": "calculator", "text": "calculator"},
				},
			},
			description: "当context中有aiToolTypes时，应该正确构造ai_tool_data",
		},
		{
			name: "context中没有aiToolTypes",
			setupContext: func() *gdp.WebContext {
				ctx := createGetWebContext()
				// 不设置aiToolTypes
				return ctx
			},
			inputData: map[string]interface{}{
				"existing_key": "existing_value",
			},
			expectedResult: map[string]interface{}{
				"existing_key": "existing_value",
				// 不应该有ai_tool_data字段
			},
			description: "当context中没有aiToolTypes时，不应该添加ai_tool_data字段",
		},
		{
			name: "aiToolTypes为空数组",
			setupContext: func() *gdp.WebContext {
				ctx := createGetWebContext()
				ctx.Set("aiToolTypes", []string{})
				return ctx
			},
			inputData: map[string]interface{}{
				"existing_key": "existing_value",
			},
			expectedResult: map[string]interface{}{
				"existing_key": "existing_value",
				"ai_tool_data": []map[string]string{},
			},
			description: "当aiToolTypes为空数组时，应该设置空的ai_tool_data数组",
		},
		{
			name: "aiToolTypes包含单个元素",
			setupContext: func() *gdp.WebContext {
				ctx := createGetWebContext()
				ctx.Set("aiToolTypes", []string{"search"})
				return ctx
			},
			inputData: map[string]interface{}{
				"existing_key": "existing_value",
			},
			expectedResult: map[string]interface{}{
				"existing_key": "existing_value",
				"ai_tool_data": []map[string]string{
					{"type": "search", "text": "search"},
				},
			},
			description: "当aiToolTypes只有一个元素时，应该正确构造单元素的ai_tool_data",
		},
		{
			name: "aiToolTypes类型不匹配",
			setupContext: func() *gdp.WebContext {
				ctx := createGetWebContext()
				ctx.Set("aiToolTypes", "not_a_slice") // 设置错误的类型
				return ctx
			},
			inputData: map[string]interface{}{
				"existing_key": "existing_value",
			},
			expectedResult: map[string]interface{}{
				"existing_key": "existing_value",
				// 不应该有ai_tool_data字段，因为类型转换失败
			},
			description: "当aiToolTypes类型不匹配时，不应该添加ai_tool_data字段",
		},
	}

	for k, tt := range tests {
		// 创建Rec实例
		ctx := tt.setupContext()
		rec := &Rec{}
		rec.ctx = ctx

		// 复制输入数据以避免修改原始数据
		incidental := make(map[string]interface{})
		for key, value := range tt.inputData {
			incidental[key] = value
		}

		// 调用被测试的函数
		rec.processAIToolData(incidental)

		// 验证结果长度
		ag.Add(fmt.Sprintf("Test Case %d: %s - map length", k, tt.name),
			len(incidental), ut.ShouldEqual, len(tt.expectedResult))

		// 验证每个期望的字段
		for key, expectedValue := range tt.expectedResult {
			actualValue, exists := incidental[key]
			ag.Add(fmt.Sprintf("Test Case %d: %s - key '%s' exists", k, tt.name, key),
				exists, ut.ShouldBeTrue)
			if exists {
				ag.Add(fmt.Sprintf("Test Case %d: %s - key '%s' value", k, tt.name, key),
					actualValue, ut.ShouldResemble, expectedValue)
			}
		}

		// 验证不应该存在的字段
		if _, hasAIToolData := tt.expectedResult["ai_tool_data"]; !hasAIToolData {
			_, actualHasAIToolData := incidental["ai_tool_data"]
			ag.Add(fmt.Sprintf("Test Case %d: %s - should not have ai_tool_data", k, tt.name),
				actualHasAIToolData, ut.ShouldBeFalse)
		}
	}

	ag.Run()
}
