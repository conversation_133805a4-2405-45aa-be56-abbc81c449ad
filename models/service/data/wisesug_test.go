package data

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestNewWiseSug(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: NewWiseSug ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	dataSrv := Wisesug{}
	dataSrv.ctx = ctx

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //*Wisesug
	}{
		{
			"TestNewWiseSug",
			args{
				ctx,
			},
			Want{
				&dataSrv,
				ut.ShouldResemble,
			},
		},
	}

	for k, tt := range tests {
		got := NewWiseSug(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestNewWiseSug, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(*Wisesug))
	}
	ag.Run()
}

func TestWisesug_BuildWiseSugRequest(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesug_Response
		CurResponse    resp
	}
	type args struct {
		query string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: BuildWiseSugRequest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //ral.HTTPRequest
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesug{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.BuildWiseSugRequest(tt.args.query)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesug_BuildWiseSugRequest, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(ral.HTTPRequest))
	}
	ag.Run()
}

func TestWisesug_ParseWiseSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesug_Response
		CurResponse    resp
	}
	type args struct {
		query    string
		response httpResp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: ParseWiseSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	ctx := createGetWebContext()
	baseData := BaseData{
		ctx: ctx,
	}
	es := Wisesug_Response{}
	rs := resp{}
	hrs := httpResp{}

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		wantErr       bool
	}{
		{
			"TestWisesug_ParseWiseSugResponse",
			fields{
				baseData,
				es,
				rs,
			},
			args{
				"test",
				hrs,
			},
			true,
		},
	}

	for k, tt := range tests {
		this := &Wisesug{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		err := this.ParseWiseSugResponse(tt.args.query, tt.args.response)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesug_ParseWiseSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
		}
	}
	ag.Run()
}

func TestWisesug_decodeWiseSugResponse(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesug_Response
		CurResponse    resp
	}
	type args struct {
		jsonstring []byte
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: decodeWiseSugResponse ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //Wisesug_Response
		wantErr       bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesug{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got, err := this.decodeWiseSugResponse(tt.args.jsonstring)
		if err != nil {
			ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesug_decodeWiseSugResponse, Error Value Compare", err != nil, ut.ShouldEqual, tt.wantErr)
			continue
		}
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesug_decodeWiseSugResponse, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesug_Response))
	}
	ag.Run()
}

func TestWisesug_isJsonpRet(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesug_Response
		CurResponse    resp
	}
	type args struct {
		body string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: isJsonpRet ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
		want          Want //bool
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesug{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.isJsonpRet(tt.args.body)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesug_isJsonpRet, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(bool))
	}
	ag.Run()
}

func TestWisesug_setWisesugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesug_Response
		CurResponse    resp
	}
	type args struct {
		r Wisesug_Response
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: setWisesugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		args          args
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		this := &Wisesug{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		this.setWisesugTplData(tt.args.r)
	}
	ag.Run()
}

func TestWisesug_GetWisesugTplData(t *testing.T) {
	type fields struct {
		BaseData       BaseData
		wisesugTplData Wisesug_Response
		CurResponse    resp
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: GetWisesugTplData ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		fields        fields
		want          Want //Wisesug_Response
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		this := &Wisesug{
			BaseData:       tt.fields.BaseData,
			wisesugTplData: tt.fields.wisesugTplData,
			CurResponse:    tt.fields.CurResponse,
		}
		got := this.GetWisesugTplData()
		ag.Add(strconv.Itoa(k)+" Test Case Of TestWisesug_GetWisesugTplData, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(Wisesug_Response))
	}
	ag.Run()
}

func Test_min(t *testing.T) {
	type args struct {
		left  int
		right int
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: min ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	tests := []struct {
		testCaseTitle string
		args          args
		want          Want //int
	}{
		// TODO: Add test cases.
	}

	for k, tt := range tests {
		got := min(tt.args.left, tt.args.right)
		ag.Add(strconv.Itoa(k)+" Test Case Of Test_min, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(int))
	}
	ag.Run()
}
