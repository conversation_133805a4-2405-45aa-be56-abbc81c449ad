package data

import (
	"fmt"
	"math/rand"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

type Hot struct {
	BaseData
	hotData map[string]interface{}
}

func NewHot(ctx *gdp.WebContext) *Hot {
	dataSrv := Hot{}
	dataSrv.ctx = ctx
	return &dataSrv
}

func (h *Hot) GetHotResponse() error {
	// 共需要12个搜索发现query
	needNum := 12

	// 用于对query去重
	querySet := map[string]struct{}{}

	guessList := []string{}
	// 随机添加guess热词
	guessList = h.appendData(guessList, needNum/2, "guess_hot.toml", querySet)
	// 随机添加guess高点击词
	guessList = h.appendData(guessList, needNum-len(guessList), "guess_high_click.toml", querySet)
	// 增加健壮性，2份词典取出的总query达到12个，就可行
	guessList = h.appendData(guessList, needNum-len(guessList), "guess_hot.toml", querySet)

	if len(guessList) != needNum {
		return fmt.Errorf("need at least 12 guess, %d", len(guessList))
	}

	// 获取 6 个随机商业词
	randomADList := []string{}
	randomADList = h.appendData(randomADList, len(guessList)/2, "ad_query.toml", querySet)

	// 合并普通和商业query
	newGuessList := h.mergeData(guessList, randomADList)

	if len(randomADList) != len(guessList)/2 {
		h.addNotice("randomADShort", strconv.Itoa(len(randomADList)))
	}

	res := map[string]interface{}{
		"list":      newGuessList,
		"moreUrl":   "baiduboxapp://v15/searchframe/searchbox?extra=%7B%7D",
		"moreUrlH5": "https://m.baidu.com",
	}

	h.setHISTplData(res)
	return nil
}

// 获取搜索发现数组
func (h *Hot) appendData(dataArr []string, appendNum int, dictName string, querySet map[string]struct{}) []string {
	if appendNum == 0 {
		return dataArr
	}

	queryList := h.getQueryByConf(dictName)
	if len(queryList) == 0 {
		return dataArr
	}

	// 候选词数量不足
	if len(queryList) <= appendNum {
		for _, query := range queryList {
			if strings.TrimSpace(query) == "" {
				continue
			}

			// 判断是否已经存在了
			_, exist := querySet[query]
			if !exist {
				dataArr = append(dataArr, query)
				querySet[query] = struct{}{}
			}
		}

		rand.Shuffle(len(dataArr), func(i, j int) {
			dataArr[i], dataArr[j] = dataArr[j], dataArr[i]
		})

		return dataArr
	}

	rand.Seed(time.Now().UnixNano())

	// 防止陷入死循环
	maxRandNum := 1000

	cnt := 0
	for cnt < appendNum {
		maxRandNum--
		if maxRandNum == 0 {
			h.addNotice("maxRandNum", dictName)
			break
		}

		random := rand.Intn(len(queryList))

		query := ""
		if random < len(queryList) {
			query = queryList[random]
		}
		if strings.TrimSpace(query) == "" {
			continue
		}

		// 判断是否已经存在了
		_, exist := querySet[query]
		if !exist {
			dataArr = append(dataArr, query)
			querySet[query] = struct{}{}
			cnt++
		}

	}

	rand.Shuffle(len(dataArr), func(i, j int) {
		dataArr[i], dataArr[j] = dataArr[j], dataArr[i]
	})

	return dataArr
}

// 商业词数据
type adQuery struct {
	Query []string `toml:"query"`
}

// 搜索发现热词
type guessHot struct {
	Query []string `toml:"query"`
}

// 搜索发现高点击词
type guessHighClick struct {
	Query []string `toml:"query"`
}

func (h *Hot) getQueryByConf(dictName string) []string {
	var queryList adQuery

	filePath := filepath.Join(env.ConfRootPath(), dictName)
	_, err := toml.DecodeFile(filePath, &queryList)
	if err != nil {
		h.addNotice("getDictErr", err.Error())
		return []string{}
	}

	if len(queryList.Query) == 0 {
		h.addNotice("getDictErr", dictName)
		return []string{}
	}

	return queryList.Query
}

// 将普通搜索发现词和商业词merge
func (h *Hot) mergeData(guessList []string, randomADList []string) []interface{} {
	newGuessList := []interface{}{}
	// 取到了第几个广告词
	adCur := 0
	index := 0

	for i, one := range guessList {
		newGuessList = append(newGuessList, map[string]interface{}{
			"query": one,
			"url":   h.genSchema(one),
			"urlH5": h.getURL(one),
			"index": index,
		})
		index++

		if (i+1)%2 == 0 {
			insertADQuery := ""
			if adCur < len(randomADList) {
				insertADQuery = randomADList[adCur]
				adCur++
			}

			if strings.TrimSpace(insertADQuery) == "" {
				continue
			}

			newGuessList = append(newGuessList, map[string]interface{}{
				"query": insertADQuery,
				"url":   h.genSchema(insertADQuery),
				"urlH5": h.getURL(insertADQuery),
				"index": index,
			})
			index++
		}
	}

	return newGuessList
}

// 跳转到端内URL
func (h *Hot) genSchema(query interface{}) string {
	queryString, ok := query.(string)
	if !ok || queryString == "" {
		return ""
	}

	// 端上打点使用
	logargs := h.getLogargs()

	res := fmt.Sprintf("baiduboxapp://v1/browser/search?upgrade=1&stay=1&query=%s&simple=0&newwindow=0&append=1&logargs=%s&needlog=1",
		url.QueryEscape(queryString),
		url.QueryEscape(logargs),
	)

	return res
}

// logargs参数
func (h *Hot) getLogargs() string {
	request := h.getRequest()
	oFrom := (*request)[constant.OFrom]
	if oFrom == "" {
		return ""
	}

	schema := `{"channel":"%s","from":"openbox","page":"other","source":"%s","type":"","value":"url"}`

	return fmt.Sprintf(schema, oFrom, oFrom)
}

// 跳转到端外URL
func (h *Hot) getURL(query interface{}) string {
	v, ok := query.(string)
	if !ok || v == "" {
		return ""
	}

	request := h.getRequest()
	oFrom := (*request)[constant.OFrom]

	jumpURL := fmt.Sprintf("https://m.baidu.com/s?word=%s", url.QueryEscape(v))
	if oFrom != "" {
		jumpURL += "&from=" + oFrom
	}

	return jumpURL
}

func (h *Hot) setHISTplData(d map[string]interface{}) {
	h.hotData = d
}

func (h *Hot) GetHISTplData() map[string]interface{} {
	return h.hotData
}
