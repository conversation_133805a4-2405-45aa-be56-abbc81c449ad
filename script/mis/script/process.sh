#!/bin/bash
curr_path=`pwd`
conf_path=$curr_path/../baidu/searchbox/go-suggest/conf
conf="prod.toml,route.toml,switch.toml,twoinone.toml"
online_conf="prod.toml,route.toml,switch.toml,twoinone.toml"
packname=output
curr_date=`date +%Y%m%d%H%M%S`
output_path=$curr_path/../output

# git pull
cd $conf_path && git pull

arr=(${conf//,/ })
arr_online=(${online_conf//,/ })
i=0
updated=false


cd $output_path
rm *.toml

for eachconf in ${arr[*]}
do
    cp $conf_path/$eachconf  $curr_path/data
    cd $curr_path 
    if [ -f "data/$eachconf.md5" ]; then 
        mv data/$eachconf.md5 data/$eachconf.last.md5
    fi
    md5sum data/$eachconf > data/$eachconf.md5

    diff=`cmp data/${eachconf}.md5  data/${eachconf}.last.md5`
    #diff="1"
   if [ "$diff" != "" ]; then
       echo "check diff ok"
       #备份配置文件
       online_conf=${arr_online[$i]}
       if [ -f "bak_conf/${online_conf}" ]; then
           mv bak_conf/${online_conf} bak_conf/${online_conf}.${curr_date}
       fi
       # 拷贝最新 生成md5
       cp data/${eachconf} bak_conf/${online_conf}
       md5sum bak_conf/${online_conf} >  bak_conf/${online_conf}.md5
       #打包上线
       cp bak_conf/${online_conf} $output_path
       updated=true
    fi
    let i+=1
done

j=0;
if $updated;then
    for each in ${arr[*]}
    do
	cp data/${arr[j]} $output_path/${arr_online[j]}
	let j+=1 
    done
    cd $output_path
    rm ${packname}.tar.bz2
    rm ${packname}.tar.bz2.md5
    tar jcvf ${packname}.tar.bz2  *.toml
    md5sum ${packname}.tar.bz2 > ${packname}.tar.bz2.md5
fi
