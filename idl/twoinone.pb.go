// Code generated by protoc-gen-go. DO NOT EDIT.
// source: twoinone.proto

package idl

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type StatusVal int32

const (
	StatusVal_OK                    StatusVal = 0
	StatusVal_FATAL_ERR             StatusVal = 1
	StatusVal_EXP_SEARCH_RESULT_ERR StatusVal = 2
)

var StatusVal_name = map[int32]string{
	0: "OK",
	1: "FATAL_ERR",
	2: "EXP_SEARCH_RESULT_ERR",
}
var StatusVal_value = map[string]int32{
	"OK":                    0,
	"FATAL_ERR":             1,
	"EXP_SEARCH_RESULT_ERR": 2,
}

func (x StatusVal) Enum() *StatusVal {
	p := new(StatusVal)
	*p = x
	return p
}
func (x StatusVal) String() string {
	return proto.EnumName(StatusVal_name, int32(x))
}
func (x *StatusVal) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StatusVal_value, data, "StatusVal")
	if err != nil {
		return err
	}
	*x = StatusVal(value)
	return nil
}
func (StatusVal) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_twoinone_dc6fe608ec6c559b, []int{0}
}

type TypeVal int32

const (
	TypeVal_SUG           TypeVal = 0
	TypeVal_SEARCH_HEADER TypeVal = 1
	TypeVal_SEARCH_BODY   TypeVal = 2
)

var TypeVal_name = map[int32]string{
	0: "SUG",
	1: "SEARCH_HEADER",
	2: "SEARCH_BODY",
}
var TypeVal_value = map[string]int32{
	"SUG":           0,
	"SEARCH_HEADER": 1,
	"SEARCH_BODY":   2,
}

func (x TypeVal) Enum() *TypeVal {
	p := new(TypeVal)
	*p = x
	return p
}
func (x TypeVal) String() string {
	return proto.EnumName(TypeVal_name, int32(x))
}
func (x *TypeVal) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TypeVal_value, data, "TypeVal")
	if err != nil {
		return err
	}
	*x = TypeVal(value)
	return nil
}
func (TypeVal) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_twoinone_dc6fe608ec6c559b, []int{1}
}

type SearchResponse struct {
	TraceId              *string           `protobuf:"bytes,1,req,name=trace_id,json=traceId" json:"trace_id,omitempty"`
	Type                 *TypeVal          `protobuf:"varint,2,req,name=type,enum=idl.TypeVal" json:"type,omitempty"`
	Status               *StatusVal        `protobuf:"varint,3,req,name=status,enum=idl.StatusVal" json:"status,omitempty"`
	PackId               *uint32           `protobuf:"varint,4,req,name=pack_id,json=packId" json:"pack_id,omitempty"`
	Sug                  *SearchResultPart `protobuf:"bytes,5,opt,name=sug" json:"sug,omitempty"`
	Header               *SearchResultPart `protobuf:"bytes,6,opt,name=header" json:"header,omitempty"`
	Result               *SearchResultPart `protobuf:"bytes,7,opt,name=result" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SearchResponse) Reset()         { *m = SearchResponse{} }
func (m *SearchResponse) String() string { return proto.CompactTextString(m) }
func (*SearchResponse) ProtoMessage()    {}
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_twoinone_dc6fe608ec6c559b, []int{0}
}
func (m *SearchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchResponse.Unmarshal(m, b)
}
func (m *SearchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchResponse.Marshal(b, m, deterministic)
}
func (dst *SearchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchResponse.Merge(dst, src)
}
func (m *SearchResponse) XXX_Size() int {
	return xxx_messageInfo_SearchResponse.Size(m)
}
func (m *SearchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchResponse proto.InternalMessageInfo

func (m *SearchResponse) GetTraceId() string {
	if m != nil && m.TraceId != nil {
		return *m.TraceId
	}
	return ""
}

func (m *SearchResponse) GetType() TypeVal {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return TypeVal_SUG
}

func (m *SearchResponse) GetStatus() StatusVal {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return StatusVal_OK
}

func (m *SearchResponse) GetPackId() uint32 {
	if m != nil && m.PackId != nil {
		return *m.PackId
	}
	return 0
}

func (m *SearchResponse) GetSug() *SearchResultPart {
	if m != nil {
		return m.Sug
	}
	return nil
}

func (m *SearchResponse) GetHeader() *SearchResultPart {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *SearchResponse) GetResult() *SearchResultPart {
	if m != nil {
		return m.Result
	}
	return nil
}

type SearchResultPart struct {
	Encoding             *string  `protobuf:"bytes,1,req,name=encoding" json:"encoding,omitempty"`
	FinStream            *uint32  `protobuf:"varint,2,req,name=fin_stream,json=finStream" json:"fin_stream,omitempty"`
	TextData             *string  `protobuf:"bytes,3,opt,name=text_data,json=textData" json:"text_data,omitempty"`
	HtmlData             []byte   `protobuf:"bytes,4,opt,name=html_data,json=htmlData" json:"html_data,omitempty"`
	More                 *uint32  `protobuf:"varint,5,opt,name=more" json:"more,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchResultPart) Reset()         { *m = SearchResultPart{} }
func (m *SearchResultPart) String() string { return proto.CompactTextString(m) }
func (*SearchResultPart) ProtoMessage()    {}
func (*SearchResultPart) Descriptor() ([]byte, []int) {
	return fileDescriptor_twoinone_dc6fe608ec6c559b, []int{1}
}
func (m *SearchResultPart) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchResultPart.Unmarshal(m, b)
}
func (m *SearchResultPart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchResultPart.Marshal(b, m, deterministic)
}
func (dst *SearchResultPart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchResultPart.Merge(dst, src)
}
func (m *SearchResultPart) XXX_Size() int {
	return xxx_messageInfo_SearchResultPart.Size(m)
}
func (m *SearchResultPart) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchResultPart.DiscardUnknown(m)
}

var xxx_messageInfo_SearchResultPart proto.InternalMessageInfo

func (m *SearchResultPart) GetEncoding() string {
	if m != nil && m.Encoding != nil {
		return *m.Encoding
	}
	return ""
}

func (m *SearchResultPart) GetFinStream() uint32 {
	if m != nil && m.FinStream != nil {
		return *m.FinStream
	}
	return 0
}

func (m *SearchResultPart) GetTextData() string {
	if m != nil && m.TextData != nil {
		return *m.TextData
	}
	return ""
}

func (m *SearchResultPart) GetHtmlData() []byte {
	if m != nil {
		return m.HtmlData
	}
	return nil
}

func (m *SearchResultPart) GetMore() uint32 {
	if m != nil && m.More != nil {
		return *m.More
	}
	return 0
}

func init() {
	proto.RegisterType((*SearchResponse)(nil), "idl.SearchResponse")
	proto.RegisterType((*SearchResultPart)(nil), "idl.SearchResultPart")
	proto.RegisterEnum("idl.StatusVal", StatusVal_name, StatusVal_value)
	proto.RegisterEnum("idl.TypeVal", TypeVal_name, TypeVal_value)
}

func init() { proto.RegisterFile("twoinone.proto", fileDescriptor_twoinone_dc6fe608ec6c559b) }

var fileDescriptor_twoinone_dc6fe608ec6c559b = []byte{
	// 388 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x91, 0x41, 0x8f, 0x93, 0x40,
	0x14, 0xc7, 0x97, 0x01, 0xa1, 0xbc, 0x5d, 0x10, 0x5f, 0xb2, 0x91, 0xd5, 0x98, 0x90, 0x3d, 0x28,
	0xd9, 0xc4, 0x1e, 0xf6, 0xe0, 0xcd, 0x03, 0x5a, 0x74, 0x37, 0x6e, 0xb2, 0xcd, 0xd0, 0x1a, 0x3d,
	0x91, 0x09, 0x33, 0x6d, 0x89, 0x14, 0xc8, 0x30, 0x8d, 0xf6, 0x83, 0x78, 0xf2, 0xcb, 0x9a, 0x99,
	0x62, 0x0f, 0x1e, 0x7a, 0xe3, 0xfd, 0x7f, 0xbf, 0xc7, 0x4c, 0xfe, 0x03, 0xa1, 0xfa, 0xd9, 0xd5,
	0x6d, 0xd7, 0x8a, 0x69, 0x2f, 0x3b, 0xd5, 0xa1, 0x5d, 0xf3, 0xe6, 0xfa, 0x37, 0x81, 0xb0, 0x10,
	0x4c, 0x56, 0x1b, 0x2a, 0x86, 0xbe, 0x6b, 0x07, 0x81, 0x57, 0x30, 0x51, 0x92, 0x55, 0xa2, 0xac,
	0x79, 0x6c, 0x25, 0x24, 0xf5, 0xa9, 0x67, 0xe6, 0x7b, 0x8e, 0x09, 0x38, 0x6a, 0xdf, 0x8b, 0x98,
	0x24, 0x24, 0x0d, 0x6f, 0x2f, 0xa6, 0x35, 0x6f, 0xa6, 0x8b, 0x7d, 0x2f, 0xbe, 0xb2, 0x86, 0x1a,
	0x82, 0xaf, 0xc1, 0x1d, 0x14, 0x53, 0xbb, 0x21, 0xb6, 0x8d, 0x13, 0x1a, 0xa7, 0x30, 0x91, 0xb6,
	0x46, 0x8a, 0xcf, 0xc1, 0xeb, 0x59, 0xf5, 0x43, 0x9f, 0xe1, 0x24, 0x24, 0x0d, 0xa8, 0xab, 0xc7,
	0x7b, 0x8e, 0x6f, 0xc0, 0x1e, 0x76, 0xeb, 0xf8, 0x49, 0x62, 0xa5, 0xe7, 0xb7, 0x97, 0x87, 0xed,
	0x7f, 0xf7, 0xdb, 0x35, 0x6a, 0xce, 0xa4, 0xa2, 0xda, 0xc0, 0xb7, 0xe0, 0x6e, 0x04, 0xe3, 0x42,
	0xc6, 0xee, 0x29, 0x77, 0x94, 0xb4, 0x2e, 0x4d, 0x1a, 0x7b, 0x27, 0xf5, 0x83, 0x74, 0xfd, 0xc7,
	0x82, 0xe8, 0x7f, 0x88, 0x2f, 0x60, 0x22, 0xda, 0xaa, 0xe3, 0x75, 0xbb, 0x1e, 0x9b, 0x39, 0xce,
	0xf8, 0x0a, 0x60, 0x55, 0xb7, 0xe5, 0xa0, 0xa4, 0x60, 0x5b, 0x53, 0x50, 0x40, 0xfd, 0x55, 0xdd,
	0x16, 0x26, 0xc0, 0x97, 0xe0, 0x2b, 0xf1, 0x4b, 0x95, 0x9c, 0x29, 0x16, 0xdb, 0x89, 0xa5, 0x77,
	0x75, 0x30, 0x63, 0x8a, 0x69, 0xb8, 0x51, 0xdb, 0xe6, 0x00, 0x9d, 0xc4, 0x4a, 0x2f, 0xe8, 0x44,
	0x07, 0x06, 0x22, 0x38, 0xdb, 0x4e, 0x0a, 0xd3, 0x48, 0x40, 0xcd, 0xf7, 0xcd, 0x7b, 0xf0, 0x8f,
	0x95, 0xa2, 0x0b, 0xe4, 0xf1, 0x4b, 0x74, 0x86, 0x01, 0xf8, 0x9f, 0xb2, 0x45, 0xf6, 0x50, 0xe6,
	0x94, 0x46, 0x16, 0x5e, 0xc1, 0x65, 0xfe, 0x6d, 0x5e, 0x16, 0x79, 0x46, 0x3f, 0xde, 0x95, 0x34,
	0x2f, 0x96, 0x0f, 0x0b, 0x83, 0xc8, 0xcd, 0x3b, 0xf0, 0xc6, 0x57, 0x43, 0x0f, 0xec, 0x62, 0xf9,
	0x39, 0x3a, 0xc3, 0x67, 0x10, 0x8c, 0xea, 0x5d, 0x9e, 0xcd, 0x72, 0xfd, 0x87, 0xa7, 0x70, 0x3e,
	0x46, 0x1f, 0x1e, 0x67, 0xdf, 0x23, 0xf2, 0x37, 0x00, 0x00, 0xff, 0xff, 0xd1, 0xef, 0x31, 0xda,
	0x42, 0x02, 0x00, 0x00,
}
