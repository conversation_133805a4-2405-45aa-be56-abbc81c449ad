syntax = "proto2";

package idl;


enum StatusVal { // 数据状态枚举
    OK = 0; // 数据正常
    FATAL_ERR = 1; // 下游异常导致数据出错，此时端需要丢弃已接收到的对应数据。比如status为1时type=resut，那应该丢弃本次预取结果；如果status=1时type=sug，那应该丢弃本次sug结果
    EXP_SEARCH_RESULT_ERR = 2;  // 搜索数据COOK分包失败
}
 
enum TypeVal {
    SUG = 0;
    SEARCH_HEADER = 1;
    SEARCH_BODY = 2;
}
 
 // sug&预取二合一response

message SearchResponse {
    required string trace_id = 1;   // 直接用端传的参数traceid，没有的情况下用go-na的logid
    required TypeVal    type = 2;       // 包内容类型，取值：sug/header/result等
    required StatusVal status = 3;     
    required uint32 pack_id = 4;       //标识包id，从第一个pb包开始，从0递增
    optional SearchResultPart sug = 5;    //可选字段，sug的rensponse，原sugResponse的json encode字符串
    optional SearchResultPart header = 6;     //可选字段，pr-nginx返回的header信息
    optional SearchResultPart result = 7; //可选字段，搜索结果的分包内容
}
 
message SearchResultPart {
    required string encoding = 1;     //数据的编码格式；type=result时，取值等同于HTTP协议header中的Content-Type；type=sug/header时，取值为json；
    required uint32 fin_stream = 2;    //标识对应数据是否最后一个包
    optional string text_data = 3;    //字符串数据， sug和header数据放于该字段
    optional bytes html_data = 4;    //html数据， 搜索结果放于该字段
    optional uint32 more = 5;    //标识改数据流下是否还有其他类型的包返回
}