// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.15.8
// source: megafoil_common.proto

package megafoil

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DataType int32

const (
	DataType_disp                  DataType = 1
	DataType_click                 DataType = 2
	DataType_video_disp            DataType = 8
	DataType_uap                   DataType = 16
	DataType_query_context_wise    DataType = 32
	DataType_user_click_urlinfo    DataType = 64
	DataType_query_persona_attr    DataType = 128
	DataType_cook_que_persona_attr DataType = 256
	DataType_query_type_attr       DataType = 512
	DataType_cook_novel_attr       DataType = 1024
	DataType_cook_video_attr       DataType = 2048
	DataType_query_nlp_attr        DataType = 4096
	DataType_cookie_nlp_attr       DataType = 8192
	DataType_query_cookie_nlp_attr DataType = 16384
	DataType_uap2                  DataType = 32768
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		1:     "disp",
		2:     "click",
		8:     "video_disp",
		16:    "uap",
		32:    "query_context_wise",
		64:    "user_click_urlinfo",
		128:   "query_persona_attr",
		256:   "cook_que_persona_attr",
		512:   "query_type_attr",
		1024:  "cook_novel_attr",
		2048:  "cook_video_attr",
		4096:  "query_nlp_attr",
		8192:  "cookie_nlp_attr",
		16384: "query_cookie_nlp_attr",
		32768: "uap2",
	}
	DataType_value = map[string]int32{
		"disp":                  1,
		"click":                 2,
		"video_disp":            8,
		"uap":                   16,
		"query_context_wise":    32,
		"user_click_urlinfo":    64,
		"query_persona_attr":    128,
		"cook_que_persona_attr": 256,
		"query_type_attr":       512,
		"cook_novel_attr":       1024,
		"cook_video_attr":       2048,
		"query_nlp_attr":        4096,
		"cookie_nlp_attr":       8192,
		"query_cookie_nlp_attr": 16384,
		"uap2":                  32768,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_megafoil_common_proto_enumTypes[0].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_megafoil_common_proto_enumTypes[0]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DataType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DataType(num)
	return nil
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_megafoil_common_proto_rawDescGZIP(), []int{0}
}

type RequestType int32

const (
	RequestType_megafoil_data RequestType = 0
	RequestType_ums_data      RequestType = 1
)

// Enum value maps for RequestType.
var (
	RequestType_name = map[int32]string{
		0: "megafoil_data",
		1: "ums_data",
	}
	RequestType_value = map[string]int32{
		"megafoil_data": 0,
		"ums_data":      1,
	}
)

func (x RequestType) Enum() *RequestType {
	p := new(RequestType)
	*p = x
	return p
}

func (x RequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_megafoil_common_proto_enumTypes[1].Descriptor()
}

func (RequestType) Type() protoreflect.EnumType {
	return &file_megafoil_common_proto_enumTypes[1]
}

func (x RequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RequestType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RequestType(num)
	return nil
}

// Deprecated: Use RequestType.Descriptor instead.
func (RequestType) EnumDescriptor() ([]byte, []int) {
	return file_megafoil_common_proto_rawDescGZIP(), []int{1}
}

type KeyType int32

const (
	KeyType_baiduid      KeyType = 0 // cookie 中的baiduid
	KeyType_qid          KeyType = 1 // 搜索pv所对应的id
	KeyType_cuid         KeyType = 2 // 手持设备所使用的user id
	KeyType_QUERY        KeyType = 3
	KeyType_PRE_QUERY    KeyType = 4
	KeyType_USER_ID      KeyType = 5
	KeyType_WORDSEG_TYPE KeyType = 6
)

// Enum value maps for KeyType.
var (
	KeyType_name = map[int32]string{
		0: "baiduid",
		1: "qid",
		2: "cuid",
		3: "QUERY",
		4: "PRE_QUERY",
		5: "USER_ID",
		6: "WORDSEG_TYPE",
	}
	KeyType_value = map[string]int32{
		"baiduid":      0,
		"qid":          1,
		"cuid":         2,
		"QUERY":        3,
		"PRE_QUERY":    4,
		"USER_ID":      5,
		"WORDSEG_TYPE": 6,
	}
)

func (x KeyType) Enum() *KeyType {
	p := new(KeyType)
	*p = x
	return p
}

func (x KeyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KeyType) Descriptor() protoreflect.EnumDescriptor {
	return file_megafoil_common_proto_enumTypes[2].Descriptor()
}

func (KeyType) Type() protoreflect.EnumType {
	return &file_megafoil_common_proto_enumTypes[2]
}

func (x KeyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *KeyType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = KeyType(num)
	return nil
}

// Deprecated: Use KeyType.Descriptor instead.
func (KeyType) EnumDescriptor() ([]byte, []int) {
	return file_megafoil_common_proto_rawDescGZIP(), []int{2}
}

type SubKeyType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyType []byte `protobuf:"bytes,1,req,name=key_type,json=keyType" json:"key_type,omitempty"` //目前支持 BAIDUID, QUERY, CUID, QID
	Key     []byte `protobuf:"bytes,2,req,name=key" json:"key,omitempty"`                        //key 可以是baiduid或者用下划线分隔的qid
}

func (x *SubKeyType) Reset() {
	*x = SubKeyType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubKeyType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubKeyType) ProtoMessage() {}

func (x *SubKeyType) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubKeyType.ProtoReflect.Descriptor instead.
func (*SubKeyType) Descriptor() ([]byte, []int) {
	return file_megafoil_common_proto_rawDescGZIP(), []int{0}
}

func (x *SubKeyType) GetKeyType() []byte {
	if x != nil {
		return x.KeyType
	}
	return nil
}

func (x *SubKeyType) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

var File_megafoil_common_proto protoreflect.FileDescriptor

var file_megafoil_common_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69,
	0x6c, 0x22, 0x39, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x0c, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x2a, 0xb3, 0x02, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x64, 0x69, 0x73,
	0x70, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x10, 0x02, 0x12, 0x0e,
	0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x10, 0x08, 0x12, 0x07,
	0x0a, 0x03, 0x75, 0x61, 0x70, 0x10, 0x10, 0x12, 0x16, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x77, 0x69, 0x73, 0x65, 0x10, 0x20, 0x12,
	0x16, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x69, 0x6e, 0x66, 0x6f, 0x10, 0x40, 0x12, 0x17, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x10, 0x80, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x63, 0x6f, 0x6f, 0x6b, 0x5f, 0x71, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x10, 0x80, 0x02, 0x12, 0x14, 0x0a, 0x0f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x10,
	0x80, 0x04, 0x12, 0x14, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x6b, 0x5f, 0x6e, 0x6f, 0x76, 0x65, 0x6c,
	0x5f, 0x61, 0x74, 0x74, 0x72, 0x10, 0x80, 0x08, 0x12, 0x14, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x6b,
	0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x10, 0x80, 0x10, 0x12, 0x13,
	0x0a, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6e, 0x6c, 0x70, 0x5f, 0x61, 0x74, 0x74, 0x72,
	0x10, 0x80, 0x20, 0x12, 0x14, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x5f, 0x6e, 0x6c,
	0x70, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x10, 0x80, 0x40, 0x12, 0x1b, 0x0a, 0x15, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x5f, 0x6e, 0x6c, 0x70, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x10, 0x80, 0x80, 0x01, 0x12, 0x0a, 0x0a, 0x04, 0x75, 0x61, 0x70, 0x32, 0x10, 0x80,
	0x80, 0x02, 0x2a, 0x2e, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x11, 0x0a, 0x0d, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x75, 0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x10, 0x01, 0x2a, 0x62, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x62, 0x61, 0x69, 0x64, 0x75, 0x69, 0x64, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x71, 0x69,
	0x64, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x63, 0x75, 0x69, 0x64, 0x10, 0x02, 0x12, 0x09, 0x0a,
	0x05, 0x51, 0x55, 0x45, 0x52, 0x59, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x45, 0x5f,
	0x51, 0x55, 0x45, 0x52, 0x59, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x49, 0x44, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x4f, 0x52, 0x44, 0x53, 0x45, 0x47, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x06,
}

var (
	file_megafoil_common_proto_rawDescOnce sync.Once
	file_megafoil_common_proto_rawDescData = file_megafoil_common_proto_rawDesc
)

func file_megafoil_common_proto_rawDescGZIP() []byte {
	file_megafoil_common_proto_rawDescOnce.Do(func() {
		file_megafoil_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_megafoil_common_proto_rawDescData)
	})
	return file_megafoil_common_proto_rawDescData
}

var file_megafoil_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_megafoil_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_megafoil_common_proto_goTypes = []interface{}{
	(DataType)(0),      // 0: megafoil.DataType
	(RequestType)(0),   // 1: megafoil.RequestType
	(KeyType)(0),       // 2: megafoil.KeyType
	(*SubKeyType)(nil), // 3: megafoil.SubKeyType
}
var file_megafoil_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_megafoil_common_proto_init() }
func file_megafoil_common_proto_init() {
	if File_megafoil_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_megafoil_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubKeyType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_megafoil_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_megafoil_common_proto_goTypes,
		DependencyIndexes: file_megafoil_common_proto_depIdxs,
		EnumInfos:         file_megafoil_common_proto_enumTypes,
		MessageInfos:      file_megafoil_common_proto_msgTypes,
	}.Build()
	File_megafoil_common_proto = out.File
	file_megafoil_common_proto_rawDesc = nil
	file_megafoil_common_proto_goTypes = nil
	file_megafoil_common_proto_depIdxs = nil
}
