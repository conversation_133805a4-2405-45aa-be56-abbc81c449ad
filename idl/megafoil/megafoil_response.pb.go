// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.15.8
// source: megafoil_response.proto

package megafoil

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrCode int32

const (
	ErrCode_ERR_OK                  ErrCode = 0
	ErrCode_ERR_AUTHORITION_FAILURE ErrCode = 100
	ErrCode_ERR_FLOW_EXCEEDED       ErrCode = 101
	ErrCode_ERR_BAD_BASE64_FORMAT   ErrCode = 6
	ErrCode_ERR_EMPTY_JSON_DATA     ErrCode = 5
	ErrCode_ERR_BAD_JSON_FORMAT     ErrCode = 4
	ErrCode_ERR_BUF_NOT_ENOUGH      ErrCode = 3   //*< 缓冲区大小不够
	ErrCode_ERR_UNSUPPORT           ErrCode = 2   //*< 接口不支持
	ErrCode_ERR_NOT_EXIST           ErrCode = 1   //*< key不存在
	ErrCode_ERR_INTERNAL            ErrCode = -1  //*< 底层API错误，请看日志
	ErrCode_ERR_REINIT              ErrCode = -2  //*< 多次初始化
	ErrCode_ERR_INVALID_PARAM       ErrCode = -3  //*< 参数错误
	ErrCode_ERR_INVALID_FEATURE     ErrCode = -4  //*< 属性异常
	ErrCode_ERR_INVALID_ROW         ErrCode = -5  //*< 数据行列不符合要求
	ErrCode_ERR_SIGNATURE           ErrCode = -6  //*< 签名失败
	ErrCode_ERR_CACHE               ErrCode = -7  //*< 缓存错误
	ErrCode_ERR_SLOT                ErrCode = -8  //*< 槽位错误
	ErrCode_ERR_OPID                ErrCode = -9  //*< 操作 id 非法，有可能已经被取过值了
	ErrCode_ERR_SLOT_FULL           ErrCode = -11 //*< 已经达到最大操作数
	ErrCode_ERR_TIMEOUT             ErrCode = -12 //*< timeout
	ErrCode_ERR_UNIMPLEMENT         ErrCode = -20 //*< 该接口尚未实现
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:   "ERR_OK",
		100: "ERR_AUTHORITION_FAILURE",
		101: "ERR_FLOW_EXCEEDED",
		6:   "ERR_BAD_BASE64_FORMAT",
		5:   "ERR_EMPTY_JSON_DATA",
		4:   "ERR_BAD_JSON_FORMAT",
		3:   "ERR_BUF_NOT_ENOUGH",
		2:   "ERR_UNSUPPORT",
		1:   "ERR_NOT_EXIST",
		-1:  "ERR_INTERNAL",
		-2:  "ERR_REINIT",
		-3:  "ERR_INVALID_PARAM",
		-4:  "ERR_INVALID_FEATURE",
		-5:  "ERR_INVALID_ROW",
		-6:  "ERR_SIGNATURE",
		-7:  "ERR_CACHE",
		-8:  "ERR_SLOT",
		-9:  "ERR_OPID",
		-11: "ERR_SLOT_FULL",
		-12: "ERR_TIMEOUT",
		-20: "ERR_UNIMPLEMENT",
	}
	ErrCode_value = map[string]int32{
		"ERR_OK":                  0,
		"ERR_AUTHORITION_FAILURE": 100,
		"ERR_FLOW_EXCEEDED":       101,
		"ERR_BAD_BASE64_FORMAT":   6,
		"ERR_EMPTY_JSON_DATA":     5,
		"ERR_BAD_JSON_FORMAT":     4,
		"ERR_BUF_NOT_ENOUGH":      3,
		"ERR_UNSUPPORT":           2,
		"ERR_NOT_EXIST":           1,
		"ERR_INTERNAL":            -1,
		"ERR_REINIT":              -2,
		"ERR_INVALID_PARAM":       -3,
		"ERR_INVALID_FEATURE":     -4,
		"ERR_INVALID_ROW":         -5,
		"ERR_SIGNATURE":           -6,
		"ERR_CACHE":               -7,
		"ERR_SLOT":                -8,
		"ERR_OPID":                -9,
		"ERR_SLOT_FULL":           -11,
		"ERR_TIMEOUT":             -12,
		"ERR_UNIMPLEMENT":         -20,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_megafoil_response_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_megafoil_response_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ErrCode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ErrCode(num)
	return nil
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{0}
}

type Source int32

const (
	Source_OTHERS Source = -1
	Source_AC     Source = 1
	Source_SP     Source = 2
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		-1: "OTHERS",
		1:  "AC",
		2:  "SP",
	}
	Source_value = map[string]int32{
		"OTHERS": -1,
		"AC":     1,
		"SP":     2,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_megafoil_response_proto_enumTypes[1].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_megafoil_response_proto_enumTypes[1]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Source) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Source(num)
	return nil
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{1}
}

type MegafoilResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MegafoilData []*MegafoilData `protobuf:"bytes,1,rep,name=megafoil_data,json=megafoilData" json:"megafoil_data,omitempty"`
	UmsData      []*UmsData      `protobuf:"bytes,2,rep,name=ums_data,json=umsData" json:"ums_data,omitempty"`
	ErrNo        *int32          `protobuf:"varint,3,req,name=err_no,json=errNo" json:"err_no,omitempty"`   // 错误号，0：请求成功
	ErrMsg       []byte          `protobuf:"bytes,4,opt,name=err_msg,json=errMsg" json:"err_msg,omitempty"` // 错误原因，当err_no不为0的时候有值
	KeyType      *KeyType        `protobuf:"varint,6,req,name=key_type,json=keyType,enum=megafoil.KeyType" json:"key_type,omitempty"`
	DataType     *uint32         `protobuf:"varint,7,req,name=data_type,json=dataType" json:"data_type,omitempty"`
	RequestType  *RequestType    `protobuf:"varint,8,req,name=request_type,json=requestType,enum=megafoil.RequestType" json:"request_type,omitempty"`
	Keys         []byte          `protobuf:"bytes,9,req,name=keys" json:"keys,omitempty"`  // key 可以是baiduid或者用下划线分隔的qid
	Expr         []byte          `protobuf:"bytes,10,opt,name=expr" json:"expr,omitempty"` // 查询表达式，支持object path 查询
}

func (x *MegafoilResponse) Reset() {
	*x = MegafoilResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilResponse) ProtoMessage() {}

func (x *MegafoilResponse) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilResponse.ProtoReflect.Descriptor instead.
func (*MegafoilResponse) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{0}
}

func (x *MegafoilResponse) GetMegafoilData() []*MegafoilData {
	if x != nil {
		return x.MegafoilData
	}
	return nil
}

func (x *MegafoilResponse) GetUmsData() []*UmsData {
	if x != nil {
		return x.UmsData
	}
	return nil
}

func (x *MegafoilResponse) GetErrNo() int32 {
	if x != nil && x.ErrNo != nil {
		return *x.ErrNo
	}
	return 0
}

func (x *MegafoilResponse) GetErrMsg() []byte {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *MegafoilResponse) GetKeyType() KeyType {
	if x != nil && x.KeyType != nil {
		return *x.KeyType
	}
	return KeyType_baiduid
}

func (x *MegafoilResponse) GetDataType() uint32 {
	if x != nil && x.DataType != nil {
		return *x.DataType
	}
	return 0
}

func (x *MegafoilResponse) GetRequestType() RequestType {
	if x != nil && x.RequestType != nil {
		return *x.RequestType
	}
	return RequestType_megafoil_data
}

func (x *MegafoilResponse) GetKeys() []byte {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *MegafoilResponse) GetExpr() []byte {
	if x != nil {
		return x.Expr
	}
	return nil
}

type MegafoilResponseV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MegafoilData []*MegafoilData `protobuf:"bytes,1,rep,name=megafoil_data,json=megafoilData" json:"megafoil_data,omitempty"`
	UmsData      []*UmsData      `protobuf:"bytes,2,rep,name=ums_data,json=umsData" json:"ums_data,omitempty"`
	ErrNo        *int32          `protobuf:"varint,3,req,name=err_no,json=errNo" json:"err_no,omitempty"`   // 错误号，0：请求成功
	ErrMsg       []byte          `protobuf:"bytes,4,opt,name=err_msg,json=errMsg" json:"err_msg,omitempty"` // 错误原因，当err_no不为0的时候有值
	DataType     *uint32         `protobuf:"varint,7,req,name=data_type,json=dataType" json:"data_type,omitempty"`
	RequestType  *RequestType    `protobuf:"varint,8,req,name=request_type,json=requestType,enum=megafoil.RequestType" json:"request_type,omitempty"`
	SubKey       []*SubKeyType   `protobuf:"bytes,9,rep,name=sub_key,json=subKey" json:"sub_key,omitempty"`
	Expr         []byte          `protobuf:"bytes,10,opt,name=expr" json:"expr,omitempty"` // 查询表达式，支持object path 查询
}

func (x *MegafoilResponseV1) Reset() {
	*x = MegafoilResponseV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilResponseV1) ProtoMessage() {}

func (x *MegafoilResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilResponseV1.ProtoReflect.Descriptor instead.
func (*MegafoilResponseV1) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{1}
}

func (x *MegafoilResponseV1) GetMegafoilData() []*MegafoilData {
	if x != nil {
		return x.MegafoilData
	}
	return nil
}

func (x *MegafoilResponseV1) GetUmsData() []*UmsData {
	if x != nil {
		return x.UmsData
	}
	return nil
}

func (x *MegafoilResponseV1) GetErrNo() int32 {
	if x != nil && x.ErrNo != nil {
		return *x.ErrNo
	}
	return 0
}

func (x *MegafoilResponseV1) GetErrMsg() []byte {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *MegafoilResponseV1) GetDataType() uint32 {
	if x != nil && x.DataType != nil {
		return *x.DataType
	}
	return 0
}

func (x *MegafoilResponseV1) GetRequestType() RequestType {
	if x != nil && x.RequestType != nil {
		return *x.RequestType
	}
	return RequestType_megafoil_data
}

func (x *MegafoilResponseV1) GetSubKey() []*SubKeyType {
	if x != nil {
		return x.SubKey
	}
	return nil
}

func (x *MegafoilResponseV1) GetExpr() []byte {
	if x != nil {
		return x.Expr
	}
	return nil
}

type MegafoilResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response []*MegafoilResponseV1 `protobuf:"bytes,1,rep,name=response" json:"response,omitempty"`
	ErrNo    *int32                `protobuf:"varint,2,req,name=err_no,json=errNo" json:"err_no,omitempty"`   // 错误号，0：请求成功
	ErrMsg   []byte                `protobuf:"bytes,3,opt,name=err_msg,json=errMsg" json:"err_msg,omitempty"` // 错误原因，当err_no不为0的时候有值
}

func (x *MegafoilResponseV2) Reset() {
	*x = MegafoilResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilResponseV2) ProtoMessage() {}

func (x *MegafoilResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilResponseV2.ProtoReflect.Descriptor instead.
func (*MegafoilResponseV2) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{2}
}

func (x *MegafoilResponseV2) GetResponse() []*MegafoilResponseV1 {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *MegafoilResponseV2) GetErrNo() int32 {
	if x != nil && x.ErrNo != nil {
		return *x.ErrNo
	}
	return 0
}

func (x *MegafoilResponseV2) GetErrMsg() []byte {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

type MegafoilResponseV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MegafoilResponse *MegafoilData `protobuf:"bytes,1,opt,name=megafoil_response,json=megafoilResponse" json:"megafoil_response,omitempty"`
	UmsResponse      *UmsData      `protobuf:"bytes,2,opt,name=ums_response,json=umsResponse" json:"ums_response,omitempty"`
	ErrNo            *int32        `protobuf:"varint,3,req,name=err_no,json=errNo" json:"err_no,omitempty"`   // 错误号，0：请求成功
	ErrMsg           []byte        `protobuf:"bytes,4,opt,name=err_msg,json=errMsg" json:"err_msg,omitempty"` // 错误原因，当err_no不为0的时候有值
}

func (x *MegafoilResponseV3) Reset() {
	*x = MegafoilResponseV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilResponseV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilResponseV3) ProtoMessage() {}

func (x *MegafoilResponseV3) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilResponseV3.ProtoReflect.Descriptor instead.
func (*MegafoilResponseV3) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{3}
}

func (x *MegafoilResponseV3) GetMegafoilResponse() *MegafoilData {
	if x != nil {
		return x.MegafoilResponse
	}
	return nil
}

func (x *MegafoilResponseV3) GetUmsResponse() *UmsData {
	if x != nil {
		return x.UmsResponse
	}
	return nil
}

func (x *MegafoilResponseV3) GetErrNo() int32 {
	if x != nil && x.ErrNo != nil {
		return *x.ErrNo
	}
	return 0
}

func (x *MegafoilResponseV3) GetErrMsg() []byte {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

type QueryKeyword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ps_query的签名
	QueryWordSign *uint64 `protobuf:"varint,1,req,name=query_word_sign,json=queryWordSign" json:"query_word_sign,omitempty"`
	// query时间戳
	Timestamp *uint32 `protobuf:"fixed32,2,req,name=timestamp" json:"timestamp,omitempty"`
	// PS query&click中的event_query字段，GBK 编码。
	PsQuery []byte `protobuf:"bytes,3,req,name=ps_query,json=psQuery" json:"ps_query,omitempty"`
	// query 搜索的 频率
	QueryFrequency *uint32                 `protobuf:"varint,4,opt,name=query_frequency,json=queryFrequency" json:"query_frequency,omitempty"`
	Url            []*QueryKeyword_UrlInfo `protobuf:"bytes,5,rep,name=url" json:"url,omitempty"`
	UserIp         []byte                  `protobuf:"bytes,6,opt,name=user_ip,json=userIp" json:"user_ip,omitempty"`
	MobileBrowser  []byte                  `protobuf:"bytes,7,opt,name=mobile_browser,json=mobileBrowser" json:"mobile_browser,omitempty"`
	UserAgent      []byte                  `protobuf:"bytes,8,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	Referer        []byte                  `protobuf:"bytes,9,opt,name=referer" json:"referer,omitempty"`
	QueryWs        []byte                  `protobuf:"bytes,10,opt,name=query_ws,json=queryWs" json:"query_ws,omitempty"` //分词
}

func (x *QueryKeyword) Reset() {
	*x = QueryKeyword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryKeyword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryKeyword) ProtoMessage() {}

func (x *QueryKeyword) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryKeyword.ProtoReflect.Descriptor instead.
func (*QueryKeyword) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{4}
}

func (x *QueryKeyword) GetQueryWordSign() uint64 {
	if x != nil && x.QueryWordSign != nil {
		return *x.QueryWordSign
	}
	return 0
}

func (x *QueryKeyword) GetTimestamp() uint32 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *QueryKeyword) GetPsQuery() []byte {
	if x != nil {
		return x.PsQuery
	}
	return nil
}

func (x *QueryKeyword) GetQueryFrequency() uint32 {
	if x != nil && x.QueryFrequency != nil {
		return *x.QueryFrequency
	}
	return 0
}

func (x *QueryKeyword) GetUrl() []*QueryKeyword_UrlInfo {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *QueryKeyword) GetUserIp() []byte {
	if x != nil {
		return x.UserIp
	}
	return nil
}

func (x *QueryKeyword) GetMobileBrowser() []byte {
	if x != nil {
		return x.MobileBrowser
	}
	return nil
}

func (x *QueryKeyword) GetUserAgent() []byte {
	if x != nil {
		return x.UserAgent
	}
	return nil
}

func (x *QueryKeyword) GetReferer() []byte {
	if x != nil {
		return x.Referer
	}
	return nil
}

func (x *QueryKeyword) GetQueryWs() []byte {
	if x != nil {
		return x.QueryWs
	}
	return nil
}

type SearchAeT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZhaopinUserTag [][]byte `protobuf:"bytes,1,rep,name=zhaopin_user_tag,json=zhaopinUserTag" json:"zhaopin_user_tag,omitempty"`
}

func (x *SearchAeT) Reset() {
	*x = SearchAeT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAeT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAeT) ProtoMessage() {}

func (x *SearchAeT) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAeT.ProtoReflect.Descriptor instead.
func (*SearchAeT) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{5}
}

func (x *SearchAeT) GetZhaopinUserTag() [][]byte {
	if x != nil {
		return x.ZhaopinUserTag
	}
	return nil
}

type UmsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryKeywordWise []*QueryKeyword   `protobuf:"bytes,1,rep,name=query_keyword_wise,json=queryKeywordWise" json:"query_keyword_wise,omitempty"`
	QueryKeyword     []*QueryKeyword   `protobuf:"bytes,2,rep,name=query_keyword,json=queryKeyword" json:"query_keyword,omitempty"`
	QueryPerInfo     *QueryPersonaInfo `protobuf:"bytes,3,opt,name=query_per_info,json=queryPerInfo" json:"query_per_info,omitempty"`
	Uap2             []byte            `protobuf:"bytes,4,opt,name=uap2" json:"uap2,omitempty"`
	Ae               *SearchAeT        `protobuf:"bytes,5,opt,name=ae" json:"ae,omitempty"`
}

func (x *UmsData) Reset() {
	*x = UmsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UmsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmsData) ProtoMessage() {}

func (x *UmsData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmsData.ProtoReflect.Descriptor instead.
func (*UmsData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{6}
}

func (x *UmsData) GetQueryKeywordWise() []*QueryKeyword {
	if x != nil {
		return x.QueryKeywordWise
	}
	return nil
}

func (x *UmsData) GetQueryKeyword() []*QueryKeyword {
	if x != nil {
		return x.QueryKeyword
	}
	return nil
}

func (x *UmsData) GetQueryPerInfo() *QueryPersonaInfo {
	if x != nil {
		return x.QueryPerInfo
	}
	return nil
}

func (x *UmsData) GetUap2() []byte {
	if x != nil {
		return x.Uap2
	}
	return nil
}

func (x *UmsData) GetAe() *SearchAeT {
	if x != nil {
		return x.Ae
	}
	return nil
}

type QueryPersonaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryPersonaAttr   []byte `protobuf:"bytes,1,opt,name=query_persona_attr,json=queryPersonaAttr" json:"query_persona_attr,omitempty"`
	QueryTypeAttr      []byte `protobuf:"bytes,2,opt,name=query_type_attr,json=queryTypeAttr" json:"query_type_attr,omitempty"`
	CookQuePersonaAttr []byte `protobuf:"bytes,3,opt,name=cook_que_persona_attr,json=cookQuePersonaAttr" json:"cook_que_persona_attr,omitempty"`
	CookNovelAttr      []byte `protobuf:"bytes,4,opt,name=cook_novel_attr,json=cookNovelAttr" json:"cook_novel_attr,omitempty"`
	CookVideoAttr      []byte `protobuf:"bytes,5,opt,name=cook_video_attr,json=cookVideoAttr" json:"cook_video_attr,omitempty"`
	QueryNlpAttr       []byte `protobuf:"bytes,6,opt,name=query_nlp_attr,json=queryNlpAttr" json:"query_nlp_attr,omitempty"`
	CookieNlpAttr      []byte `protobuf:"bytes,7,opt,name=cookie_nlp_attr,json=cookieNlpAttr" json:"cookie_nlp_attr,omitempty"`
	QueryCookieNlpAttr []byte `protobuf:"bytes,8,opt,name=query_cookie_nlp_attr,json=queryCookieNlpAttr" json:"query_cookie_nlp_attr,omitempty"`
}

func (x *QueryPersonaInfo) Reset() {
	*x = QueryPersonaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPersonaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPersonaInfo) ProtoMessage() {}

func (x *QueryPersonaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPersonaInfo.ProtoReflect.Descriptor instead.
func (*QueryPersonaInfo) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{7}
}

func (x *QueryPersonaInfo) GetQueryPersonaAttr() []byte {
	if x != nil {
		return x.QueryPersonaAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetQueryTypeAttr() []byte {
	if x != nil {
		return x.QueryTypeAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetCookQuePersonaAttr() []byte {
	if x != nil {
		return x.CookQuePersonaAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetCookNovelAttr() []byte {
	if x != nil {
		return x.CookNovelAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetCookVideoAttr() []byte {
	if x != nil {
		return x.CookVideoAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetQueryNlpAttr() []byte {
	if x != nil {
		return x.QueryNlpAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetCookieNlpAttr() []byte {
	if x != nil {
		return x.CookieNlpAttr
	}
	return nil
}

func (x *QueryPersonaInfo) GetQueryCookieNlpAttr() []byte {
	if x != nil {
		return x.QueryCookieNlpAttr
	}
	return nil
}

type ImageText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name []byte `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"` //图文子信息的名字
	Uri  []byte `protobuf:"bytes,2,opt,name=uri" json:"uri,omitempty"`   //图文子信息uri
}

func (x *ImageText) Reset() {
	*x = ImageText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageText) ProtoMessage() {}

func (x *ImageText) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageText.ProtoReflect.Descriptor instead.
func (*ImageText) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{8}
}

func (x *ImageText) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *ImageText) GetUri() []byte {
	if x != nil {
		return x.Uri
	}
	return nil
}

type Offset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title         []byte       `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`                                        // 标题
	TargetUrl     []byte       `protobuf:"bytes,2,opt,name=target_url,json=targetUrl" json:"target_url,omitempty"`               // target url
	ShowUrl       []byte       `protobuf:"bytes,3,opt,name=show_url,json=showUrl" json:"show_url,omitempty"`                     // show url
	Summary       []byte       `protobuf:"bytes,4,opt,name=summary" json:"summary,omitempty"`                                    // 摘要
	ImageTextInfo []*ImageText `protobuf:"bytes,5,rep,name=image_text_info,json=imageTextInfo" json:"image_text_info,omitempty"` //图文子信息
	TitleSeg      []byte       `protobuf:"bytes,6,opt,name=title_seg,json=titleSeg" json:"title_seg,omitempty"`                  //切词处理过后的title
	AskContent    []byte       `protobuf:"bytes,7,opt,name=ask_content,json=askContent" json:"ask_content,omitempty"`            //追问反问内容
	Reference     []byte       `protobuf:"bytes,8,opt,name=reference" json:"reference,omitempty"`                                //模型生成的参考来源
	Cid           []byte       `protobuf:"bytes,9,opt,name=cid" json:"cid,omitempty"`                                            //生成通路logid,重新生成
	UrlCategory   []byte       `protobuf:"bytes,10,opt,name=url_category,json=urlCategory" json:"url_category,omitempty"`        //url类别，由ranker产出上传vui写入, ranker上数据源http://mis.baidu.com:8088/stream/view?data_name=aladdin_ctr_fea_map
}

func (x *Offset) Reset() {
	*x = Offset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Offset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Offset) ProtoMessage() {}

func (x *Offset) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Offset.ProtoReflect.Descriptor instead.
func (*Offset) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{9}
}

func (x *Offset) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *Offset) GetTargetUrl() []byte {
	if x != nil {
		return x.TargetUrl
	}
	return nil
}

func (x *Offset) GetShowUrl() []byte {
	if x != nil {
		return x.ShowUrl
	}
	return nil
}

func (x *Offset) GetSummary() []byte {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *Offset) GetImageTextInfo() []*ImageText {
	if x != nil {
		return x.ImageTextInfo
	}
	return nil
}

func (x *Offset) GetTitleSeg() []byte {
	if x != nil {
		return x.TitleSeg
	}
	return nil
}

func (x *Offset) GetAskContent() []byte {
	if x != nil {
		return x.AskContent
	}
	return nil
}

func (x *Offset) GetReference() []byte {
	if x != nil {
		return x.Reference
	}
	return nil
}

func (x *Offset) GetCid() []byte {
	if x != nil {
		return x.Cid
	}
	return nil
}

func (x *Offset) GetUrlCategory() []byte {
	if x != nil {
		return x.UrlCategory
	}
	return nil
}

type RsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title   []byte  `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	Feature *uint32 `protobuf:"varint,2,opt,name=feature" json:"feature,omitempty"`
}

func (x *RsData) Reset() {
	*x = RsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RsData) ProtoMessage() {}

func (x *RsData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RsData.ProtoReflect.Descriptor instead.
func (*RsData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{10}
}

func (x *RsData) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *RsData) GetFeature() uint32 {
	if x != nil && x.Feature != nil {
		return *x.Feature
	}
	return 0
}

type AladdinTitleUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title []byte `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	Url   []byte `protobuf:"bytes,2,opt,name=url" json:"url,omitempty"`
}

func (x *AladdinTitleUrl) Reset() {
	*x = AladdinTitleUrl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AladdinTitleUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AladdinTitleUrl) ProtoMessage() {}

func (x *AladdinTitleUrl) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AladdinTitleUrl.ProtoReflect.Descriptor instead.
func (*AladdinTitleUrl) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{11}
}

func (x *AladdinTitleUrl) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AladdinTitleUrl) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

type ResultScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FinalScore      *uint32 `protobuf:"varint,1,opt,name=final_score,json=finalScore" json:"final_score,omitempty"`
	ModelScore      *uint32 `protobuf:"varint,2,opt,name=model_score,json=modelScore" json:"model_score,omitempty"`
	ClickModelScore *uint32 `protobuf:"varint,3,opt,name=click_model_score,json=clickModelScore" json:"click_model_score,omitempty"`
}

func (x *ResultScore) Reset() {
	*x = ResultScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResultScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultScore) ProtoMessage() {}

func (x *ResultScore) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultScore.ProtoReflect.Descriptor instead.
func (*ResultScore) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{12}
}

func (x *ResultScore) GetFinalScore() uint32 {
	if x != nil && x.FinalScore != nil {
		return *x.FinalScore
	}
	return 0
}

func (x *ResultScore) GetModelScore() uint32 {
	if x != nil && x.ModelScore != nil {
		return *x.ModelScore
	}
	return 0
}

func (x *ResultScore) GetClickModelScore() uint32 {
	if x != nil && x.ClickModelScore != nil {
		return *x.ClickModelScore
	}
	return 0
}

type MergeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Srcid           *uint32            `protobuf:"varint,1,opt,name=srcid" json:"srcid,omitempty"`                            // 资源号
	Source          *Source            `protobuf:"varint,2,opt,name=source,enum=megafoil.Source" json:"source,omitempty"`     // 标识来源
	OffsetInfo      *Offset            `protobuf:"bytes,3,opt,name=offset_info,json=offsetInfo" json:"offset_info,omitempty"` // 记录本条结果的title, url等信息
	VoiceText       []byte             `protobuf:"bytes,4,opt,name=voice_text,json=voiceText" json:"voice_text,omitempty"`
	ResultScore     *ResultScore       `protobuf:"bytes,5,opt,name=result_score,json=resultScore" json:"result_score,omitempty"`
	KnowledgeAnswer []byte             `protobuf:"bytes,6,opt,name=knowledge_answer,json=knowledgeAnswer" json:"knowledge_answer,omitempty"`
	ClickRcmdList   [][]byte           `protobuf:"bytes,7,rep,name=click_rcmd_list,json=clickRcmdList" json:"click_rcmd_list,omitempty"`       // 点击后推荐信息
	AladdinTitleUrl []*AladdinTitleUrl `protobuf:"bytes,8,rep,name=aladdin_title_url,json=aladdinTitleUrl" json:"aladdin_title_url,omitempty"` //aladdin 卡片子链信息
	UrlTransFeature []byte             `protobuf:"bytes,9,opt,name=url_trans_feature,json=urlTransFeature" json:"url_trans_feature,omitempty"` // url级别特征信息通用字段
	SubResult       []*SubBlock        `protobuf:"bytes,10,rep,name=sub_result,json=subResult" json:"sub_result,omitempty"`                    //子链信息, 兼容百看、富媒体
}

func (x *MergeItem) Reset() {
	*x = MergeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeItem) ProtoMessage() {}

func (x *MergeItem) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeItem.ProtoReflect.Descriptor instead.
func (*MergeItem) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{13}
}

func (x *MergeItem) GetSrcid() uint32 {
	if x != nil && x.Srcid != nil {
		return *x.Srcid
	}
	return 0
}

func (x *MergeItem) GetSource() Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return Source_OTHERS
}

func (x *MergeItem) GetOffsetInfo() *Offset {
	if x != nil {
		return x.OffsetInfo
	}
	return nil
}

func (x *MergeItem) GetVoiceText() []byte {
	if x != nil {
		return x.VoiceText
	}
	return nil
}

func (x *MergeItem) GetResultScore() *ResultScore {
	if x != nil {
		return x.ResultScore
	}
	return nil
}

func (x *MergeItem) GetKnowledgeAnswer() []byte {
	if x != nil {
		return x.KnowledgeAnswer
	}
	return nil
}

func (x *MergeItem) GetClickRcmdList() [][]byte {
	if x != nil {
		return x.ClickRcmdList
	}
	return nil
}

func (x *MergeItem) GetAladdinTitleUrl() []*AladdinTitleUrl {
	if x != nil {
		return x.AladdinTitleUrl
	}
	return nil
}

func (x *MergeItem) GetUrlTransFeature() []byte {
	if x != nil {
		return x.UrlTransFeature
	}
	return nil
}

func (x *MergeItem) GetSubResult() []*SubBlock {
	if x != nil {
		return x.SubResult
	}
	return nil
}

// 阿拉丁子链块信息
type SubBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubLink    []*SubLink `protobuf:"bytes,1,rep,name=sub_link,json=subLink" json:"sub_link,omitempty"`           //
	BlockIndex *uint32    `protobuf:"varint,2,opt,name=block_index,json=blockIndex" json:"block_index,omitempty"` //子链的块信息，如百看卡，其他无此信息的默认为0
	SubQuery   []byte     `protobuf:"bytes,3,opt,name=sub_query,json=subQuery" json:"sub_query,omitempty"`        //子链的块信息，sub_query属性
}

func (x *SubBlock) Reset() {
	*x = SubBlock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubBlock) ProtoMessage() {}

func (x *SubBlock) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubBlock.ProtoReflect.Descriptor instead.
func (*SubBlock) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{14}
}

func (x *SubBlock) GetSubLink() []*SubLink {
	if x != nil {
		return x.SubLink
	}
	return nil
}

func (x *SubBlock) GetBlockIndex() uint32 {
	if x != nil && x.BlockIndex != nil {
		return *x.BlockIndex
	}
	return 0
}

func (x *SubBlock) GetSubQuery() []byte {
	if x != nil {
		return x.SubQuery
	}
	return nil
}

// 阿拉丁子链信息，隶属于块信息
type SubLink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubPos   *uint32 `protobuf:"varint,1,opt,name=sub_pos,json=subPos" json:"sub_pos,omitempty"` //子链的序号信息，由block_index，sub_pos唯一确定子链位置
	Title    []byte  `protobuf:"bytes,2,opt,name=title" json:"title,omitempty"`                  // 子链标题
	Url      []byte  `protobuf:"bytes,3,opt,name=url" json:"url,omitempty"`                      //子链url
	Urlsign  *uint64 `protobuf:"varint,4,opt,name=urlsign" json:"urlsign,omitempty"`             //子链urlsign
	Abstract []byte  `protobuf:"bytes,5,opt,name=abstract" json:"abstract,omitempty"`            //子链摘要, 点后推产品升级需求，截止20250514vui只存入61570/19两张卡信息
}

func (x *SubLink) Reset() {
	*x = SubLink{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubLink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubLink) ProtoMessage() {}

func (x *SubLink) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubLink.ProtoReflect.Descriptor instead.
func (*SubLink) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{15}
}

func (x *SubLink) GetSubPos() uint32 {
	if x != nil && x.SubPos != nil {
		return *x.SubPos
	}
	return 0
}

func (x *SubLink) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SubLink) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *SubLink) GetUrlsign() uint64 {
	if x != nil && x.Urlsign != nil {
		return *x.Urlsign
	}
	return 0
}

func (x *SubLink) GetAbstract() []byte {
	if x != nil {
		return x.Abstract
	}
	return nil
}

type DisplayUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryId []byte `protobuf:"bytes,1,opt,name=query_id,json=queryId" json:"query_id,omitempty"` // 本条信息对应的同步检索的qid
	// 根据业务方的需求定义，如包含无线10条结果的title, target url, 摘要(待定，如果不需要，先不传，以减少带宽), 资源号, 来源(AC/SP)信息
	Items             []*MergeItem         `protobuf:"bytes,2,rep,name=items" json:"items,omitempty"`                                    // item数组中的每一条代表一条结果的信息
	QueryWord         []byte               `protobuf:"bytes,3,opt,name=query_word,json=queryWord" json:"query_word,omitempty"`           // 本条信息对应的同步检索query
	TimestampSec      *uint64              `protobuf:"varint,4,opt,name=timestamp_sec,json=timestampSec" json:"timestamp_sec,omitempty"` // 秒级时间戳
	PageNum           *uint32              `protobuf:"varint,5,opt,name=page_num,json=pageNum" json:"page_num,omitempty"`                // 页面号
	Sids              []uint32             `protobuf:"varint,6,rep,name=sids" json:"sids,omitempty"`
	Eids              []uint32             `protobuf:"varint,7,rep,name=eids" json:"eids,omitempty"`
	WiseReqInfo       *WiseReqInfo         `protobuf:"bytes,8,opt,name=wise_req_info,json=wiseReqInfo" json:"wise_req_info,omitempty"` // 无线请求相关字段
	FullReplaceQuery  []byte               `protobuf:"bytes,9,opt,name=full_replace_query,json=fullReplaceQuery" json:"full_replace_query,omitempty"`
	Url               []byte               `protobuf:"bytes,10,opt,name=url" json:"url,omitempty"`
	AsResultNum       *uint32              `protobuf:"varint,11,opt,name=as_result_num,json=asResultNum" json:"as_result_num,omitempty"`
	SpResultNum       *uint32              `protobuf:"varint,12,opt,name=sp_result_num,json=spResultNum" json:"sp_result_num,omitempty"`
	SyncClickrecmdNum *uint32              `protobuf:"varint,17,opt,name=sync_clickrecmd_num,json=syncClickrecmdNum" json:"sync_clickrecmd_num,omitempty"`
	Product           []byte               `protobuf:"bytes,13,opt,name=product" json:"product,omitempty"`
	RsData            []*RsData            `protobuf:"bytes,14,rep,name=rs_data,json=rsData" json:"rs_data,omitempty"`
	Time              *uint32              `protobuf:"varint,15,opt,name=time" json:"time,omitempty"`    // 用户数据到达服务器的时间
	Fenlei            []byte               `protobuf:"bytes,16,opt,name=fenlei" json:"fenlei,omitempty"` // 字符串，作为流量标记，可能为空
	AsyncData         []*AsyncMegafoilData `protobuf:"bytes,18,rep,name=async_data,json=asyncData" json:"async_data,omitempty"`
	QueryTransFeature []byte               `protobuf:"bytes,19,opt,name=query_trans_feature,json=queryTransFeature" json:"query_trans_feature,omitempty"` // query级别特征信息通用字段
	StaratlasData     *StaratlasData       `protobuf:"bytes,20,opt,name=staratlas_data,json=staratlasData" json:"staratlas_data,omitempty"`               //星图信息数据字段
	SearchFrequency   []byte               `protobuf:"bytes,21,opt,name=search_frequency,json=searchFrequency" json:"search_frequency,omitempty"`         //query历史搜索频次
	IsRecom           *uint32              `protobuf:"varint,22,opt,name=is_recom,json=isRecom" json:"is_recom,omitempty"`                                //是否推荐导流
	QueryWordSeg      []byte               `protobuf:"bytes,23,opt,name=query_word_seg,json=queryWordSeg" json:"query_word_seg,omitempty"`                //切词处理过后的query word
	GenSearch         []byte               `protobuf:"bytes,24,opt,name=gen_search,json=genSearch" json:"gen_search,omitempty"`                           //da返回给vui相关多轮使用的数据
}

func (x *DisplayUnit) Reset() {
	*x = DisplayUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayUnit) ProtoMessage() {}

func (x *DisplayUnit) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayUnit.ProtoReflect.Descriptor instead.
func (*DisplayUnit) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{16}
}

func (x *DisplayUnit) GetQueryId() []byte {
	if x != nil {
		return x.QueryId
	}
	return nil
}

func (x *DisplayUnit) GetItems() []*MergeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *DisplayUnit) GetQueryWord() []byte {
	if x != nil {
		return x.QueryWord
	}
	return nil
}

func (x *DisplayUnit) GetTimestampSec() uint64 {
	if x != nil && x.TimestampSec != nil {
		return *x.TimestampSec
	}
	return 0
}

func (x *DisplayUnit) GetPageNum() uint32 {
	if x != nil && x.PageNum != nil {
		return *x.PageNum
	}
	return 0
}

func (x *DisplayUnit) GetSids() []uint32 {
	if x != nil {
		return x.Sids
	}
	return nil
}

func (x *DisplayUnit) GetEids() []uint32 {
	if x != nil {
		return x.Eids
	}
	return nil
}

func (x *DisplayUnit) GetWiseReqInfo() *WiseReqInfo {
	if x != nil {
		return x.WiseReqInfo
	}
	return nil
}

func (x *DisplayUnit) GetFullReplaceQuery() []byte {
	if x != nil {
		return x.FullReplaceQuery
	}
	return nil
}

func (x *DisplayUnit) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *DisplayUnit) GetAsResultNum() uint32 {
	if x != nil && x.AsResultNum != nil {
		return *x.AsResultNum
	}
	return 0
}

func (x *DisplayUnit) GetSpResultNum() uint32 {
	if x != nil && x.SpResultNum != nil {
		return *x.SpResultNum
	}
	return 0
}

func (x *DisplayUnit) GetSyncClickrecmdNum() uint32 {
	if x != nil && x.SyncClickrecmdNum != nil {
		return *x.SyncClickrecmdNum
	}
	return 0
}

func (x *DisplayUnit) GetProduct() []byte {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *DisplayUnit) GetRsData() []*RsData {
	if x != nil {
		return x.RsData
	}
	return nil
}

func (x *DisplayUnit) GetTime() uint32 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

func (x *DisplayUnit) GetFenlei() []byte {
	if x != nil {
		return x.Fenlei
	}
	return nil
}

func (x *DisplayUnit) GetAsyncData() []*AsyncMegafoilData {
	if x != nil {
		return x.AsyncData
	}
	return nil
}

func (x *DisplayUnit) GetQueryTransFeature() []byte {
	if x != nil {
		return x.QueryTransFeature
	}
	return nil
}

func (x *DisplayUnit) GetStaratlasData() *StaratlasData {
	if x != nil {
		return x.StaratlasData
	}
	return nil
}

func (x *DisplayUnit) GetSearchFrequency() []byte {
	if x != nil {
		return x.SearchFrequency
	}
	return nil
}

func (x *DisplayUnit) GetIsRecom() uint32 {
	if x != nil && x.IsRecom != nil {
		return *x.IsRecom
	}
	return 0
}

func (x *DisplayUnit) GetQueryWordSeg() []byte {
	if x != nil {
		return x.QueryWordSeg
	}
	return nil
}

func (x *DisplayUnit) GetGenSearch() []byte {
	if x != nil {
		return x.GenSearch
	}
	return nil
}

type ClickUnitItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 根据业务方的需求定义，如包含点击请求的原始信息，以及解密后的url
	ClickRequest []byte  `protobuf:"bytes,1,opt,name=click_request,json=clickRequest" json:"click_request,omitempty"` // 点击请求及解密后的url
	DecryptedUrl []byte  `protobuf:"bytes,2,opt,name=decrypted_url,json=decryptedUrl" json:"decrypted_url,omitempty"` // 解密后的url
	Time         *uint32 `protobuf:"varint,3,opt,name=time" json:"time,omitempty"`                                    // 用户数据到达服务器的时间
}

func (x *ClickUnitItem) Reset() {
	*x = ClickUnitItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClickUnitItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClickUnitItem) ProtoMessage() {}

func (x *ClickUnitItem) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClickUnitItem.ProtoReflect.Descriptor instead.
func (*ClickUnitItem) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{17}
}

func (x *ClickUnitItem) GetClickRequest() []byte {
	if x != nil {
		return x.ClickRequest
	}
	return nil
}

func (x *ClickUnitItem) GetDecryptedUrl() []byte {
	if x != nil {
		return x.DecryptedUrl
	}
	return nil
}

func (x *ClickUnitItem) GetTime() uint32 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

type ClickUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryId    []byte           `protobuf:"bytes,1,opt,name=query_id,json=queryId" json:"query_id,omitempty"`          // 本条信息对应的同步检索的qid
	ClickItems []*ClickUnitItem `protobuf:"bytes,2,rep,name=click_items,json=clickItems" json:"click_items,omitempty"` // item数组中的每一条代表一条点击结果的信息
}

func (x *ClickUnit) Reset() {
	*x = ClickUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClickUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClickUnit) ProtoMessage() {}

func (x *ClickUnit) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClickUnit.ProtoReflect.Descriptor instead.
func (*ClickUnit) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{18}
}

func (x *ClickUnit) GetQueryId() []byte {
	if x != nil {
		return x.QueryId
	}
	return nil
}

func (x *ClickUnit) GetClickItems() []*ClickUnitItem {
	if x != nil {
		return x.ClickItems
	}
	return nil
}

type DisplayData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusCode   *uint32        `protobuf:"varint,1,req,name=status_code,json=statusCode" json:"status_code,omitempty"`
	DisplayUnits []*DisplayUnit `protobuf:"bytes,2,rep,name=display_units,json=displayUnits" json:"display_units,omitempty"` // 因为以后可能有需要取同一baiduId下所有qid结果的需求，所以这里是一个数组，每个元素代表其中一个qid对应的数据
}

func (x *DisplayData) Reset() {
	*x = DisplayData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayData) ProtoMessage() {}

func (x *DisplayData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayData.ProtoReflect.Descriptor instead.
func (*DisplayData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{19}
}

func (x *DisplayData) GetStatusCode() uint32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *DisplayData) GetDisplayUnits() []*DisplayUnit {
	if x != nil {
		return x.DisplayUnits
	}
	return nil
}

type ClickData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusCode *uint32      `protobuf:"varint,1,req,name=status_code,json=statusCode" json:"status_code,omitempty"`
	ClickUnits []*ClickUnit `protobuf:"bytes,2,rep,name=click_units,json=clickUnits" json:"click_units,omitempty"` // 因为以后可能有需要取同一baiduId下所有qid结果的需求，所以这里是一个数组，每个元素代表其中一个qid对应的数据
}

func (x *ClickData) Reset() {
	*x = ClickData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClickData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClickData) ProtoMessage() {}

func (x *ClickData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClickData.ProtoReflect.Descriptor instead.
func (*ClickData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{20}
}

func (x *ClickData) GetStatusCode() uint32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *ClickData) GetClickUnits() []*ClickUnit {
	if x != nil {
		return x.ClickUnits
	}
	return nil
}

type MegafoilData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayData *DisplayData `protobuf:"bytes,1,opt,name=display_data,json=displayData" json:"display_data,omitempty"` // odp展现相关透传数据
	ClickData   *ClickData   `protobuf:"bytes,2,opt,name=click_data,json=clickData" json:"click_data,omitempty"`       // 点击相关透传数据
}

func (x *MegafoilData) Reset() {
	*x = MegafoilData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilData) ProtoMessage() {}

func (x *MegafoilData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilData.ProtoReflect.Descriptor instead.
func (*MegafoilData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{21}
}

func (x *MegafoilData) GetDisplayData() *DisplayData {
	if x != nil {
		return x.DisplayData
	}
	return nil
}

func (x *MegafoilData) GetClickData() *ClickData {
	if x != nil {
		return x.ClickData
	}
	return nil
}

type WiseReqInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PriorWptName    []byte  `protobuf:"bytes,1,opt,name=prior_wpt_name,json=priorWptName" json:"prior_wpt_name,omitempty"`            // 优先版式名称，其优先级高于wise_adapter内的版式名，ASP内部将版式名映射为版式ID
	Wst             []byte  `protobuf:"bytes,2,opt,name=wst" json:"wst,omitempty"`                                                    // 无线前端流量标示
	Wchnid          []byte  `protobuf:"bytes,3,opt,name=wchnid" json:"wchnid,omitempty"`                                              // 频道号
	Wfrom           []byte  `protobuf:"bytes,4,opt,name=wfrom" json:"wfrom,omitempty"`                                                // 渠道号
	Sp              []byte  `protobuf:"bytes,5,opt,name=sp" json:"sp,omitempty"`                                                      // 用户搜索类型
	WisePu          []byte  `protobuf:"bytes,6,opt,name=wise_pu,json=wisePu" json:"wise_pu,omitempty"`                                // 客户端参数信息集pu串,其格式为:"key1@value1,key2@value2,key3@value3.."
	WiseAdapter     []byte  `protobuf:"bytes,7,opt,name=wise_adapter,json=wiseAdapter" json:"wise_adapter,omitempty"`                 // adapter适配串,其格式为json
	Wosid           *int32  `protobuf:"varint,8,opt,name=wosid" json:"wosid,omitempty"`                                               //设备osid
	Wpt             *int32  `protobuf:"varint,9,opt,name=wpt" json:"wpt,omitempty"`                                                   //版式id
	Wadptid         *int32  `protobuf:"varint,10,opt,name=wadptid" json:"wadptid,omitempty"`                                          //机型id
	Wbwsid          *int32  `protobuf:"varint,11,opt,name=wbwsid" json:"wbwsid,omitempty"`                                            //浏览器id
	Wosver          []byte  `protobuf:"bytes,12,opt,name=wosver" json:"wosver,omitempty"`                                             //os版本
	Wbwsver         []byte  `protobuf:"bytes,13,opt,name=wbwsver" json:"wbwsver,omitempty"`                                           //浏览器版本
	Wuid            []byte  `protobuf:"bytes,14,opt,name=wuid" json:"wuid,omitempty"`                                                 //baiduid
	Wspeed          *int32  `protobuf:"varint,15,opt,name=wspeed" json:"wspeed,omitempty"`                                            //网速
	Wcallingid      []byte  `protobuf:"bytes,16,opt,name=wcallingid" json:"wcallingid,omitempty"`                                     //无线电话号码,截断长度100
	Whost           []byte  `protobuf:"bytes,17,opt,name=whost" json:"whost,omitempty"`                                               //域名,截断长度200
	Gps             []byte  `protobuf:"bytes,18,opt,name=gps" json:"gps,omitempty"`                                                   //gps,截断长度3072
	Cuid            []byte  `protobuf:"bytes,19,opt,name=cuid" json:"cuid,omitempty"`                                                 //客户端用户唯一识别标示
	Sz              []byte  `protobuf:"bytes,20,opt,name=sz" json:"sz,omitempty"`                                                     //屏幕分辨率
	Wgtag           *int32  `protobuf:"varint,21,opt,name=wgtag" json:"wgtag,omitempty"`                                              //gps来源类型,
	WiseWptName     []byte  `protobuf:"bytes,22,opt,name=wise_wpt_name,json=wiseWptName" json:"wise_wpt_name,omitempty"`              //无线url里tn对应的版式,iphone,bmbadr,zbios等,与prior_wpt_name是多对一的关系
	IsLighttpdRetry *uint32 `protobuf:"varint,23,opt,name=is_lighttpd_retry,json=isLighttpdRetry" json:"is_lighttpd_retry,omitempty"` //是否是上游nginx重试，0表示非retry，1表示lighttpd retry
	IsOdpRetry      *uint32 `protobuf:"varint,24,opt,name=is_odp_retry,json=isOdpRetry" json:"is_odp_retry,omitempty"`                //是否是odp重试，0表示非retry，1表示odp retry
	Wnettype        []byte  `protobuf:"bytes,25,opt,name=wnettype" json:"wnettype,omitempty"`                                         //网络类型
	Screenwidth     *int32  `protobuf:"varint,26,opt,name=screenwidth" json:"screenwidth,omitempty"`                                  //手机屏幕宽度
	Screenheight    *int32  `protobuf:"varint,27,opt,name=screenheight" json:"screenheight,omitempty"`                                //手机屏幕高度
	InnerWidth      *int32  `protobuf:"varint,28,opt,name=innerWidth" json:"innerWidth,omitempty"`                                    //手机屏幕逻辑宽度
	InnerHeight     *int32  `protobuf:"varint,29,opt,name=innerHeight" json:"innerHeight,omitempty"`                                  //手机屏幕逻辑高度
}

func (x *WiseReqInfo) Reset() {
	*x = WiseReqInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiseReqInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiseReqInfo) ProtoMessage() {}

func (x *WiseReqInfo) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiseReqInfo.ProtoReflect.Descriptor instead.
func (*WiseReqInfo) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{22}
}

func (x *WiseReqInfo) GetPriorWptName() []byte {
	if x != nil {
		return x.PriorWptName
	}
	return nil
}

func (x *WiseReqInfo) GetWst() []byte {
	if x != nil {
		return x.Wst
	}
	return nil
}

func (x *WiseReqInfo) GetWchnid() []byte {
	if x != nil {
		return x.Wchnid
	}
	return nil
}

func (x *WiseReqInfo) GetWfrom() []byte {
	if x != nil {
		return x.Wfrom
	}
	return nil
}

func (x *WiseReqInfo) GetSp() []byte {
	if x != nil {
		return x.Sp
	}
	return nil
}

func (x *WiseReqInfo) GetWisePu() []byte {
	if x != nil {
		return x.WisePu
	}
	return nil
}

func (x *WiseReqInfo) GetWiseAdapter() []byte {
	if x != nil {
		return x.WiseAdapter
	}
	return nil
}

func (x *WiseReqInfo) GetWosid() int32 {
	if x != nil && x.Wosid != nil {
		return *x.Wosid
	}
	return 0
}

func (x *WiseReqInfo) GetWpt() int32 {
	if x != nil && x.Wpt != nil {
		return *x.Wpt
	}
	return 0
}

func (x *WiseReqInfo) GetWadptid() int32 {
	if x != nil && x.Wadptid != nil {
		return *x.Wadptid
	}
	return 0
}

func (x *WiseReqInfo) GetWbwsid() int32 {
	if x != nil && x.Wbwsid != nil {
		return *x.Wbwsid
	}
	return 0
}

func (x *WiseReqInfo) GetWosver() []byte {
	if x != nil {
		return x.Wosver
	}
	return nil
}

func (x *WiseReqInfo) GetWbwsver() []byte {
	if x != nil {
		return x.Wbwsver
	}
	return nil
}

func (x *WiseReqInfo) GetWuid() []byte {
	if x != nil {
		return x.Wuid
	}
	return nil
}

func (x *WiseReqInfo) GetWspeed() int32 {
	if x != nil && x.Wspeed != nil {
		return *x.Wspeed
	}
	return 0
}

func (x *WiseReqInfo) GetWcallingid() []byte {
	if x != nil {
		return x.Wcallingid
	}
	return nil
}

func (x *WiseReqInfo) GetWhost() []byte {
	if x != nil {
		return x.Whost
	}
	return nil
}

func (x *WiseReqInfo) GetGps() []byte {
	if x != nil {
		return x.Gps
	}
	return nil
}

func (x *WiseReqInfo) GetCuid() []byte {
	if x != nil {
		return x.Cuid
	}
	return nil
}

func (x *WiseReqInfo) GetSz() []byte {
	if x != nil {
		return x.Sz
	}
	return nil
}

func (x *WiseReqInfo) GetWgtag() int32 {
	if x != nil && x.Wgtag != nil {
		return *x.Wgtag
	}
	return 0
}

func (x *WiseReqInfo) GetWiseWptName() []byte {
	if x != nil {
		return x.WiseWptName
	}
	return nil
}

func (x *WiseReqInfo) GetIsLighttpdRetry() uint32 {
	if x != nil && x.IsLighttpdRetry != nil {
		return *x.IsLighttpdRetry
	}
	return 0
}

func (x *WiseReqInfo) GetIsOdpRetry() uint32 {
	if x != nil && x.IsOdpRetry != nil {
		return *x.IsOdpRetry
	}
	return 0
}

func (x *WiseReqInfo) GetWnettype() []byte {
	if x != nil {
		return x.Wnettype
	}
	return nil
}

func (x *WiseReqInfo) GetScreenwidth() int32 {
	if x != nil && x.Screenwidth != nil {
		return *x.Screenwidth
	}
	return 0
}

func (x *WiseReqInfo) GetScreenheight() int32 {
	if x != nil && x.Screenheight != nil {
		return *x.Screenheight
	}
	return 0
}

func (x *WiseReqInfo) GetInnerWidth() int32 {
	if x != nil && x.InnerWidth != nil {
		return *x.InnerWidth
	}
	return 0
}

func (x *WiseReqInfo) GetInnerHeight() int32 {
	if x != nil && x.InnerHeight != nil {
		return *x.InnerHeight
	}
	return 0
}

type AsyncMegafoilData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AsyncQueryId []byte      `protobuf:"bytes,1,opt,name=async_query_id,json=asyncQueryId" json:"async_query_id,omitempty"` // 本条信息对应的异步检索的qid
	RcmdData     []*RcmdData `protobuf:"bytes,2,rep,name=rcmd_data,json=rcmdData" json:"rcmd_data,omitempty"`               //异步点后推数据
	ArsData      []*ArsData  `protobuf:"bytes,3,rep,name=ars_data,json=arsData" json:"ars_data,omitempty"`                  //异步 rs 数据
}

func (x *AsyncMegafoilData) Reset() {
	*x = AsyncMegafoilData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AsyncMegafoilData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncMegafoilData) ProtoMessage() {}

func (x *AsyncMegafoilData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncMegafoilData.ProtoReflect.Descriptor instead.
func (*AsyncMegafoilData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{23}
}

func (x *AsyncMegafoilData) GetAsyncQueryId() []byte {
	if x != nil {
		return x.AsyncQueryId
	}
	return nil
}

func (x *AsyncMegafoilData) GetRcmdData() []*RcmdData {
	if x != nil {
		return x.RcmdData
	}
	return nil
}

func (x *AsyncMegafoilData) GetArsData() []*ArsData {
	if x != nil {
		return x.ArsData
	}
	return nil
}

type RcmdData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Up          [][]byte `protobuf:"bytes,1,rep,name=up" json:"up,omitempty"`
	Down        [][]byte `protobuf:"bytes,2,rep,name=down" json:"down,omitempty"`
	ExternLevel []byte   `protobuf:"bytes,3,opt,name=extern_level,json=externLevel" json:"extern_level,omitempty"`
}

func (x *RcmdData) Reset() {
	*x = RcmdData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RcmdData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RcmdData) ProtoMessage() {}

func (x *RcmdData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RcmdData.ProtoReflect.Descriptor instead.
func (*RcmdData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{24}
}

func (x *RcmdData) GetUp() [][]byte {
	if x != nil {
		return x.Up
	}
	return nil
}

func (x *RcmdData) GetDown() [][]byte {
	if x != nil {
		return x.Down
	}
	return nil
}

func (x *RcmdData) GetExternLevel() []byte {
	if x != nil {
		return x.ExternLevel
	}
	return nil
}

type ArsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ArsData) Reset() {
	*x = ArsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArsData) ProtoMessage() {}

func (x *ArsData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArsData.ProtoReflect.Descriptor instead.
func (*ArsData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{25}
}

type StaratlasData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryClassification []byte `protobuf:"bytes,1,opt,name=query_classification,json=queryClassification" json:"query_classification,omitempty"` //query分类信息
	QueryRequire        []byte `protobuf:"bytes,2,opt,name=query_require,json=queryRequire" json:"query_require,omitempty"`                      //query需求信息
}

func (x *StaratlasData) Reset() {
	*x = StaratlasData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaratlasData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaratlasData) ProtoMessage() {}

func (x *StaratlasData) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaratlasData.ProtoReflect.Descriptor instead.
func (*StaratlasData) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{26}
}

func (x *StaratlasData) GetQueryClassification() []byte {
	if x != nil {
		return x.QueryClassification
	}
	return nil
}

func (x *StaratlasData) GetQueryRequire() []byte {
	if x != nil {
		return x.QueryRequire
	}
	return nil
}

// 点击的url 信息
type QueryKeyword_UrlInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url          []byte  `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	UrlFrequency *uint32 `protobuf:"varint,2,opt,name=url_frequency,json=urlFrequency" json:"url_frequency,omitempty"`
	Title        []byte  `protobuf:"bytes,3,opt,name=title" json:"title,omitempty"`
	Timestamp    *uint32 `protobuf:"varint,4,opt,name=timestamp" json:"timestamp,omitempty"`
	Srcid        []byte  `protobuf:"bytes,5,opt,name=srcid" json:"srcid,omitempty"`
	TitleWs      []byte  `protobuf:"bytes,6,opt,name=title_ws,json=titleWs" json:"title_ws,omitempty"` //分词
}

func (x *QueryKeyword_UrlInfo) Reset() {
	*x = QueryKeyword_UrlInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_response_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryKeyword_UrlInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryKeyword_UrlInfo) ProtoMessage() {}

func (x *QueryKeyword_UrlInfo) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_response_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryKeyword_UrlInfo.ProtoReflect.Descriptor instead.
func (*QueryKeyword_UrlInfo) Descriptor() ([]byte, []int) {
	return file_megafoil_response_proto_rawDescGZIP(), []int{4, 0}
}

func (x *QueryKeyword_UrlInfo) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *QueryKeyword_UrlInfo) GetUrlFrequency() uint32 {
	if x != nil && x.UrlFrequency != nil {
		return *x.UrlFrequency
	}
	return 0
}

func (x *QueryKeyword_UrlInfo) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *QueryKeyword_UrlInfo) GetTimestamp() uint32 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *QueryKeyword_UrlInfo) GetSrcid() []byte {
	if x != nil {
		return x.Srcid
	}
	return nil
}

func (x *QueryKeyword_UrlInfo) GetTitleWs() []byte {
	if x != nil {
		return x.TitleWs
	}
	return nil
}

var File_megafoil_response_proto protoreflect.FileDescriptor

var file_megafoil_response_proto_rawDesc = []byte{
	0x0a, 0x17, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x65, 0x67, 0x61, 0x66,
	0x6f, 0x69, 0x6c, 0x1a, 0x15, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x02, 0x0a, 0x10, 0x4d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3b, 0x0a, 0x0d, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69,
	0x6c, 0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x08,
	0x75, 0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x55, 0x6d, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x07, 0x75, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x72,
	0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x4e,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x2c, 0x0a, 0x08, 0x6b, 0x65,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x09, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x04, 0x6b,
	0x65, 0x79, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x65, 0x78, 0x70, 0x72, 0x22, 0xc9, 0x02, 0x0a, 0x12, 0x4d, 0x65, 0x67, 0x61,
	0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x3b,
	0x0a, 0x0d, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c,
	0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x08, 0x75,
	0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x55, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x07, 0x75, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x72, 0x72,
	0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x4e, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x53, 0x75, 0x62,
	0x4b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x73, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x65,
	0x78, 0x70, 0x72, 0x22, 0x7e, 0x0a, 0x12, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x4e, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x72,
	0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x65, 0x72, 0x72,
	0x4d, 0x73, 0x67, 0x22, 0xbf, 0x01, 0x0a, 0x12, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x33, 0x12, 0x43, 0x0a, 0x11, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c,
	0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x10, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x34, 0x0a, 0x0c, 0x75, 0x6d, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c,
	0x2e, 0x55, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x75, 0x6d, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x5f, 0x6e, 0x6f, 0x18,
	0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x4e, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x65,
	0x72, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x86, 0x04, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f,
	0x77, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52,
	0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x07, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x73, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x07,
	0x70, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x30, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x2e, 0x55, 0x72, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x6f, 0x77, 0x73,
	0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x57, 0x73, 0x1a, 0xa5, 0x01, 0x0a, 0x07, 0x55, 0x72, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x72, 0x6c, 0x5f, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x75, 0x72, 0x6c,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x72, 0x63, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x73, 0x72,
	0x63, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x77, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x57, 0x73, 0x22, 0x37,
	0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x61, 0x65, 0x5f, 0x74, 0x12, 0x28, 0x0a,
	0x10, 0x7a, 0x68, 0x61, 0x6f, 0x70, 0x69, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0e, 0x7a, 0x68, 0x61, 0x6f, 0x70, 0x69, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x22, 0x89, 0x02, 0x0a, 0x07, 0x55, 0x6d, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x5f, 0x77, 0x69, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x10, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x57, 0x69, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x40, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f,
	0x70, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x50, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x61, 0x70, 0x32,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x75, 0x61, 0x70, 0x32, 0x12, 0x25, 0x0a, 0x02,
	0x61, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66,
	0x6f, 0x69, 0x6c, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x61, 0x65, 0x5f, 0x74, 0x52,
	0x02, 0x61, 0x65, 0x22, 0xec, 0x02, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x41, 0x74, 0x74, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x41, 0x74, 0x74, 0x72, 0x12, 0x31,
	0x0a, 0x15, 0x63, 0x6f, 0x6f, 0x6b, 0x5f, 0x71, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x63,
	0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x41, 0x74, 0x74,
	0x72, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x6b, 0x5f, 0x6e, 0x6f, 0x76, 0x65, 0x6c, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x63, 0x6f, 0x6f, 0x6b,
	0x4e, 0x6f, 0x76, 0x65, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6f,
	0x6b, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0d, 0x63, 0x6f, 0x6f, 0x6b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x74, 0x74,
	0x72, 0x12, 0x24, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6e, 0x6c, 0x70, 0x5f, 0x61,
	0x74, 0x74, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x4e, 0x6c, 0x70, 0x41, 0x74, 0x74, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x6b, 0x69,
	0x65, 0x5f, 0x6e, 0x6c, 0x70, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0d, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x4e, 0x6c, 0x70, 0x41, 0x74, 0x74, 0x72, 0x12,
	0x31, 0x0a, 0x15, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x5f,
	0x6e, 0x6c, 0x70, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x4e, 0x6c, 0x70, 0x41, 0x74,
	0x74, 0x72, 0x22, 0x31, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x03, 0x75, 0x72, 0x69, 0x22, 0xc0, 0x02, 0x0a, 0x06, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x55, 0x72, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x0f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x53, 0x65, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x73, 0x6b, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x72, 0x6c, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x75, 0x72, 0x6c,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x38, 0x0a, 0x06, 0x52, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x22, 0x39, 0x0a, 0x0f, 0x41, 0x6c, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x7b, 0x0a,
	0x0b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xd0, 0x03, 0x0a, 0x09, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x72, 0x63, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x72, 0x63, 0x69, 0x64, 0x12, 0x28,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10,
	0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x52,
	0x0a, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x09, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x72, 0x63, 0x6d, 0x64, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x52,
	0x63, 0x6d, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x11, 0x61, 0x6c, 0x61, 0x64, 0x64,
	0x69, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x41, 0x6c,
	0x61, 0x64, 0x64, 0x69, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x0f, 0x61,
	0x6c, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2a,
	0x0a, 0x11, 0x75, 0x72, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x75, 0x72, 0x6c, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x73, 0x75,
	0x62, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x53, 0x75, 0x62, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x09, 0x73, 0x75, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x76, 0x0a,
	0x08, 0x53, 0x75, 0x62, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x53, 0x75, 0x62, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x73, 0x75, 0x62,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x22, 0x80, 0x01, 0x0a, 0x07, 0x53, 0x75, 0x62, 0x4c, 0x69, 0x6e,
	0x6b, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x73, 0x75, 0x62, 0x50, 0x6f, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x72, 0x6c, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x75, 0x72, 0x6c, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x22, 0xf5, 0x06, 0x0a, 0x0b, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x53,
	0x65, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x64,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x04, 0x65, 0x69, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x77, 0x69, 0x73, 0x65, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x57, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x77, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2c, 0x0a, 0x12, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x66, 0x75,
	0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x0d, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x70, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x79, 0x6e, 0x63,
	0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x72, 0x65, 0x63, 0x6d, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x73, 0x79, 0x6e, 0x63, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x72, 0x65, 0x63, 0x6d, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x72, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x65, 0x6e, 0x6c, 0x65, 0x69, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x06, 0x66, 0x65, 0x6e, 0x6c, 0x65, 0x69, 0x12, 0x3a, 0x0a, 0x0a, 0x61, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x4d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x61, 0x73, 0x79, 0x6e,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x11, 0x71, 0x75, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x3e, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x12, 0x24, 0x0a, 0x0e, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x67, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x53, 0x65,
	0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x67, 0x65, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x22, 0x6d, 0x0a, 0x0d, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x64,
	0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0x60, 0x0a, 0x09, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x6e, 0x69,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x6a, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66,
	0x6f, 0x69, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x52,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x62, 0x0a,
	0x09, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x63,
	0x6b, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x22, 0x7c, 0x0a, 0x0c, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x38, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f,
	0x69, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x0a, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x8f, 0x06, 0x0a, 0x0b, 0x57, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x24, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x5f, 0x77, 0x70, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x57, 0x70,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x03, 0x77, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x63, 0x68, 0x6e, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x77, 0x63, 0x68, 0x6e, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x77, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x77, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x02, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x69, 0x73, 0x65, 0x5f, 0x70, 0x75,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x77, 0x69, 0x73, 0x65, 0x50, 0x75, 0x12, 0x21,
	0x0a, 0x0c, 0x77, 0x69, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x77, 0x69, 0x73, 0x65, 0x41, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x6f, 0x73, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x77, 0x6f, 0x73, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x70, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x77, 0x70, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x61, 0x64,
	0x70, 0x74, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x77, 0x61, 0x64, 0x70,
	0x74, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x62, 0x77, 0x73, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x62, 0x77, 0x73, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x77,
	0x6f, 0x73, 0x76, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x77, 0x6f, 0x73,
	0x76, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x62, 0x77, 0x73, 0x76, 0x65, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x77, 0x62, 0x77, 0x73, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x77, 0x75, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x77, 0x75, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x77, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x63, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x77,
	0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x68, 0x6f,
	0x73, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x77, 0x68, 0x6f, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x67, 0x70, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x67, 0x70,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x75, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x63, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x7a, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x02, 0x73, 0x7a, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x67, 0x74, 0x61, 0x67, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x67, 0x74, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x77,
	0x69, 0x73, 0x65, 0x5f, 0x77, 0x70, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x77, 0x69, 0x73, 0x65, 0x57, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x74, 0x70, 0x64, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x69, 0x73, 0x4c, 0x69,
	0x67, 0x68, 0x74, 0x74, 0x70, 0x64, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x6f, 0x64, 0x70, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x69, 0x73, 0x4f, 0x64, 0x70, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x77, 0x6e, 0x65, 0x74, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x08, 0x77, 0x6e, 0x65, 0x74, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x22, 0x98, 0x01, 0x0a, 0x11, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x67, 0x61, 0x66,
	0x6f, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x73, 0x79, 0x6e, 0x63,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0c, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x51, 0x75, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a,
	0x09, 0x72, 0x63, 0x6d, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52, 0x63, 0x6d, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x63, 0x6d, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c,
	0x0a, 0x08, 0x61, 0x72, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x41, 0x72, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x07, 0x61, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x08,
	0x52, 0x63, 0x6d, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x70, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0c, 0x52, 0x02, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x6f, 0x77, 0x6e,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0x09, 0x0a, 0x07, 0x41, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x22, 0x67, 0x0a, 0x0d, 0x53, 0x74,
	0x61, 0x72, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x14, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x13, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x2a, 0x9e, 0x04, 0x0a, 0x07, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x0a, 0x0a, 0x06, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x45,
	0x52, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x52, 0x52, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x65, 0x12,
	0x19, 0x0a, 0x15, 0x45, 0x52, 0x52, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x36,
	0x34, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x52,
	0x52, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x52, 0x52, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x4a,
	0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12,
	0x45, 0x52, 0x52, 0x5f, 0x42, 0x55, 0x46, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55,
	0x47, 0x48, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x55,
	0x50, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x0c, 0x45, 0x52,
	0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x17, 0x0a, 0x0a, 0x45, 0x52, 0x52, 0x5f, 0x52, 0x45, 0x49,
	0x4e, 0x49, 0x54, 0x10, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1e,
	0x0a, 0x11, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x10, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x20,
	0x0a, 0x13, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x12, 0x1c, 0x0a, 0x0f, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x52, 0x4f, 0x57, 0x10, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1a,
	0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10,
	0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x16, 0x0a, 0x09, 0x45, 0x52,
	0x52, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x10, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0x01, 0x12, 0x15, 0x0a, 0x08, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x10, 0xf8,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x15, 0x0a, 0x08, 0x45, 0x52, 0x52,
	0x5f, 0x4f, 0x50, 0x49, 0x44, 0x10, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x12, 0x1a, 0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x46, 0x55, 0x4c,
	0x4c, 0x10, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x18, 0x0a, 0x0b,
	0x45, 0x52, 0x52, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0xf4, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1c, 0x0a, 0x0f, 0x45, 0x52, 0x52, 0x5f, 0x55, 0x4e,
	0x49, 0x4d, 0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0x01, 0x2a, 0x2d, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x13,
	0x0a, 0x06, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x41, 0x43, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x53,
	0x50, 0x10, 0x02,
}

var (
	file_megafoil_response_proto_rawDescOnce sync.Once
	file_megafoil_response_proto_rawDescData = file_megafoil_response_proto_rawDesc
)

func file_megafoil_response_proto_rawDescGZIP() []byte {
	file_megafoil_response_proto_rawDescOnce.Do(func() {
		file_megafoil_response_proto_rawDescData = protoimpl.X.CompressGZIP(file_megafoil_response_proto_rawDescData)
	})
	return file_megafoil_response_proto_rawDescData
}

var file_megafoil_response_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_megafoil_response_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_megafoil_response_proto_goTypes = []interface{}{
	(ErrCode)(0),                 // 0: megafoil.ErrCode
	(Source)(0),                  // 1: megafoil.Source
	(*MegafoilResponse)(nil),     // 2: megafoil.MegafoilResponse
	(*MegafoilResponseV1)(nil),   // 3: megafoil.MegafoilResponseV1
	(*MegafoilResponseV2)(nil),   // 4: megafoil.MegafoilResponseV2
	(*MegafoilResponseV3)(nil),   // 5: megafoil.MegafoilResponseV3
	(*QueryKeyword)(nil),         // 6: megafoil.QueryKeyword
	(*SearchAeT)(nil),            // 7: megafoil.search_ae_t
	(*UmsData)(nil),              // 8: megafoil.UmsData
	(*QueryPersonaInfo)(nil),     // 9: megafoil.QueryPersonaInfo
	(*ImageText)(nil),            // 10: megafoil.ImageText
	(*Offset)(nil),               // 11: megafoil.Offset
	(*RsData)(nil),               // 12: megafoil.RsData
	(*AladdinTitleUrl)(nil),      // 13: megafoil.AladdinTitleUrl
	(*ResultScore)(nil),          // 14: megafoil.ResultScore
	(*MergeItem)(nil),            // 15: megafoil.MergeItem
	(*SubBlock)(nil),             // 16: megafoil.SubBlock
	(*SubLink)(nil),              // 17: megafoil.SubLink
	(*DisplayUnit)(nil),          // 18: megafoil.DisplayUnit
	(*ClickUnitItem)(nil),        // 19: megafoil.ClickUnitItem
	(*ClickUnit)(nil),            // 20: megafoil.ClickUnit
	(*DisplayData)(nil),          // 21: megafoil.DisplayData
	(*ClickData)(nil),            // 22: megafoil.ClickData
	(*MegafoilData)(nil),         // 23: megafoil.MegafoilData
	(*WiseReqInfo)(nil),          // 24: megafoil.WiseReqInfo
	(*AsyncMegafoilData)(nil),    // 25: megafoil.AsyncMegafoilData
	(*RcmdData)(nil),             // 26: megafoil.RcmdData
	(*ArsData)(nil),              // 27: megafoil.ArsData
	(*StaratlasData)(nil),        // 28: megafoil.StaratlasData
	(*QueryKeyword_UrlInfo)(nil), // 29: megafoil.QueryKeyword.UrlInfo
	(KeyType)(0),                 // 30: megafoil.KeyType
	(RequestType)(0),             // 31: megafoil.RequestType
	(*SubKeyType)(nil),           // 32: megafoil.SubKeyType
}
var file_megafoil_response_proto_depIdxs = []int32{
	23, // 0: megafoil.MegafoilResponse.megafoil_data:type_name -> megafoil.MegafoilData
	8,  // 1: megafoil.MegafoilResponse.ums_data:type_name -> megafoil.UmsData
	30, // 2: megafoil.MegafoilResponse.key_type:type_name -> megafoil.KeyType
	31, // 3: megafoil.MegafoilResponse.request_type:type_name -> megafoil.RequestType
	23, // 4: megafoil.MegafoilResponseV1.megafoil_data:type_name -> megafoil.MegafoilData
	8,  // 5: megafoil.MegafoilResponseV1.ums_data:type_name -> megafoil.UmsData
	31, // 6: megafoil.MegafoilResponseV1.request_type:type_name -> megafoil.RequestType
	32, // 7: megafoil.MegafoilResponseV1.sub_key:type_name -> megafoil.SubKeyType
	3,  // 8: megafoil.MegafoilResponseV2.response:type_name -> megafoil.MegafoilResponseV1
	23, // 9: megafoil.MegafoilResponseV3.megafoil_response:type_name -> megafoil.MegafoilData
	8,  // 10: megafoil.MegafoilResponseV3.ums_response:type_name -> megafoil.UmsData
	29, // 11: megafoil.QueryKeyword.url:type_name -> megafoil.QueryKeyword.UrlInfo
	6,  // 12: megafoil.UmsData.query_keyword_wise:type_name -> megafoil.QueryKeyword
	6,  // 13: megafoil.UmsData.query_keyword:type_name -> megafoil.QueryKeyword
	9,  // 14: megafoil.UmsData.query_per_info:type_name -> megafoil.QueryPersonaInfo
	7,  // 15: megafoil.UmsData.ae:type_name -> megafoil.search_ae_t
	10, // 16: megafoil.Offset.image_text_info:type_name -> megafoil.ImageText
	1,  // 17: megafoil.MergeItem.source:type_name -> megafoil.Source
	11, // 18: megafoil.MergeItem.offset_info:type_name -> megafoil.Offset
	14, // 19: megafoil.MergeItem.result_score:type_name -> megafoil.ResultScore
	13, // 20: megafoil.MergeItem.aladdin_title_url:type_name -> megafoil.AladdinTitleUrl
	16, // 21: megafoil.MergeItem.sub_result:type_name -> megafoil.SubBlock
	17, // 22: megafoil.SubBlock.sub_link:type_name -> megafoil.SubLink
	15, // 23: megafoil.DisplayUnit.items:type_name -> megafoil.MergeItem
	24, // 24: megafoil.DisplayUnit.wise_req_info:type_name -> megafoil.WiseReqInfo
	12, // 25: megafoil.DisplayUnit.rs_data:type_name -> megafoil.RsData
	25, // 26: megafoil.DisplayUnit.async_data:type_name -> megafoil.AsyncMegafoilData
	28, // 27: megafoil.DisplayUnit.staratlas_data:type_name -> megafoil.StaratlasData
	19, // 28: megafoil.ClickUnit.click_items:type_name -> megafoil.ClickUnitItem
	18, // 29: megafoil.DisplayData.display_units:type_name -> megafoil.DisplayUnit
	20, // 30: megafoil.ClickData.click_units:type_name -> megafoil.ClickUnit
	21, // 31: megafoil.MegafoilData.display_data:type_name -> megafoil.DisplayData
	22, // 32: megafoil.MegafoilData.click_data:type_name -> megafoil.ClickData
	26, // 33: megafoil.AsyncMegafoilData.rcmd_data:type_name -> megafoil.RcmdData
	27, // 34: megafoil.AsyncMegafoilData.ars_data:type_name -> megafoil.ArsData
	35, // [35:35] is the sub-list for method output_type
	35, // [35:35] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_megafoil_response_proto_init() }
func file_megafoil_response_proto_init() {
	if File_megafoil_response_proto != nil {
		return
	}
	file_megafoil_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_megafoil_response_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilResponseV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilResponseV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryKeyword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAeT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UmsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPersonaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Offset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AladdinTitleUrl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResultScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubBlock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubLink); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClickUnitItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClickUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClickData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiseReqInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AsyncMegafoilData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RcmdData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaratlasData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_response_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryKeyword_UrlInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_megafoil_response_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_megafoil_response_proto_goTypes,
		DependencyIndexes: file_megafoil_response_proto_depIdxs,
		EnumInfos:         file_megafoil_response_proto_enumTypes,
		MessageInfos:      file_megafoil_response_proto_msgTypes,
	}.Build()
	File_megafoil_response_proto = out.File
	file_megafoil_response_proto_rawDesc = nil
	file_megafoil_response_proto_goTypes = nil
	file_megafoil_response_proto_depIdxs = nil
}
