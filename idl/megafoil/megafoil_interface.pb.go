// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.15.8
// source: megafoil_interface.proto

package megafoil

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_megafoil_interface_proto protoreflect.FileDescriptor

var file_megafoil_interface_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x65, 0x67, 0x61,
	0x66, 0x6f, 0x69, 0x6c, 0x1a, 0x16, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xd9, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f,
	0x69, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x03, 0x67, 0x65, 0x74,
	0x12, 0x19, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x67, 0x61,
	0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x32, 0x12, 0x1b, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x67,
	0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x1a, 0x1c,
	0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x43, 0x0a, 0x06,
	0x67, 0x65, 0x74, 0x5f, 0x76, 0x33, 0x12, 0x1b, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69,
	0x6c, 0x2e, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x33, 0x1a, 0x1c, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x4d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x33, 0x42, 0x03, 0x80, 0x01, 0x01,
}

var file_megafoil_interface_proto_goTypes = []interface{}{
	(*MegafoilRequest)(nil),    // 0: megafoil.MegafoilRequest
	(*MegafoilRequestV2)(nil),  // 1: megafoil.MegafoilRequestV2
	(*MegafoilRequestV3)(nil),  // 2: megafoil.MegafoilRequestV3
	(*MegafoilResponse)(nil),   // 3: megafoil.MegafoilResponse
	(*MegafoilResponseV2)(nil), // 4: megafoil.MegafoilResponseV2
	(*MegafoilResponseV3)(nil), // 5: megafoil.MegafoilResponseV3
}
var file_megafoil_interface_proto_depIdxs = []int32{
	0, // 0: megafoil.MegafoilService.get:input_type -> megafoil.MegafoilRequest
	1, // 1: megafoil.MegafoilService.get_v2:input_type -> megafoil.MegafoilRequestV2
	2, // 2: megafoil.MegafoilService.get_v3:input_type -> megafoil.MegafoilRequestV3
	3, // 3: megafoil.MegafoilService.get:output_type -> megafoil.MegafoilResponse
	4, // 4: megafoil.MegafoilService.get_v2:output_type -> megafoil.MegafoilResponseV2
	5, // 5: megafoil.MegafoilService.get_v3:output_type -> megafoil.MegafoilResponseV3
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_megafoil_interface_proto_init() }
func file_megafoil_interface_proto_init() {
	if File_megafoil_interface_proto != nil {
		return
	}
	file_megafoil_request_proto_init()
	file_megafoil_response_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_megafoil_interface_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_megafoil_interface_proto_goTypes,
		DependencyIndexes: file_megafoil_interface_proto_depIdxs,
	}.Build()
	File_megafoil_interface_proto = out.File
	file_megafoil_interface_proto_rawDesc = nil
	file_megafoil_interface_proto_goTypes = nil
	file_megafoil_interface_proto_depIdxs = nil
}
