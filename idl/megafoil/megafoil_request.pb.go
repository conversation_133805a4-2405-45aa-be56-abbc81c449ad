// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.15.8
// source: megafoil_request.proto

package megafoil

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName []byte `protobuf:"bytes,1,req,name=user_name,json=userName" json:"user_name,omitempty"` // 访问者的标识
	Token    []byte `protobuf:"bytes,2,req,name=token" json:"token,omitempty"`                       // 用于验证访问者的权限
	Product  []byte `protobuf:"bytes,3,req,name=product" json:"product,omitempty"`                   // 用于标识访问者对应的产品线
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_megafoil_request_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *UserInfo) GetToken() []byte {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *UserInfo) GetProduct() []byte {
	if x != nil {
		return x.Product
	}
	return nil
}

type MegafoilRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyType            *KeyType     `protobuf:"varint,1,req,name=key_type,json=keyType,enum=megafoil.KeyType" json:"key_type,omitempty"`
	DataType           *uint32      `protobuf:"varint,2,req,name=data_type,json=dataType" json:"data_type,omitempty"`
	RequestType        *RequestType `protobuf:"varint,3,req,name=request_type,json=requestType,enum=megafoil.RequestType" json:"request_type,omitempty"`
	Keys               []byte       `protobuf:"bytes,4,req,name=keys" json:"keys,omitempty"`                                                            // key 可以是baiduid或者用下划线分隔的qid
	Expr               []byte       `protobuf:"bytes,5,opt,name=expr" json:"expr,omitempty"`                                                            // 查询表达式，支持object path 查询
	AllowCrossIdcRetry *bool        `protobuf:"varint,6,opt,name=allow_cross_idc_retry,json=allowCrossIdcRetry" json:"allow_cross_idc_retry,omitempty"` // 是否允许跨机房重试
	UserInfo           *UserInfo    `protobuf:"bytes,7,req,name=user_info,json=userInfo" json:"user_info,omitempty"`                                    // 用于客户端访问控制
	LogId              *uint64      `protobuf:"varint,8,req,name=log_id,json=logId" json:"log_id,omitempty"`
}

func (x *MegafoilRequest) Reset() {
	*x = MegafoilRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilRequest) ProtoMessage() {}

func (x *MegafoilRequest) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilRequest.ProtoReflect.Descriptor instead.
func (*MegafoilRequest) Descriptor() ([]byte, []int) {
	return file_megafoil_request_proto_rawDescGZIP(), []int{1}
}

func (x *MegafoilRequest) GetKeyType() KeyType {
	if x != nil && x.KeyType != nil {
		return *x.KeyType
	}
	return KeyType_baiduid
}

func (x *MegafoilRequest) GetDataType() uint32 {
	if x != nil && x.DataType != nil {
		return *x.DataType
	}
	return 0
}

func (x *MegafoilRequest) GetRequestType() RequestType {
	if x != nil && x.RequestType != nil {
		return *x.RequestType
	}
	return RequestType_megafoil_data
}

func (x *MegafoilRequest) GetKeys() []byte {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *MegafoilRequest) GetExpr() []byte {
	if x != nil {
		return x.Expr
	}
	return nil
}

func (x *MegafoilRequest) GetAllowCrossIdcRetry() bool {
	if x != nil && x.AllowCrossIdcRetry != nil {
		return *x.AllowCrossIdcRetry
	}
	return false
}

func (x *MegafoilRequest) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *MegafoilRequest) GetLogId() uint64 {
	if x != nil && x.LogId != nil {
		return *x.LogId
	}
	return 0
}

type RequestKeys struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestType        *RequestType  `protobuf:"varint,1,opt,name=request_type,json=requestType,enum=megafoil.RequestType" json:"request_type,omitempty"` //请求的结果类型，megafoil or ums
	DataType           *uint32       `protobuf:"varint,2,req,name=data_type,json=dataType" json:"data_type,omitempty"`                                    //具体的哪份数据
	SubKey             []*SubKeyType `protobuf:"bytes,3,rep,name=sub_key,json=subKey" json:"sub_key,omitempty"`
	Expr               []byte        `protobuf:"bytes,5,opt,name=expr" json:"expr,omitempty"`                                                            // 查询表达式，支持object path 查询
	AllowCrossIdcRetry *bool         `protobuf:"varint,6,opt,name=allow_cross_idc_retry,json=allowCrossIdcRetry" json:"allow_cross_idc_retry,omitempty"` // 是否允许跨机房重试
	Idmapping2Baiduid  *bool         `protobuf:"varint,7,opt,name=idmapping2baiduid" json:"idmapping2baiduid,omitempty"`
	MegafoilDataNums   *uint32       `protobuf:"varint,8,opt,name=megafoil_data_nums,json=megafoilDataNums" json:"megafoil_data_nums,omitempty"` //需要请求多少条megafoil数据
}

func (x *RequestKeys) Reset() {
	*x = RequestKeys{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestKeys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestKeys) ProtoMessage() {}

func (x *RequestKeys) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestKeys.ProtoReflect.Descriptor instead.
func (*RequestKeys) Descriptor() ([]byte, []int) {
	return file_megafoil_request_proto_rawDescGZIP(), []int{2}
}

func (x *RequestKeys) GetRequestType() RequestType {
	if x != nil && x.RequestType != nil {
		return *x.RequestType
	}
	return RequestType_megafoil_data
}

func (x *RequestKeys) GetDataType() uint32 {
	if x != nil && x.DataType != nil {
		return *x.DataType
	}
	return 0
}

func (x *RequestKeys) GetSubKey() []*SubKeyType {
	if x != nil {
		return x.SubKey
	}
	return nil
}

func (x *RequestKeys) GetExpr() []byte {
	if x != nil {
		return x.Expr
	}
	return nil
}

func (x *RequestKeys) GetAllowCrossIdcRetry() bool {
	if x != nil && x.AllowCrossIdcRetry != nil {
		return *x.AllowCrossIdcRetry
	}
	return false
}

func (x *RequestKeys) GetIdmapping2Baiduid() bool {
	if x != nil && x.Idmapping2Baiduid != nil {
		return *x.Idmapping2Baiduid
	}
	return false
}

func (x *RequestKeys) GetMegafoilDataNums() uint32 {
	if x != nil && x.MegafoilDataNums != nil {
		return *x.MegafoilDataNums
	}
	return 0
}

type MegafoilRequestV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys     []*RequestKeys `protobuf:"bytes,1,rep,name=keys" json:"keys,omitempty"`
	UserInfo *UserInfo      `protobuf:"bytes,2,req,name=user_info,json=userInfo" json:"user_info,omitempty"` // 用于客户端访问控制
	LogId    []byte         `protobuf:"bytes,3,req,name=log_id,json=logId" json:"log_id,omitempty"`
	UrlPack  []byte         `protobuf:"bytes,4,opt,name=url_pack,json=urlPack" json:"url_pack,omitempty"`
}

func (x *MegafoilRequestV2) Reset() {
	*x = MegafoilRequestV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilRequestV2) ProtoMessage() {}

func (x *MegafoilRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilRequestV2.ProtoReflect.Descriptor instead.
func (*MegafoilRequestV2) Descriptor() ([]byte, []int) {
	return file_megafoil_request_proto_rawDescGZIP(), []int{3}
}

func (x *MegafoilRequestV2) GetKeys() []*RequestKeys {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *MegafoilRequestV2) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *MegafoilRequestV2) GetLogId() []byte {
	if x != nil {
		return x.LogId
	}
	return nil
}

func (x *MegafoilRequestV2) GetUrlPack() []byte {
	if x != nil {
		return x.UrlPack
	}
	return nil
}

type MegafoilRequestV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MegafoilKey *RequestKeys `protobuf:"bytes,1,opt,name=megafoil_key,json=megafoilKey" json:"megafoil_key,omitempty"`
	UmsKey      *RequestKeys `protobuf:"bytes,2,opt,name=ums_key,json=umsKey" json:"ums_key,omitempty"`
	UserInfo    *UserInfo    `protobuf:"bytes,3,req,name=user_info,json=userInfo" json:"user_info,omitempty"` // 用于客户端访问控制
	LogId       []byte       `protobuf:"bytes,4,req,name=log_id,json=logId" json:"log_id,omitempty"`
}

func (x *MegafoilRequestV3) Reset() {
	*x = MegafoilRequestV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_megafoil_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MegafoilRequestV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MegafoilRequestV3) ProtoMessage() {}

func (x *MegafoilRequestV3) ProtoReflect() protoreflect.Message {
	mi := &file_megafoil_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MegafoilRequestV3.ProtoReflect.Descriptor instead.
func (*MegafoilRequestV3) Descriptor() ([]byte, []int) {
	return file_megafoil_request_proto_rawDescGZIP(), []int{4}
}

func (x *MegafoilRequestV3) GetMegafoilKey() *RequestKeys {
	if x != nil {
		return x.MegafoilKey
	}
	return nil
}

func (x *MegafoilRequestV3) GetUmsKey() *RequestKeys {
	if x != nil {
		return x.UmsKey
	}
	return nil
}

func (x *MegafoilRequestV3) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *MegafoilRequestV3) GetLogId() []byte {
	if x != nil {
		return x.LogId
	}
	return nil
}

var File_megafoil_request_proto protoreflect.FileDescriptor

var file_megafoil_request_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f,
	0x69, 0x6c, 0x1a, 0x15, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x0c, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x22, 0xb9, 0x02, 0x0a, 0x0f, 0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66,
	0x6f, 0x69, 0x6c, 0x2e, 0x4b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f,
	0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6b,
	0x65, 0x79, 0x73, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x65,
	0x78, 0x70, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x72, 0x6f,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x63, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x49, 0x64,
	0x63, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x67, 0x61,
	0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x6c, 0x6f, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x02, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x22, 0xb6,
	0x02, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x38,
	0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69,
	0x6c, 0x2e, 0x53, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x73, 0x75,
	0x62, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x65, 0x78, 0x70, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x63, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x72,
	0x6f, 0x73, 0x73, 0x49, 0x64, 0x63, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x2c, 0x0a, 0x11, 0x69,
	0x64, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x32, 0x62, 0x61, 0x69, 0x64, 0x75, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x64, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x32, 0x62, 0x61, 0x69, 0x64, 0x75, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x65, 0x67,
	0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x75, 0x6d, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x4e, 0x75, 0x6d, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x11, 0x4d, 0x65, 0x67, 0x61,
	0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x12, 0x29, 0x0a,
	0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65,
	0x79, 0x73, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x65,
	0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x6c, 0x6f, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x75, 0x72, 0x6c, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x75, 0x72, 0x6c, 0x50, 0x61, 0x63, 0x6b, 0x22, 0xc5, 0x01, 0x0a, 0x11,
	0x4d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x33, 0x12, 0x38, 0x0a, 0x0c, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f,
	0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x0b,
	0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x4b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x07, 0x75,
	0x6d, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d,
	0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b,
	0x65, 0x79, 0x73, 0x52, 0x06, 0x75, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x6d, 0x65, 0x67, 0x61, 0x66, 0x6f, 0x69, 0x6c, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06,
	0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x05, 0x6c, 0x6f,
	0x67, 0x49, 0x64,
}

var (
	file_megafoil_request_proto_rawDescOnce sync.Once
	file_megafoil_request_proto_rawDescData = file_megafoil_request_proto_rawDesc
)

func file_megafoil_request_proto_rawDescGZIP() []byte {
	file_megafoil_request_proto_rawDescOnce.Do(func() {
		file_megafoil_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_megafoil_request_proto_rawDescData)
	})
	return file_megafoil_request_proto_rawDescData
}

var file_megafoil_request_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_megafoil_request_proto_goTypes = []interface{}{
	(*UserInfo)(nil),          // 0: megafoil.UserInfo
	(*MegafoilRequest)(nil),   // 1: megafoil.MegafoilRequest
	(*RequestKeys)(nil),       // 2: megafoil.RequestKeys
	(*MegafoilRequestV2)(nil), // 3: megafoil.MegafoilRequestV2
	(*MegafoilRequestV3)(nil), // 4: megafoil.MegafoilRequestV3
	(KeyType)(0),              // 5: megafoil.KeyType
	(RequestType)(0),          // 6: megafoil.RequestType
	(*SubKeyType)(nil),        // 7: megafoil.SubKeyType
}
var file_megafoil_request_proto_depIdxs = []int32{
	5,  // 0: megafoil.MegafoilRequest.key_type:type_name -> megafoil.KeyType
	6,  // 1: megafoil.MegafoilRequest.request_type:type_name -> megafoil.RequestType
	0,  // 2: megafoil.MegafoilRequest.user_info:type_name -> megafoil.UserInfo
	6,  // 3: megafoil.RequestKeys.request_type:type_name -> megafoil.RequestType
	7,  // 4: megafoil.RequestKeys.sub_key:type_name -> megafoil.SubKeyType
	2,  // 5: megafoil.MegafoilRequestV2.keys:type_name -> megafoil.RequestKeys
	0,  // 6: megafoil.MegafoilRequestV2.user_info:type_name -> megafoil.UserInfo
	2,  // 7: megafoil.MegafoilRequestV3.megafoil_key:type_name -> megafoil.RequestKeys
	2,  // 8: megafoil.MegafoilRequestV3.ums_key:type_name -> megafoil.RequestKeys
	0,  // 9: megafoil.MegafoilRequestV3.user_info:type_name -> megafoil.UserInfo
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_megafoil_request_proto_init() }
func file_megafoil_request_proto_init() {
	if File_megafoil_request_proto != nil {
		return
	}
	file_megafoil_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_megafoil_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestKeys); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilRequestV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_megafoil_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MegafoilRequestV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_megafoil_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_megafoil_request_proto_goTypes,
		DependencyIndexes: file_megafoil_request_proto_depIdxs,
		MessageInfos:      file_megafoil_request_proto_msgTypes,
	}.Build()
	File_megafoil_request_proto = out.File
	file_megafoil_request_proto_rawDesc = nil
	file_megafoil_request_proto_goTypes = nil
	file_megafoil_request_proto_depIdxs = nil
}
