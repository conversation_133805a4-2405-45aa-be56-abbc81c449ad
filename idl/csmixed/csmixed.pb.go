// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.15.7
// source: csmixed.proto

package csmixed

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatusVal int32

const (
	StatusVal_OK                    StatusVal = 0 // 数据正常
	StatusVal_FATAL_ERR             StatusVal = 1 // 下游异常导致数据出错，出错情况下的处理，见【异常处理】部分
	StatusVal_EXP_SEARCH_RESULT_ERR StatusVal = 2 // 搜索数据COOK分包失败
)

// Enum value maps for StatusVal.
var (
	StatusVal_name = map[int32]string{
		0: "OK",
		1: "FATAL_ERR",
		2: "EXP_SEARCH_RESULT_ERR",
	}
	StatusVal_value = map[string]int32{
		"OK":                    0,
		"FATAL_ERR":             1,
		"EXP_SEARCH_RESULT_ERR": 2,
	}
)

func (x StatusVal) Enum() *StatusVal {
	p := new(StatusVal)
	*p = x
	return p
}

func (x StatusVal) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusVal) Descriptor() protoreflect.EnumDescriptor {
	return file_csmixed_proto_enumTypes[0].Descriptor()
}

func (StatusVal) Type() protoreflect.EnumType {
	return &file_csmixed_proto_enumTypes[0]
}

func (x StatusVal) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *StatusVal) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = StatusVal(num)
	return nil
}

// Deprecated: Use StatusVal.Descriptor instead.
func (StatusVal) EnumDescriptor() ([]byte, []int) {
	return file_csmixed_proto_rawDescGZIP(), []int{0}
}

type TypeVal int32

const (
	TypeVal_SUG           TypeVal = 0 //sug
	TypeVal_SEARCH_HEADER TypeVal = 1 //(2合1专用的header包，可忽略）
	TypeVal_TAB           TypeVal = 2 //tab-bar
	TypeVal_A_PAGE        TypeVal = 3 //A页面
	TypeVal_LOWER_FLOOR   TypeVal = 4 //负一楼
	TypeVal_HIGHER_FLOOR  TypeVal = 5 //平行空间
	TypeVal_VOICE_RSG     TypeVal = 6 //语音播报
	TypeVal_EASTER_EGG    TypeVal = 7 //搜索彩蛋
)

// Enum value maps for TypeVal.
var (
	TypeVal_name = map[int32]string{
		0: "SUG",
		1: "SEARCH_HEADER",
		2: "TAB",
		3: "A_PAGE",
		4: "LOWER_FLOOR",
		5: "HIGHER_FLOOR",
		6: "VOICE_RSG",
		7: "EASTER_EGG",
	}
	TypeVal_value = map[string]int32{
		"SUG":           0,
		"SEARCH_HEADER": 1,
		"TAB":           2,
		"A_PAGE":        3,
		"LOWER_FLOOR":   4,
		"HIGHER_FLOOR":  5,
		"VOICE_RSG":     6,
		"EASTER_EGG":    7,
	}
)

func (x TypeVal) Enum() *TypeVal {
	p := new(TypeVal)
	*p = x
	return p
}

func (x TypeVal) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TypeVal) Descriptor() protoreflect.EnumDescriptor {
	return file_csmixed_proto_enumTypes[1].Descriptor()
}

func (TypeVal) Type() protoreflect.EnumType {
	return &file_csmixed_proto_enumTypes[1]
}

func (x TypeVal) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TypeVal) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TypeVal(num)
	return nil
}

// Deprecated: Use TypeVal.Descriptor instead.
func (TypeVal) EnumDescriptor() ([]byte, []int) {
	return file_csmixed_proto_rawDescGZIP(), []int{1}
}

type SearchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceId      *string           `protobuf:"bytes,1,req,name=trace_id,json=traceId" json:"trace_id,omitempty"`             // 直接用端传的参数traceid，没有的情况下用go-na的logid
	Type         *TypeVal          `protobuf:"varint,2,req,name=type,enum=search.csmixed.idl.TypeVal" json:"type,omitempty"` // 包业务类型，取值：sug/header/tab等
	Status       *StatusVal        `protobuf:"varint,3,req,name=status,enum=search.csmixed.idl.StatusVal" json:"status,omitempty"`
	PackId       *uint32           `protobuf:"varint,4,req,name=pack_id,json=packId" json:"pack_id,omitempty"`                  //标识包id，搜索结果页的第一个pb包开始，从0递增
	Sug          *SearchResultPart `protobuf:"bytes,5,opt,name=sug" json:"sug,omitempty"`                                       //可选字段，sug的rensponse，原sugResponse的json encode字符串
	Header       *SearchResultPart `protobuf:"bytes,6,opt,name=header" json:"header,omitempty"`                                 //可选字段，pr-nginx返回的header信息
	Result       *SearchResultPart `protobuf:"bytes,7,opt,name=result" json:"result,omitempty"`                                 //可选字段，搜索结果的分包内容
	MainTemplate *string           `protobuf:"bytes,8,opt,name=main_template,json=mainTemplate" json:"main_template,omitempty"` //NA数据模版名，表示一类NA数据的协议和渲染方式，后续要走平台管理。
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csmixed_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_csmixed_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_csmixed_proto_rawDescGZIP(), []int{0}
}

func (x *SearchResponse) GetTraceId() string {
	if x != nil && x.TraceId != nil {
		return *x.TraceId
	}
	return ""
}

func (x *SearchResponse) GetType() TypeVal {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return TypeVal_SUG
}

func (x *SearchResponse) GetStatus() StatusVal {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return StatusVal_OK
}

func (x *SearchResponse) GetPackId() uint32 {
	if x != nil && x.PackId != nil {
		return *x.PackId
	}
	return 0
}

func (x *SearchResponse) GetSug() *SearchResultPart {
	if x != nil {
		return x.Sug
	}
	return nil
}

func (x *SearchResponse) GetHeader() *SearchResultPart {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SearchResponse) GetResult() *SearchResultPart {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SearchResponse) GetMainTemplate() string {
	if x != nil && x.MainTemplate != nil {
		return *x.MainTemplate
	}
	return ""
}

type SearchResultPart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Encoding  *string `protobuf:"bytes,1,req,name=encoding" json:"encoding,omitempty"`                     //数据的编码格式；type=result时，取值等同于HTTP协议header中的Content-Type；type=sug/header时，取值为json；
	FinStream *uint32 `protobuf:"varint,2,req,name=fin_stream,json=finStream" json:"fin_stream,omitempty"` //标识对应数据是否最后一个包
	TextData  *string `protobuf:"bytes,3,opt,name=text_data,json=textData" json:"text_data,omitempty"`     //字符串数据， na／sug／header数据放于该字段
	HtmlData  []byte  `protobuf:"bytes,4,opt,name=html_data,json=htmlData" json:"html_data,omitempty"`     //html数据， 搜索结果放于该字段
	More      *uint32 `protobuf:"varint,5,opt,name=more" json:"more,omitempty"`                            // fin_stream=1时，如果业务上判断接下来还有数据，则设置more字段；字段存在时取值为1，否则字段不存在；
}

func (x *SearchResultPart) Reset() {
	*x = SearchResultPart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csmixed_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResultPart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResultPart) ProtoMessage() {}

func (x *SearchResultPart) ProtoReflect() protoreflect.Message {
	mi := &file_csmixed_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResultPart.ProtoReflect.Descriptor instead.
func (*SearchResultPart) Descriptor() ([]byte, []int) {
	return file_csmixed_proto_rawDescGZIP(), []int{1}
}

func (x *SearchResultPart) GetEncoding() string {
	if x != nil && x.Encoding != nil {
		return *x.Encoding
	}
	return ""
}

func (x *SearchResultPart) GetFinStream() uint32 {
	if x != nil && x.FinStream != nil {
		return *x.FinStream
	}
	return 0
}

func (x *SearchResultPart) GetTextData() string {
	if x != nil && x.TextData != nil {
		return *x.TextData
	}
	return ""
}

func (x *SearchResultPart) GetHtmlData() []byte {
	if x != nil {
		return x.HtmlData
	}
	return nil
}

func (x *SearchResultPart) GetMore() uint32 {
	if x != nil && x.More != nil {
		return *x.More
	}
	return 0
}

var File_csmixed_proto protoreflect.FileDescriptor

var file_csmixed_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x63, 0x73, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x12, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x63, 0x73, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x2e,
	0x69, 0x64, 0x6c, 0x22, 0x85, 0x03, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x63, 0x73, 0x6d, 0x69, 0x78, 0x65, 0x64,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x02,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x63, 0x73, 0x6d, 0x69,
	0x78, 0x65, 0x64, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61,
	0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x63,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x61, 0x63, 0x6b,
	0x49, 0x64, 0x12, 0x36, 0x0a, 0x03, 0x73, 0x75, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x63, 0x73, 0x6d, 0x69, 0x78, 0x65, 0x64,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x50, 0x61, 0x72, 0x74, 0x52, 0x03, 0x73, 0x75, 0x67, 0x12, 0x3c, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x63, 0x73, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x72, 0x74,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x63, 0x73, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x72, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x61, 0x69, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x10,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x72, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d,
	0x52, 0x09, 0x66, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x65, 0x78, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x65, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x74, 0x6d, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x68, 0x74, 0x6d,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x6d, 0x6f, 0x72, 0x65, 0x2a, 0x3d, 0x0a, 0x09, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x46, 0x41, 0x54, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x45, 0x58, 0x50, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x55,
	0x4c, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x02, 0x2a, 0x7c, 0x0a, 0x07, 0x54, 0x79, 0x70, 0x65,
	0x56, 0x61, 0x6c, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x55, 0x47, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12,
	0x07, 0x0a, 0x03, 0x54, 0x41, 0x42, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x5f, 0x50, 0x41,
	0x47, 0x45, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x46, 0x4c,
	0x4f, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x48, 0x49, 0x47, 0x48, 0x45, 0x52, 0x5f,
	0x46, 0x4c, 0x4f, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x4f, 0x49, 0x43, 0x45,
	0x5f, 0x52, 0x53, 0x47, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x41, 0x53, 0x54, 0x45, 0x52,
	0x5f, 0x45, 0x47, 0x47, 0x10, 0x07, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2f, 0x3b, 0x63, 0x73, 0x6d,
	0x69, 0x78, 0x65, 0x64,
}

var (
	file_csmixed_proto_rawDescOnce sync.Once
	file_csmixed_proto_rawDescData = file_csmixed_proto_rawDesc
)

func file_csmixed_proto_rawDescGZIP() []byte {
	file_csmixed_proto_rawDescOnce.Do(func() {
		file_csmixed_proto_rawDescData = protoimpl.X.CompressGZIP(file_csmixed_proto_rawDescData)
	})
	return file_csmixed_proto_rawDescData
}

var file_csmixed_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_csmixed_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_csmixed_proto_goTypes = []interface{}{
	(StatusVal)(0),           // 0: search.csmixed.idl.StatusVal
	(TypeVal)(0),             // 1: search.csmixed.idl.TypeVal
	(*SearchResponse)(nil),   // 2: search.csmixed.idl.SearchResponse
	(*SearchResultPart)(nil), // 3: search.csmixed.idl.SearchResultPart
}
var file_csmixed_proto_depIdxs = []int32{
	1, // 0: search.csmixed.idl.SearchResponse.type:type_name -> search.csmixed.idl.TypeVal
	0, // 1: search.csmixed.idl.SearchResponse.status:type_name -> search.csmixed.idl.StatusVal
	3, // 2: search.csmixed.idl.SearchResponse.sug:type_name -> search.csmixed.idl.SearchResultPart
	3, // 3: search.csmixed.idl.SearchResponse.header:type_name -> search.csmixed.idl.SearchResultPart
	3, // 4: search.csmixed.idl.SearchResponse.result:type_name -> search.csmixed.idl.SearchResultPart
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_csmixed_proto_init() }
func file_csmixed_proto_init() {
	if File_csmixed_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_csmixed_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csmixed_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResultPart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_csmixed_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_csmixed_proto_goTypes,
		DependencyIndexes: file_csmixed_proto_depIdxs,
		EnumInfos:         file_csmixed_proto_enumTypes,
		MessageInfos:      file_csmixed_proto_msgTypes,
	}.Build()
	File_csmixed_proto = out.File
	file_csmixed_proto_rawDesc = nil
	file_csmixed_proto_goTypes = nil
	file_csmixed_proto_depIdxs = nil
}
