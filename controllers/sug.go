package controllers

import (
	//"net/http"
	// "fmt"
	"icode.baidu.com/baidu/gdp/gdp"
)

// Index app index
func SugRewrite(ctx *gdp.WebContext) {
	request := ctx.Request
	if request.Form == nil { //表单解析前
		request.URL.ForceQuery = true
		if request.URL.RawQuery != "" {
			request.URL.RawQuery += "&"
		}
		request.URL.RawQuery += "ctl=sug&action=sug"
	} else { //表单解析后
		request.Form.Set("action", "sug")
		request.Form.Set("ctl", "sug")
	}
	Suggest(ctx)
}
