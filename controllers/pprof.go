package controllers

import (
	"fmt"
	"net"
	"net/http"
	"runtime/pprof"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/net/gaddr"
)

// Pprof 性能分析
func Pprof(ctx *gdp.WebContext) {
	if !isInternalIP(ctx) {
		ctx.Writer.WriteHeader(http.StatusForbidden)
		_, _ = fmt.Fprintf(ctx.Writer, "not support for %s", ctx.Request.RemoteAddr)
		return
	}

	name := ctx.Request.FormValue("name")
	switch name {
	case "profile":
		profile(ctx)
		return
	}

	debug, err := strconv.Atoi(ctx.Request.FormValue("debug"))
	if err != nil {
		debug = 1
	}
	if debug == 0 {
		filenameHeader(ctx.Writer, name)
	}

	pp := pprof.Lookup(name)
	if pp == nil {
		ctx.Writer.WriteHeader(http.StatusBadRequest)
		_, _ = fmt.Fprintf(ctx.Writer, "not support for name=%s", name)
		return
	}

	_ = pp.WriteTo(ctx.Writer, debug)

	return
}

func filenameHeader(w gin.ResponseWriter, name string) {
	w.Header().Set("Content-Type", "application/octet-stream")
	ip := strings.ReplaceAll(env.LocalIP(), ".", "_")
	ip = strings.ReplaceAll(ip, ":", "_")
	filename := fmt.Sprintf("%s_%s_%s_%s", env.AppName(), ip, name, time.Now().Format("20060102_150405"))
	w.Header().Set("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, filename))
}

func profile(ctx *gdp.WebContext) {
	ctx.Writer.Header().Set("X-Content-Type-Options", "nosniff")
	sec, err := strconv.ParseInt(ctx.Request.FormValue("seconds"), 10, 64)
	if sec <= 0 || err != nil {
		sec = 30
	}

	ctx.Writer.Header().Set("Content-Type", "application/octet-stream")
	ctx.Writer.Header().Set("Content-Disposition", `attachment; filename="profile"`)

	if err := pprof.StartCPUProfile(ctx.Writer); err != nil {
		// StartCPUProfile failed, so no writes yet.
		tplStrs := fmt.Sprintf("Could not enable CPU profiling: %s", err)
		ctx.String(http.StatusInternalServerError, "%s", tplStrs)
		return
	}
	sleep(ctx.Request, time.Duration(sec)*time.Second)
	pprof.StopCPUProfile()
}

func sleep(r *http.Request, d time.Duration) {
	select {
	case <-time.After(d):
	case <-r.Context().Done():
	}
}

func isInternalIP(ctx *gdp.WebContext) bool {
	host, _, err := net.SplitHostPort(ctx.Request.RemoteAddr)
	if err != nil {
		return false
	}
	return gaddr.IsInternalIP(net.ParseIP(host)) && gaddr.IsInternalIP(net.ParseIP(ctx.ClientIP()))
}
