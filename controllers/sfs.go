// @Title		controllers/sfs.go
// @Description	sfs controller
// <AUTHOR>
// @Update		2022-04-07 11:00
package controllers

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/sfs"
)

// Sfs 搜索预测模块
func Sfs(ctx *gdp.WebContext) {

	// 原始context
	stdCtx := ctx.StdContext()
	// index.log标记下游
	logit.AddMetaFields(stdCtx, logit.String("api", sfs.ServiceModelRec))

	// service.log标记下游
	ctx.AddNotice("api", sfs.ServiceModelRec)
	// 推荐相关处理
	var sbp sfs.BasePage
	pageSrc, err := sfs.NewPager(sfs.ServiceModelRec, ctx)
	if err != nil {
		errData := sfs.ErrRecJSONResponseData(ctx, sfs.ServiceModelRec, err)
		sbp.SetErr() // 用于index.log标记错误
		sbp.SetJSONResponse(errData)
		return
	}

	// 主函数调用
	pageSrc.Execute()
	return
}
