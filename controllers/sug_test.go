package controllers

import (
	"net/http"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"

	//"strconv"
	"math"
	"net/http/httptest"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/gdp/logit"
)

func TestSugRewrite(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: SugRewrite ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	webctx := createInfoGetWebContext1()
	utInst.MockFunc(Suggest, MockSuggest)
	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"index func test",
			args{
				webctx,
			},
		},
	}

	for _, tt := range tests {
		SugRewrite(tt.args.ctx)
	}
	ag.Run()
}

var (
	httpRspBody1 *httptest.ResponseRecorder
)

func createInfoGetWebContext1() *gdp.WebContext {
	httpRspBody1 = httptest.NewRecorder() //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody1)
	//构造Request                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           //获取Gin提供的Context，WebContext包括gin.Context
	req, _ := http.NewRequest("GET", `/sug`, nil)

	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req
	//构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "1.1.1.1"),
	}

	rc := logit.WithContext(wc.Request.Context())
	wc.Request = wc.Request.WithContext(rc)
	return wc
}
func MockSuggest(ctx *gdp.WebContext) {
	return
}
