package controllers

import (
	"strconv"
	"testing"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
)

func TestSuggest(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Suggest ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()

	conf := ConfRoute{}
	conf.His = []string{"sdel", "list"}
	conf.Error = "error"
	conf.Sug.Default = "sug"
	conf.Sug.Sug = "sug"
	conf.Sug.Open = []string{"app", "wenku"}
	utInst.MockGlobalVariable(&confRoute, conf)
	webctx := createInfoGetWebContext1()
	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"TestSuggest",
			args{
				webctx,
			},
		},
	}

	for _, tt := range tests {
		Suggest(tt.args.ctx)
	}
	ag.Run()
}

func TestCreateSugPageSrv(t *testing.T) {
	type args struct {
		adaption map[string]string
		action   string
		ctl      string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: CreateSugPageSrv ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	adaptions := map[string]string{
		"platform": "android",
	}
	tests := []struct {
		testCaseTitle string
		args          args
		want          Want // string
	}{
		{
			"testCreateSugPageSrv",
			args{
				adaptions,
				"sug",
				"",
			},
			Want{
				"sug-android",
				ut.ShouldEqual,
			},
		},
		{
			"testCreateSugPageSrv",
			args{
				adaptions,
				"app",
				"sug",
			},
			Want{
				"sug-open",
				ut.ShouldEqual,
			},
		},
		{
			"testCreateSugPageSrv",
			args{
				adaptions,
				"appqqq",
				"sug",
			},
			Want{
				"sug-union",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := CreateSugPageSrv(tt.args.adaption, tt.args.action, tt.args.ctl)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestCreateSugPageSrv, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}

func TestCreateHisPageSrv(t *testing.T) {
	type args struct {
		adaption map[string]string
		action   string
		ctl      string
	}

	type Want struct {
		Value  interface{}
		Assert func(actual interface{}, expected ...interface{}) string
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: CreateHisPageSrv ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	adaptions := map[string]string{
		"platform": "android",
	}
	tests := []struct {
		testCaseTitle string
		args          args
		want          Want // string
	}{
		{
			"TestCreateHisPageSrv",
			args{
				adaptions,
				"sdel",
				"his",
			},
			Want{
				"his-sdel",
				ut.ShouldEqual,
			},
		},
		{
			"TestCreateHisPageSrv",
			args{
				adaptions,
				"list",
				"his",
			},
			Want{
				"his-list-android",
				ut.ShouldEqual,
			},
		},
		{
			"TestCreateHisPageSrv",
			args{
				adaptions,
				"qqqqqqq",
				"his",
			},
			Want{
				"error",
				ut.ShouldEqual,
			},
		},
	}

	for k, tt := range tests {
		got := CreateHisPageSrv(tt.args.adaption, tt.args.action, tt.args.ctl)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestCreateHisPageSrv, Result Index:0 Value Compare", got, tt.want.Assert, tt.want.Value.(string))
	}
	ag.Run()
}
