package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type assistantErr struct {
	Status string `json:"status"`
	Msg    string `json:"msg"`
}

// Assistant 手百交互式搜索框架流量转发
func Assistant(ctx *gdp.WebContext) {
	var res string
	var err error
	res, err = common.TransRequest(ctx, "assistant_ui", "/assistant/cmd")

	if err != nil {
		ret := assistantErr{
			Status: "500",
			Msg:    "service exception",
		}
		tplByte, _ := json.Marshal(ret)
		ctx.AddNotice("finalres", fmt.Sprintf("%s", tplByte))
		ctx.String(http.StatusOK, "%s", tplByte)
		return
	}
	ctx.AddNotice("finalres", fmt.Sprintf("%s", res))
	ctx.String(http.StatusOK, "%s", res)
}
