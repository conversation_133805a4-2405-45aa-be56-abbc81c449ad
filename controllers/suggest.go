package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/encoding/toml"
	"icode.baidu.com/baidu/gdp/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/ai"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/clickrec"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/cpage"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/his"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/output"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/realtimerec"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/set"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/sug"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/twoinone"
	"icode.baidu.com/baidu/searchbox/go-suggest/models/service/page/ups"
)

type ConfRoute struct {
	His        []string `toml:"his"`
	ChillinAdv []string `toml:"chilladv"`
	Error      string   `toml:"error"`
	Sug        struct {
		Default string   `toml:"__default"`
		Sug     string   `toml:"sug"`
		Open    []string `toml:"open"`
	} `toml:"sug"`
	ChillinSugRoute map[string]string `toml:"chillin_sug_route"`
}

type responseErr struct {
	Errno     string `json:"errno"`
	Errmsg    string `json:"errmsg"`
	Requestid string `json:"requestid"`
}

func Suggest(ctx *gdp.WebContext) {
	ctl := ctx.Context.Request.FormValue("ctl")
	action := ctx.Context.Request.FormValue("action")
	ctx.AddNotice("twoinonedegrade", sug.GetTwoinoneDegrade())
	var adaption map[string]string
	if val, exists := ctx.Get(constant.ADAPARAMS); exists && val != nil {
		adaption, _ = val.(map[string]string)
	}

	// 未找到对应服务，统一设置成error
	srvErr := "error"
	pageSrvName := "error"
	stdCtx := ctx.StdContext()

	// 二合一降级请求和普通sug请求处理逻辑一致
	if ctl == "sug" || (ctl == "sug_prefetch" && (sug.GetTwoinoneDegrade() == "on")) {
		if ctl == "sug_prefetch" {
			ctl = "sug"
		}
		pageSrvName = CreateSugPageSrv(adaption, action, ctl)
		pageSrc, err := sug.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}

		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))
		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.AddNotice("finalres", fmt.Sprintf("%s", tplByte))
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}
		pageSrc.Execute()
		// ctx.AddNotice("finalres", fmt.Sprintf("%s", pageSrc.GetTplData()))
		ctx.Header("Content-Type", "application/json; charset=utf-8")
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if ctl == "sug_prefetch" {
		ctx.AddNotice("api", ctl)
		logit.AddMetaFields(stdCtx, logit.String("api", ctl))
		pageSrc, _ := twoinone.NewPager("twoinone", ctx)
		pageSrc.Execute()
	} else if ctl == "his" {
		pageSrvName = CreateHisPageSrv(adaption, action, ctl)
		pageSrc, err := his.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}

		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))

		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.AddNotice("finalres", fmt.Sprintf("%s", tplByte))
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}
		pageSrc.Execute()

		// his-universal接口包含热榜内容，数据太大了
		if action != "universal" && action != "rec" && action != "board" {
			ctx.AddNotice("finalres", fmt.Sprintf("%s", pageSrc.GetTplData()))
		}

		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if ctl == "set" {
		pageSrvName = CreateSetPageSrv(action, ctl)
		pageSrc, err := set.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}

		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))
		if err != nil {
			ret := responseErr{
				Errno:     "-1",
				Errmsg:    "set fail",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.AddNotice("finalres", fmt.Sprintf("%s", tplByte))
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}
		pageSrc.Execute()
		ctx.AddNotice("finalres", fmt.Sprintf("%s", pageSrc.GetTplData()))
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if chillinSugPath, ok := confRoute.ChillinSugRoute[ctl]; ok {
		var res string
		var err error
		res, err = common.TransRequest(ctx, "chillin_suggest", chillinSugPath)
		ctx.AddNotice("api", ctl)
		logit.AddMetaFields(stdCtx, logit.String("api", ctl))

		if err != nil {
			ret := responseErr{
				Errno:     "-1",
				Errmsg:    err.Error(),
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.AddNotice("finalres", fmt.Sprintf("%s", tplByte))
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}
		ctx.AddNotice("finalres", fmt.Sprintf("%s", res))
		ctx.String(http.StatusOK, "%s", res)
	} else if ctl == "cpage" {
		pageSrvName = CreateCPagePageSrv(action, ctl)
		pageSrc, err := cpage.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}

		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))

		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "not found api",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}

		pageSrc.Execute()
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if ctl == "ups" {
		pageSrvName = CreateUPSPageSrv(action, ctl)
		pageSrc, err := ups.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}

		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))

		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "not found api",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}

		pageSrc.Execute()
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
		ctx.AddNotice("finalres", fmt.Sprintf("%s", pageSrc.GetTplData()))
	} else if ctl == "output" {
		pageSrvName = CreateOutputPageSrv(action, ctl)
		pageSrc, err := output.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}
		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))
		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "not found api",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}

		pageSrc.Execute()
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if ctl == "realtimerec" { // 简搜异步推荐
		pageSrvName := "realtime-rec"
		pageSrc, err := realtimerec.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}
		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))
		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.AddNotice("finalres", string(tplByte))
			ctx.String(http.StatusOK, "%s", string(tplByte))
			return
		}

		pageSrc.Execute()
		ctx.AddNotice("finalres", pageSrc.GetTplData())
		ctx.Header("Content-Type", "application/json; charset=utf-8")
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if ctl == "ai" { // 互动tab的sug接口
		pageSrvName = CreateAIPageSrv(action, ctl)
		pageSrc, err := ai.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}
		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))
		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.AddNotice("finalres", string(tplByte))
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}
		pageSrc.Execute()
		ctx.Header("Content-Type", "application/json; charset=utf-8")
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else if ctl == "click_rec" {
		// 点后推升级sug接口
		pageSrvName = CreatClickRecPageSrv(action, ctl)
		pageSrc, err := clickrec.NewPager(pageSrvName, ctx)
		if err != nil {
			pageSrvName = srvErr
		}

		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))

		if err != nil {
			ret := responseErr{
				Errno:     "-9999",
				Errmsg:    "not found api",
				Requestid: ctx.GetLogID(),
			}
			tplByte, _ := json.Marshal(ret)
			ctx.String(http.StatusOK, "%s", tplByte)
			return
		}

		pageSrc.Execute()
		ctx.String(http.StatusOK, "%s", pageSrc.GetTplData())
	} else {
		ctx.AddNotice("api", pageSrvName)
		logit.AddMetaFields(stdCtx, logit.String("api", pageSrvName))
		ret := responseErr{
			Errno:     "-9999",
			Errmsg:    "",
			Requestid: ctx.GetLogID(),
		}
		tplByte, _ := json.Marshal(ret)
		ctx.AddNotice("finalres", fmt.Sprintf("%s", tplByte))
		ctx.String(http.StatusOK, "%s", tplByte)
		return
	}
}

var confRoute ConfRoute

func init() {
	filePath := filepath.Join(env.ConfRootPath(), "route.toml")
	_, err := toml.DecodeFile(filePath, &confRoute)
	if err != nil {
		panic(err)
	}
}

func CreateSugPageSrv(adaption map[string]string, action string, ctl string) string {
	// session_rec
	if action == "session_rec" {
		return "sug-session_rec"
	}
	// 普通sug
	if action == "sug" || action == "" {
		platform := adaption["platform"]
		return "sug" + "-" + platform
	}
	// 判断是否在六合sug 配置白名单内
	for _, nodeName := range confRoute.Sug.Open {
		if nodeName == action {
			return ctl + "-" + "open"
		}
	}
	// action有值但不在白名单内，交由union处理
	return ctl + "-" + "union"
}

func CreateHisPageSrv(adaption map[string]string, action, ctl string) string {
	for _, nodeName := range confRoute.His {
		if nodeName == action {
			if action == "list" {
				platform := adaption["platform"]
				return ctl + "-" + nodeName + "-" + platform
			}
			return ctl + "-" + nodeName
		}
	}
	return "error"
}

func CreateSetPageSrv(action string, ctl string) string {
	if action == "prefetch" {
		return "set-prefetch"
	}
	if action == "twoinone" {
		return "set-twoinone"
	}
	return "error"
}

func CreateCPagePageSrv(action string, ctl string) string {
	return ctl + "-" + action
}

func CreateUPSPageSrv(action string, ctl string) string {
	return ctl + "-" + action
}

func CreateOutputPageSrv(action string, ctl string) string {
	return ctl + "-" + action
}

func CreateAIPageSrv(action string, ctl string) string {
	return ctl + "-" + action
}

func CreatClickRecPageSrv(action string, ctl string) string {
	return ctl + "-" + action
}
