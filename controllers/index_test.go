package controllers

import (
	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"testing"
)

func TestIndex(t *testing.T) {
	type args struct {
		ctx *gdp.WebContext
	}

	utInst := ut.New(t, "Unit tests for FUNCTION: Index ")
	defer utInst.RestoreAll()
	ag := utInst.NewAssertGroup()
	webctx := createInfoGetWebContext()
	tests := []struct {
		testCaseTitle string
		args          args
	}{
		{
			"index func test",
			args{
				webctx,
			},
		},
	}

	for k, tt := range tests {
		Index(tt.args.ctx)
		ag.Add(strconv.Itoa(k)+" Test Case Of TestGet, Result Index:0 Value Compare", httpRspBody.Body.String(), ut.ShouldEqual, `{"errno":"-1","msg":"Unexpect request"}`)
	}
	ag.Run()
}

var (
	httpRspBody *httptest.ResponseRecorder
)

func createInfoGetWebContext() *gdp.WebContext {
	httpRspBody = httptest.NewRecorder()       //输出Writer
	c, _ := gin.CreateTestContext(httpRspBody) //获取Gin提供的Context，WebContext包括gin.Context
	req, _ := http.NewRequest("GET", `/`, nil) //构造Request
	req.AddCookie(&http.Cookie{
		Name:     "BDUSS",
		Value:    url.QueryEscape("ValidBduss"),
		MaxAge:   math.MaxInt64,
		Path:     "*",
		Domain:   ".baidu.com",
		Secure:   false,
		HttpOnly: false,
	})
	c.Request = req //构造WebContext
	wc := &gdp.WebContext{
		c,
		gdp.NewLogContext("1", "1.1.1.1"),
	}
	return wc
}
