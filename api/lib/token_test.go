package lib

import (
	"testing"
)

func TestGetAITabBaseToken(t *testing.T) {
	tests := []struct {
		name     string
		lid      string
		expected string
	}{
		{
			name:     "empty lid",
			lid:      "",
			expected: "",
		},
		{
			name:     "normal lid",
			lid:      "test123",
			expected: "308326ad",
		},
		{
			name:     "long lid",
			lid:      "this_is_a_very_long_lid_value_for_testing_purposes",
			expected: "939a0b87",
		},
		{
			name:     "special chars lid",
			lid:      "!@#$%^&*()",
			expected: "1b88c949",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetAITabBaseToken(tt.lid)
			if got != tt.expected {
				t.<PERSON>rrorf("GetAITabBaseToken() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestGenHash(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "empty string",
			input:    "",
			expected: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
		},
		{
			name:     "normal string",
			input:    "hello world",
			expected: "b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9",
		},
		{
			name:     "special characters",
			input:    "!@#$%^&*()",
			expected: "95ce789c5c9d18490972709838ca3a9719094bca3ac16332cfec0652b0236141",
		},
		{
			name:     "long string",
			input:    "this_is_a_very_long_string_for_testing_purposes_with_many_characters_to_test_hash_function",
			expected: "3671d5b32ae40b9eefd107b85c2cb87e82f1536c5308f563b197f17da590f413",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := genHash(tt.input)
			if got != tt.expected {
				t.Errorf("genHash() = %v, want %v", got, tt.expected)
			}
		})
	}
}
