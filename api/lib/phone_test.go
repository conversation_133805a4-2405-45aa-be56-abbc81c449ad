package lib

import (
	"errors"
	"net/url"
	"strconv"
	"strings"
	"testing"
)

func TestGenerateCallPageSchema(t *testing.T) {
	tests := []struct {
		name          string
		input         ProductParams
		expectErr     error
		expectContain string // 用于断言schema中包含的字段
	}{
		{
			name: "valid input with default ReRank and phone call",
			input: ProductParams{
				Logid:        "test-logid",
				Sa:           "test-sa",
				EnterType:    "test-enter",
				CallPageFrom: 100,
				Type:         "0",
			},
			expectErr:     nil,
			expectContain: "test-logid",
		},
		{
			name: "valid input with video call",
			input: ProductParams{
				Logid:        "test-logid",
				Sa:           "test-sa",
				EnterType:    "test-enter",
				CallPageFrom: 100,
				Type:         "1",
				ReRank:       "2",
			},
			expectErr:     nil,
			expectContain: `"initialCallType":"1"`,
		},
		{
			name: "missing logid",
			input: ProductParams{
				Sa:           "test-sa",
				EnterType:    "test-enter",
				CallPageFrom: 100,
			},
			expectErr: ErrLogidEmpty,
		},
		{
			name: "missing sa",
			input: ProductParams{
				Logid:        "test-logid",
				EnterType:    "test-enter",
				CallPageFrom: 100,
			},
			expectErr: ErrSaEmpty,
		},
		{
			name: "missing enterType",
			input: ProductParams{
				Logid:        "test-logid",
				Sa:           "test-sa",
				CallPageFrom: 100,
			},
			expectErr: ErrEnterTypeEmpty,
		},
		{
			name: "missing callPageFrom",
			input: ProductParams{
				Logid:     "test-logid",
				Sa:        "test-sa",
				EnterType: "test-enter",
			},
			expectErr: ErrCallPageFromEmpty,
		},
		{
			name: "invalid ReRank",
			input: ProductParams{
				Logid:        "test-logid",
				Sa:           "test-sa",
				EnterType:    "test-enter",
				CallPageFrom: 100,
				ReRank:       "abc", // 非法
			},
			expectErr: &strconv.NumError{Func: "Atoi"}, // 类型匹配
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			schema, err := GenerateCallPageSchema(tt.input)

			// 判断错误
			if tt.expectErr != nil {
				if err == nil || !errorMatch(err, tt.expectErr) {
					t.Errorf("expected error %v, got %v", tt.expectErr, err)
				}
				return
			}

			// 正常路径下断言 schema 非空
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if !strings.HasPrefix(schema, callPageSchemaPrefix) {
				t.Errorf("schema should start with prefix: %s", schema)
			}

			// URL 解码并断言内容包含预期字段
			parsed, _ := url.Parse(schema)
			params := parsed.Query().Get("params")
			unescaped, _ := url.QueryUnescape(params)
			if tt.expectContain != "" && !strings.Contains(unescaped, tt.expectContain) {
				t.Errorf("schema content missing %q: %s", tt.expectContain, unescaped)
			}
		})
	}
}

func errorMatch(actual, expected error) bool {
	if actual == nil || expected == nil {
		return actual == expected
	}

	// 类型判断用 errors.As 最稳妥
	switch expected.(type) {
	case *strconv.NumError:
		var numErr *strconv.NumError
		return errors.As(actual, &numErr)
	default:
		// 其他错误类型直接比对 error 字符串
		return actual.Error() == expected.Error()
	}
}
