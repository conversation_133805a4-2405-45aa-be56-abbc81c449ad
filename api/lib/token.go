package lib

import (
	"crypto/sha256"
	"encoding/hex"
)

var indices = []int{12, 37, 5, 23, 48, 15, 62, 33}

// 计算hash值
func genHash(plain string) string {
	hasher := sha256.New()
	hasher.Write([]byte(plain))
	hashVal := hasher.Sum(nil)
	return hex.EncodeToString(hashVal)
}

// GetAITabBaseToken pc AI助手的base token，前端根据base token生成实际的对话token
func GetAITabBaseToken(lid string) string {
	// 如果lid为空，直接返回空字符串
	if lid == "" {
		return ""
	}

	oriToken := genHash(lid)
	baseToken := make([]byte, len(indices))
	for i, idx := range indices {
		baseToken[i] = oriToken[idx]
	}
	return string(baseToken)
}
