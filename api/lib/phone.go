package lib

import (
	"encoding/json"
	"errors"
	"net/url"
	"strconv"
	"strings"
)

const callPageSchemaPrefix = "baiduboxapp://aiCall/open"

// 错误定义
var (
	ErrLogidEmpty        = errors.New("logid is empty")
	ErrEnterTypeEmpty    = errors.New("enterType is empty")
	ErrSaEmpty           = errors.New("sa is empty")
	ErrCallPageFromEmpty = errors.New("callPageFrom is empty")
)

type PhoneParams struct {
	AsrInfo        AsrInfo  `json:"asrInfo"`        // 语音识别服务
	MetaInfo       MetaInfo `json:"metaInfo"`       // 打电话/视频通话页面设置
	TtsInfo        TtsInfo  `json:"ttsInfo"`        // 语音播报服务
	ReqInfo        ReqInfo  `json:"reqInfo"`        // 请求信息
	LogInfo        LogInfo  `json:"logInfo"`        // 打点信息
	CallbackAction string   `json:"callbackAction"` // 回调
}

type AsrInfo struct {
	Key string `json:"key"` // 语音识别服务的key
	Pid string `json:"pid"` // 语音识别服务的pid
	URL string `json:"url"` // 语音识别服务的url
}

type MetaInfo struct {
	BackgroundStartColor string `json:"backgroundStartColor"` // 页面背景起始颜色
	FigureType           string `json:"figureType"`           // 页面角色类型，固定3
	Title                string `json:"title"`                // 页面标题，固定AI助手
	HeadPortraitImg      string `json:"headPortraitImg"`      // 头像图片，目前为空
	InitialCallType      string `json:"initialCallType"`      // 通话类型， 0: 打电话， 1: 视频通话
}

type TtsInfo struct {
	TtsSDKInfo TtsSDKInfo `json:"ttsSDKInfo"` // 语音播报服务
	Type       string     `json:"type"`       // 目前为ttssdk
}

type TtsSDKInfo struct {
	Pid       string `json:"pid"`       // 语音播报服务的pid
	SpeakerId string `json:"speakerId"` // 语音播报服务的speakerId
}

type ReqInfo struct {
	ReqURL         string            `json:"reqURL"`         // 请求地址，暂不开放给业务方
	BusinessParams BusinessParams    `json:"businessParams"` // 业务参数
	HeaderParams   map[string]string `json:"headerParams"`   // 请求头参数，透传逻辑，暂不开放给业务方
	SessionID      string            `json:"sessionId"`      // 会话id，可以为空，为空时新起对话
	Source         string            `json:"source"`         // 端上显示为必传字段，但没用
}

type BusinessParams struct {
	InteractionInfo InteractionInfo `json:"interactionInfo"` // AI助手打电话业务信息
	Type            string          `json:"type"`            // 固定为"interaction"
}

type InteractionInfo struct {
	PageLid    string `json:"pageLid"`    // 页面id,需要业务方传入
	BaseToken  string `json:"baseToken"`  // 由pageLid生成
	IsNewFrame bool   `json:"isNewFrame"` // 是否是新框架，固定true
	Sa         string `json:"sa"`         // 业务方传入，由数据pm分配
	EnterType  string `json:"enterType"`  // 进入类型，业务方传入，由数据pm分配
	ReRank     string `json:"re_rank"`    // 对话轮次，AI助手外调起默认"1"
}

// 打点信息
type LogInfo struct {
	Ext    ExtData `json:"ext"`    // 额外字段
	Source string  `json:"source"` // 固定为3841000010001000
}

type ExtData struct {
	CallPageFrom int `json:"call_page_from"` // 根据sa来设置，看具体打点需求
	FigureType   int `json:"figure_type"`    // 默认3
}

// 各业务调用需要传入的参数
type ProductParams struct {
	Logid          string `json:"logid"`          // 用于生成token，必传
	Sa             string `json:"sa"`             // 数据pm分配sa，必传
	EnterType      string `json:"enterType"`      // 入口类型，数据pm分配，必传
	ReRank         string `json:"reRank"`         // 对话轮次，AI助手外调起默认传"1"，非必传
	SessionID      string `json:"sessionID"`      // 会话id，AI助手外调起默认传""，非必传
	Type           string `json:"type"`           // 类型，区分是视频通话还是打电话，打电话，必传
	CallbackAction string `json:"callbackAction"` // 回调，非必传，各业务方需要回调操作时传入
	CallPageFrom   int    `json:"callPageFrom"`   // call_page_from，必传，端打点字段，每个业务需要传入
}

func GenerateCallPageSchema(productParams ProductParams) (string, error) {
	// 强校验，必传
	if productParams.Logid == "" {
		return "", ErrLogidEmpty
	}
	if productParams.EnterType == "" {
		return "", ErrEnterTypeEmpty
	}
	if productParams.Sa == "" {
		return "", ErrSaEmpty
	}
	if productParams.CallPageFrom == 0 {
		return "", ErrCallPageFromEmpty
	}
	// 轮次校验
	if productParams.ReRank == "" {
		productParams.ReRank = "1"
	} else {
		_, err := strconv.Atoi(productParams.ReRank)
		if err != nil {
			return "", err
		}
	}

	// 通话类型乱传都认为是打电话
	if productParams.Type != "1" {
		productParams.Type = "0"
	}
	reqInfo := ReqInfo{
		ReqURL: "https://chat.baidu.com/aichat/api/conversation?phone=1",
		BusinessParams: BusinessParams{
			InteractionInfo: InteractionInfo{
				PageLid:    productParams.Logid,
				BaseToken:  GetAITabBaseToken(productParams.Logid),
				IsNewFrame: true,
				Sa:         productParams.Sa,
				EnterType:  productParams.EnterType,
				ReRank:     productParams.ReRank,
			},
			Type: "interaction",
		},
		Source:    "interactionTab",
		SessionID: productParams.SessionID,
		HeaderParams: map[string]string{
			"source": "wise_csaitab",
		},
	}
	// 打点信息
	logInfo := LogInfo{
		Ext: ExtData{
			CallPageFrom: productParams.CallPageFrom,
			FigureType:   3,
		},
		Source: "3841000010001000",
	}
	phoneParams := PhoneParams{
		AsrInfo: AsrInfo{
			Key: "com.baidu.searchbox",
			Pid: "1233",
			URL: "https://vse.baidu.com/v2",
		},
		MetaInfo: MetaInfo{
			BackgroundStartColor: "#EDF0FD",
			FigureType:           "3",
			Title:                "AI\u52a9\u624b",
			InitialCallType:      productParams.Type,
		},
		TtsInfo: TtsInfo{
			TtsSDKInfo: TtsSDKInfo{
				Pid:       "10395",
				SpeakerId: "4189",
			},
			Type: "ttssdk",
		},
		ReqInfo:        reqInfo,
		LogInfo:        logInfo,
		CallbackAction: productParams.CallbackAction,
	}

	bytes, _ := json.Marshal(phoneParams)
	encodeString := url.QueryEscape(string(bytes))
	var phoneSchemaBuilder strings.Builder
	phoneSchemaBuilder.WriteString(callPageSchemaPrefix)
	phoneSchemaBuilder.WriteString("?params=")
	phoneSchemaBuilder.WriteString(encodeString)
	defer phoneSchemaBuilder.Reset()
	return phoneSchemaBuilder.String(), nil
}
