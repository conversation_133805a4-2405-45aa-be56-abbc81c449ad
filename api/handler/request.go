package handler

type FileType string

const (
	FileTypeImage FileType = "image"
	FileTypeFile  FileType = "file"
)

// FileSearchValidateRequest 定义了 AiToolValidate 接口的请求结构
type FileSearchValidateRequest struct {
	File []FileItem `json:"file"`
}

// FileItem 定义了文件对象的结构
type FileItem struct {
	ID   string   `json:"id"`
	Name string   `json:"name"`
	Path string   `json:"path"`
	Type FileType `json:"type"`
	Size int64    `json:"size"`
}

type FileSearchSugRequest struct {
	Word string     `json:"word"`
	File []FileItem `json:"file"`
}
