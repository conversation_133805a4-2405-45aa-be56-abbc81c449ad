package handler

import (
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// --- AiToolValidate Structures ---

// FileSearchValidateResponse 定义了 AiToolValidate 接口的响应结构
type FileSearchValidateResponse struct {
	Code  int          `json:"errno"`
	Data  ResponseData `json:"data"`
	Msg   string       `json:"errmsg"`
	LogID string       `json:"requestid"`
}

// ResponseData 定义了响应数据中 data 字段的结构
type ResponseData struct {
	Files []FileValidationResult `json:"files"`
}

// FileValidationResult 定义了文件校验结果的结构
type FileValidationResult struct {
	ID        string          `json:"id"`
	Status    string          `json:"status"`
	Code      entity.RiskCode `json:"code"`
	Msg       string          `json:"errmsg"`
	WordCount int             `json:"word_count"`
}

// --- AiToolSTS Structures ---

// FileSearchSTSResponse 定义了 AiToolSTS 接口的响应结构
type FileSearchSTSResponse struct {
	Code  int                       `json:"errno"`
	Data  FileSearchSTSResponseData `json:"data"`
	Msg   string                    `json:"errmsg"`
	LogID string                    `json:"requestid"`
}

// FileSearchSTSResponseData 定义了 AiToolSTS 接口响应中 data 字段的结构
type FileSearchSTSResponseData struct {
	PreFixPath string `json:"preFixPath"`
	BceURL     string `json:"bceUrl"`
	BucketName string `json:"bucketName"`
	Ak         string `json:"ak"`
	Token      string `json:"token"`
	Sk         string `json:"sk"`
}

// --- AiToolSug Structures (New Version) ---

// Pos 定义了 AiToolSug 响应中 sug 项内 pos 数组元素的结构
type Pos struct {
	Begin int `json:"begin"`
	End   int `json:"end"`
}

// FileSearchItem 定义 AiToolSug 接口响应中 sug 数组元素的结构
type FileSearchItem struct {
	Word   string `json:"word"`
	Type   string `json:"type"`
	WapURL string `json:"wap_url,omitempty"`
	WwwURL string `json:"www_url,omitempty"`
	SA     string `json:"sa"`
	Pos    []Pos  `json:"pos,omitempty"`
}

// Query 定义了 AiToolSug响应中 extend.query 字段的结构
type Query struct {
	Word string `json:"word"`
	Type string `json:"type"`
}

// Extend 定义了 AiToolSug 响应中 extend 字段的结构
type Extend struct {
	Query    Query  `json:"query"`
	Prefetch string `json:"prefetch"`
}

// SwitchControl 定义了 AiToolSug 响应中 switch_control 字段的结构
type SwitchControl struct {
	RequestLocation int `json:"requestLocation"`
}

// BoxData 定义了 AiToolSug 响应中 boxData 字段的结构
type BoxData struct {
	ShowText string `json:"show_text"`
}

// FileSearchSugResponse 定义新的 AiToolSug 接口的响应结构
type FileSearchSugResponse struct {
	Errno          int              `json:"errno"`
	Msg            string           `json:"msg"`
	Type           string           `json:"type"`
	Slid           string           `json:"slid"`
	Extend         Extend           `json:"extend"`
	BoxData        BoxData          `json:"boxData"`
	Data           []FileSearchItem `json:"data"`
	GMot           []interface{}    `json:"g_mot"`
	SwitchControl  SwitchControl    `json:"switch_control"`
	GMotStyle      int              `json:"g_mot_style"`
	GMotTitle      string           `json:"g_mot_title"`
	LogID          string           `json:"requestid"`
	FileSearchTool interface{}      `json:"ai_tool"`
}
