package handler

import (
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// 为了向后兼容，重新导出entity中的类型
type FileSearchValidateResponse = entity.FileSearchValidateResponse
type ResponseData = entity.ResponseData
type FileValidationResult = entity.FileValidationResult
type FileSearchSTSResponse = entity.FileSearchSTSResponse
type FileSearchSTSResponseData = entity.FileSearchSTSResponseData

// 为了向后兼容，重新导出entity中的类型
type FileSearchItem = entity.FileSearchItem
type Query = entity.Query
type Extend = entity.Extend
type SwitchControl = entity.SwitchControl
type BoxData = entity.BoxData
type FileSearchSugResponse = entity.FileSearchSugResponse
