package handler

import (
	"fmt"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/const"
	"math/rand"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/service"
)

// --- Predefined Error Responses ---
var (
	responseInvalidRequestBody = func(ctx *gdp.WebContext) map[string]interface{} {
		return map[string]interface{}{
			"errno":     400,
			"errmsg":    "invalid_request_body",
			"requestid": ctx.GetLogID(),
		}
	}
	responseFailedToGetSTSInfo = func(ctx *gdp.WebContext) map[string]interface{} {
		return map[string]interface{}{
			"errno":     505,
			"errmsg":    "Failed to get STS info",
			"requestid": ctx.GetLogID(),
		}
	}
)

// Note: All request and response structs have been moved to
// request.go and response.go respectively.

// allSugWords was here, user has removed it.

func init() {
	rand.Seed(time.Now().UnixNano())
}

func FileSearchSTS(ctx *gdp.WebContext) {
	reqParams := entity.GetRequestParams(ctx)
	if reqParams == nil {
		ctx.Warning("request params is nil")
		ctx.JSON(200, responseInvalidRequestBody(ctx)) // HTTP status is 200, error in body
		return
	}
	bosService := service.NewBosService()
	stsInfo, err := bosService.GetSTSInfo(constants.PathPerfix, constants.STSExpirationTime, reqParams.UID) // 注意：cuid "123" 是一个示例值
	if err != nil {
		ctx.WarningF("failed to get sts info: %v", err)
		ctx.JSON(200, responseFailedToGetSTSInfo(ctx)) // HTTP status is 200, error in body
		return
	}

	respData := FileSearchSTSResponseData{
		PreFixPath: stsInfo.PreFixPath,
		BceURL:     stsInfo.Endpoint,
		BucketName: stsInfo.BucketName,
		Ak:         stsInfo.AccessKeyID,
		Token:      stsInfo.SessionToken,
		Sk:         stsInfo.SecretAccessKey,
	}

	ctx.JSON(200, FileSearchSTSResponse{
		Code:  0,
		Data:  respData,
		Msg:   "success",
		LogID: ctx.GetLogID(),
	})
}

func FileSearchSug(ctx *gdp.WebContext) {
	reqParams := entity.GetRequestParams(ctx)
	if reqParams == nil {
		ctx.Warning("request params is nil")
		ctx.JSON(200, responseInvalidRequestBody(ctx)) // HTTP status is 200, error in body
		return
	}
	var req FileSearchSugRequest
	if err := ctx.BindJSON(&req); err != nil {
		ctx.JSON(400, responseInvalidRequestBody(ctx)) // HTTP status is 400
		return
	}
	// Temporary placeholder for allSugWords since it was removed by the user.
	// You'll need to either restore it, move its definition from elsewhere,
	// or adjust the logic if it's no longer needed.

	// 随机选择10个词条
	var sugWords []string
	var defaultBox string
	var fileTool *service.FileSearchToolResponse
	if len(req.File) > 0 {
		switch req.File[0].Type {
		case "file":
			sugWords = FileSugWords
			defaultBox = FileSugDefaultBox
		case "image":
			sugWords = ImageSugWords
			defaultBox = ImageSugDefaultBox
			aiToolSrv := service.NewAiToolService(ctx)
			fileTool, _ = aiToolSrv.GetFileSearchTool(reqParams.UID, req.File[0].Path, "pic")
		default:
			sugWords = FileSugWords
			defaultBox = FileSugDefaultBox
		}
	} else {
		sugWords = FileSugWords
		defaultBox = FileSugDefaultBox
	}
	selectedWords := make([]string, 0, 10)
	if len(sugWords) <= 10 {
		selectedWords = sugWords
	} else {
		perm := rand.Perm(len(sugWords))
		for i := 0; i < 10; i++ {
			selectedWords = append(selectedWords, sugWords[perm[i]])
		}
	}
	var sugItems []FileSearchItem
	for i, word := range selectedWords {
		sugItems = append(sugItems, FileSearchItem{
			Word:   word,
			Type:   "0",
			WapURL: "",
			WwwURL: "",
			SA:     fmt.Sprintf("aib_aifile_s_%d", i+1),
			// Pos 字段根据需要添加，这里暂时不加
		})
	}

	// 构造响应数据
	response := FileSearchSugResponse{
		Errno: 0, // 通常成功是 "0"
		Msg:   "success",
		Type:  "sug",
		Slid:  "3984000149", // 固定值
		Extend: Extend{
			Query: Query{
				Word: req.Word, // 从 reqParams 获取
				Type: "0",      // 固定值
			},
			Prefetch: "off", // 固定值
		},
		BoxData: BoxData{
			ShowText: defaultBox,
		},
		FileSearchTool: fileTool,
		Data:           sugItems,
		GMot:           []interface{}{}, // 固定值
		SwitchControl: SwitchControl{
			RequestLocation: 1, // 固定值
		},
		GMotStyle: 0,  // 固定值
		GMotTitle: "", // 固定值
		LogID:     ctx.GetLogID(),
	}

	ctx.JSON(200, response)
}

func FileSearchValidate(ctx *gdp.WebContext) {
	reqParams := entity.GetRequestParams(ctx)
	if reqParams == nil {
		ctx.Warning("request params is nil")
		ctx.JSON(200, responseInvalidRequestBody(ctx)) // HTTP status is 200, error in body
		return
	}
	st := ctx.Query("stk")
	var req FileSearchValidateRequest
	if err := ctx.BindJSON(&req); err != nil {
		ctx.JSON(200, responseInvalidRequestBody(ctx)) // HTTP status is 400
		return
	}

	var validatedFiles []FileValidationResult
	metaInfoErrCount := 0
	authURLErrCount := 0
	// 获取文件基本信息，如果基本信息都没有那就不请求后面的服务了。
	riskRequestFiles := make([]service.RiskRequestFile, 0, len(req.File))
	bosService := service.NewBosService()
	userPath := bosService.GetBosPath(constants.PathPerfix, reqParams.UID)

	for _, fileItem := range req.File {
		// 仅允许访问该 CUID 对应目录下的文件。路径有问题需要用户重传
		if !strings.Contains(fileItem.Path, userPath) {
			validatedFiles = append(validatedFiles, FileValidationResult{
				ID:     fileItem.ID,
				Code:   entity.RiskCodeReupload,
				Msg:    "need re-upload",
				Status: "failed",
			})
			continue
		}
		switch fileItem.Type {
		case FileTypeImage:
			info, err := bosService.GetImageMetaInfo(fileItem.Path)
			if err != nil {
				metaInfoErrCount++
				continue
			}
			authURL, err := bosService.GetAuthURL(constants.PathPerfix, constants.AuthURLExpirationTime, reqParams.UID, []string{fileItem.Path})
			if err != nil || len(authURL) == 0 {
				authURLErrCount++
				continue
			}
			riskRequestFiles = append(riskRequestFiles, service.RiskRequestFile{
				ID:          fileItem.ID,
				Name:        fileItem.Name,
				URL:         authURL[0],
				Type:        string(fileItem.Type),
				Size:        info.Size,
				MimeType:    info.MimeType,
				ImageWidth:  info.Width,
				ImageHeight: info.Height,
			})
		case FileTypeFile:
			info, err := bosService.GetObjectMetaInfo(fileItem.Path)
			if err != nil {
				metaInfoErrCount++
				continue
			}
			authURL, err := bosService.GetAuthURL(constants.PathPerfix, constants.AuthURLExpirationTime, reqParams.UID, []string{fileItem.Path})
			if err != nil || len(authURL) == 0 {
				authURLErrCount++
				continue
			}
			riskRequestFiles = append(riskRequestFiles, service.RiskRequestFile{
				ID:       fileItem.ID,
				Name:     fileItem.Name,
				URL:      authURL[0],
				Type:     string(fileItem.Type),
				Size:     info.Size,
				MimeType: strings.ToUpper(strings.TrimPrefix(filepath.Ext(fileItem.Name), ".")),
			})
		}

	}

	// 请求APP480 接口进行风控 这里是接口POST /his-ai/validate?
	riskService := service.NewRiskService(ctx)
	riskResult, err := riskService.CheckRisk(riskRequestFiles)
	if err != nil {
		ctx.JSON(200, FileSearchValidateResponse{
			Code: 503,
			Data: ResponseData{
				Files: nil,
			},
			Msg:   "failed",
			LogID: ctx.GetLogID(),
		})
		return
	}

	// 获取可读取的sts授权链接存入 redis 供 APP480 读取。最终数据会提供给结果页获取用户对应的文件。
	// 用于APP480 POST /his-ai/get-fileinfo 接口获取文件信息，这个接口由结果页调用。go-sug 只负责传递数据到 redis
	fileInfos := make([]service.FileSearchInfo, 0, len(req.File))
	for _, fileItem := range riskRequestFiles {
		result, ok := riskResult[service.FileID(fileItem.ID)]
		if !ok {
			validatedFiles = append(validatedFiles, FileValidationResult{
				ID:     fileItem.ID,
				Code:   entity.RiskCodeReupload,
				Msg:    "need re-upload",
				Status: "failed",
			})
			continue
		}
		fileInfos = append(fileInfos, service.FileSearchInfo{
			ID:       result.ID,
			Name:     fileItem.Name,
			Type:     string(fileItem.Type),
			MimeType: fileItem.MimeType,
			Size:     fileItem.Size,
			URL:      fileItem.URL,
		})

		validatedFile := FileValidationResult{
			ID:   result.ID,
			Code: result.Code,
			Msg:  result.Message,
		}
		if result.Code == entity.RiskCodeSuccess {
			validatedFile.Status = "success"
		} else {
			validatedFile.Status = "failed"
		}
		validatedFiles = append(validatedFiles, validatedFile)

	}
	// 设置文件信息到redis
	fileSearchService := service.NewFileSearchService(ctx)
	err = fileSearchService.SetFileInfo(fileInfos, st)
	if err != nil {
		ctx.WarningF("set file info failed: %v", err)
		for i := range validatedFiles {
			validatedFiles[i].Status = "failed"
			validatedFiles[i].Code = entity.RiskCodeReupload
			validatedFiles[i].Msg = "need re-upload"
		}
	}
	sort.Slice(validatedFiles, func(i, j int) bool {
		idI, _ := strconv.Atoi(validatedFiles[i].ID)
		idJ, _ := strconv.Atoi(validatedFiles[j].ID)
		return idI < idJ
	})
	resp := FileSearchValidateResponse{
		Code: 0,
		Data: ResponseData{
			Files: validatedFiles,
		},
		Msg:   "success",
		LogID: ctx.GetLogID(),
	}

	ctx.JSON(200, resp)
}
