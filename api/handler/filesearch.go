package handler

import (
	"icode.baidu.com/baidu/gdp/gdp"
	constants "icode.baidu.com/baidu/searchbox/go-suggest/api/const"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/service"
)

// --- Predefined Error Responses ---
var (
	responseInvalidRequestBody = func(ctx *gdp.WebContext) map[string]interface{} {
		return map[string]interface{}{
			"errno":     400,
			"errmsg":    "invalid_request_body",
			"requestid": ctx.GetLogID(),
		}
	}
	responseFailedToGetSTSInfo = func(ctx *gdp.WebContext) map[string]interface{} {
		return map[string]interface{}{
			"errno":     505,
			"errmsg":    "Failed to get STS info",
			"requestid": ctx.GetLogID(),
		}
	}
)

// Note: All request and response structs have been moved to
// request.go and response.go respectively.

func FileSearchSTS(ctx *gdp.WebContext) {
	reqParams := entity.GetRequestParams(ctx)
	if reqParams == nil {
		ctx.Warning("request params is nil")
		ctx.JSON(200, responseInvalidRequestBody(ctx)) // HTTP status is 200, error in body
		return
	}
	bosService := service.NewBosService()
	stsInfo, err := bosService.GetSTSInfo(constants.PathPerfix, constants.STSExpirationTime, reqParams.UID) // 注意：cuid "123" 是一个示例值
	if err != nil {
		ctx.WarningF("failed to get sts info: %v", err)
		ctx.JSON(200, responseFailedToGetSTSInfo(ctx)) // HTTP status is 200, error in body
		return
	}

	respData := entity.FileSearchSTSResponseData{
		PreFixPath: stsInfo.PreFixPath,
		BceURL:     stsInfo.Endpoint,
		BucketName: stsInfo.BucketName,
		Ak:         stsInfo.AccessKeyID,
		Token:      stsInfo.SessionToken,
		Sk:         stsInfo.SecretAccessKey,
	}

	ctx.JSON(200, entity.FileSearchSTSResponse{
		Code:  0,
		Data:  respData,
		Msg:   "success",
		LogID: ctx.GetLogID(),
	})
}

func FileSearchSug(ctx *gdp.WebContext) {
	// 1. 参数验证
	reqParams := entity.GetRequestParams(ctx)
	if reqParams == nil {
		ctx.Warning("request params is nil")
		ctx.JSON(200, responseInvalidRequestBody(ctx))
		return
	}

	var req entity.FileSearchSugRequest
	if err := ctx.BindJSON(&req); err != nil {
		ctx.JSON(400, responseInvalidRequestBody(ctx))
		return
	}

	// 2. 调用business service
	businessService := service.NewFileSearchBusinessService(ctx)
	response, err := businessService.GenerateFileSuggestions(&req, reqParams.UID)
	if err != nil {
		ctx.WarningF("business service error: %v", err)
		ctx.JSON(200, responseInvalidRequestBody(ctx))
		return
	}

	// 3. 返回响应
	ctx.JSON(200, response)
}

func FileSearchValidate(ctx *gdp.WebContext) {
	// 1. 参数验证
	reqParams := entity.GetRequestParams(ctx)
	if reqParams == nil {
		ctx.Warning("request params is nil")
		ctx.JSON(200, responseInvalidRequestBody(ctx))
		return
	}

	var req entity.FileSearchValidateRequest
	if err := ctx.BindJSON(&req); err != nil {
		ctx.JSON(200, responseInvalidRequestBody(ctx))
		return
	}

	// 2. 调用business service
	businessService := service.NewFileSearchBusinessService(ctx)
	response, err := businessService.ValidateFiles(&req, ctx.Query("stk"), reqParams.UID)
	if err != nil {
		ctx.WarningF("business service error: %v", err)
		ctx.JSON(200, responseInvalidRequestBody(ctx))
		return
	}

	// 3. 返回响应
	ctx.JSON(200, response)
}
