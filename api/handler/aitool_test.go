package handler

import (
	"bytes"
	"encoding/json"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
)

func TestAiToolSug(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name: "正常文件类型请求",
			requestBody: FileSearchSugRequest{
				Word: "test",
				File: []FileItem{
					{
						Type: "file",
						Path: "/test/path",
					},
				},
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Equal(t, float64(0), response["errno"])
				assert.Equal(t, "success", response["msg"])
				assert.Equal(t, "sug", response["type"])

				// 检查返回的建议词条数量
				data, ok := response["data"].([]interface{})
				assert.True(t, ok)
				assert.LessOrEqual(t, len(data), 10)
			},
		},
		{
			name: "正常图片类型请求",
			requestBody: FileSearchSugRequest{
				Word: "test",
				File: []FileItem{
					{
						Type: "image",
						Path: "/test/path",
					},
				},
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Equal(t, float64(0), response["errno"])
				assert.Equal(t, "success", response["msg"])
				assert.Equal(t, "sug", response["type"])

				// 检查返回的建议词条数量
				data, ok := response["data"].([]interface{})
				assert.True(t, ok)
				assert.LessOrEqual(t, len(data), 10)
			},
		},
		{
			name:           "无效请求体",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Equal(t, float64(400), response["errno"])
				assert.Equal(t, "invalid_request_body", response["errmsg"])
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试请求
			body, err := json.Marshal(tt.requestBody)
			if err != nil {
				t.Fatal(err)
			}

			req := httptest.NewRequest(http.MethodPost, "/aitool/sug", nil)
			if tt.name != "无效请求体" {
				req = httptest.NewRequest(http.MethodPost, "/aitool/sug", bytes.NewBuffer(body))
			}

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 创建gin上下文
			gin.SetMode(gin.TestMode)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// 创建GDP上下文
			ctx := gdp.NewWebContext(c)
			reqParams := &entity.RequestParams{
				UID:     "testUID123|abcde",
				Query:   "测试查询",
				Service: "test-service",
				From:    "test-from",
			}
			ctx.Set(entity.ReqParamsKey, reqParams)
			// 调用被测试的函数
			FileSearchSug(ctx)

			// 检查状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析响应
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			// 执行自定义检查
			tt.checkResponse(t, response)
		})
	}
}
