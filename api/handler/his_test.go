package handler

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/got/ut"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

func TestHisUniversal(t *testing.T) {
	// 初始化测试环境
	utInst := ut.New(t, "单元测试 HisUniversal 函数")
	defer utInst.RestoreAll()

	// Mock SetUserPrivacyInfo 函数，避免实际调用外部服务
	utInst.MockFunc(common.SetUserPrivacyInfo, func(cuid string, ctx *gdp.WebContext) {
		// 模拟设置用户隐私信息
		ctx.Set("userPrivacyInfo", &common.UserPrivacyInfo{
			OAID: "test-oaid",
		})
	})

	// 创建测试场景
	tests := []struct {
		name           string
		setupRequest   func(*http.Request)
		setupContext   func(*gdp.WebContext)
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name: "正常请求场景",
			setupRequest: func(req *http.Request) {
				// 设置测试请求参数
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)")
			},
			setupContext: func(ctx *gdp.WebContext) {
				// 设置请求参数
				reqParams := &entity.RequestParams{
					UID:     "testUID123|abcde",
					Query:   "测试查询",
					Service: "test-service",
					From:    "test-from",
				}
				ctx.Set(entity.ReqParamsKey, reqParams)
			},
			expectedStatus: 200,
			expectedBody: map[string]interface{}{
				"errno":  "0",
				"errmsg": "",
				"data":   nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置Gin测试环境
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)

			// 创建HTTP请求
			req := httptest.NewRequest("GET", "/api/his/universal", nil)
			if tt.setupRequest != nil {
				tt.setupRequest(req)
			}
			ctx.Request = req

			// 创建GDP WebContext
			webCtx := gdp.NewWebContext(ctx)
			webCtx.SetLogID("test-log-id")

			// 设置测试上下文
			if tt.setupContext != nil {
				tt.setupContext(webCtx)
			}

			// 执行被测试函数
			HisUniversal(webCtx)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析JSON响应
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			// 验证返回的响应体
			assert.Equal(t, tt.expectedBody["errno"], response["errno"])
			assert.Equal(t, tt.expectedBody["errmsg"], response["errmsg"])
			assert.Equal(t, "test-log-id", response["requestid"])

			if tt.expectedBody["data"] != nil {
				assert.Equal(t, tt.expectedBody["data"], response["data"])
			}
		})
	}
}
