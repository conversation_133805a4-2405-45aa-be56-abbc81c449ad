// Package handler 新接口 handler 层
package handler

import (
	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/logit"
)

func HisUniversal(c *gdp.WebContext) {
	stdCtx := c.StdContext()
	logit.AddMetaFields(stdCtx, logit.String("api", "his-universal-v2"))
	// request := middleware.GetRequestParams(c)
	// if request == nil {
	// 	c.JSON(200, gin.H{
	// 		"errno":     "-1",
	// 		"errmsg":    "request is nil",
	// 		"requestid": c.GetLogID(),
	// 	})
	// 	return
	// }
	// common.SetUserPrivacyInfo(request.UID, c)
	// 创建服务实例
	// hisService, err := service.NewUniversalService(c)
	// if err != nil {
	// 	c.JSON(200, gin.H{
	// 		"errno":     "-1",
	// 		"errmsg":    err.Error(),
	// 		"requestid": c.GetLogID(),
	// 	})
	// }
	// 调用服务获取数据
	// result, err := hisService.GetData(c)

	// if err != nil {
	// 	c.JSON(200, gin.H{
	// 		"errno":     "-1",
	// 		"errmsg":    err.Error(),
	// 		"requestid": c.GetLogID(),
	// 		"data":      []interface{}{},
	// 	})
	// 	return
	// }

	// 成功返回结果
	c.JSON(200, gin.H{
		"errno":     "0",
		"errmsg":    "",
		"requestid": c.GetLogID(),
		"data":      nil,
	})
}
