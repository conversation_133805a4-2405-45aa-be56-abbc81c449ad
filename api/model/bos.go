package model

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/url"
	"strconv"
	"strings"
	"sync"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/baidubce/bce-sdk-go/services/sts"
)

// STSInfo 包含STS临时凭证所需的信息
type STSInfo struct {
	AccessKeyID     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	PreFixPath      string `json:"preFixPath"` // 对象前缀路径，例如 "yourPathPrefix/encryptedCuid/"
	BucketName      string `json:"bucketName"` // Bucket 名称
	Endpoint        string `json:"endpoint"`   // BOS Endpoint
}

// 注意：AK/SK 硬编码在此仅为示例，生产环境中应使用更安全的配置管理方式（如环境变量、配置文件、密钥管理服务等）
const (
	AdminAK  = "ALTAKyg7EYfa3qN5p3phAi5Mxi"
	AdminSK  = "97e7dfe169444e7a8392ba3e03a4c6b7"
	Endpoint = "http://bj.bcebos.com"
)

var (
	globalSTSClient *sts.Client
	globalBosClient *bos.Client
	clientOnce      sync.Once
	clientMux       sync.Mutex
	initClientErr   error
)

func init() {
	initializeGlobalSTSClient()
	initializeGlobalBosClient()
}

func initializeGlobalSTSClient() {
	clientOnce.Do(func() {
		globalSTSClient, initClientErr = sts.NewClient(AdminAK, AdminSK)
		if initClientErr != nil {
			// 考虑记录错误，例如使用 log 包
			fmt.Printf("Error initializing global STS client in init: %v\n", initClientErr)
		}
	})
}

func initializeGlobalBosClient() {
	clientOnce.Do(func() {
		globalBosClient, initClientErr = bos.NewClient(AdminAK, AdminSK, Endpoint)
		if initClientErr != nil {
			fmt.Printf("Error initializing global BOS client in init: %v\n", initClientErr)
		}
	})
}

// getSTSClient 获取全局 STS 客户端，如果尚未初始化或初始化失败，则尝试初始化。
func getSTSClient() (*sts.Client, error) {
	clientMux.Lock()
	defer clientMux.Unlock()

	if globalSTSClient == nil {
		// 如果 init() 中的初始化失败了，或者由于某种原因 clientOnce 没有执行/重置了
		// 这里尝试再次初始化
		// 理想情况下，init() 应该能处理好，但这是一种备用方案
		var err error
		globalSTSClient, err = sts.NewClient(AdminAK, AdminSK)
		if err != nil {
			return nil, fmt.Errorf("failed to create STS client on demand: %w", err)
		}
		initClientErr = nil // 清除之前的初始化错误（如果有）
	} else if initClientErr != nil {
		// 如果 init() 失败了，但 globalSTSClient 不是 nil (不太可能，但为了健壮性)
		// 或者我们想在每次获取失败的客户端时都尝试重新创建
		// 也可以选择直接返回 initClientErr
		var err error
		globalSTSClient, err = sts.NewClient(AdminAK, AdminSK)
		if err != nil {
			return nil, fmt.Errorf("failed to re-create STS client after initial error: %w ", err)
		}
		initClientErr = nil
	}
	return globalSTSClient, nil
}

func getBosClient() (*bos.Client, error) {
	clientMux.Lock()
	defer clientMux.Unlock()

	if globalBosClient == nil {
		var err error
		globalBosClient, err = bos.NewClient(AdminAK, AdminSK, Endpoint)
		if err != nil {
			return nil, fmt.Errorf("failed to create BOS client on demand: %w", err)
		}
		initClientErr = nil
	}
	return globalBosClient, nil
}

// BosModel 定义 BOS 操作的接口
//go:generate mockgen -source=bos.go -destination=bos_mock.go -package=model
// 方便 mock 和依赖注入

type BosModel interface {
	GetSTSInfo(expirationInSeconds int, acl string) (*STSInfo, error)
	GetAuthURL(expirationInSeconds int, acl string, bucketName string, objectName []string) ([]string, error)
	GetObjectInfo(bucketName string, objectName string) (ObjectInfo, error)
	GetImageInfo(bucketName string, objectName string) (ImageInfo, error)
}

type bosModelImpl struct{}

// 全局实例，业务代码只依赖接口
var GlobalBosModel BosModel = &bosModelImpl{}

func (b *bosModelImpl) GetSTSInfo(expirationInSeconds int, acl string) (*STSInfo, error) {
	client, err := getSTSClient()
	if err != nil {
		return nil, fmt.Errorf("failed to get STS client: %w", err)
	}
	if client == nil {
		return nil, fmt.Errorf("STS client is nil after getSTSClient call")
	}
	bceResponse, err := client.GetSessionToken(expirationInSeconds, acl)
	if err != nil {
		return nil, fmt.Errorf("failed to get session token from BCE SDK: %w", err)
	}
	if bceResponse == nil {
		return nil, fmt.Errorf("received nil response from BCE SDK")
	}
	return &STSInfo{
		AccessKeyID:     bceResponse.AccessKeyId,
		SecretAccessKey: bceResponse.SecretAccessKey,
		SessionToken:    bceResponse.SessionToken,
	}, nil
}

func (b *bosModelImpl) GetAuthURL(expirationInSeconds int, acl string, bucketName string, objectName []string) ([]string, error) {
	bosClient, err := bos.NewClient(AdminAK, AdminSK, "")
	if err != nil {
		return nil, fmt.Errorf("failed to create bos client: %w", err)
	}
	urls := make([]string, 0, len(objectName))
	for _, object := range objectName {
		u := bosClient.BasicGeneratePresignedUrl(bucketName, object, expirationInSeconds)
		urls = append(urls, url.QueryEscape(u))
	}
	return urls, nil
}

func (b *bosModelImpl) GetObjectInfo(bucketName string, objectName string) (info ObjectInfo, err error) {
	client, err := getBosClient()
	if err != nil {
		return info, fmt.Errorf("failed to get BOS client: %w", err)
	}
	meta, err := api.GetObjectMeta(client, bucketName, objectName, client.BosContext)
	if err != nil || meta == nil {
		return info, fmt.Errorf("failed to get object meta info: %w", err)
	}
	if meta.ContentLength == 0 {
		return info, fmt.Errorf("object size is 0")
	}
	return ObjectInfo{
		Size: meta.ContentLength,
	}, nil
}

func (b *bosModelImpl) GetImageInfo(bucketName string, objectName string) (info ImageInfo, err error) {
	client, err := getBosClient()
	if err != nil {
		return info, fmt.Errorf("failed to get BOS client: %w", err)
	}
	data, err := client.GetObject(bucketName, objectName, map[string]string{
		"x-bce-process": "image/info",
	})
	if err != nil {
		return info, fmt.Errorf("failed to get image info: %w", err)
	}
	if data == nil {
		return info, fmt.Errorf("failed to get image info: data is nil")
	}
	stream := data.Body
	defer stream.Close()
	body, err := ioutil.ReadAll(stream)
	if err != nil {
		return info, fmt.Errorf("failed to read stream: %w", err)
	}
	err = json.Unmarshal(body, &info)
	if err != nil {
		return info, fmt.Errorf("failed to unmarshal image info: %w", err)
	}
	info.Size, _ = strconv.ParseInt(info.ImageSize.Value, 10, 64)
	info.Width, _ = strconv.ParseInt(info.ImageWidth.Value, 10, 64)
	info.Height, _ = strconv.ParseInt(info.ImageHeight.Value, 10, 64)
	if parts := strings.Split(info.ImageMimeType.Value, "/"); len(parts) > 1 {
		info.MimeType = strings.ToUpper(parts[1])
	} else {
		info.MimeType = ""
	}
	return info, nil
}

type ObjectInfo struct {
	Size int64
}

type ImageValueWrapper struct {
	Value string `json:"value"`
}

type ImageInfo struct {
	ImageSize     ImageValueWrapper `json:"imageSize"`
	ImageWidth    ImageValueWrapper `json:"imageWidth"`
	ImageHeight   ImageValueWrapper `json:"imageHeight"`
	ImageMimeType ImageValueWrapper `json:"mimeType"`
	Size          int64             `json:"-"` // 从 ObjectMeta 获取，不从 JSON 解析
	Width         int64             `json:"-"` // 解析后转换存储
	Height        int64             `json:"-"` // 解析后转换存储
	MimeType      string            `json:"-"` // 解析后转换存储
}
