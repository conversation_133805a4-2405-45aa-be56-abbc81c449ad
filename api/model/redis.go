package model

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

// ======================== 全局默认实例 ========================
var DefaultRedisModel = NewDefaultRedisModel()

// RedisModel 定义 Redis 操作的接口
type RedisModel interface {
	SetHash(ctx context.Context, key, field string, value interface{}, expiration time.Duration) error
	SetHashMap(ctx context.Context, key string, values map[string]interface{}, expiration time.Duration) error
	GetHash(ctx context.Context, key, field string) (string, error)
	GetHashMap(ctx context.Context, key string) (map[string]string, error)
	MultiGet(ctx context.Context, keys []string) ([]interface{}, error)
}

// ======================== 默认实现 struct ========================
type redisModelImpl struct{}

func NewDefaultRedisModel() RedisModel {
	return &redisModelImpl{}
}

func (m *redisModelImpl) SetHash(ctx context.Context, key, field string, value interface{}, expiration time.Duration) error {
	err := common.RedisClient.HSet(ctx, key, field, value).Err()
	if err != nil {
		return err
	}
	if expiration > 0 {
		return common.RedisClient.Expire(ctx, key, expiration).Err()
	}
	return nil
}

func (m *redisModelImpl) SetHashMap(ctx context.Context, key string, values map[string]interface{}, expiration time.Duration) error {
	if expiration == 0 {
		return common.RedisClient.HMSet(ctx, key, values).Err()
	}
	return common.RedisClient.TxPipelined(ctx, func(p redis.Pipeliner) error {
		p.HMSet(ctx, key, values)
		p.Expire(ctx, key, expiration)
		_, err := p.Exec(ctx)
		return err
	})
}

func (m *redisModelImpl) GetHash(ctx context.Context, key, field string) (string, error) {
	return common.RedisClient.HGet(ctx, key, field).Result()
}

func (m *redisModelImpl) GetHashMap(ctx context.Context, key string) (map[string]string, error) {
	return common.RedisClient.HGetAll(ctx, key).Result()
}

func (m *redisModelImpl) MultiGet(ctx context.Context, keys []string) ([]interface{}, error) {
	return common.RedisClient.MGet(ctx, keys...).Result()
}
