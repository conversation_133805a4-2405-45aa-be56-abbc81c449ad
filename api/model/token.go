package model

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/binary"
	"fmt"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/crypt"
	baidubase64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

const (
	BaseTokenEncryptKey    = "nt1B7DQqBpTirBcsVmbHTNgPPaFjnidC8CKzUq47QUY="
	SessionTokenEncryptKey = "a7G+1/X4HvEYqmuaAnLuEnLmehKTbAD4EseRAebvTjU="
	BaseTokenIV            = "8+L/f0Bm41c6VKgSm3CwxQ=="
	SessionTokenIV         = "sGWaSIUJ7H6E2rXOA7BvmQ=="
)

type TokenModel interface {
	GenerateBaseToken(cuid string) string
	ValidateBaseToken(baseToken string, expectedCuid string) (timestamp int64, isValid bool)
	GenerateSessionToken(logID string) string
	ValidateSessionToken(sessionToken string) bool
	GenerateClientToken(baseToken string, cuid string) string
	ValidateClientToken(clientToken string) (cuid string, baseTokenTimestamp int64, diff time.Duration, ok bool)
}

type TokenDefaultModel struct {
	ctx           *gdp.WebContext
	requestParams *entity.RequestParams
}

func NewTokenDefaultModel(ctx *gdp.WebContext, requestParams *entity.RequestParams) TokenModel {
	if ctx == nil || requestParams == nil {
		return (*TokenDefaultModel)(nil)
	}
	return &TokenDefaultModel{ctx: ctx, requestParams: requestParams}
}

func (m *TokenDefaultModel) getBaseCryptor() *crypt.AESCryptor {
	key, _ := base64.StdEncoding.DecodeString(BaseTokenEncryptKey)
	iv, _ := base64.StdEncoding.DecodeString(BaseTokenIV)
	return crypt.NewCryptor(key, iv)
}

// 生成随机数
func generateRandom() uint64 {
	b := make([]byte, 8)
	_, _ = rand.Read(b)
	return binary.BigEndian.Uint64(b)
}

func (m *TokenDefaultModel) GenerateBaseToken(cuid string) string {
	parts := strings.Split(cuid, "|")
	if len(parts) == 2 {
		cuid = parts[0]
	}
	// 使用竖线分割字段，格式：cuid|timestamp|random
	tokenStr := fmt.Sprintf("%s|%d|%d", cuid, time.Now().Unix(), generateRandom())

	// 先用 AES 加密
	cryptor := m.getBaseCryptor()
	encrypted, err := cryptor.CBCEncrypt([]byte(tokenStr))
	if err != nil {
		return ""
	}

	// 再用 baidubase64 加密
	return baidubase64.Encode([]byte(encrypted), 0)
}

// ValidateBaseToken 校验基础token的有效性。
// 它会检查token结构、CUID是否与expectedCuid匹配以及token是否在24小时有效期内。
// 返回解析出的token内部时间戳以及一个布尔值表示token是否有效。
func (m *TokenDefaultModel) ValidateBaseToken(baseToken string, expectedCuid string) (timestamp int64, isValid bool) {
	// 先用 baidubase64 解密
	decoded, err := baidubase64.Deocde(baseToken, 0)
	if err != nil {
		return 0, false // 解码失败
	}

	// 再用 AES 解密
	cryptor := m.getBaseCryptor()
	decrypted, err := cryptor.CBCDecryptor(string(decoded))
	if err != nil {
		return 0, false // AES解密失败
	}

	// 解析字段
	fields := strings.Split(string(decrypted), "|")
	if len(fields) != 3 {
		return 0, false // 格式不正确
	}

	// 尝试解析时间戳
	ts, err := strconv.ParseInt(fields[1], 10, 64)
	if err != nil {
		return 0, false // 时间戳格式无效
	}

	// 验证 CUID
	if fields[0] != expectedCuid {
		return ts, false // CUID 不匹配，但时间戳已解析
	}

	// 验证时间戳（token 有效期 24 小时）
	if time.Since(time.Unix(ts, 0)) > 24*time.Hour {
		return ts, false // Token 过期，但时间戳已解析
	}

	// 所有检查通过
	return ts, true
}

func (m *TokenDefaultModel) GenerateSessionToken(logID string) string {
	token := baidubase64.Encode([]byte(logID), 0)
	return token
}

func (m *TokenDefaultModel) ValidateSessionToken(sessionToken string) bool {
	decoded, _ := baidubase64.Deocde(sessionToken, 0)
	return len(decoded) != 0
}

// ValidateClientToken 校验客户端token，并返回CUID、base_token的Unix时间戳、client_token与base_token时间戳的差值以及校验结果。
// 校验逻辑:
// 1. clientToken本身结构正确。
// 2. clientToken 内含的 base_token 有效（通过 ValidateBaseToken 校验，包括CUID匹配和base_token未过期）。
// 3. clientToken 的时间戳不早于 base_token 的时间戳。
// 如果上述任一条件不满足，ok 为 false。
// baseTokenTimestamp 会在 base_token 解析出时间戳时被填充，否则为0。
func (m *TokenDefaultModel) ValidateClientToken(clientToken string) (cuid string, baseTokenTimestamp int64, diff time.Duration, ok bool) {
	// 1. 使用 baidubase64 解码 clientToken
	decodedBytes, err := baidubase64.Deocde(clientToken, 0)
	if err != nil {
		return "", 0, 0, false // 解码错误
	}
	if len(decodedBytes) == 0 {
		return "", 0, 0, false // 解码后内容为空
	}

	decodedStr := string(decodedBytes)

	// 2. 分割解码后的字符串，格式：{base_token}|{timeStamp}|{CUID}
	parts := strings.Split(decodedStr, "|")
	if len(parts) != 3 && len(parts) != 4 {
		return "", 0, 0, false // clientToken 格式不正确
	}

	baseTokenFromClient := parts[0]
	timestampStrFromClient := parts[1]
	deviceIDFromClientToken := parts[2]

	// 3. 验证 base_token 部分，并获取其时间戳
	// ValidateBaseToken 会验证 baseTokenFromClient 的有效性 (CUID匹配, 未过期等)
	baseTokenTsUnix, valid := m.ValidateBaseToken(baseTokenFromClient, deviceIDFromClientToken)

	// 如果初次验证失败，且是 h0 分支，就用 HUID 再验一次
	if !valid && m.requestParams != nil && m.requestParams.RealOSBranch == "h0" && m.requestParams.HUID != "" {
		parts := strings.Split(m.requestParams.UID, "|")
		deviceIDFromClientParam := parts[0]
		if deviceIDFromClientParam == deviceIDFromClientToken {
			parts := strings.Split(m.requestParams.HUID, "|")
			deviceIDFromHUID := parts[0]
			baseTokenTsUnix, valid = m.ValidateBaseToken(baseTokenFromClient, deviceIDFromHUID)
		}
	}
	if !valid {
		return deviceIDFromClientToken, baseTokenTsUnix, 0, false
	}

	// 4. 解析 clientToken 中的时间戳
	clientTsUnix, err := strconv.ParseInt(timestampStrFromClient, 10, 64)
	if err != nil {
		// clientToken 的时间戳格式无效
		return deviceIDFromClientToken, baseTokenTsUnix, 0, false
	}

	// 5. 计算时间对象和差值
	clientTime := time.Unix(clientTsUnix/1000, (clientTsUnix%1000)*1000000)
	// 如果 clientTime 距今超过 35min，则校验失败
	if time.Since(clientTime) > 35*time.Minute {
		return deviceIDFromClientToken, baseTokenTsUnix, 0, false
	}
	baseTime := time.Unix(baseTokenTsUnix, 0) // baseTokenTsUnix 在 isBaseTokenValid 为 true 时是有效的

	duration := clientTime.Sub(baseTime)

	// 6. 检查差值是否为负, 如果差值小于-10秒，则校验失败
	if duration.Seconds() < -10 {
		// client_token 的时间戳早于 base_token 的时间戳，校验失败
		return deviceIDFromClientToken, baseTokenTsUnix, duration, false
	}

	// 所有检查通过
	return deviceIDFromClientToken, baseTokenTsUnix, duration, true
}

func (m *TokenDefaultModel) GenerateClientToken(baseToken string, cuid string) string {
	// 构造 token 字符串，格式：{base_token}|{timeStamp}|{CUID}
	parts := strings.Split(cuid, "|")
	if len(parts) == 2 {
		cuid = parts[0]
	}
	timestamp := time.Now().Unix() * 1000
	tokenStr := fmt.Sprintf("%s|%d|%s", baseToken, timestamp, cuid)

	// 使用 baidubase64 编码
	return baidubase64.Encode([]byte(tokenStr), 0)
}
