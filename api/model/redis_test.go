package model

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

// MockRedisModel 实现了 RedisModel 接口，用于测试
type MockRedisModel struct {
	mock.Mock
}

// NewMockRedisModel 创建一个新的 MockRedisModel 实例
func NewMockRedisModel() *MockRedisModel {
	return &MockRedisModel{}
}

func (m *MockRedisModel) SetHash(ctx context.Context, key, field string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, field, value, expiration)
	return args.Error(0)
}

func (m *MockRedisModel) SetHashMap(ctx context.Context, key string, values map[string]interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, values, expiration)
	return args.Error(0)
}

func (m *MockRedisModel) GetHash(ctx context.Context, key, field string) (string, error) {
	args := m.Called(ctx, key, field)
	return args.String(0), args.Error(1)
}

func (m *MockRedisModel) GetHashMap(ctx context.Context, key string) (map[string]string, error) {
	args := m.Called(ctx, key)
	return args.Get(0).(map[string]string), args.Error(1)
}

func (m *MockRedisModel) MultiGet(ctx context.Context, keys []string) ([]interface{}, error) {
	args := m.Called(ctx, keys)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]interface{}), args.Error(1)
}

// SetupMockRedisModelSuccess 设置一个"成功"场景的 MockRedisModel
func SetupMockRedisModelSuccess(methods ...string) *MockRedisModel {
	mockModel := NewMockRedisModel()
	for _, m := range methods {
		switch m {
		case "SetHash":
			mockModel.On("SetHash",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
				mock.Anything,
				mock.AnythingOfType("time.Duration"),
			).Return(nil)
		case "SetHashMap":
			mockModel.On("SetHashMap",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("map[string]interface {}"),
				mock.AnythingOfType("time.Duration"),
			).Return(nil)
		case "GetHash":
			mockModel.On("GetHash",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return("test-value", nil)
		case "GetHashMap":
			hashMapResult := map[string]string{
				"field1": "value1",
				"field2": "value2",
				"field3": "value3",
			}
			mockModel.On("GetHashMap",
				mock.Anything,
				mock.AnythingOfType("string"),
			).Return(hashMapResult, nil)
		case "MultiGet":
			multiGetResult := []interface{}{
				"value1",
				"value2",
				"value3",
			}
			mockModel.On("MultiGet",
				mock.Anything,
				mock.AnythingOfType("[]string"),
			).Return(multiGetResult, nil)
		}
	}
	return mockModel
}

// SetupMockRedisModelFailed 设置一个"失败"场景的 MockRedisModel
func SetupMockRedisModelFailed() *MockRedisModel {
	mockModel := NewMockRedisModel()

	// 设置失败场景
	mockModel.On("SetHash",
		mock.Anything,
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
		mock.Anything,
		mock.AnythingOfType("time.Duration"),
	).Return(assert.AnError)
	mockModel.On("SetHashMap",
		mock.Anything,
		mock.AnythingOfType("string"),
		mock.AnythingOfType("map[string]interface {}"),
		mock.AnythingOfType("time.Duration"),
	).Return(assert.AnError)
	mockModel.On("GetHash",
		mock.Anything,
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
	).Return("", assert.AnError)
	mockModel.On("GetHashMap",
		mock.Anything,
		mock.AnythingOfType("string"),
	).Return(map[string]string{}, assert.AnError)
	mockModel.On("MultiGet",
		mock.Anything,
		mock.AnythingOfType("[]string"),
	).Return(nil, assert.AnError)

	return mockModel
}

// SetupMockRedisModelCustom 创建一个可以自定义行为的 MockRedisModel
func SetupMockRedisModelCustom() *MockRedisModel {
	return NewMockRedisModel()
}

// 使用示例：在测试中使用 MockRedisModel
func TestMockRedisModel_Usage(t *testing.T) {
	// ========== 示例1：SetHash 成功场景 ==========
	t.Run("SetHash成功场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelSuccess("SetHash")
		ctx := context.Background()
		err := mockModel.SetHash(ctx, "test-key", "test-field", "test-value", time.Hour)
		assert.NoError(t, err)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例2：SetHashMap 成功场景 ==========
	t.Run("SetHashMap成功场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelSuccess("SetHashMap")
		ctx := context.Background()
		values := map[string]interface{}{
			"field1": "value1",
			"field2": 123,
			"field3": true,
		}
		err := mockModel.SetHashMap(ctx, "test-key", values, time.Hour)
		assert.NoError(t, err)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例3：GetHash 成功场景 ==========
	t.Run("GetHash成功场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelSuccess("GetHash")
		ctx := context.Background()
		result, err := mockModel.GetHash(ctx, "test-key", "test-field")
		assert.NoError(t, err)
		assert.Equal(t, "test-value", result)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例4：GetHashMap 成功场景 ==========
	t.Run("GetHashMap成功场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelSuccess("GetHashMap")
		ctx := context.Background()
		result, err := mockModel.GetHashMap(ctx, "test-key")
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 3)
		assert.Equal(t, "value1", result["field1"])
		assert.Equal(t, "value2", result["field2"])
		assert.Equal(t, "value3", result["field3"])
		mockModel.AssertExpectations(t)
	})

	// ========== 示例5：失败场景 ==========
	t.Run("失败场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelFailed()

		ctx := context.Background()

		// 测试 SetHash 失败
		setHashErr := mockModel.SetHash(ctx, "test-key", "test-field", "test-value", time.Hour)
		assert.Error(t, setHashErr)

		// 测试 SetHashMap 失败
		values := map[string]interface{}{"field": "value"}
		setHashMapErr := mockModel.SetHashMap(ctx, "test-key", values, time.Hour)
		assert.Error(t, setHashMapErr)

		// 测试 GetHash 失败
		getHashResult, getHashErr := mockModel.GetHash(ctx, "test-key", "test-field")
		assert.Error(t, getHashErr)
		assert.Empty(t, getHashResult)

		// 测试 GetHashMap 失败
		getHashMapResult, getHashMapErr := mockModel.GetHashMap(ctx, "test-key")
		assert.Error(t, getHashMapErr)
		assert.Empty(t, getHashMapResult)

		// 测试 MultiGet 失败
		multiGetResult, multiGetErr := mockModel.MultiGet(ctx, []string{"key1", "key2"})
		assert.Error(t, multiGetErr)
		assert.Nil(t, multiGetResult)

		mockModel.AssertExpectations(t)
	})

	// ========== 示例6：自定义场景 ==========
	t.Run("自定义场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelCustom()

		ctx := context.Background()

		// 自定义 GetHash 返回特定值
		mockModel.On("GetHash", ctx, "custom-key", "custom-field").Return("custom-value", nil)

		result, err := mockModel.GetHash(ctx, "custom-key", "custom-field")

		assert.NoError(t, err)
		assert.Equal(t, "custom-value", result)

		mockModel.AssertExpectations(t)
	})

	// ========== 示例7：带过期时间的场景 ==========
	t.Run("带过期时间的场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelCustom()

		ctx := context.Background()

		// 设置特定过期时间的 SetHash
		mockModel.On("SetHash", ctx, "expiring-key", "expiring-field", "expiring-value", 30*time.Minute).Return(nil)

		err := mockModel.SetHash(ctx, "expiring-key", "expiring-field", "expiring-value", 30*time.Minute)

		assert.NoError(t, err)

		mockModel.AssertExpectations(t)
	})

	// ========== 示例8：无过期时间的场景 ==========
	t.Run("无过期时间的场景", func(t *testing.T) {
		mockModel := SetupMockRedisModelCustom()

		ctx := context.Background()

		// 设置无过期时间的 SetHashMap
		values := map[string]interface{}{
			"permanent_field": "permanent_value",
		}
		mockModel.On("SetHashMap", ctx, "permanent-key", values, time.Duration(0)).Return(nil)

		err := mockModel.SetHashMap(ctx, "permanent-key", values, 0)

		assert.NoError(t, err)

		mockModel.AssertExpectations(t)
	})
}

func dumpMiniRedis(t *testing.T, mr *redis.Testing) {
	mini := mr.MiniRedis()
	if mini == nil {
		t.Log("[miniredis] nil instance")
		return
	}
	keys := mini.Keys()
	t.Logf("[miniredis] keys: %+v", keys)
	for _, k := range keys {
		typeStr := mini.Type(k)
		t.Logf("[miniredis] key=%s, type=%s", k, typeStr)
	}
	val := mini.Dump()
	t.Logf("[miniredis] dump: %+v", val)
}

func TestSetHashMapWithExpireTime_MiniRedis(t *testing.T) {
	testEnv := &redis.Testing{}
	testEnv.Start()
	defer testEnv.Stop()

	oldClient := common.RedisClient
	common.RedisClient = testEnv.Client()
	defer func() { common.RedisClient = oldClient }()

	ctx := context.Background()

	t.Run("SetHash & GetHash 正常", func(t *testing.T) {
		err := DefaultRedisModel.SetHash(ctx, "hkey", "f1", "v1", 0)
		dumpMiniRedis(t, testEnv)
		assert.NoError(t, err)
		val, err := DefaultRedisModel.GetHash(ctx, "hkey", "f1")
		dumpMiniRedis(t, testEnv)
		assert.NoError(t, err)
		assert.Equal(t, "v1", val)
	})

	t.Run("GetHash 不存在字段", func(t *testing.T) {
		dumpMiniRedis(t, testEnv)
		_, err := DefaultRedisModel.GetHash(ctx, "hkey", "notfound")
		assert.Error(t, err)
	})

	t.Run("GetHashMap 不存在key", func(t *testing.T) {
		dumpMiniRedis(t, testEnv)
		vals, err := DefaultRedisModel.GetHashMap(ctx, "notfoundkey")
		assert.NoError(t, err)
		assert.Equal(t, 0, len(vals))
	})

}
