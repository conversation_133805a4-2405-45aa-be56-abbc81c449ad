package model

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/gdp/gdp/ral"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

type RiskModelResponseCode int

const (
	// RiskModelResponseCodeSuccess 风控通过
	RiskModelResponseCodeSuccess RiskModelResponseCode = 0
	// RiskModelResponseCodeFailed 风控失败
	RiskModelResponseCodeFailed RiskModelResponseCode = -1
	// RiskModelResponseCodeSizeTooLarge 文件大小超过限制
	RiskModelResponseCodeSizeTooLarge RiskModelResponseCode = 300
	// RiskModelResponseCodeFileNameRisk 文件名风险
	RiskModelResponseCodeFileNameRisk RiskModelResponseCode = 401
	// RiskModelResponseCodeContentRisk 文件内容风险
	RiskModelResponseCodeContentRisk RiskModelResponseCode = 402
)

type RiskModel interface {
	GetRiskResult(request *RiskRequest) (*RiskResponse, error)
}

type DefaultRiskModel struct {
	ctx *gdp.WebContext
}

func NewDefaultRiskModel(ctx *gdp.WebContext) RiskModel {
	return &DefaultRiskModel{ctx: ctx}
}

type RiskResponseItem struct {
	ID        int                   `json:"id"`
	Code      RiskModelResponseCode `json:"code"`
	Message   string                `json:"msg"`
	WordCount int                   `json:"word_count,omitempty"`
}
type RiskResponseData struct {
	Files []RiskResponseItem `json:"files"`
}

type RiskResponse struct {
	Code    int                         `json:"code"`
	Message string                      `json:"msg"`
	Data    RiskResponseData            `json:"data"`
	DataMap map[string]RiskResponseItem `json:"-"`
}

// BoxDataRalResponse 表示空框热词RAL响应
type RiskRalResponse struct {
	Head ral.HTTPHead `ral:"head"`
	Body RiskResponse `ral:"body"`
}

type RiskRequest struct {
	File []RiskRequestFile `json:"file"`
}

type RiskRequestFile struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	URL         string `json:"url"`
	Type        string `json:"type"`
	Size        int64  `json:"size"`
	MimeTypes   string `json:"mime_types"`
	ImageWidth  int64  `json:"width"`
	ImageHeight int64  `json:"height"`
}

// GetRiskResult 获取风控结果
func (m *DefaultRiskModel) GetRiskResult(request *RiskRequest) (*RiskResponse, error) {

	ralReq := ral.HTTPRequest{
		Method:    "POST",
		Path:      "/his-ai/validate",
		Body:      request,
		Converter: ral.JSONConverter,
		LogID:     m.ctx.GetLogID(),
		Ctx:       m.ctx,
	}

	var resp RiskRalResponse
	common.DumpData(m.ctx, "2app480", ralReq)
	err := ral.Ral(App480ServiceName, ralReq, &resp, ral.JSONConverter)
	if err != nil {
		return nil, err
	}
	resp.Body.DataMap = make(map[string]RiskResponseItem)
	for _, item := range resp.Body.Data.Files {
		resp.Body.DataMap[fmt.Sprintf("%d", item.ID)] = item
	}
	return &resp.Body, nil
}
