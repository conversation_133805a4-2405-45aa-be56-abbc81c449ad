package model

import (
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	baidubase64 "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

// 创建测试用的 TokenModel
func createTestTokenModel() TokenModel {
	ctx := &gdp.WebContext{}
	requestParams := &entity.RequestParams{
		UID:          "test_cuid",
		HUID:         "test_huid",
		RealOSBranch: "",
	}
	return NewTokenDefaultModel(ctx, requestParams)
}

func TestNewTokenDefaultModel(t *testing.T) {
	t.Run("Normal creation", func(t *testing.T) {
		ctx := &gdp.WebContext{}
		requestParams := &entity.RequestParams{}
		model := NewTokenDefaultModel(ctx, requestParams)
		assert.NotNil(t, model)
		assert.IsType(t, &TokenDefaultModel{}, model)
	})

	t.Run("Nil context", func(t *testing.T) {
		requestParams := &entity.RequestParams{}
		model := NewTokenDefaultModel(nil, requestParams)
		assert.Nil(t, model)
	})

	t.Run("Nil requestParams", func(t *testing.T) {
		ctx := &gdp.WebContext{}
		model := NewTokenDefaultModel(ctx, nil)
		assert.Nil(t, model)
	})

	t.Run("Both nil", func(t *testing.T) {
		model := NewTokenDefaultModel(nil, nil)
		assert.Nil(t, model)
	})
}

func TestGenerateBaseToken(t *testing.T) {
	model := createTestTokenModel()

	t.Run("Normal CUID", func(t *testing.T) {
		cuid := "test_cuid_123"
		token := model.GenerateBaseToken(cuid)
		assert.NotEmpty(t, token)

		// 验证生成的 token
		timestamp, isValid := model.ValidateBaseToken(token, cuid)
		assert.True(t, isValid)
		assert.Greater(t, timestamp, int64(0))
		assert.WithinDuration(t, time.Now(), time.Unix(timestamp, 0), 2*time.Second)
	})

	t.Run("CUID with pipe", func(t *testing.T) {
		cuid := "test_cuid|extra_part"
		expectedCuid := "test_cuid"
		token := model.GenerateBaseToken(cuid)
		assert.NotEmpty(t, token)

		// 验证时应该只使用第一部分
		timestamp, isValid := model.ValidateBaseToken(token, expectedCuid)
		assert.True(t, isValid)
		assert.Greater(t, timestamp, int64(0))
	})

	t.Run("Empty CUID", func(t *testing.T) {
		token := model.GenerateBaseToken("")
		assert.NotEmpty(t, token)

		// 验证空 CUID
		timestamp, isValid := model.ValidateBaseToken(token, "")
		assert.True(t, isValid)
		assert.Greater(t, timestamp, int64(0))
	})

	t.Run("Multiple tokens should be different", func(t *testing.T) {
		cuid := "test_cuid"
		token1 := model.GenerateBaseToken(cuid)
		time.Sleep(10 * time.Millisecond)
		token2 := model.GenerateBaseToken(cuid)

		assert.NotEqual(t, token1, token2)
	})
}

func TestValidateBaseToken(t *testing.T) {
	model := createTestTokenModel()

	t.Run("Valid token", func(t *testing.T) {
		cuid := "test_cuid"
		token := model.GenerateBaseToken(cuid)

		timestamp, isValid := model.ValidateBaseToken(token, cuid)
		assert.True(t, isValid)
		assert.Greater(t, timestamp, int64(0))
	})

	t.Run("Invalid base64", func(t *testing.T) {
		timestamp, isValid := model.ValidateBaseToken("invalid_base64!@#", "test_cuid")
		assert.False(t, isValid)
		assert.Equal(t, int64(0), timestamp)
	})

	t.Run("Wrong CUID", func(t *testing.T) {
		cuid := "test_cuid"
		token := model.GenerateBaseToken(cuid)

		timestamp, isValid := model.ValidateBaseToken(token, "wrong_cuid")
		assert.False(t, isValid)
		assert.Greater(t, timestamp, int64(0)) // 时间戳仍然被解析
	})

	t.Run("Expired token", func(t *testing.T) {
		// 需要模拟一个过期的 token
		// 由于我们无法直接修改 token 中的时间戳，这里使用一个技巧
		cryptor := (&TokenDefaultModel{}).getBaseCryptor()
		oldTimestamp := time.Now().Add(-25 * time.Hour).Unix()
		tokenStr := "test_cuid|" + strconv.FormatInt(oldTimestamp, 10) + "|12345678"
		encrypted, _ := cryptor.CBCEncrypt([]byte(tokenStr))
		expiredToken := baidubase64.Encode([]byte(encrypted), 0)

		timestamp, isValid := model.ValidateBaseToken(expiredToken, "test_cuid")
		assert.False(t, isValid)
		assert.Equal(t, oldTimestamp, timestamp)
	})

	t.Run("Malformed token - wrong field count", func(t *testing.T) {
		cryptor := (&TokenDefaultModel{}).getBaseCryptor()
		tokenStr := "only_one_field"
		encrypted, _ := cryptor.CBCEncrypt([]byte(tokenStr))
		malformedToken := baidubase64.Encode([]byte(encrypted), 0)

		timestamp, isValid := model.ValidateBaseToken(malformedToken, "test_cuid")
		assert.False(t, isValid)
		assert.Equal(t, int64(0), timestamp)
	})

	t.Run("Invalid timestamp format", func(t *testing.T) {
		cryptor := (&TokenDefaultModel{}).getBaseCryptor()
		tokenStr := "test_cuid|not_a_number|12345678"
		encrypted, _ := cryptor.CBCEncrypt([]byte(tokenStr))
		invalidTsToken := baidubase64.Encode([]byte(encrypted), 0)

		timestamp, isValid := model.ValidateBaseToken(invalidTsToken, "test_cuid")
		assert.False(t, isValid)
		assert.Equal(t, int64(0), timestamp)
	})
}

func TestGenerateSessionToken(t *testing.T) {
	model := createTestTokenModel()

	t.Run("Normal logID", func(t *testing.T) {
		logID := "test_log_id_123456"
		token := model.GenerateSessionToken(logID)
		assert.NotEmpty(t, token)

		// 验证可以解码
		decoded, err := baidubase64.Deocde(token, 0)
		assert.NoError(t, err)
		assert.Equal(t, logID, string(decoded))
	})

	t.Run("Empty logID", func(t *testing.T) {
		token := model.GenerateSessionToken("")
		assert.NotEmpty(t, token)

		decoded, _ := baidubase64.Deocde(token, 0)
		assert.Equal(t, "", string(decoded))
	})

	t.Run("Special characters", func(t *testing.T) {
		logID := "test|log:id@123#456"
		token := model.GenerateSessionToken(logID)
		assert.NotEmpty(t, token)

		decoded, _ := baidubase64.Deocde(token, 0)
		assert.Equal(t, logID, string(decoded))
	})
}

func TestValidateSessionToken(t *testing.T) {
	model := createTestTokenModel()

	t.Run("Valid token", func(t *testing.T) {
		logID := "test_log_id"
		token := model.GenerateSessionToken(logID)
		isValid := model.ValidateSessionToken(token)
		assert.True(t, isValid)
	})

	t.Run("Invalid token", func(t *testing.T) {
		isValid := model.ValidateSessionToken("invalid_base64!@#")
		assert.False(t, isValid)
	})

	t.Run("Empty token", func(t *testing.T) {
		isValid := model.ValidateSessionToken("")
		assert.False(t, isValid)
	})

	t.Run("Valid empty content token", func(t *testing.T) {
		token := baidubase64.Encode([]byte(""), 0)
		isValid := model.ValidateSessionToken(token)
		assert.False(t, isValid)
	})
}

func TestGenerateClientToken(t *testing.T) {
	model := createTestTokenModel()

	t.Run("Normal case", func(t *testing.T) {
		cuid := "test_cuid"
		baseToken := model.GenerateBaseToken(cuid)
		clientToken := model.GenerateClientToken(baseToken, cuid)

		assert.NotEmpty(t, clientToken)

		// 解码验证格式
		decoded, err := baidubase64.Deocde(clientToken, 0)
		require.NoError(t, err)

		parts := strings.Split(string(decoded), "|")
		assert.Len(t, parts, 3)
		assert.Equal(t, baseToken, parts[0])
		assert.Equal(t, cuid, parts[2])
	})

	t.Run("CUID with pipe", func(t *testing.T) {
		cuid := "test_cuid|extra"
		expectedCuid := "test_cuid"
		baseToken := model.GenerateBaseToken(cuid)
		clientToken := model.GenerateClientToken(baseToken, cuid)

		decoded, _ := baidubase64.Deocde(clientToken, 0)
		parts := strings.Split(string(decoded), "|")
		assert.Equal(t, expectedCuid, parts[2])
	})
}

func TestValidateClientToken(t *testing.T) {
	model := createTestTokenModel()

	t.Run("Valid client token", func(t *testing.T) {
		cuid := "test_cuid"
		baseToken := model.GenerateBaseToken(cuid)
		clientToken := model.GenerateClientToken(baseToken, cuid)

		parsedCuid, baseTs, diff, ok := model.ValidateClientToken(clientToken)
		assert.True(t, ok)
		assert.Equal(t, cuid, parsedCuid)
		assert.Greater(t, baseTs, int64(0))
		assert.GreaterOrEqual(t, diff.Seconds(), float64(0))
		assert.Less(t, diff.Seconds(), float64(2)) // 应该在2秒内
	})

	t.Run("Invalid base64", func(t *testing.T) {
		cuid, baseTs, diff, ok := model.ValidateClientToken("invalid_base64!@#")
		assert.False(t, ok)
		assert.Empty(t, cuid)
		assert.Equal(t, int64(0), baseTs)
		assert.Equal(t, time.Duration(0), diff)
	})

	t.Run("Wrong format - 2 parts", func(t *testing.T) {
		token := baidubase64.Encode([]byte("part1|part2"), 0)
		cuid, baseTs, diff, ok := model.ValidateClientToken(token)
		assert.False(t, ok)
		assert.Empty(t, cuid)
		assert.Equal(t, int64(0), baseTs)
		assert.Equal(t, time.Duration(0), diff)
	})

	t.Run("Invalid base token", func(t *testing.T) {
		invalidBaseToken := "invalid_base_token"
		timestamp := time.Now().Unix() * 1000
		tokenStr := invalidBaseToken + "|" + strconv.FormatInt(timestamp, 10) + "|test_cuid"
		clientToken := baidubase64.Encode([]byte(tokenStr), 0)

		cuid, baseTs, diff, ok := model.ValidateClientToken(clientToken)
		assert.False(t, ok)
		assert.Equal(t, "test_cuid", cuid)
		assert.Equal(t, int64(0), baseTs)
		assert.Equal(t, time.Duration(0), diff)
	})

	t.Run("Client token timestamp too old", func(t *testing.T) {
		cuid := "test_cuid"
		baseToken := model.GenerateBaseToken(cuid)

		// 创建一个时间戳超过35分钟的 client token
		oldTimestamp := (time.Now().Add(-36 * time.Minute).Unix()) * 1000
		tokenStr := baseToken + "|" + strconv.FormatInt(oldTimestamp, 10) + "|" + cuid
		clientToken := baidubase64.Encode([]byte(tokenStr), 0)

		parsedCuid, baseTs, diff, ok := model.ValidateClientToken(clientToken)
		assert.False(t, ok)
		assert.Equal(t, cuid, parsedCuid)
		assert.Greater(t, baseTs, int64(0))
		assert.Equal(t, time.Duration(0), diff)
	})

	t.Run("Client token timestamp earlier than base token", func(t *testing.T) {
		cuid := "test_cuid"
		baseToken := model.GenerateBaseToken(cuid)

		// 创建一个时间戳早于 base token 11秒的 client token
		earlierTimestamp := (time.Now().Add(-11 * time.Second).Unix()) * 1000
		tokenStr := baseToken + "|" + strconv.FormatInt(earlierTimestamp, 10) + "|" + cuid
		clientToken := baidubase64.Encode([]byte(tokenStr), 0)
		parsedCuid, baseTs, diff, ok := model.ValidateClientToken(clientToken)
		assert.False(t, ok)
		assert.Equal(t, cuid, parsedCuid)
		assert.Greater(t, baseTs, int64(0))
		assert.Less(t, diff.Seconds(), float64(-10))
	})
	t.Run("H0 branch fallback", func(t *testing.T) {
		// 1) 构造一个带 h0 分支的 model
		ctx := &gdp.WebContext{}
		requestParams := &entity.RequestParams{
			UID:          "test_cuid", // client token 里会带的 CUID
			HUID:         "test_huid", // 回退用的 HUID
			RealOSBranch: "h0",        // 触发回退逻辑
		}
		model := NewTokenDefaultModel(ctx, requestParams)
		require.NotNil(t, model)

		// 2) 生成一个 baseToken，此时内部是用 HUID 生成的
		baseToken := model.GenerateBaseToken("test_huid")
		assert.NotEmpty(t, baseToken)

		// 3) 用 client 的 CUID 组装 clientToken，使 ValidateBaseToken(baseToken, clientCuid) 失败，
		//    再用 HUID 回退成功
		nowMs := time.Now().Unix() * 1000
		tokenStr := fmt.Sprintf("%s|%d|%s", baseToken, nowMs, "test_cuid")
		clientToken := baidubase64.Encode([]byte(tokenStr), 0)

		// 4) 调用 ValidateClientToken
		parsedCuid, baseTs, diff, ok := model.ValidateClientToken(clientToken)

		// 5) 断言：回退后整体校验通过
		assert.True(t, ok, "期待通过 h0 回退逻辑校验")
		assert.Equal(t, "test_cuid", parsedCuid, "应解析出 client 端 CUID")
		assert.Greater(t, baseTs, int64(0), "baseToken 时间戳应大于 0")
		assert.GreaterOrEqual(t, diff.Seconds(), float64(0), "clientTime>=baseTime")
		assert.Less(t, diff.Seconds(), float64(2), "时间差应该在合理范围内")
	})
}
