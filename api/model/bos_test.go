package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockBosModel 实现了 BosModel 接口，用于测试
type MockBosModel struct {
	mock.Mock
}

func (m *MockBosModel) GetSTSInfo(expirationInSeconds int, acl string) (*STSInfo, error) {
	args := m.Called(expirationInSeconds, acl)
	return args.Get(0).(*STSInfo), args.Error(1)
}

func (m *MockBosModel) GetAuthURL(expirationInSeconds int, acl string, bucketName string, objectName []string) ([]string, error) {
	args := m.Called(expirationInSeconds, acl, bucketName, objectName)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockBosModel) GetObjectInfo(bucketName string, objectName string) (ObjectInfo, error) {
	args := m.Called(bucketName, objectName)
	return args.Get(0).(ObjectInfo), args.Error(1)
}

func (m *MockBosModel) GetImageInfo(bucketName string, objectName string) (ImageInfo, error) {
	args := m.Called(bucketName, objectName)
	return args.Get(0).(ImageInfo), args.Error(1)
}

func (m *MockBosModel) GetBosPath(bucketName string, objectName string) string {
	args := m.Called(bucketName, objectName)
	return args.String(0)
}

// NewMockBosModel 创建一个新的 MockBosModel 实例
func NewMockBosModel() *MockBosModel {
	return &MockBosModel{}
}

// SetupMockBosModelSuccess 设置一个"成功"场景的 MockBosModel
func SetupMockBosModelSuccess(methods ...string) *MockBosModel {
	mockModel := NewMockBosModel()
	for _, m := range methods {
		switch m {
		case "GetSTSInfo":
			stsInfo := &STSInfo{
				AccessKeyID:     "test-access-key-id",
				SecretAccessKey: "test-secret-access-key",
				SessionToken:    "test-session-token",
				PreFixPath:      "test-prefix/",
				BucketName:      "test-bucket",
				Endpoint:        "http://bj.bcebos.com",
			}
			mockModel.On("GetSTSInfo", mock.AnythingOfType("int"), mock.AnythingOfType("string")).Return(stsInfo, nil)
		case "GetAuthURL":
			authURLs := []string{
				"http://example.com/auth-url-1",
				"http://example.com/auth-url-2",
			}
			mockModel.On("GetAuthURL",
				mock.AnythingOfType("int"),
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
				mock.AnythingOfType("[]string"),
			).Return(authURLs, nil)
		case "GetObjectInfo":
			objectInfo := ObjectInfo{Size: 1024}
			mockModel.On("GetObjectInfo",
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return(objectInfo, nil)
		case "GetImageInfo":
			imageInfo := ImageInfo{
				ImageSize:     ImageValueWrapper{Value: "1024"},
				ImageWidth:    ImageValueWrapper{Value: "800"},
				ImageHeight:   ImageValueWrapper{Value: "600"},
				ImageMimeType: ImageValueWrapper{Value: "image/jpeg"},
				Size:          1024,
				Width:         800,
				Height:        600,
				MimeType:      "image/jpeg",
			}
			mockModel.On("GetImageInfo",
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return(imageInfo, nil)
		case "GetBosPath":
			mockModel.On("GetBosPath",
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return("http://bj.bcebos.com/test-bucket/test-object")
		}
	}
	return mockModel
}

// SetupMockBosModelFailed 设置一个"失败"场景的 MockBosModel
func SetupMockBosModelFailed(methods ...string) *MockBosModel {
	mockModel := NewMockBosModel()
	for _, m := range methods {
		switch m {
		case "GetSTSInfo":
			mockModel.On("GetSTSInfo",
				mock.AnythingOfType("int"),
				mock.AnythingOfType("string"),
			).Return((*STSInfo)(nil), assert.AnError)
		case "GetAuthURL":
			mockModel.On("GetAuthURL",
				mock.AnythingOfType("int"),
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
				mock.AnythingOfType("[]string"),
			).Return(([]string)(nil), assert.AnError)
		case "GetObjectInfo":
			mockModel.On("GetObjectInfo",
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return(ObjectInfo{}, assert.AnError)
		case "GetImageInfo":
			mockModel.On("GetImageInfo",
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return(ImageInfo{}, assert.AnError)
		case "GetBosPath":
			mockModel.On("GetBosPath",
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return("")
		}
	}
	return mockModel
}

// SetupMockBosModelCustom 创建一个可以自定义行为的 MockBosModel
func SetupMockBosModelCustom() *MockBosModel {
	return NewMockBosModel()
}

// 使用示例：在测试中使用 MockBosModel
func TestMockBosModel_Usage(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("STS信息获取成功", func(t *testing.T) {
		mockModel := SetupMockBosModelSuccess("GetSTSInfo")
		result, err := mockModel.GetSTSInfo(3600, "read")
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-access-key-id", result.AccessKeyID)
		assert.Equal(t, "test-secret-access-key", result.SecretAccessKey)
		assert.Equal(t, "test-session-token", result.SessionToken)
		assert.Equal(t, "test-prefix/", result.PreFixPath)
		assert.Equal(t, "test-bucket", result.BucketName)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例2：授权URL获取成功 ==========
	t.Run("授权URL获取成功", func(t *testing.T) {
		mockModel := SetupMockBosModelSuccess("GetAuthURL")
		objectNames := []string{"object1.txt", "object2.txt"}
		result, err := mockModel.GetAuthURL(3600, "read", "test-bucket", objectNames)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)
		assert.Equal(t, "http://example.com/auth-url-1", result[0])
		assert.Equal(t, "http://example.com/auth-url-2", result[1])
		mockModel.AssertExpectations(t)
	})

	// ========== 示例3：对象信息获取成功 ==========
	t.Run("对象信息获取成功", func(t *testing.T) {
		mockModel := SetupMockBosModelSuccess("GetObjectInfo")
		result, err := mockModel.GetObjectInfo("test-bucket", "test-object.txt")
		assert.NoError(t, err)
		assert.Equal(t, int64(1024), result.Size)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例4：图片信息获取成功 ==========
	t.Run("图片信息获取成功", func(t *testing.T) {
		mockModel := SetupMockBosModelSuccess("GetImageInfo")
		result, err := mockModel.GetImageInfo("test-bucket", "test-image.jpg")
		assert.NoError(t, err)
		assert.Equal(t, int64(1024), result.Size)
		assert.Equal(t, int64(800), result.Width)
		assert.Equal(t, int64(600), result.Height)
		assert.Equal(t, "image/jpeg", result.MimeType)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例5：失败场景 ==========
	t.Run("失败场景", func(t *testing.T) {
		mockModel := SetupMockBosModelFailed("GetSTSInfo", "GetAuthURL", "GetObjectInfo", "GetImageInfo", "GetBosPath")
		// 测试 STS 信息获取失败
		stsResult, stsErr := mockModel.GetSTSInfo(3600, "read")
		assert.Error(t, stsErr)
		assert.Nil(t, stsResult)
		// 测试授权 URL 获取失败
		urlResult, urlErr := mockModel.GetAuthURL(3600, "read", "test-bucket", []string{"test.txt"})
		assert.Error(t, urlErr)
		assert.Nil(t, urlResult)
		// 测试对象信息获取失败
		objResult, objErr := mockModel.GetObjectInfo("test-bucket", "test-object.txt")
		assert.Error(t, objErr)
		assert.Equal(t, ObjectInfo{}, objResult)
		// 测试图片信息获取失败
		imgResult, imgErr := mockModel.GetImageInfo("test-bucket", "test-image.jpg")
		assert.Error(t, imgErr)
		assert.Equal(t, ImageInfo{}, imgResult)
		// 测试BOS路径获取失败
		path := mockModel.GetBosPath("test-bucket", "test-object.txt")
		assert.Empty(t, path)
		mockModel.AssertExpectations(t)
	})

	// ========== 示例6：自定义场景 ==========
	t.Run("自定义场景", func(t *testing.T) {
		mockModel := SetupMockBosModelCustom()

		// 自定义 STS 信息
		customSTS := &STSInfo{
			AccessKeyID:     "custom-access-key",
			SecretAccessKey: "custom-secret-key",
			SessionToken:    "custom-session-token",
			PreFixPath:      "custom-prefix/",
			BucketName:      "custom-bucket",
			Endpoint:        "http://custom.endpoint.com",
		}

		mockModel.On("GetSTSInfo", 7200, "write").Return(customSTS, nil)

		result, err := mockModel.GetSTSInfo(7200, "write")

		assert.NoError(t, err)
		assert.Equal(t, customSTS, result)
		assert.Equal(t, "custom-access-key", result.AccessKeyID)
		assert.Equal(t, "custom-prefix/", result.PreFixPath)

		mockModel.AssertExpectations(t)
	})
}

/*
// 在实际的测试文件中的使用示例

func TestBosService_GetSTSInfo(t *testing.T) {
	// 创建 MockBosModel
	mockModel := model.SetupMockBosModelSuccess()

	// 创建你的 service，注入 mock
	service := NewBosService(mockModel)

	// 执行 service
	result, err := service.GetSTSInfo(3600, "read")

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证 BosModel 的方法被正确调用
	mockModel.AssertExpectations(t)
}

// 测试获取授权URL的场景
func TestBosService_GetAuthURL(t *testing.T) {
	// 创建 MockBosModel
	mockModel := model.SetupMockBosModelSuccess()

	service := NewBosService(mockModel)

	objectNames := []string{"file1.txt", "file2.jpg"}
	result, err := service.GetAuthURL(3600, "read", "my-bucket", objectNames)

	// 验证结果
	assert.NoError(t, err)
	assert.NotEmpty(t, result)

	mockModel.AssertExpectations(t)
}
*/
