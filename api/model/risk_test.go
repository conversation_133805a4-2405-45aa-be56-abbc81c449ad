package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRiskModel 实现了 RiskModel 接口，用于测试
type MockRiskModel struct {
	mock.Mock
}

func (m *MockRiskModel) GetRiskResult(request *RiskRequest) (*RiskResponse, error) {
	args := m.Called(request)
	return args.Get(0).(*RiskResponse), args.Error(1)
}

// NewMockRiskModel 创建一个新的 MockRiskModel 实例
func NewMockRiskModel() *MockRiskModel {
	return &MockRiskModel{}
}

// SetupMockRiskModelSuccess 设置一个"成功"场景的 MockRiskModel
func SetupMockRiskModelSuccess() *MockRiskModel {
	mockModel := NewMockRiskModel()

	// 设置成功返回的数据
	successResponse := &RiskResponse{
		Code:    0,
		Message: "风控检查通过",
		Data: RiskResponseData{
			Files: []RiskResponseItem{
				{
					ID:        1,
					Code:      RiskModelResponseCodeSuccess,
					Message:   "文件检查通过",
					WordCount: 100,
				},
				{
					ID:        2,
					Code:      RiskModelResponseCodeSuccess,
					Message:   "文件检查通过",
					WordCount: 200,
				},
			},
		},
		DataMap: map[string]RiskResponseItem{
			"1": {
				ID:        1,
				Code:      RiskModelResponseCodeSuccess,
				Message:   "文件检查通过",
				WordCount: 100,
			},
			"2": {
				ID:        2,
				Code:      RiskModelResponseCodeSuccess,
				Message:   "文件检查通过",
				WordCount: 200,
			},
		},
	}

	mockModel.On("GetRiskResult", mock.AnythingOfType("*model.RiskRequest")).Return(successResponse, nil)

	return mockModel
}

// SetupMockRiskModelFailed 设置一个"失败"场景的 MockRiskModel
func SetupMockRiskModelFailed() *MockRiskModel {
	mockModel := NewMockRiskModel()

	// 设置失败场景
	mockModel.On("GetRiskResult", mock.AnythingOfType("*model.RiskRequest")).Return((*RiskResponse)(nil), assert.AnError)

	return mockModel
}

// SetupMockRiskModelRisky 设置一个"包含风险文件"场景的 MockRiskModel
func SetupMockRiskModelRisky() *MockRiskModel {
	mockModel := NewMockRiskModel()

	// 设置包含风险文件的数据
	riskyResponse := &RiskResponse{
		Code:    0,
		Message: "检查完成，存在风险文件",
		Data: RiskResponseData{
			Files: []RiskResponseItem{
				{
					ID:        1,
					Code:      RiskModelResponseCodeSuccess,
					Message:   "文件检查通过",
					WordCount: 100,
				},
				{
					ID:        2,
					Code:      RiskModelResponseCodeFileNameRisk,
					Message:   "文件名包含敏感词",
					WordCount: 0,
				},
				{
					ID:        3,
					Code:      RiskModelResponseCodeContentRisk,
					Message:   "文件内容存在风险",
					WordCount: 50,
				},
			},
		},
		DataMap: map[string]RiskResponseItem{
			"1": {
				ID:        1,
				Code:      RiskModelResponseCodeSuccess,
				Message:   "文件检查通过",
				WordCount: 100,
			},
			"2": {
				ID:        2,
				Code:      RiskModelResponseCodeFileNameRisk,
				Message:   "文件名包含敏感词",
				WordCount: 0,
			},
			"3": {
				ID:        3,
				Code:      RiskModelResponseCodeContentRisk,
				Message:   "文件内容存在风险",
				WordCount: 50,
			},
		},
	}

	mockModel.On("GetRiskResult", mock.AnythingOfType("*model.RiskRequest")).Return(riskyResponse, nil)

	return mockModel
}

// SetupMockRiskModelCustom 创建一个可以自定义行为的 MockRiskModel
func SetupMockRiskModelCustom() *MockRiskModel {
	return NewMockRiskModel()
}

// 使用示例：在测试中使用 MockRiskModel
func TestMockRiskModel_Usage(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("成功场景", func(t *testing.T) {
		mockModel := SetupMockRiskModelSuccess()

		// 准备测试请求
		request := &RiskRequest{
			File: []RiskRequestFile{
				{
					ID:          "1",
					Name:        "test.txt",
					URL:         "http://example.com/test.txt",
					Type:        "text",
					Size:        1024,
					MimeTypes:   "text/plain",
					ImageWidth:  0,
					ImageHeight: 0,
				},
			},
		}

		result, err := mockModel.GetRiskResult(request)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, 0, result.Code)
		assert.Len(t, result.Data.Files, 2)
		assert.Equal(t, RiskModelResponseCodeSuccess, result.Data.Files[0].Code)
		assert.Equal(t, "文件检查通过", result.Data.Files[0].Message)

		mockModel.AssertExpectations(t)
	})

	// ========== 示例2：失败场景 ==========
	t.Run("失败场景", func(t *testing.T) {
		mockModel := SetupMockRiskModelFailed()

		request := &RiskRequest{
			File: []RiskRequestFile{
				{
					ID:   "1",
					Name: "test.txt",
				},
			},
		}

		result, err := mockModel.GetRiskResult(request)

		assert.Error(t, err)
		assert.Nil(t, result)

		mockModel.AssertExpectations(t)
	})

	// ========== 示例3：包含风险文件场景 ==========
	t.Run("包含风险文件场景", func(t *testing.T) {
		mockModel := SetupMockRiskModelRisky()

		request := &RiskRequest{
			File: []RiskRequestFile{
				{ID: "1", Name: "normal.txt"},
				{ID: "2", Name: "sensitive.txt"},
				{ID: "3", Name: "content.txt"},
			},
		}

		result, err := mockModel.GetRiskResult(request)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, 0, result.Code)
		assert.Len(t, result.Data.Files, 3)

		// 验证风险文件
		assert.Equal(t, RiskModelResponseCodeFileNameRisk, result.Data.Files[1].Code)
		assert.Equal(t, "文件名包含敏感词", result.Data.Files[1].Message)
		assert.Equal(t, RiskModelResponseCodeContentRisk, result.Data.Files[2].Code)
		assert.Equal(t, "文件内容存在风险", result.Data.Files[2].Message)

		mockModel.AssertExpectations(t)
	})

	// ========== 示例4：自定义场景 ==========
	t.Run("自定义场景", func(t *testing.T) {
		mockModel := SetupMockRiskModelCustom()

		// 自定义返回数据
		customResponse := &RiskResponse{
			Code:    200,
			Message: "自定义风控结果",
			Data: RiskResponseData{
				Files: []RiskResponseItem{
					{
						ID:        999,
						Code:      RiskModelResponseCodeSizeTooLarge,
						Message:   "文件大小超过限制",
						WordCount: 0,
					},
				},
			},
			DataMap: map[string]RiskResponseItem{
				"999": {
					ID:        999,
					Code:      RiskModelResponseCodeSizeTooLarge,
					Message:   "文件大小超过限制",
					WordCount: 0,
				},
			},
		}

		request := &RiskRequest{
			File: []RiskRequestFile{
				{ID: "999", Name: "large.txt", Size: 1024 * 1024 * 100}, // 100MB
			},
		}

		mockModel.On("GetRiskResult", request).Return(customResponse, nil)

		result, err := mockModel.GetRiskResult(request)

		assert.NoError(t, err)
		assert.Equal(t, customResponse, result)
		assert.Equal(t, 200, result.Code)
		assert.Equal(t, RiskModelResponseCodeSizeTooLarge, result.Data.Files[0].Code)

		mockModel.AssertExpectations(t)
	})
}

/*
// 在实际的测试文件中的使用示例

func TestRiskService_CheckRisk(t *testing.T) {
	// 创建 MockRiskModel
	mockModel := model.SetupMockRiskModelSuccess()

	// 创建你的 service，注入 mock
	service := NewRiskService(mockModel)

	// 准备测试数据
	request := &model.RiskRequest{
		File: []model.RiskRequestFile{
			{
				ID:   "1",
				Name: "test.txt",
				URL:  "http://example.com/test.txt",
			},
		},
	}

	// 执行 service
	result, err := service.CheckRisk(request)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证 RiskModel 的方法被正确调用
	mockModel.AssertExpectations(t)
}

// 测试风险检查失败的场景
func TestRiskService_CheckRisk_Failed(t *testing.T) {
	// 创建失败场景的 MockRiskModel
	mockModel := model.SetupMockRiskModelFailed()

	service := NewRiskService(mockModel)
	request := &model.RiskRequest{
		File: []model.RiskRequestFile{{ID: "1", Name: "test.txt"}},
	}

	// 测试失败处理
	result, err := service.CheckRisk(request)

	// 验证错误处理
	assert.Error(t, err)
	assert.Nil(t, result)

	mockModel.AssertExpectations(t)
}
*/
