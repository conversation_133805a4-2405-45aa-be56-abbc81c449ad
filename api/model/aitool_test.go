package model

import (
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
)

// 配置测试上下文
func configureTestContext(ctx *gdp.WebContext, realOsBranch, osBranch, bdVersion string, panelVer map[string]int, isIncognito bool) {
	// 设置请求参数
	reqParams := &entity.RequestParams{
		RealOSBranch: realOsBranch,
		OSBranch:     osBranch,
	}
	ctx.Set(entity.ReqParamsKey, reqParams)

	// 设置适配参数
	adaptParams := &entity.AdaptionParams{
		BDVersion: bdVersion,
	}
	ctx.Set(entity.AdapParamsKey, adaptParams)

	// 设置表单数据
	formData := &entity.FormData{
		AiToolPanelVersion: panelVer,
	}
	ctx.Set(entity.FormDataKey, formData)

	// 设置隐身模式
	if isIncognito {
		ctx.Request.Header.Set("incognito-mode", "1")
	}
}

// 设置mock数据用于测试
func setupMockData(mockManager *background.MockAiToolManager) {
	// 工具项定义
	toolV0 := entity.AiToolItem{
		Text:     "testToolV0",
		Type:     "testTool",
		PanelVer: 1,
	}

	toolV1 := entity.AiToolItem{
		Text:     "testToolV1",
		Type:     "testTool",
		PanelVer: 2,
	}

	toolInc := entity.AiToolItem{
		Text:     "testToolInc",
		Type:     "testTool",
		PanelVer: 2,
	}

	// 添加工具项
	mockManager.AddToolItem("testToolV0", toolV0)
	mockManager.AddToolItem("testToolV1", toolV1)
	mockManager.AddToolItem("testToolInc", toolInc)

	// 添加默认版本
	mockManager.AddToolVersion("default", entity.ToolVersion{
		AppVer:         "1.0",
		Tools:          []string{"testToolV0"},
		IncognitoTools: []string{"testToolV0"},
	})

	// 添加iOS版本 - 两个不同的版本
	mockManager.AddToolVersion("i0", entity.ToolVersion{
		AppVer:         "15.0",
		Tools:          []string{"testToolV1"},
		IncognitoTools: []string{"testToolInc"},
	})

	// 添加Android版本
	mockManager.AddToolVersion("a0", entity.ToolVersion{
		AppVer:         "15.0",
		Tools:          []string{"testToolV0"},
		IncognitoTools: []string{"testToolV0"},
	})
}

// 测试DefaultAiToolModel
func TestDefaultAiToolModel(t *testing.T) {
	// 先设置mock管理器
	mockManager := background.SetupMockAiToolManagerForTest()
	defer func() {
		background.AiToolManager = nil
	}()

	// 构造测试数据
	setupMockData(mockManager)

	tests := []struct {
		name         string
		osBranch     string
		realOsBranch string
		bdVersion    string
		panelVer     map[string]int
		isIncognito  bool
		expectedText string
		expectedNull bool
	}{
		{
			name:         "iOS正常模式,版本匹配",
			osBranch:     "i0",
			realOsBranch: "i0",
			bdVersion:    "15.5",
			panelVer:     map[string]int{"testTool": 2},
			isIncognito:  false,
			expectedText: "testToolV1",
			expectedNull: true, // panel版本匹配应该为nil
		},
		{
			name:         "iOS正常模式,版本不匹配",
			osBranch:     "i0",
			realOsBranch: "i0",
			bdVersion:    "15.5",
			panelVer:     map[string]int{"testTool": 1},
			isIncognito:  false,
			expectedText: "testToolV1",
			expectedNull: false,
		},
		{
			name:         "iOS隐身模式",
			osBranch:     "i0",
			realOsBranch: "i0",
			bdVersion:    "15.5",
			panelVer:     map[string]int{},
			isIncognito:  true,
			expectedText: "testToolInc",
			expectedNull: false,
		},
		{
			name:         "Android版本",
			osBranch:     "a0",
			realOsBranch: "a0",
			bdVersion:    "15.5",
			panelVer:     map[string]int{},
			isIncognito:  false,
			expectedText: "testToolV0",
			expectedNull: false,
		},
		{
			name:         "版本过低",
			osBranch:     "i0",
			realOsBranch: "i0",
			bdVersion:    "14.0",
			panelVer:     map[string]int{},
			isIncognito:  false,
			expectedText: "testToolV0", // 应该返回默认版本
			expectedNull: false,
		},
		{
			name:         "分支不存在",
			osBranch:     "nonexistent",
			realOsBranch: "nonexistent",
			bdVersion:    "15.5",
			panelVer:     map[string]int{},
			isIncognito:  false,
			expectedText: "testToolV0", // 应该返回默认版本
			expectedNull: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// 创建标准的 gin 上下文和 gdp 上下文
			w := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(w)
			ginCtx.Request = httptest.NewRequest("GET", "/", nil)
			ctx := gdp.NewWebContext(ginCtx)

			// 配置上下文
			configureTestContext(ctx, tc.realOsBranch, tc.osBranch, tc.bdVersion, tc.panelVer, tc.isIncognito)
			mockManager := background.SetupMockAiToolManagerForTest()
			setupMockData(mockManager)

			// 创建model
			model := NewDefaultAiToolModel(ctx)
			require.NotNil(t, model)

			// 获取AI工具配置
			tools := model.GetAiTool()
			require.NotNil(t, tools)

			// 验证结果

			if assert.NotEmpty(t, tools) {
				assert.Equal(t, tc.expectedText, tools[0].Text)
				if tc.expectedNull {
					assert.Nil(t, tools[0].Panel, "Panel应该为nil")
				}
			}
		})
	}

	// 测试参数缺失
	t.Run("参数缺失", func(t *testing.T) {
		// 创建一个新的上下文，但不设置必要的参数
		w := httptest.NewRecorder()
		ginCtx, _ := gin.CreateTestContext(w)
		ginCtx.Request = httptest.NewRequest("GET", "/", nil)
		ctx := gdp.NewWebContext(ginCtx)

		model := NewDefaultAiToolModel(ctx)
		assert.Nil(t, model)
	})
}

// TestGetPlusSignFunction 测试 GetPlusSignFunction 方法
func TestGetPlusSignFunction(t *testing.T) {
	tests := []struct {
		name           string
		setupContext   func() *gdp.WebContext
		expectedResult bool // true表示期望有结果，false表示期望nil
		expectedCount  int  // 期望的工具数量
		checkAiCall    bool // 是否检查AI通话工具
	}{
		{
			name: "正常情况-包含AI通话",
			setupContext: func() *gdp.WebContext {
				w := httptest.NewRecorder()
				ginCtx, _ := gin.CreateTestContext(w)
				ginCtx.Request = httptest.NewRequest("GET", "/", nil)
				ginCtx.Request.Header.Set("X-Log-Id", "test-logid-123")
				ctx := gdp.NewWebContext(ginCtx)

				// 设置必要的参数
				configureTestContext(ctx, "i0", "i0", "15.0", map[string]int{}, false)
				return ctx
			},
			expectedResult: true,
			expectedCount:  3, // image, file, aicall
			checkAiCall:    true,
		},
		{
			name: "model为nil",
			setupContext: func() *gdp.WebContext {
				return nil
			},
			expectedResult: false,
			expectedCount:  0,
			checkAiCall:    false,
		},
		{
			name: "model字段为nil",
			setupContext: func() *gdp.WebContext {
				w := httptest.NewRecorder()
				ginCtx, _ := gin.CreateTestContext(w)
				ginCtx.Request = httptest.NewRequest("GET", "/", nil)
				ctx := gdp.NewWebContext(ginCtx)
				// 不设置必要的参数，这样NewDefaultAiToolModel会返回nil
				return ctx
			},
			expectedResult: false,
			expectedCount:  0,
			checkAiCall:    false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctx := tc.setupContext()

			var model *DefaultAiToolModel
			if ctx != nil {
				model = NewDefaultAiToolModel(ctx)
			}

			// 调用GetPlusSignFunction
			var result interface{}
			if model != nil {
				result = model.GetPlusSignFunction()
			} else {
				// 测试nil model的情况
				var nilModel *DefaultAiToolModel
				result = nilModel.GetPlusSignFunction()
			}

			if !tc.expectedResult {
				assert.Nil(t, result, "期望结果为nil")
				return
			}

			// 验证结果不为nil
			require.NotNil(t, result, "期望结果不为nil")

			// 验证结果类型
			tools, ok := result.([]PlusSignToolItem)
			require.True(t, ok, "结果应该是[]PlusSignToolItem类型")

			// 验证工具数量
			assert.Equal(t, tc.expectedCount, len(tools), "工具数量不匹配")

			if len(tools) >= 2 {
				// 验证基础工具
				assert.Equal(t, "image", tools[0].ID, "第一个工具应该是image")
				assert.Equal(t, "上传图片", tools[0].Name, "第一个工具名称不正确")
				assert.Equal(t, 0, tools[0].Login, "image工具不需要登录")
				assert.NotEmpty(t, tools[0].Icon, "image工具应该有图标")

				assert.Equal(t, "file", tools[1].ID, "第二个工具应该是file")
				assert.Equal(t, "上传文件", tools[1].Name, "第二个工具名称不正确")
				assert.Equal(t, 0, tools[1].Login, "file工具不需要登录")
				assert.NotEmpty(t, tools[1].Icon, "file工具应该有图标")
			}

			// 检查AI通话工具
			if tc.checkAiCall && len(tools) >= 3 {
				aiCallTool := tools[2]
				assert.Equal(t, "aicall", aiCallTool.ID, "第三个工具应该是aicall")
				assert.Equal(t, "视频通话", aiCallTool.Name, "AI通话工具名称不正确")
				assert.Equal(t, 1, aiCallTool.Login, "AI通话工具需要登录")
				assert.NotEmpty(t, aiCallTool.Icon, "AI通话工具应该有图标")
				assert.NotEmpty(t, aiCallTool.Scheme, "AI通话工具应该有scheme")
				assert.True(t, strings.HasPrefix(aiCallTool.Scheme, "baiduboxapp://aiCall/open"), "AI通话scheme格式不正确")
			}
		})
	}
}

func TestGetAiToolsTypes(t *testing.T) {
	// 测试用例1：正常情况 - 包含多个工具
	t.Run("TestCase1_NormalCase_MultipleTools", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
			{
				Type: "aiQues",
				Text: "AI解题",
				Icon: "ques_icon.png",
			},
		}

		// 调用函数
		result := aiTools.GetAiToolsTypes()

		// 验证结果
		expected := []string{"aiWrite", "aiDraw", "aiQues"}
		assert.Equal(t, expected, result, "Should return all tool types in order")
		assert.Len(t, result, 3, "Should return 3 tool types")
	})

	// 测试用例2：空工具列表
	t.Run("TestCase2_EmptyToolsList", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{}

		// 调用函数
		result := aiTools.GetAiToolsTypes()

		// 验证结果
		assert.Empty(t, result, "Should return empty slice for empty tools list")
		assert.NotNil(t, result, "Should return non-nil slice")
		assert.Len(t, result, 0, "Should have length 0")
	})

	// 测试用例3：nil工具列表
	t.Run("TestCase3_NilToolsList", func(t *testing.T) {
		// 构造测试数据
		var aiTools AiTools

		// 调用函数
		result := aiTools.GetAiToolsTypes()

		// 验证结果
		assert.Empty(t, result, "Should return empty slice for nil tools list")
		assert.NotNil(t, result, "Should return non-nil slice")
		assert.Len(t, result, 0, "Should have length 0")
	})

	// 测试用例4：单个工具
	t.Run("TestCase4_SingleTool", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
		}

		// 调用函数
		result := aiTools.GetAiToolsTypes()

		// 验证结果
		expected := []string{"aiWrite"}
		assert.Equal(t, expected, result, "Should return single tool type")
		assert.Len(t, result, 1, "Should return 1 tool type")
	})

	// 测试用例5：包含重复类型的工具
	t.Run("TestCase5_DuplicateTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作V1",
				Icon: "write_icon_v1.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
			{
				Type: "aiWrite",
				Text: "AI写作V2",
				Icon: "write_icon_v2.png",
			},
		}

		// 调用函数
		result := aiTools.GetAiToolsTypes()

		// 验证结果
		expected := []string{"aiWrite", "aiDraw", "aiWrite"}
		assert.Equal(t, expected, result, "Should return all types including duplicates")
		assert.Len(t, result, 3, "Should return 3 tool types")
	})

	// 测试用例6：包含空类型的工具
	t.Run("TestCase6_EmptyTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
			{
				Type: "",
				Text: "空类型工具",
				Icon: "empty_icon.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
		}

		// 调用函数
		result := aiTools.GetAiToolsTypes()

		// 验证结果
		expected := []string{"aiWrite", "", "aiDraw"}
		assert.Equal(t, expected, result, "Should return all types including empty ones")
		assert.Len(t, result, 3, "Should return 3 tool types")
	})
}

func TestSortAiToolsByTypes(t *testing.T) {
	// 测试用例1：正常排序情况
	t.Run("TestCase1_NormalSorting", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
			{
				Type: "aiQues",
				Text: "AI解题",
				Icon: "ques_icon.png",
			},
			{
				Type: "aiTour",
				Text: "AI出游",
				Icon: "tour_icon.png",
			},
		}

		// 指定排序顺序
		types := []string{"aiQues", "aiTour", "aiWrite"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Len(t, result, 4, "Should return all tools")
		assert.Equal(t, "aiQues", result[0].Type, "First tool should be aiQues")
		assert.Equal(t, "aiTour", result[1].Type, "Second tool should be aiTour")
		assert.Equal(t, "aiWrite", result[2].Type, "Third tool should be aiWrite")
		assert.Equal(t, "aiDraw", result[3].Type, "Fourth tool should be aiDraw (not in types)")
	})

	// 测试用例2：空types数组
	t.Run("TestCase2_EmptyTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
		}

		// 空的排序顺序
		types := []string{}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果 - 应该返回原始顺序
		assert.Equal(t, aiTools, result, "Should return original order when types is empty")
	})

	// 测试用例3：空工具列表
	t.Run("TestCase3_EmptyTools", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{}

		// 指定排序顺序
		types := []string{"aiWrite", "aiDraw"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Empty(t, result, "Should return empty slice for empty tools")
		assert.Equal(t, aiTools, result, "Should return original empty slice")
	})

	// 测试用例4：types中包含不存在的工具类型
	t.Run("TestCase4_NonExistentTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
		}

		// 包含不存在类型的排序顺序
		types := []string{"aiQues", "aiWrite", "aiTour"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Len(t, result, 2, "Should return all existing tools")
		assert.Equal(t, "aiWrite", result[0].Type, "First tool should be aiWrite")
		assert.Equal(t, "aiDraw", result[1].Type, "Second tool should be aiDraw")
	})

	// 测试用例5：所有工具都在types中
	t.Run("TestCase5_AllToolsInTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
			{
				Type: "aiQues",
				Text: "AI解题",
				Icon: "ques_icon.png",
			},
		}

		// 包含所有工具类型的排序顺序（逆序）
		types := []string{"aiQues", "aiDraw", "aiWrite"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Len(t, result, 3, "Should return all tools")
		assert.Equal(t, "aiQues", result[0].Type, "First tool should be aiQues")
		assert.Equal(t, "aiDraw", result[1].Type, "Second tool should be aiDraw")
		assert.Equal(t, "aiWrite", result[2].Type, "Third tool should be aiWrite")
	})

	// 测试用例6：包含重复工具类型 - 每个类型只保留第一个
	t.Run("TestCase6_DuplicateToolTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作V1",
				Icon: "write_icon_v1.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
			{
				Type: "aiWrite",
				Text: "AI写作V2",
				Icon: "write_icon_v2.png",
			},
		}

		// 指定排序顺序
		types := []string{"aiWrite", "aiDraw"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果 - 每个类型只保留第一个匹配的工具
		assert.Len(t, result, 2, "Should return 2 tools (one per type)")
		assert.Equal(t, "aiWrite", result[0].Type, "First tool should be aiWrite")
		assert.Equal(t, "AI写作V1", result[0].Text, "First tool should be the first aiWrite")
		assert.Equal(t, "aiDraw", result[1].Type, "Second tool should be aiDraw")
	})

	// 测试用例7：nil工具列表
	t.Run("TestCase7_NilTools", func(t *testing.T) {
		// 构造测试数据
		var aiTools AiTools

		// 指定排序顺序
		types := []string{"aiWrite", "aiDraw"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Nil(t, result, "Should return nil for nil tools")
	})

	// 测试用例8：nil types
	t.Run("TestCase8_NilTypes", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作",
				Icon: "write_icon.png",
			},
		}

		// nil排序顺序
		var types []string

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Equal(t, aiTools, result, "Should return original order when types is nil")
	})

	// 测试用例9：重复类型但不在排序列表中的工具
	t.Run("TestCase9_DuplicateTypesNotInSortList", func(t *testing.T) {
		// 构造测试数据
		aiTools := AiTools{
			{
				Type: "aiWrite",
				Text: "AI写作V1",
				Icon: "write_icon_v1.png",
			},
			{
				Type: "aiDraw",
				Text: "AI画图",
				Icon: "draw_icon.png",
			},
			{
				Type: "aiWrite",
				Text: "AI写作V2",
				Icon: "write_icon_v2.png",
			},
			{
				Type: "aiQues",
				Text: "AI解题",
				Icon: "ques_icon.png",
			},
		}

		// 只指定部分类型的排序顺序
		types := []string{"aiDraw"}

		// 调用函数
		result := aiTools.SortAiToolsByTypes(types)

		// 验证结果
		assert.Len(t, result, 4, "Should return all tools")
		assert.Equal(t, "aiDraw", result[0].Type, "First tool should be aiDraw")
		// 剩余的工具应该按原始顺序排列，包括两个aiWrite
		assert.Equal(t, "aiWrite", result[1].Type, "Second tool should be first aiWrite")
		assert.Equal(t, "AI写作V1", result[1].Text, "Second tool should be AI写作V1")
		assert.Equal(t, "aiWrite", result[2].Type, "Third tool should be second aiWrite")
		assert.Equal(t, "AI写作V2", result[2].Text, "Third tool should be AI写作V2")
		assert.Equal(t, "aiQues", result[3].Type, "Fourth tool should be aiQues")
	})
}
