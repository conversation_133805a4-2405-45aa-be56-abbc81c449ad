// Package model 新接口 model 层
package model

import (
	"fmt"
	"net/url"
	"strconv"
	"time"

	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"

	"github.com/mcuadros/go-version"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/lib"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/background"
)

// AiToolConfig 和 entity 中保持一致，方便调用方更新
type AiToolConfig = entity.AiToolConfig
type ToolVersion = entity.ToolVersion
type AiToolItem = entity.AiToolItem

type AiTools []AiToolItem
type PlusSignToolItem struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Icon      string `json:"icon"`
	IconDark  string `json:"iconDark"`
	IconNight string `json:"iconNight"`
	Login     int    `json:"login"`
	Back      int    `json:"back"`
	Scheme    string `json:"scheme"`
}

// FileSearchToolItem 定义了单个图片/文件搜索工具项的结构
type FileSearchToolItem struct {
	FileType  entity.FileType `json:"fileType"`
	Schema    string          `koanf:"schema" json:"schema"`
	Icon      string          `koanf:"icon" json:"icon"`
	IconDark  string          `koanf:"iconDark" json:"iconDark"`
	IconNight string          `koanf:"iconNight" json:"iconNight"`
	Text      string          `koanf:"text" json:"text"`
	Type      string          `koanf:"type" json:"type"`
	Panel     interface{}     `koanf:"panel" json:"panel"`
	PanelVer  int             `koanf:"panelVer" json:"version"`
	Login     int             `koanf:"login" json:"login"`
	Back      int             `koanf:"back" json:"back"`
}

func GetCompatibleAiToolConfig() interface{} {
	return background.GetDefaultAiTools()
}

type DefaultAiToolModel struct {
	ctx            *gdp.WebContext
	RequestParams  *entity.RequestParams
	AdaptionParams *entity.AdaptionParams
	FormData       *entity.FormData
}

type AiToolService interface {
	GetAiTool(ctx *gdp.WebContext) []AiToolItem
	GetPlusSignFunction(ctx *gdp.WebContext) []PlusSignToolItem
	GetFileSearchTool(ctx *gdp.WebContext) []FileSearchToolItem
}

func NewDefaultAiToolModel(ctx *gdp.WebContext) *DefaultAiToolModel {
	RequestParams := entity.GetRequestParams(ctx)
	if RequestParams == nil {
		return nil
	}
	AdaptionParams := entity.GetAdaptionParams(ctx)
	if AdaptionParams == nil {
		return nil
	}
	FormData := entity.GetFormData(ctx)
	if FormData == nil {
		return nil
	}
	return &DefaultAiToolModel{
		ctx:            ctx,
		RequestParams:  RequestParams,
		AdaptionParams: AdaptionParams,
		FormData:       FormData,
	}
}

func (s *DefaultAiToolModel) GetAiTool() AiTools {
	if s == nil || s.RequestParams == nil || s.FormData == nil || s.AdaptionParams == nil {
		return nil
	}

	// 获取当前配置
	bgConfig := background.GetAiToolConfig()
	aiTool := []AiToolItem{}

	// 百度App 版本
	appVer := s.AdaptionParams.BDVersion
	// 当前发起请求的客户端具有的工具版本
	appToolVer := s.FormData.AiToolPanelVersion
	// ai 工具配置集
	tools := bgConfig.Tools
	// 当前版本手百（主板/极速版...）对应的配置集合
	toolVers := bgConfig.ToolVer[s.RequestParams.RealOSBranch]

	for i := len(toolVers) - 1; i >= 0; i-- {
		if version.Compare(toolVers[i].AppVer, appVer, "<=") {
			toolVer := toolVers[i].Tools
			if s.ctx.GetHeader("incognito-mode") == "1" {
				toolVer = toolVers[i].IncognitoTools
			}
			for _, toolVer := range toolVer {
				toolItem := tools[toolVer]

				if v, ok := appToolVer[toolItem.Type]; ok && v == toolItem.PanelVer {
					// 版本匹配不下发 panel 数据
					toolItem.Panel = nil
				}
				if toolItem.Type == "aiCall" {
					toolItem.Schema, _ = lib.GenerateCallPageSchema(
						lib.ProductParams{
							Logid:          s.ctx.GetLogID(),
							Sa:             AIToolAICallSA,
							EnterType:      AIToolAICallEnterType,
							ReRank:         "1", // AI助手外调起默认传"1"
							SessionID:      "",  // 会话id，AI助手外调起默认传""
							Type:           "1", // 传0表示打电话
							CallbackAction: "com.baidu.searchbox.sug.aicall.notifyEvent." + strconv.FormatInt(time.Now().Unix(), 10),
							CallPageFrom:   6,
						})
				}
				if toolItem.Type == "picArt" && common.GetSampleValue(s.ctx, "reverse_pic_art", "0") == "1" {
					continue
				}
				aiTool = append(aiTool, toolItem)
			}
			break
		}
	}
	if len(aiTool) == 0 {
		defaultVer, ok := bgConfig.ToolVer["default"]
		if ok && len(defaultVer) > 0 {
			for _, toolItem := range defaultVer[0].Tools {
				if tool, exists := tools[toolItem]; exists {
					aiTool = append(aiTool, tool)
				}
			}
		}
	}
	return aiTool
}

// GetPlusSignFunction 获取 PlusSignFunction 的响应 PM 说迭代频率不高，暂且使用静态配置
func (s *DefaultAiToolModel) GetPlusSignFunction() []PlusSignToolItem {
	if s == nil || s.ctx == nil || s.ctx.Request == nil {
		return nil
	}

	imageTool := PlusSignToolItem{
		ID:        "image",
		Name:      "上传图片",
		Icon:      "https://gips1.baidu.com/it/u=1110977681,3671711770&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
		IconDark:  "https://gips1.baidu.com/it/u=1186457530,3532027422&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
		IconNight: "https://gips2.baidu.com/it/u=616494268,2470475550&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
		Login:     0,
		Scheme:    "",
	}
	fileTool := PlusSignToolItem{
		ID:        "file",
		Name:      "上传文件",
		Icon:      "https://gips0.baidu.com/it/u=701053108,1027270607&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
		IconDark:  "https://gips1.baidu.com/it/u=2176904703,2605274376&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
		IconNight: "https://gips3.baidu.com/it/u=2300346369,2270105784&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
		Login:     0,
		Scheme:    "",
	}
	plusSignTool := []PlusSignToolItem{imageTool, fileTool}
	aiCallScheme, err := lib.GenerateCallPageSchema(
		lib.ProductParams{
			Logid:          s.ctx.GetLogID(),
			Sa:             PlusSignAICallSA,
			EnterType:      PlusSignAICallEnterType,
			ReRank:         "1", // AI助手外调起默认传"1"
			SessionID:      "",  // 会话id，AI助手外调起默认传""
			Type:           "1", // 传0表示打电话
			CallbackAction: "com.baidu.searchbox.sug.aicall.notifyEvent." + strconv.FormatInt(time.Now().Unix(), 10),
			CallPageFrom:   5,
		})
	if err == nil {
		aiCallTool := PlusSignToolItem{
			ID:        "aicall",
			Name:      "视频通话",
			Icon:      "https://gips2.baidu.com/it/u=952701137,2111489262&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
			IconDark:  "https://gips2.baidu.com/it/u=1476924199,3453484266&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
			IconNight: "https://gips0.baidu.com/it/u=3269690855,1077631341&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f48_48",
			Login:     1,
			Back:      1,
			Scheme:    aiCallScheme,
		}
		plusSignTool = append(plusSignTool, aiCallTool)
	}

	return plusSignTool

}

// GetFileSearchPicTool 获取文件搜索
func (s *DefaultAiToolModel) GetFileSearchTool(fileURL string, fileType entity.FileType) []FileSearchToolItem {
	var fileSearchTool = make([]FileSearchToolItem, 0)
	if fileType == entity.FileTypeIMG {

		var schemaList = make([]string, 5)
		var functionList = []string{"1", "8", "11", "5", "17"}

		for i := range schemaList {
			var h5URL = url.QueryEscape(fmt.Sprintf("https://m.baidu.com/sf/vsearch/image/front/edit?objUrl=%s&thumbUrl=%s&toolType=%s&pageFr=his_capsule", fileURL, fileURL, functionList[i]))
			var schema = fmt.Sprintf("baiduboxapp://v1/browser/open?url=%s&statusBarStyle=2&notShowLandingTopBar=1&landingSupporDarkmode=1&isContainer=1&bottomBarType=5", h5URL)
			schemaList[i] = schema
		}

		fileSearchTool = []FileSearchToolItem{
			FileSearchToolItem{
				FileType:  fileType,
				Schema:    schemaList[0],
				Icon:      "https://gips3.baidu.com/it/u=3959023387,52194414&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconDark:  "https://gips1.baidu.com/it/u=98019978,3017851020&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconNight: "https://gips2.baidu.com/it/u=1962211579,1769122873&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				Text:      "去水印",
				Type:      "remove",
				Panel:     nil,
				PanelVer:  1,
				Login:     0,
				Back:      1,
			},
			FileSearchToolItem{
				FileType:  fileType,
				Schema:    schemaList[1],
				Icon:      "https://gips1.baidu.com/it/u=542905184,2303592760&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconDark:  "https://gips1.baidu.com/it/u=1982656613,3326890553&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconNight: "https://gips1.baidu.com/it/u=3132424337,4067769580&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				Text:      "消除涂抹",
				Type:      "eliminate",
				Panel:     nil,
				PanelVer:  1,
				Login:     0,
				Back:      1,
			},
			FileSearchToolItem{
				FileType:  fileType,
				Schema:    schemaList[2],
				Icon:      "https://gips2.baidu.com/it/u=1514093326,3339827298&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconDark:  "https://gips1.baidu.com/it/u=1225859355,520728321&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconNight: "https://gips0.baidu.com/it/u=3474077020,2442431322&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				Text:      "背景替换",
				Type:      "background",
				Panel:     nil,
				PanelVer:  1,
				Login:     0,
				Back:      1,
			},
			FileSearchToolItem{
				FileType:  fileType,
				Schema:    schemaList[3],
				Icon:      "https://gips3.baidu.com/it/u=2875989202,1527389544&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconDark:  "https://gips3.baidu.com/it/u=1216301932,1398102231&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconNight: "https://gips2.baidu.com/it/u=4129501838,3435738306&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				Text:      "局部替换",
				Type:      "replace",
				Panel:     nil,
				PanelVer:  1,
				Login:     0,
				Back:      1,
			},
			FileSearchToolItem{
				FileType:  fileType,
				Schema:    schemaList[4],
				Icon:      "https://gips2.baidu.com/it/u=215706742,341382080&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconDark:  "https://gips0.baidu.com/it/u=4048608044,177801202&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				IconNight: "https://gips0.baidu.com/it/u=2391536655,2238088837&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f49_49",
				Text:      "文字转换",
				Type:      "transform",
				Panel:     nil,
				PanelVer:  1,
				Login:     0,
				Back:      1,
			},
		}
	}
	return fileSearchTool
}

func (a AiTools) GetAiToolsTypes() []string {
	toolTypes := make([]string, 0, len(a))

	for _, tool := range a {
		toolTypes = append(toolTypes, tool.Type)
	}

	return toolTypes
}

func (a AiTools) GetAiToolsIcons() map[string]map[string]string {
	icons := make(map[string]map[string]string, len(a))

	for _, tool := range a {
		iconMap := make(map[string]string, 3)
		iconMap["icon"] = tool.Icon
		iconMap["iconDark"] = tool.IconDark
		iconMap["iconNight"] = tool.IconNight
		icons[tool.Type] = iconMap
	}
	return icons
}

// sortAiToolsByTypes 根据 types 数组的顺序对 aiTool 进行排序
func (a AiTools) SortAiToolsByTypes(types []string) AiTools {
	if len(types) == 0 || len(a) == 0 {
		return a
	}

	// 创建 type 到索引的映射，用于确定排序优先级
	typeOrder := make(map[string]int)
	for i, t := range types {
		typeOrder[t] = i
	}

	// 创建 type 到工具的映射，避免重复遍历
	typeToTool := make(map[string]AiToolItem)
	var unorderedTools []AiToolItem

	// 一次遍历分类所有工具
	for _, tool := range a {
		if _, exists := typeOrder[tool.Type]; exists {
			// 如果该类型还没有工具，则记录第一个匹配的工具
			if _, hasValue := typeToTool[tool.Type]; !hasValue {
				typeToTool[tool.Type] = tool
			}
		} else {
			// 不在排序列表中的工具
			unorderedTools = append(unorderedTools, tool)
		}
	}

	// 创建结果数组，按照 types 的顺序排列
	sortedTools := make([]AiToolItem, 0, len(a))

	// 按照 types 的顺序添加工具
	for _, targetType := range types {
		if tool, exists := typeToTool[targetType]; exists {
			sortedTools = append(sortedTools, tool)
		}
	}

	// 添加不在 types 中的工具，保持原有顺序
	sortedTools = append(sortedTools, unorderedTools...)

	return sortedTools
}
