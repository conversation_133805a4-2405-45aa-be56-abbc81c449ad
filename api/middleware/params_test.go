package middleware

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"regexp"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/middlewares"
)

// 创建测试用WebContext
func createTestContext(t *testing.T) *gdp.WebContext {
	_ = t
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest("GET", "http://example.com/test", nil)
	c.Request = req
	wc := &gdp.WebContext{
		Context:    c,
		LogContext: gdp.NewLogContext("1", "127.0.0.1"),
	}
	return wc
}

// TestStructuredRequestParams_FromCurlFile 测试从 curl 文件到结构化 RequestParams 的完整流程
func TestStructuredRequestParams_FromCurlFile(t *testing.T) {
	// 1. 解析 curl 文件
	curlData, err := parseCurlFile("../test/res/hisUniversal.curl")
	assert.NoError(t, err, "应该能够成功解析 curl 文件")
	assert.NotNil(t, curlData, "curl 数据不应为空")

	// 2. 创建 WebContext 并运行中间件
	ctx, err := createWebContextFromCurl(curlData)
	assert.NoError(t, err, "应该能够成功创建 WebContext")

	// 调用 request 中间件处理请求，生成原始参数 map
	middlewares.RequestWare(ctx)

	// 验证原始参数 map 存在
	if val, exists := ctx.Get("____request"); exists && val != nil {
		requestParamsMap, ok := val.(map[string]string)
		assert.True(t, ok, "应该能够转换为 map[string]string")
		assert.NotEmpty(t, requestParamsMap, "请求参数 map 不应为空")

		// 3. 调用 StructuredRequestParams 将 map 转换为结构体
		StructuredRequestParams(ctx)

		// 4. 获取转换后的结构体
		reqParams := entity.GetRequestParams(ctx)
		assert.NotNil(t, reqParams, "RequestParams 结构体不应为空")

		// 5. 验证结构体中的数据和 map 中的数据对齐
		// 验证基本参数
		assert.Equal(t, requestParamsMap["query"], reqParams.Query, "Query 参数应该对齐")
		assert.Equal(t, requestParamsMap["v"], reqParams.V, "V 参数应该对齐")
		assert.Equal(t, requestParamsMap["service"], reqParams.Service, "Service 参数应该对齐")
		assert.Equal(t, requestParamsMap["from"], reqParams.From, "From 参数应该对齐")
		assert.Equal(t, requestParamsMap["cfrom"], reqParams.CFrom, "CFrom 参数应该对齐")
		assert.Equal(t, requestParamsMap["network"], reqParams.Network, "Network 参数应该对齐")
		assert.Equal(t, requestParamsMap["ctl"], reqParams.Ctl, "Ctl 参数应该对齐")
		assert.Equal(t, requestParamsMap["action"], reqParams.Action, "Action 参数应该对齐")
		assert.Equal(t, requestParamsMap["uid"], reqParams.UID, "UID 参数应该对齐")
		assert.Equal(t, requestParamsMap["puid"], reqParams.PUID, "PUID 参数应该对齐")
		assert.Equal(t, requestParamsMap["ua"], reqParams.UA, "UA 参数应该对齐")
		assert.Equal(t, requestParamsMap["osbranch"], reqParams.OSBranch, "OSBranch 参数应该对齐")
		assert.Equal(t, requestParamsMap["osname"], reqParams.OSName, "OSName 参数应该对齐")
		assert.Equal(t, requestParamsMap["st"], reqParams.ST, "ST 参数应该对齐")
		assert.Equal(t, requestParamsMap["lid"], reqParams.LID, "LID 参数应该对齐")
		assert.Equal(t, requestParamsMap["max_return_num"], reqParams.MaxReturnNum, "MaxReturnNum 参数应该对齐")
		assert.Equal(t, requestParamsMap["sst"], reqParams.SST, "SST 参数应该对齐")
		assert.Equal(t, requestParamsMap["_uid"], reqParams.CookieUID, "CookieUID 参数应该对齐")

		// NetworkType 可能由中间件从其他地方设置，检查是否存在
		if networkType, exists := requestParamsMap["network_type"]; exists {
			assert.Equal(t, networkType, reqParams.NetworkType, "NetworkType 参数应该对齐")
		} else {
			// NetworkType 不在原始 map 中，这是正常的，中间件可能从其他地方设置
			t.Logf("NetworkType 不在原始参数 map 中，这是正常的")
		}

		assert.Equal(t, requestParamsMap["branchname"], reqParams.BranchName, "BranchName 参数应该对齐")
		assert.Equal(t, requestParamsMap["mpv"], reqParams.MPV, "MPV 参数应该对齐")

		// 验证数据字段（如果存在）
		if dataVal, exists := requestParamsMap["data"]; exists {
			assert.Equal(t, dataVal, reqParams.Data, "Data 参数应该对齐")
		}

		t.Logf("StructuredRequestParams 测试通过，验证了 %d+ 个参数的对齐", 20)
		t.Logf("Map 中总共有 %d 个参数，结构体成功解析", len(requestParamsMap))

		// 验证特殊参数的转换
		// CIP 参数转换为 IPV4
		if cip, exists := requestParamsMap["cip"]; exists && cip != "" {
			assert.Equal(t, cip, reqParams.IPV4, "CIP 应该正确映射到 IPV4")
			assert.Equal(t, cip, reqParams.CIP, "CIP 字段应该保持原值")
		}
	} else {
		t.Error("未找到原始请求参数 map")
	}
}

// TestStructuredAdaptionParams_FromCurlFile 测试从 curl 文件到结构化 AdaptionParams 的完整流程
func TestStructuredAdaptionParams_FromCurlFile(t *testing.T) {
	// 1. 解析 curl 文件
	curlData, err := parseCurlFile("../test/res/hisUniversal.curl")
	assert.NoError(t, err, "应该能够成功解析 curl 文件")
	assert.NotNil(t, curlData, "curl 数据不应为空")

	// 2. 创建 WebContext
	ctx, err := createWebContextFromCurl(curlData)
	assert.NoError(t, err, "应该能够成功创建 WebContext")

	// 3. 手动设置一些适配参数到 ctx 中（模拟适配中间件的行为）
	// 从 curl 文件中可以提取到一些设备信息
	adapParams := map[string]string{
		"platform":          "iphone", // 从 UA 中可以判断
		"realPlatform":      "iphone",
		"bd_version":        "**********", // 从 UA 中提取
		"device_model":      "iPhone",     // 从 UT 中提取
		"os_version":        "18.5",       // 从 User-Agent 中提取
		"resolution_width":  "1320",       // 从 UA 中提取
		"resolution_height": "2868",       // 从 UA 中提取
		"device_dpi":        "3.0",        // 根据设备推算
		"device_dpis":       "xxhdpi",     // 根据 DPI 推算
		"isIOS":             "1",          // 根据平台设置
		"isAndroid":         "0",          // 根据平台设置
		"device_hd":         "1",          // 高分辨率设备
	}

	// 设置适配参数到 context
	ctx.Set("____adaption", adapParams)

	// 添加一些 Cookie 信息（从 curl 文件中提取）
	// 设置一些测试用的 Cookie 值
	req := ctx.Request
	if cookies := curlData.Cookies; len(cookies) > 0 {
		if sid, exists := cookies["H_WISE_SIDS"]; exists {
			req.AddCookie(&http.Cookie{Name: "H_WISE_SIDS", Value: sid})
		}
		if fontsize, exists := cookies["fontsize"]; exists {
			req.AddCookie(&http.Cookie{Name: "fontsize", Value: fontsize})
		}
	}

	// 设置默认测试 Cookie
	if _, err := req.Cookie("H_WISE_SIDS"); err != nil {
		req.AddCookie(&http.Cookie{Name: "H_WISE_SIDS", Value: "123_456_789"})
	}
	if _, err := req.Cookie("fontsize"); err != nil {
		req.AddCookie(&http.Cookie{Name: "fontsize", Value: "1.00"})
	}

	// 4. 调用 StructuredAdaptionParams 将 map 转换为结构体
	StructuredAdaptionParams(ctx)

	// 5. 获取转换后的结构体
	adaptParams := entity.GetAdaptionParams(ctx)
	assert.NotNil(t, adaptParams, "AdaptionParams 结构体不应为空")

	// 6. 验证结构体中的数据和 map 中的数据对齐
	assert.Equal(t, adapParams["platform"], adaptParams.Platform, "Platform 参数应该对齐")
	assert.Equal(t, adapParams["realPlatform"], adaptParams.RealPlatform, "RealPlatform 参数应该对齐")
	assert.Equal(t, adapParams["bd_version"], adaptParams.BDVersion, "BDVersion 参数应该对齐")
	assert.Equal(t, adapParams["device_model"], adaptParams.DeviceModel, "DeviceModel 参数应该对齐")
	assert.Equal(t, adapParams["os_version"], adaptParams.OSVersion, "OSVersion 参数应该对齐")
	assert.Equal(t, adapParams["resolution_width"], adaptParams.ResolutionWidth, "ResolutionWidth 参数应该对齐")
	assert.Equal(t, adapParams["resolution_height"], adaptParams.ResolutionHeight, "ResolutionHeight 参数应该对齐")
	assert.Equal(t, adapParams["device_dpi"], adaptParams.DeviceDpi, "DeviceDpi 参数应该对齐")
	assert.Equal(t, adapParams["device_dpis"], adaptParams.DeviceDpis, "DeviceDpis 参数应该对齐")
	assert.Equal(t, adapParams["isIOS"], adaptParams.IsIOS, "IsIOS 参数应该对齐")
	assert.Equal(t, adapParams["isAndroid"], adaptParams.IsAndroid, "IsAndroid 参数应该对齐")
	assert.Equal(t, adapParams["device_hd"], adaptParams.DeviceHD, "DeviceHD 参数应该对齐")

	// 验证从 Cookie 解析的参数
	if adaptParams.Sid != "" {
		assert.NotEmpty(t, adaptParams.Sid, "Sid 应该从 Cookie 中解析")
		assert.NotEmpty(t, adaptParams.SidInt, "SidInt 应该从 Sid 转换而来")

		// 验证 SidInt 的转换
		if adaptParams.Sid == "123_456_789" {
			expectedSidInt := []int{123, 456, 789}
			assert.Equal(t, expectedSidInt, adaptParams.SidInt, "SidInt 转换应该正确")
		}
	}

	if adaptParams.FontSize > 0 {
		assert.Greater(t, adaptParams.FontSize, 0.0, "FontSize 应该从 Cookie 中解析")
	}

	t.Logf("StructuredAdaptionParams 测试通过，验证了 %d+ 个参数的对齐", 12)
	t.Logf("平台: %s, 版本: %s, 设备: %s", adaptParams.Platform, adaptParams.BDVersion, adaptParams.DeviceModel)
	t.Logf("分辨率: %sx%s, DPI: %s", adaptParams.ResolutionWidth, adaptParams.ResolutionHeight, adaptParams.DeviceDpi)
	t.Logf("Sid: %s, SidInt: %v, FontSize: %.2f", adaptParams.Sid, adaptParams.SidInt, adaptParams.FontSize)
}

// 辅助函数：解析 curl 文件的数据结构
type CurlData struct {
	URL     string
	Method  string
	Headers map[string]string
	Body    string
	Cookies map[string]string
}

// parseCurlFile 解析 curl 文件，提取完整的请求信息
func parseCurlFile(filePath string) (*CurlData, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取 curl 文件失败: %w", err)
	}

	curlData := &CurlData{
		Method:  "GET",
		Headers: make(map[string]string),
		Cookies: make(map[string]string),
	}

	// 解析 curl 命令
	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 提取 URL
		if strings.Contains(line, "curl") && strings.Contains(line, "http") {
			// 提取 URL 部分
			urlRegex := regexp.MustCompile(`https?://[^\s'"]+`)
			matches := urlRegex.FindString(line)
			if matches != "" {
				curlData.URL = matches
			}
		}

		// 提取 Headers
		if strings.Contains(line, "-H") || strings.Contains(line, "--header") {
			headerRegex := regexp.MustCompile(`-H ['"]([^:]+):\s*([^'"]+)['"]`)
			matches := headerRegex.FindStringSubmatch(line)
			if len(matches) == 3 {
				curlData.Headers[matches[1]] = matches[2]
			}
		}

		// 提取 Cookies
		if strings.Contains(line, "Cookie:") {
			cookieRegex := regexp.MustCompile(`Cookie:\s*([^'"]+)`)
			matches := cookieRegex.FindStringSubmatch(line)
			if len(matches) == 2 {
				cookieStr := matches[1]
				cookies := strings.Split(cookieStr, ";")
				for _, cookie := range cookies {
					cookie = strings.TrimSpace(cookie)
					if strings.Contains(cookie, "=") {
						parts := strings.SplitN(cookie, "=", 2)
						if len(parts) == 2 {
							curlData.Cookies[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
						}
					}
				}
			}
		}

		// 提取 POST 数据
		if strings.Contains(line, "-d") || strings.Contains(line, "--data") {
			dataRegex := regexp.MustCompile(`-d ['"]([^'"]+)['"]`)
			matches := dataRegex.FindStringSubmatch(line)
			if len(matches) == 2 {
				curlData.Body = matches[1]
				curlData.Method = "POST"
			}
		}
	}

	return curlData, nil
}

// createWebContextFromCurl 根据 curl 数据创建 WebContext
func createWebContextFromCurl(curlData *CurlData) (*gdp.WebContext, error) {
	// 解析 URL 来验证其有效性
	_, err := url.Parse(curlData.URL)
	if err != nil {
		return nil, fmt.Errorf("解析 URL 失败: %w", err)
	}

	// 创建请求体
	var body io.Reader
	if curlData.Body != "" {
		body = strings.NewReader(curlData.Body)
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest(curlData.Method, curlData.URL, body)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置 Headers
	for key, value := range curlData.Headers {
		req.Header.Set(key, value)
	}

	// 设置 Cookies
	for name, value := range curlData.Cookies {
		req.AddCookie(&http.Cookie{Name: name, Value: value})
	}

	// 如果是 POST 请求，设置 Content-Type
	if curlData.Method == "POST" && curlData.Body != "" {
		if req.Header.Get("Content-Type") == "" {
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		}
	}

	// 创建测试用的 ResponseWriter 和 Gin Context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 创建 WebContext
	webCtx := &gdp.WebContext{
		Context:    c,
		LogContext: gdp.NewLogContext("test", "127.0.0.1"),
	}

	return webCtx, nil
}
