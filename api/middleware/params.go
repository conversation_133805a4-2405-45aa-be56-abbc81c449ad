// Package middleware 新接口 middleware 层
package middleware

import (
	"encoding/json"
	"net"
	"strconv"
	"strings"

	"github.com/mitchellh/mapstructure"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/constant"
)

// StructuredFormData 解析请求中的data字段为FormData结构体
func StructuredFormData(ctx *gdp.WebContext) {
	dataStr := ctx.Request.FormValue("data")
	if dataStr == "" {
		return
	}

	formData := &entity.FormData{}
	if err := json.Unmarshal([]byte(dataStr), formData); err != nil {
		ctx.AddNotice("paramErr", "json unmarshal error: "+err.Error())
		return
	}
	ctx.Set(entity.FormDataKey, formData)
}

// StructuredRequestParams 从上下文中获取RequestParams
func StructuredRequestParams(ctx *gdp.WebContext) {
	// 获取原有map[string]string
	var rawParams map[string]string
	if val, exists := ctx.Get(constant.REQPARAMS); exists && val != nil {
		rawParams, _ = val.(map[string]string)
	} else {
		rawParams = make(map[string]string)
	}

	// 使用mapstructure进行映射
	params := &entity.RequestParams{}
	config := &mapstructure.DecoderConfig{
		WeaklyTypedInput: true,
		Result:           params,
		TagName:          "mapstructure",
	}
	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return
	}
	if err := decoder.Decode(rawParams); err != nil {
		return
	}

	cip := params.CIP
	if cip == "" {
		cip = ctx.ClientIP()
	}
	ip := net.ParseIP(cip)
	useripv6 := ""
	if ip.To16() != nil {
		useripv6 = ip.To16().String()
	}
	params.IPV4 = cip
	params.IPV6 = useripv6
	address, errMsg := IP2Poi(cip)
	if errMsg != nil {
		params.Address = address
	}

	common.SetUserPrivacyInfo(params.UID, ctx)

	ctx.Set(entity.ReqParamsKey, params)

}

// StructuredAdaptionParams 从上下文中获取AdaptionParams
func StructuredAdaptionParams(ctx *gdp.WebContext) {
	// 获取原有map[string]string
	var rawParams map[string]string
	if val, exists := ctx.Get(constant.ADAPARAMS); exists && val != nil {
		rawParams, _ = val.(map[string]string)
	} else {
		rawParams = make(map[string]string)
	}

	// 使用mapstructure进行映射
	params := &entity.AdaptionParams{}
	config := &mapstructure.DecoderConfig{
		WeaklyTypedInput: true,
		Result:           params,
		TagName:          "mapstructure",
	}
	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return
	}

	if err := decoder.Decode(rawParams); err != nil {
		return
	}
	// 解析cookie
	if HSids, err := ctx.Request.Cookie("H_WISE_SIDS"); err == nil {
		params.Sid = HSids.Value
		sids := strings.Split(HSids.Value, "_")
		for _, sid := range sids {
			numid, _ := strconv.Atoi(sid)
			params.SidInt = append(params.SidInt, numid)
		}
	}
	if CSids, err := ctx.Request.Cookie("CS_W_SIDS"); err == nil {
		params.CSid = CSids.Value
	}

	wisePm, err := ctx.Cookie("WISE_HIS_PM")
	if err != nil {
		wisePm = "1"
	}
	params.WisePM = wisePm
	fontSize, _ := ctx.Cookie("fontsize")
	fontSizeFloat, _ := strconv.ParseFloat(fontSize, 64)
	params.FontSize = fontSizeFloat
	bdEnvMod, _ := ctx.Cookie("bdenvmode")
	params.BDEnvMod = bdEnvMod

	logidstr := ctx.GetLogID()
	logid64, _ := strconv.Atoi(logidstr)
	params.LogID = uint64(logid64)

	ctx.Set(entity.AdapParamsKey, params)

}

func StructuredParamsWare(ctx *gdp.WebContext) {
	StructuredRequestParams(ctx)
	StructuredAdaptionParams(ctx)
	StructuredFormData(ctx)

	ctx.Next()
}
