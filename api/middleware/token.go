package middleware

import (
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/service"
)

// ValidateTokenMiddleware 校验 tk 和 sk 参数的中间件
func ValidateTokenMiddleware(ctx *gdp.WebContext) {
	tk := ctx.Query("tk")
	stk := ctx.Query("stk")
	requestParams := entity.GetRequestParams(ctx)
	if requestParams == nil {
		ctx.JSON(200, map[string]interface{}{
			"errno":  500,
			"errmsg": "服务请求失败",
		})
		ctx.Abort()
		return
	}

	tokenService := service.NewTokenService(ctx, requestParams)

	if !tokenService.ValidateClientToken(tk) {
		ctx.JSON(200, map[string]interface{}{
			"errno":  502,
			"errmsg": "client_token 认证不通过",
		})
		ctx.Abort()
		return
	}

	if !tokenService.ValidateSessionToken(stk) {
		ctx.JSON(200, map[string]interface{}{
			"errno":  504,
			"errmsg": "session_token 认证不通过",
		})
		ctx.Abort()
		return
	}

	ctx.Next()
}
