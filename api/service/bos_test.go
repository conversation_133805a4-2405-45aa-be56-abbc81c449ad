package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

// ======================== 本地 MockBosModel 实现 ========================
type MockBosModel struct {
	mock.Mock
}

func (m *MockBosModel) GetSTSInfo(expirationInSeconds int, acl string) (*model.STSInfo, error) {
	args := m.Called(expirationInSeconds, acl)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.STSInfo), args.Error(1)
}

func (m *MockBosModel) GetAuthURL(expirationInSeconds int, acl string, bucketName string, objectName []string) ([]string, error) {
	args := m.Called(expirationInSeconds, acl, bucketName, objectName)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockBosModel) GetObjectInfo(bucketName string, objectName string) (model.ObjectInfo, error) {
	args := m.Called(bucketName, objectName)
	return args.Get(0).(model.ObjectInfo), args.Error(1)
}

func (m *MockBosModel) GetImageInfo(bucketName string, objectName string) (model.ImageInfo, error) {
	args := m.Called(bucketName, objectName)
	return args.Get(0).(model.ImageInfo), args.Error(1)
}

// ======================== mock model.GlobalBosModel 的单元测试 ========================

func TestGetSTSInfo_Success(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	stsInfo := &model.STSInfo{
		AccessKeyID:     "test-access-key-id",
		SecretAccessKey: "test-secret-access-key",
		SessionToken:    "test-session-token",
		PreFixPath:      "test-prefix/",
		BucketName:      "test-bucket",
		Endpoint:        "http://bj.bcebos.com",
	}
	mockModel.On("GetSTSInfo", mock.AnythingOfType("int"), mock.AnythingOfType("string")).Return(stsInfo, nil)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	info, err := service.GetSTSInfo("uploads", 3600, "test-cuid")
	assert.NoError(t, err)
	assert.NotNil(t, info)
	assert.Equal(t, "test-access-key-id", info.AccessKeyID)
}

func TestGetSTSInfo_Failed(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	mockModel.On("GetSTSInfo",
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
	).Return((*model.STSInfo)(nil), assert.AnError)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	info, err := service.GetSTSInfo("uploads", 3600, "test-cuid")
	assert.Error(t, err)
	assert.Nil(t, info)
}

func TestGetAuthURL_Success(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	authURLs := []string{"http://example.com/auth-url-1", "http://example.com/auth-url-2"}
	mockModel.On("GetAuthURL",
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
		mock.AnythingOfType("[]string"),
	).Return(authURLs, nil)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	urls, err := service.GetAuthURL("uploads", 3600, "test-cuid", []string{"object1", "object2"})
	assert.NoError(t, err)
	assert.NotNil(t, urls)
	assert.Len(t, urls, 2)
}

func TestGetAuthURL_Failed(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	mockModel.On("GetAuthURL",
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
		mock.AnythingOfType("[]string"),
	).Return(([]string)(nil), assert.AnError)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	urls, err := service.GetAuthURL("uploads", 3600, "test-cuid", []string{"object1"})
	assert.Error(t, err)
	assert.Nil(t, urls)
}

func TestGetObjectMetaInfo_Success(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	objectInfo := model.ObjectInfo{Size: 1024}
	mockModel.On("GetObjectInfo", mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(objectInfo, nil)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	info, err := service.GetObjectMetaInfo("test-object")
	assert.NoError(t, err)
	assert.Equal(t, int64(1024), info.Size)
}

func TestGetObjectMetaInfo_Failed(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	mockModel.On("GetObjectInfo",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
	).Return(model.ObjectInfo{}, assert.AnError)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	info, err := service.GetObjectMetaInfo("test-object")
	assert.Error(t, err)
	assert.Equal(t, int64(0), info.Size)
}

func TestGetImageMetaInfo_Success(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	imageInfo := model.ImageInfo{
		ImageSize:     model.ImageValueWrapper{Value: "1024"},
		ImageWidth:    model.ImageValueWrapper{Value: "800"},
		ImageHeight:   model.ImageValueWrapper{Value: "600"},
		ImageMimeType: model.ImageValueWrapper{Value: "image/jpeg"},
		Size:          1024,
		Width:         800,
		Height:        600,
		MimeType:      "image/jpeg",
	}
	mockModel.On("GetImageInfo", mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(imageInfo, nil)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	info, err := service.GetImageMetaInfo("test-image")
	assert.NoError(t, err)
	assert.Equal(t, int64(1024), info.Size)
	assert.Equal(t, int64(800), info.Width)
	assert.Equal(t, int64(600), info.Height)
	assert.Equal(t, "image/jpeg", info.MimeType)
}

func TestGetImageMetaInfo_Failed(t *testing.T) {
	origin := model.GlobalBosModel
	mockModel := &MockBosModel{}
	mockModel.On("GetImageInfo",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
	).Return(model.ImageInfo{}, assert.AnError)
	model.GlobalBosModel = mockModel
	defer func() { model.GlobalBosModel = origin }()

	service := NewBosService()
	info, err := service.GetImageMetaInfo("test-image")
	assert.Error(t, err)
	assert.Equal(t, int64(0), info.Size)
	assert.Equal(t, int64(0), info.Width)
	assert.Equal(t, int64(0), info.Height)
	assert.Equal(t, "", info.MimeType)
}

// BosService 定义 BOS 服务接口（为了便于测试而定义的接口）
type BosService interface {
	GetSTSInfo(pathPrefix string, expirationSecond int, cuid string) (*UploadInfo, error)
	GetAuthURL(pathPrefix string, expirationSecond int, cuid string, objects []string) ([]string, error)
	GetObjectMetaInfo(objectName string) (ObjectMetaInfo, error)
	GetImageMetaInfo(objectName string) (ObjectMetaInfo, error)
	GetBosPath(pathPrefix string, cuid string) string
}

// MockBosService 实现了 BosService 接口，用于测试
type MockBosService struct {
	mock.Mock
}

func (m *MockBosService) GetSTSInfo(pathPrefix string, expirationSecond int, cuid string) (*UploadInfo, error) {
	args := m.Called(pathPrefix, expirationSecond, cuid)
	return args.Get(0).(*UploadInfo), args.Error(1)
}

func (m *MockBosService) GetAuthURL(pathPrefix string, expirationSecond int, cuid string, objects []string) ([]string, error) {
	args := m.Called(pathPrefix, expirationSecond, cuid, objects)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockBosService) GetObjectMetaInfo(objectName string) (ObjectMetaInfo, error) {
	args := m.Called(objectName)
	return args.Get(0).(ObjectMetaInfo), args.Error(1)
}

func (m *MockBosService) GetImageMetaInfo(objectName string) (ObjectMetaInfo, error) {
	args := m.Called(objectName)
	return args.Get(0).(ObjectMetaInfo), args.Error(1)
}

func (m *MockBosService) GetBosPath(pathPrefix string, cuid string) string {
	args := m.Called(pathPrefix, cuid)
	return args.String(0)
}

// NewMockBosService 创建一个新的 MockBosService 实例
func NewMockBosService() *MockBosService {
	return &MockBosService{}
}

// SetupMockBosServiceSuccess 设置一个"成功"场景的 MockBosService
func SetupMockBosServiceSuccess() *MockBosService {
	mockService := NewMockBosService()

	// 设置成功返回的数据
	successSTSInfo := &UploadInfo{
		AccessKeyID:     "mock-access-key-id",
		SecretAccessKey: "mock-secret-access-key",
		SessionToken:    "mock-session-token",
		PreFixPath:      "uploads/encrypted-cuid/",
		BucketName:      BucketName,
		Endpoint:        EndPoint,
	}

	successAuthURLs := []string{
		"https://bj.bcebos.com/rec-ui/uploads/test-object-1?authorization=xxx",
		"https://bj.bcebos.com/rec-ui/uploads/test-object-2?authorization=xxx",
	}

	successObjectInfo := ObjectMetaInfo{
		Size:     1024,
		Width:    0,
		Height:   0,
		MimeType: "application/octet-stream",
	}

	successImageInfo := ObjectMetaInfo{
		Size:     2048,
		Width:    800,
		Height:   600,
		MimeType: "image/jpeg",
	}

	// 设置方法返回值
	mockService.On("GetSTSInfo",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
	).Return(successSTSInfo, nil)
	mockService.On("GetAuthURL",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
		mock.AnythingOfType("[]string"),
	).Return(successAuthURLs, nil)
	mockService.On("GetObjectMetaInfo",
		mock.AnythingOfType("string"),
	).Return(successObjectInfo, nil)
	mockService.On("GetImageMetaInfo",
		mock.AnythingOfType("string"),
	).Return(successImageInfo, nil)
	mockService.On("GetBosPath",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
	).Return("uploads/encrypted-cuid/")

	return mockService
}

// SetupMockBosServiceFailed 设置一个"失败"场景的 MockBosService
func SetupMockBosServiceFailed() *MockBosService {
	mockService := NewMockBosService()

	// 设置失败场景
	mockService.On("GetSTSInfo",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
	).Return((*UploadInfo)(nil), assert.AnError)
	mockService.On("GetAuthURL",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("int"),
		mock.AnythingOfType("string"),
		mock.AnythingOfType("[]string"),
	).Return([]string(nil), assert.AnError)
	mockService.On("GetObjectMetaInfo",
		mock.AnythingOfType("string"),
	).Return(ObjectMetaInfo{}, assert.AnError)
	mockService.On("GetImageMetaInfo",
		mock.AnythingOfType("string"),
	).Return(ObjectMetaInfo{}, assert.AnError)
	mockService.On("GetBosPath",
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
	).Return("")

	return mockService
}

// SetupMockBosServiceCustom 创建一个可以自定义行为的 MockBosService
func SetupMockBosServiceCustom() *MockBosService {
	return NewMockBosService()
}

// 使用示例：在 handler 测试中使用 MockBosService
func TestMockBosService_Usage(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("handler测试-成功场景", func(t *testing.T) {
		mockService := SetupMockBosServiceSuccess()

		// 测试 GetSTSInfo
		stsInfo, err := mockService.GetSTSInfo("uploads", 3600, "test-cuid")
		assert.NoError(t, err)
		assert.NotNil(t, stsInfo)
		assert.Equal(t, "mock-access-key-id", stsInfo.AccessKeyID)

		// 测试 GetAuthURL
		authURLs, err := mockService.GetAuthURL("uploads", 3600, "test-cuid", []string{"object1", "object2"})
		assert.NoError(t, err)
		assert.Len(t, authURLs, 2)

		// 测试 GetObjectMetaInfo
		objInfo, err := mockService.GetObjectMetaInfo("test-object")
		assert.NoError(t, err)
		assert.Equal(t, int64(1024), objInfo.Size)

		// 测试 GetImageMetaInfo
		imgInfo, err := mockService.GetImageMetaInfo("test-image")
		assert.NoError(t, err)
		assert.Equal(t, int64(800), imgInfo.Width)
		assert.Equal(t, int64(600), imgInfo.Height)

		// 测试 GetBosPath
		path := mockService.GetBosPath("uploads", "test-cuid")
		assert.Equal(t, "uploads/encrypted-cuid/", path)

		mockService.AssertExpectations(t)
	})

	// ========== 示例2：失败场景 ==========
	t.Run("handler测试-失败场景", func(t *testing.T) {
		mockService := SetupMockBosServiceFailed()

		// 测试失败场景
		stsInfo, err := mockService.GetSTSInfo("uploads", 3600, "test-cuid")
		assert.Error(t, err)
		assert.Nil(t, stsInfo)

		authURLs, err := mockService.GetAuthURL("uploads", 3600, "test-cuid", []string{"object1"})
		assert.Error(t, err)
		assert.Nil(t, authURLs)

		objInfo, err := mockService.GetObjectMetaInfo("test-object")
		assert.Error(t, err)
		assert.Equal(t, ObjectMetaInfo{}, objInfo)

		imgInfo, err := mockService.GetImageMetaInfo("test-image")
		assert.Error(t, err)
		assert.Equal(t, ObjectMetaInfo{}, imgInfo)

		path := mockService.GetBosPath("uploads", "test-cuid")
		assert.Empty(t, path)

		mockService.AssertExpectations(t)
	})

	// ========== 示例3：自定义场景 ==========
	t.Run("handler测试-自定义场景", func(t *testing.T) {
		mockService := SetupMockBosServiceCustom()

		// 自定义特定的行为
		customSTSInfo := &UploadInfo{
			AccessKeyID:     "custom-access-key",
			SecretAccessKey: "custom-secret-key",
			SessionToken:    "custom-session-token",
			PreFixPath:      "custom/path/",
			BucketName:      "custom-bucket",
			Endpoint:        "https://custom.endpoint.com",
		}

		mockService.On("GetSTSInfo", "custom-prefix", 7200, "custom-cuid").Return(customSTSInfo, nil)
		mockService.On("GetBosPath", "custom-prefix", "custom-cuid").Return("custom/encrypted-path/")

		// 测试自定义行为
		stsInfo, err := mockService.GetSTSInfo("custom-prefix", 7200, "custom-cuid")
		assert.NoError(t, err)
		assert.Equal(t, customSTSInfo, stsInfo)

		path := mockService.GetBosPath("custom-prefix", "custom-cuid")
		assert.Equal(t, "custom/encrypted-path/", path)

		mockService.AssertExpectations(t)
	})
}

/*
// 在实际的 handler 测试文件中的使用示例
// 文件：api/handler/bos_handler_test.go

func TestBosHandler_GetUploadInfo(t *testing.T) {
	// 创建 MockBosService
	mockService := service.SetupMockBosServiceSuccess()

	// 创建你的 handler，注入 mock
	handler := NewBosHandler(mockService)

	// 创建测试请求
	req := httptest.NewRequest("POST", "/bos/upload-info", strings.NewReader(`{"pathPrefix":"uploads","cuid":"test-cuid"}`))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行 handler
	handler.GetUploadInfo(c)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证 BosService 的方法被正确调用
	mockService.AssertExpectations(t)
}

// 测试上传失败的场景
func TestBosHandler_GetUploadInfo_Failed(t *testing.T) {
	// 创建失败场景的 MockBosService
	mockService := service.SetupMockBosServiceFailed()

	handler := NewBosHandler(mockService)

	req := httptest.NewRequest("POST", "/bos/upload-info", strings.NewReader(`{"pathPrefix":"uploads","cuid":"test-cuid"}`))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	handler.GetUploadInfo(c)

	// 验证返回错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	mockService.AssertExpectations(t)
}
*/
