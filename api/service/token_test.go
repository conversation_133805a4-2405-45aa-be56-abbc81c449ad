package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// MockTokenService 实现了 TokenService 接口，用于 handler 层测试
type MockTokenService struct {
	mock.Mock
}

func (m *MockTokenService) GetBaseToken() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockTokenService) GetSessionToken() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockTokenService) GetClientToken(baseToken string) string {
	args := m.Called(baseToken)
	return args.String(0)
}

func (m *MockTokenService) ValidateBaseToken(baseToken string) bool {
	args := m.Called(baseToken)
	return args.Bool(0)
}

func (m *MockTokenService) ValidateSessionToken(sessionToken string) bool {
	args := m.Called(sessionToken)
	return args.Bool(0)
}

func (m *MockTokenService) ValidateClientToken(clientToken string) bool {
	args := m.Called(clientToken)
	return args.Bool(0)
}

// NewMockTokenService 创建一个新的 MockTokenService 实例
func NewMockTokenService() *MockTokenService {
	return &MockTokenService{}
}

// SetupMockTokenServiceSuccess 设置一个"成功"场景的 MockTokenService
// 所有验证方法返回 true，所有生成方法返回有效的 token
func SetupMockTokenServiceSuccess() *MockTokenService {
	mockService := NewMockTokenService()

	// 设置生成方法的返回值
	mockService.On("GetBaseToken").Return("mock-base-token-123")
	mockService.On("GetSessionToken").Return("mock-session-token-456")
	mockService.On("GetClientToken", mock.AnythingOfType("string")).Return("mock-client-token-789")

	// 设置验证方法的返回值
	mockService.On("ValidateBaseToken", mock.AnythingOfType("string")).Return(true)
	mockService.On("ValidateSessionToken", mock.AnythingOfType("string")).Return(true)
	mockService.On("ValidateClientToken", mock.AnythingOfType("string")).Return(true)

	return mockService
}

// SetupMockTokenServiceFailed 设置一个"失败"场景的 MockTokenService
// 所有验证方法返回 false，生成方法返回空字符串
func SetupMockTokenServiceFailed() *MockTokenService {
	mockService := NewMockTokenService()

	// 设置生成方法的返回值
	mockService.On("GetBaseToken").Return("")
	mockService.On("GetSessionToken").Return("")
	mockService.On("GetClientToken", mock.AnythingOfType("string")).Return("")

	// 设置验证方法的返回值
	mockService.On("ValidateBaseToken", mock.AnythingOfType("string")).Return(false)
	mockService.On("ValidateSessionToken", mock.AnythingOfType("string")).Return(false)
	mockService.On("ValidateClientToken", mock.AnythingOfType("string")).Return(false)

	return mockService
}

// SetupMockTokenServiceCustom 创建一个可以自定义行为的 MockTokenService
// 使用方式：
// mockService := SetupMockTokenServiceCustom()
// mockService.On("ValidateBaseToken", "specific-token").Return(true)
// mockService.On("ValidateBaseToken", "invalid-token").Return(false)
func SetupMockTokenServiceCustom() *MockTokenService {
	return NewMockTokenService()
}

// MockTokenModel 实现了 TokenModel 接口
type MockTokenModel struct {
	mock.Mock
}

func (m *MockTokenModel) GenerateBaseToken(cuid string) string {
	args := m.Called(cuid)
	return args.String(0)
}

func (m *MockTokenModel) ValidateBaseToken(baseToken string, expectedCuid string) (timestamp int64, isValid bool) {
	args := m.Called(baseToken, expectedCuid)
	return args.Get(0).(int64), args.Bool(1)
}

func (m *MockTokenModel) GenerateSessionToken(logID string) string {
	args := m.Called(logID)
	return args.String(0)
}

func (m *MockTokenModel) ValidateSessionToken(sessionToken string) bool {
	args := m.Called(sessionToken)
	return args.Bool(0)
}

func (m *MockTokenModel) GenerateClientToken(baseToken string, cuid string) string {
	args := m.Called(baseToken, cuid)
	return args.String(0)
}

func (m *MockTokenModel) ValidateClientToken(clientToken string) (cuid string, baseTokenTimestamp int64, diff time.Duration, ok bool) {
	args := m.Called(clientToken)
	return args.String(0), args.Get(1).(int64), args.Get(2).(time.Duration), args.Bool(3)
}

// createTestRequestParams 创建测试用的RequestParams
func createTestRequestParams(uid string) *entity.RequestParams {
	return &entity.RequestParams{
		UID: uid,
	}
}

// newTokenServiceWithMock 创建带有Mock TokenModel的TokenService实例
func newTokenServiceWithMock(ctx *gdp.WebContext, requestParams *entity.RequestParams, mockTokenModel *MockTokenModel) *tokenServiceImpl {
	return &tokenServiceImpl{
		ctx:           ctx,
		requestParams: requestParams,
		tokenModel:    mockTokenModel,
	}
}

func TestNewTokenService(t *testing.T) {
	tests := []struct {
		name          string
		ctx           *gdp.WebContext
		requestParams *entity.RequestParams
		want          TokenService
		wantNil       bool
	}{
		{
			name:          "正常情况",
			ctx:           createTestWebContext(),
			requestParams: createTestRequestParams("test-uid"),
			wantNil:       false,
		},
		{
			name:          "requestParams为nil",
			ctx:           createTestWebContext(),
			requestParams: nil,
			want:          nil,
			wantNil:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewTokenService(tt.ctx, tt.requestParams)
			if tt.wantNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
			}
		})
	}
}

func TestTokenService_GetBaseToken(t *testing.T) {
	tests := []struct {
		name    string
		setup   func() (*MockTokenModel, *tokenServiceImpl)
		want    string
		wantErr bool
	}{
		{
			name: "正常情况",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("GenerateBaseToken", "test-uid").Return("base-token-123")
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: "base-token-123",
		},
		{
			name: "service为nil",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				return &MockTokenModel{}, nil
			},
			want: "",
		},
		{
			name: "requestParams为nil",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want: "",
		},
		// 新增测试：测试鸿蒙HUID处理逻辑
		{
			name: "鸿蒙h0分支且HUID不为空时使用HUID",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("GenerateBaseToken", "test-huid").Return("huid-base-token-123")
				requestParams := &entity.RequestParams{
					UID:          "test-uid",
					HUID:         "test-huid",
					RealOSBranch: "h0",
				}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					requestParams,
					mockModel,
				)
				return mockModel, service
			},
			want: "huid-base-token-123",
		},
		{
			name: "鸿蒙h0分支但HUID为空时使用UID",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("GenerateBaseToken", "test-uid").Return("uid-base-token-123")
				requestParams := &entity.RequestParams{
					UID:          "test-uid",
					HUID:         "", // HUID为空
					RealOSBranch: "h0",
				}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					requestParams,
					mockModel,
				)
				return mockModel, service
			},
			want: "uid-base-token-123",
		},
		{
			name: "非h0分支即使有HUID也使用UID",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("GenerateBaseToken", "test-uid").Return("uid-base-token-123")
				requestParams := &entity.RequestParams{
					UID:          "test-uid",
					HUID:         "test-huid",
					RealOSBranch: "i0", // 非h0分支
				}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					requestParams,
					mockModel,
				)
				return mockModel, service
			},
			want: "uid-base-token-123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result string
			if service != nil {
				result = service.GetBaseToken()
			} else {
				var nilService *tokenServiceImpl
				result = nilService.GetBaseToken()
			}

			assert.Equal(t, tt.want, result)
			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

func TestTokenService_GetSessionToken(t *testing.T) {
	tests := []struct {
		name  string
		setup func() (*MockTokenModel, *tokenServiceImpl)
		want  string
	}{
		{
			name: "正常情况",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("GenerateSessionToken", mock.AnythingOfType("string")).Return("session-token-123")
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: "session-token-123",
		},
		{
			name: "service为nil",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				return &MockTokenModel{}, nil
			},
			want: "",
		},
		{
			name: "requestParams为nil",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result string
			if service != nil {
				result = service.GetSessionToken()
			} else {
				var nilService *tokenServiceImpl
				result = nilService.GetSessionToken()
			}

			assert.Equal(t, tt.want, result)
			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

func TestTokenService_GetClientToken(t *testing.T) {
	tests := []struct {
		name      string
		baseToken string
		setup     func() (*MockTokenModel, *tokenServiceImpl)
		want      string
	}{
		{
			name:      "正常情况",
			baseToken: "base-token-123",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("GenerateClientToken", "base-token-123", "test-uid").Return("client-token-123")
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: "client-token-123",
		},
		{
			name:      "service为nil",
			baseToken: "base-token-123",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				return &MockTokenModel{}, nil
			},
			want: "",
		},
		{
			name:      "requestParams为nil",
			baseToken: "base-token-123",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result string
			if service != nil {
				result = service.GetClientToken(tt.baseToken)
			} else {
				var nilService *tokenServiceImpl
				result = nilService.GetClientToken(tt.baseToken)
			}

			assert.Equal(t, tt.want, result)
			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

func TestTokenService_ValidateBaseToken(t *testing.T) {
	tests := []struct {
		name      string
		baseToken string
		setup     func() (*MockTokenModel, *tokenServiceImpl)
		want      bool
	}{
		{
			name:      "正常情况-有效token",
			baseToken: "valid-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				// 返回15分钟前的时间戳（在有效期内）
				validTimestamp := time.Now().Add(-15 * time.Minute).Unix()
				mockModel.On("ValidateBaseToken", "valid-token", "test-uid").Return(validTimestamp, true)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: true,
		},
		{
			name:      "TokenModel校验失败",
			baseToken: "invalid-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("ValidateBaseToken", "invalid-token", "test-uid").Return(int64(0), false)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:      "token已过期",
			baseToken: "expired-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				// 返回超过baseTokenTimeout的时间戳
				expiredTimestamp := time.Now().Add(-baseTokenTimeout - 1*time.Hour).Unix()
				mockModel.On("ValidateBaseToken", "expired-token", "test-uid").Return(expiredTimestamp, true)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:      "service为nil",
			baseToken: "any-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				return &MockTokenModel{}, nil
			},
			want: false,
		},
		{
			name:      "requestParams为nil",
			baseToken: "any-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result bool
			if service != nil {
				result = service.ValidateBaseToken(tt.baseToken)
			} else {
				var nilService *tokenServiceImpl
				result = nilService.ValidateBaseToken(tt.baseToken)
			}

			assert.Equal(t, tt.want, result)
			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

func TestTokenService_ValidateSessionToken(t *testing.T) {
	tests := []struct {
		name         string
		sessionToken string
		setup        func() (*MockTokenModel, *tokenServiceImpl)
		want         bool
	}{
		{
			name:         "正常情况-有效token",
			sessionToken: "valid-session-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("ValidateSessionToken", "valid-session-token").Return(true)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: true,
		},
		{
			name:         "无效token",
			sessionToken: "invalid-session-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("ValidateSessionToken", "invalid-session-token").Return(false)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:         "service为nil",
			sessionToken: "any-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				return &MockTokenModel{}, nil
			},
			want: false,
		},
		{
			name:         "requestParams为nil",
			sessionToken: "any-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result bool
			if service != nil {
				result = service.ValidateSessionToken(tt.sessionToken)
			} else {
				var nilService *tokenServiceImpl
				result = nilService.ValidateSessionToken(tt.sessionToken)
			}

			assert.Equal(t, tt.want, result)
			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

func TestTokenService_ValidateClientToken(t *testing.T) {
	tests := []struct {
		name        string
		clientToken string
		setup       func() (*MockTokenModel, *tokenServiceImpl)
		want        bool
	}{
		{
			name:        "正常情况-有效token",
			clientToken: "valid-client-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				validTimestamp := time.Now().Add(-15 * time.Minute).Unix()
				validDiff := 5 * time.Minute // 小于clientTokenDiffTimeout
				mockModel.On("ValidateClientToken", "valid-client-token").Return(
					"test-uid", validTimestamp, validDiff, true,
				)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: true,
		},
		{
			name:        "TokenModel校验失败",
			clientToken: "invalid-client-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				mockModel.On("ValidateClientToken", "invalid-client-token").Return(
					"", int64(0), time.Duration(0), false,
				)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:        "时间差超过限制",
			clientToken: "diff-exceeded-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				validTimestamp := time.Now().Add(-15 * time.Minute).Unix()
				excessiveDiff := clientTokenDiffTimeout + 1*time.Hour // 超过限制
				mockModel.On("ValidateClientToken", "diff-exceeded-token").Return(
					"test-uid", validTimestamp, excessiveDiff, true,
				)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:        "CUID不匹配",
			clientToken: "wrong-cuid-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				validTimestamp := time.Now().Add(-15 * time.Minute).Unix()
				validDiff := 5 * time.Minute
				mockModel.On("ValidateClientToken", "wrong-cuid-token").Return(
					"different-uid", validTimestamp, validDiff, true,
				)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:        "带管道符的UID匹配",
			clientToken: "pipe-uid-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				validTimestamp := time.Now().Add(-15 * time.Minute).Unix()
				validDiff := 5 * time.Minute
				mockModel.On("ValidateClientToken", "pipe-uid-token").Return(
					"test-uid", validTimestamp, validDiff, true,
				)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid|extra-part"),
					mockModel,
				)
				return mockModel, service
			},
			want: true,
		},
		{
			name:        "baseToken过期",
			clientToken: "expired-base-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				expiredTimestamp := time.Now().Add(-baseTokenTimeout - 1*time.Hour).Unix()
				validDiff := 5 * time.Minute
				mockModel.On("ValidateClientToken", "expired-base-token").Return(
					"test-uid", expiredTimestamp, validDiff, true,
				)
				service := newTokenServiceWithMock(
					createTestWebContext(),
					createTestRequestParams("test-uid"),
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
		{
			name:        "service为nil",
			clientToken: "any-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				return &MockTokenModel{}, nil
			},
			want: false,
		},
		{
			name:        "requestParams为nil",
			clientToken: "any-token",
			setup: func() (*MockTokenModel, *tokenServiceImpl) {
				mockModel := &MockTokenModel{}
				service := newTokenServiceWithMock(
					createTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result bool
			if service != nil {
				result = service.ValidateClientToken(tt.clientToken)
			} else {
				var nilService *tokenServiceImpl
				result = nilService.ValidateClientToken(tt.clientToken)
			}

			assert.Equal(t, tt.want, result)
			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

// 使用示例：在 handler 测试中使用 MockTokenService
func TestMockTokenService_InHandlerTest(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("handler测试-成功场景", func(t *testing.T) {
		// 创建自定义的 MockTokenService，只设置需要用到的方法
		mockTokenService := SetupMockTokenServiceCustom()
		mockTokenService.On("GetBaseToken").Return("mock-base-token-123")
		mockTokenService.On("GetSessionToken").Return("mock-session-token-456")
		mockTokenService.On("GetClientToken", "some-base-token").Return("mock-client-token-789")
		mockTokenService.On("ValidateClientToken", "some-token").Return(true)

		// 在你的 handler 测试中，将这个 mock 注入到依赖中
		// 例如：handler := NewYourHandler(mockTokenService)

		// 验证调用
		baseToken := mockTokenService.GetBaseToken()
		assert.Equal(t, "mock-base-token-123", baseToken)

		sessionToken := mockTokenService.GetSessionToken()
		assert.Equal(t, "mock-session-token-456", sessionToken)

		clientToken := mockTokenService.GetClientToken("some-base-token")
		assert.Equal(t, "mock-client-token-789", clientToken)

		isValid := mockTokenService.ValidateClientToken("some-token")
		assert.True(t, isValid)

		// 验证所有期望的方法都被调用了
		mockTokenService.AssertExpectations(t)
	})

	// ========== 示例2：失败场景 ==========
	t.Run("handler测试-失败场景", func(t *testing.T) {
		// 创建自定义的 MockTokenService，只设置需要用到的方法
		mockTokenService := SetupMockTokenServiceCustom()
		mockTokenService.On("ValidateClientToken", "invalid-token").Return(false)
		mockTokenService.On("GetBaseToken").Return("")

		// 测试验证失败的情况
		isValid := mockTokenService.ValidateClientToken("invalid-token")
		assert.False(t, isValid)

		baseToken := mockTokenService.GetBaseToken()
		assert.Empty(t, baseToken)

		mockTokenService.AssertExpectations(t)
	})

	// ========== 示例3：自定义场景 ==========
	t.Run("handler测试-自定义场景", func(t *testing.T) {
		// 创建自定义的 MockTokenService
		mockTokenService := SetupMockTokenServiceCustom()

		// 自定义特定的行为
		mockTokenService.On("ValidateClientToken", "valid-token").Return(true)
		mockTokenService.On("ValidateClientToken", "invalid-token").Return(false)
		mockTokenService.On("GetBaseToken").Return("custom-base-token")

		// 测试特定行为
		assert.True(t, mockTokenService.ValidateClientToken("valid-token"))
		assert.False(t, mockTokenService.ValidateClientToken("invalid-token"))
		assert.Equal(t, "custom-base-token", mockTokenService.GetBaseToken())

		mockTokenService.AssertExpectations(t)
	})
}

// 在实际的 handler 测试文件中的使用示例
/*
// 文件：api/handler/your_handler_test.go

func TestYourHandler_SomeEndpoint(t *testing.T) {
	// 创建 MockTokenService
	mockTokenService := service.SetupMockTokenServiceSuccess()

	// 创建你的 handler，注入 mock
	handler := NewYourHandler(mockTokenService)

	// 创建测试请求
	req := httptest.NewRequest("POST", "/your-endpoint", nil)
	req.Header.Set("Authorization", "Bearer some-token")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行 handler
	handler.YourEndpoint(c)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证 TokenService 的方法被正确调用
	mockTokenService.AssertExpectations(t)
}

// 测试认证失败的场景
func TestYourHandler_SomeEndpoint_AuthFailed(t *testing.T) {
	// 创建失败场景的 MockTokenService
	mockTokenService := service.SetupMockTokenServiceFailed()

	// 或者自定义特定的失败行为
	mockTokenService := service.SetupMockTokenServiceCustom()
	mockTokenService.On("ValidateClientToken", "invalid-token").Return(false)

	handler := NewYourHandler(mockTokenService)

	req := httptest.NewRequest("POST", "/your-endpoint", nil)
	req.Header.Set("Authorization", "Bearer invalid-token")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	handler.YourEndpoint(c)

	// 验证返回认证失败
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	mockTokenService.AssertExpectations(t)
}
*/
