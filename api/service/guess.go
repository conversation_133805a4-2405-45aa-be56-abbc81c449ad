package service

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

const (
	guessImagePrefix = "guess:query-image:"
)

// GuessService 定义猜测服务的接口
type GuessService interface {
	GetGuessImage(query []string) (map[string]string, error)
}

// guessServiceImpl 猜测服务的实现
type guessServiceImpl struct {
	ctx           *gdp.WebContext
	requestParams *entity.RequestParams
	redisModel    model.RedisModel
}

// NewGuessService 创建新的猜测服务实例
func NewGuessService(ctx *gdp.WebContext, requestParams *entity.RequestParams) GuessService {
	if requestParams == nil {
		return (*guessServiceImpl)(nil)
	}
	return &guessServiceImpl{
		ctx:           ctx,
		requestParams: requestParams,
		redisModel:    model.DefaultRedisModel,
	}
}

// GetGuessImage 根据查询词数组获取猜测图片
// query: 查询词数组
// 返回: map[string]string，key为查询词，value为对应的图片URL
func (s *guessServiceImpl) GetGuessImage(query []string) (map[string]string, error) {
	if s == nil || s.requestParams == nil {
		return nil, fmt.Errorf("service not properly initialized")
	}

	if len(query) == 0 {
		return make(map[string]string), nil
	}

	// 构建Redis键数组
	keys := make([]string, len(query))
	for i, q := range query {
		keys[i] = guessImagePrefix + q
	}

	values, err := s.redisModel.MultiGet(s.ctx.StdContext(), keys)
	if err != nil {
		return nil, fmt.Errorf("failed to get values from redis: %w", err)
	}

	// 构建结果map
	result := make(map[string]string)
	for i, value := range values {
		if i < len(query) && value != nil {
			// Redis返回的值可能是interface{}类型，需要转换为string
			if strValue, ok := value.(string); ok && strValue != "" {
				result[query[i]] = strValue
			}
		}
	}

	return result, nil
}
