package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
)

// FileSearchInfo 定义了his 智能化改造使用的文件信息，该信息会被存储到 redis 中。
type FileSearchInfo struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	URL      string `json:"url"`
	MimeType string `json:"mime_type"`
	Size     int64  `json:"size"`
}

type FileSearchService interface {
	SetFileInfo(fileInfo []FileSearchInfo, sessionToken string) error
	GetFileIDs(sessionToken string) (map[string]bool, error)
}

type aiToolFileServiceImpl struct {
	ctx       *gdp.WebContext
	reqParams *entity.RequestParams
}

func NewFileSearchService(ctx *gdp.WebContext) FileSearchService {
	if ctx == nil {
		return &aiToolFileServiceImpl{ctx: ctx, reqParams: nil}
	}
	reqParams := entity.GetRequestParams(ctx)
	return &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}
}

func (s *aiToolFileServiceImpl) SetFileInfo(fileInfo []FileSearchInfo, sessionToken string) error {
	if s == nil || s.ctx == nil {
		return errors.New("failed to initialize AiToolFileService")
	}
	if s.reqParams == nil {
		return errors.New("failed to get reqParams")
	}
	if s.reqParams.UID == "" {
		return errors.New("failed to get uid")
	}
	parts := strings.Split(s.reqParams.UID, "|")
	if len(parts) < 1 {
		return errors.New("failed to get uid")
	}
	key := fmt.Sprintf("rec-ui:%s:%s", parts[0], sessionToken)
	values := make(map[string]interface{}, len(fileInfo))
	for _, info := range fileInfo {
		// 将结构体序列化为JSON字符串
		jsonBytes, err := json.Marshal(info)
		if err != nil {
			return err
		}
		values[info.ID] = string(jsonBytes)
	}
	dumpValues, _ := json.Marshal(values)
	common.DumpData(s.ctx, "2redis2app480", dumpValues)
	err := model.DefaultRedisModel.SetHashMap(s.ctx, key, values, 30*time.Minute)
	if s.reqParams.RealOSBranch == "h0" {
		parts := strings.Split(s.reqParams.HUID, "|")
		if len(parts) < 1 {
			return err
		}
		HWKey := fmt.Sprintf("rec-ui:%s:%s", parts[0], sessionToken)
		err = model.DefaultRedisModel.SetHashMap(s.ctx, HWKey, values, 30*time.Minute)
	}
	return err
}

// GetFileInfoIDs 从redis中获取文件信息
func (s *aiToolFileServiceImpl) GetFileIDs(sessionToken string) (map[string]bool, error) {
	if s == nil || s.ctx == nil {
		return nil, errors.New("failed to initialize AiToolFileService")
	}
	if s.reqParams == nil {
		return nil, errors.New("failed to get reqParams")
	}
	if s.reqParams.UID == "" {
		return nil, errors.New("failed to get uid")
	}
	parts := strings.Split(s.reqParams.UID, "|")
	if len(parts) < 1 {
		return nil, errors.New("failed to get uid")
	}
	key := fmt.Sprintf("rec-ui:%s:%s", parts[0], sessionToken)
	values, err := model.DefaultRedisModel.GetHashMap(s.ctx, key)
	if err != nil {
		return nil, err
	}
	ids := make(map[string]bool, len(values))
	for id := range values {
		ids[id] = true
	}
	return ids, nil
}
