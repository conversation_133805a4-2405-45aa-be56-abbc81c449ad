package service

import (
	"encoding/base64"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/searchbox/go-suggest/api/model" // 导入 model 包
	"icode.baidu.com/baidu/searchbox/go-suggest/library/crypt"
)

const (
	// BucketName BOS Bucket 名称
	BucketName = "rec-ui"
	// EndPoint BOS Endpoint
	EndPoint = "https://bj.bcebos.com" //

	// AesENCKEY AES 加密密钥 (16, 24, or 32 bytes)

	AesENCKEY = "VWQnCMKzIGP0Z3hFHtF4RbKGOQmopJkSZtnckDwM3nQ="
	// AesIV AES 初始化向量 (16 bytes)

	AesIV = "OBQ9FacFwX618YDT6R8HSA=="
)

// UploadInfo 定义了返回给调用者的 STS 相关信息，现在从 model.STSInfo 映射而来
// 注意：这个结构体与 model.STSInfo 字段一致，但位于 service 层，用于解耦
// 如果将来 service 层的返回结构需要与 model 层有所不同，这里可以灵活调整
type UploadInfo struct {
	AccessKeyID     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	PreFixPath      string `json:"preFixPath"`
	BucketName      string `json:"bucketName"`
	Endpoint        string `json:"endpoint"`
}

// ACLOne 定义单个访问控制策略
type ACLOne struct {
	Eid        string   `json:"eid,omitempty"`
	Service    string   `json:"service"`
	Region     string   `json:"region"`
	Effect     string   `json:"effect"`
	Resource   []string `json:"resource"`
	Permission []string `json:"permission"`
}

// ACL 定义完整的访问控制列表
type ACL struct {
	AccessControlList []ACLOne `json:"accessControlList"`
}

// ======================== BosService 默认实现 ========================
type BosServiceImpl struct{}

func NewBosService() *BosServiceImpl {
	return &BosServiceImpl{}
}

func (s *BosServiceImpl) GetSTSInfo(pathPrefix string, expirationSecond int, cuid string) (*UploadInfo, error) {
	path := s.GetBosPath(pathPrefix, cuid)
	acl, err := createACL(path, []string{"WRITE"})
	if err != nil {
		return nil, fmt.Errorf("failed to create ACL: %w", err)
	}

	modelSTSInfo, err := model.GlobalBosModel.GetSTSInfo(expirationSecond, acl)
	if err != nil {
		return nil, fmt.Errorf("failed to get STSInfo from model: %w", err)
	}

	if modelSTSInfo == nil {
		return nil, fmt.Errorf("received nil STSInfo from model")
	}

	return &UploadInfo{
		AccessKeyID:     modelSTSInfo.AccessKeyID,
		SecretAccessKey: modelSTSInfo.SecretAccessKey,
		SessionToken:    modelSTSInfo.SessionToken,
		PreFixPath:      path,
		BucketName:      BucketName,
		Endpoint:        EndPoint,
	}, nil
}

func (s *BosServiceImpl) GetAuthURL(pathPrefix string, expirationSecond int, cuid string, objects []string) (authURLs []string, err error) {
	path := s.GetBosPath(pathPrefix, cuid)
	acl, err := createACL(path, []string{"READ"})
	if err != nil {
		return nil, fmt.Errorf("failed to create ACL: %w", err)
	}
	authURLs, err = model.GlobalBosModel.GetAuthURL(expirationSecond, acl, BucketName, objects)
	if err != nil {
		return nil, fmt.Errorf("failed to get auth URL: %w", err)
	}
	return authURLs, nil
}

func (s *BosServiceImpl) GetObjectMetaInfo(objectName string) (info ObjectMetaInfo, err error) {
	data, err := model.GlobalBosModel.GetObjectInfo(BucketName, objectName)
	if err != nil {
		return info, fmt.Errorf("failed to get object info: %w", err)
	}
	info.Size = data.Size
	return info, nil
}

func (s *BosServiceImpl) GetImageMetaInfo(objectName string) (info ObjectMetaInfo, err error) {
	data, err := model.GlobalBosModel.GetImageInfo(BucketName, objectName)
	if err != nil {
		return info, fmt.Errorf("failed to get image info: %w", err)
	}
	if data.Size == 0 {
		return info, fmt.Errorf("object size is 0")
	}
	if data.Width == 0 || data.Height == 0 {
		return info, fmt.Errorf("image width or height is 0")
	}
	info.Size = data.Size
	info.Width = data.Width
	info.Height = data.Height
	info.MimeType = data.MimeType
	return info, nil
}

func (s *BosServiceImpl) GetBosPath(pathPrefix string, cuid string) (path string) {
	if cuid == "" {
		return ""
	}
	if pathPrefix == "" {
		return ""
	}

	key, _ := base64.StdEncoding.DecodeString(AesENCKEY)
	iv, _ := base64.StdEncoding.DecodeString(AesIV)

	aesCryptor := crypt.NewCryptor(key, iv)
	uk, err := aesCryptor.CBCEncrypt([]byte(cuid))
	if err != nil {
		return ""
	}
	if uk == "" {
		return ""
	}

	path = fmt.Sprintf("%s/%s/", pathPrefix, uk)
	return path
}

func createACL(path string, permission []string) (acl string, err error) {

	resourcePattern := fmt.Sprintf("%s/%s*", BucketName, path)

	aclStruct := ACL{
		AccessControlList: []ACLOne{
			{
				Service:    "bce:bos",
				Region:     "bj",
				Effect:     "Allow",
				Resource:   []string{resourcePattern},
				Permission: permission,
			},
		},
	}

	// 4. 将结构体序列化为JSON字符串
	aclBytes, err := json.Marshal(aclStruct)
	if err != nil {
		return "", fmt.Errorf("failed to marshal ACL to JSON: %w", err)
	}

	acl = string(aclBytes)
	return
}

type ObjectMetaInfo struct {
	Size     int64
	Width    int64
	Height   int64
	MimeType string
}
