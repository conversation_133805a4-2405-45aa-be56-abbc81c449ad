package service

import (
	"context"
	"errors"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

// MockRedisModel 模拟 Redis 操作
type MockRedisModel struct {
	mock.Mock
}

func (m *MockRedisModel) SetHash(ctx context.Context, key, field string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, field, value, expiration)
	return args.Error(0)
}

func (m *MockRedisModel) SetHashMap(ctx context.Context, key string, values map[string]interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, values, expiration)
	return args.Error(0)
}

func (m *MockRedisModel) GetHash(ctx context.Context, key, field string) (string, error) {
	args := m.Called(ctx, key, field)
	return args.String(0), args.Error(1)
}

func (m *MockRedisModel) GetHashMap(ctx context.Context, key string) (map[string]string, error) {
	args := m.Called(ctx, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]string), args.Error(1)
}

func (m *MockRedisModel) MultiGet(ctx context.Context, keys []string) ([]interface{}, error) {
	args := m.Called(ctx, keys)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]interface{}), args.Error(1)
}

// MockFileSearchService 实现了 FileSearchService 接口，用于测试
type MockFileSearchService struct {
	mock.Mock
}

func (m *MockFileSearchService) SetFileInfo(fileInfo []FileSearchInfo, sessionToken string) error {
	args := m.Called(fileInfo, sessionToken)
	return args.Error(0)
}

func (m *MockFileSearchService) GetFileIDs(sessionToken string) (map[string]bool, error) {
	args := m.Called(sessionToken)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]bool), args.Error(1)
}

// createTestWebContext 创建测试用的 WebContext
func createTestWebContext() *gdp.WebContext {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	return gdp.NewWebContext(c)
}

// NewMockFileSearchService 创建一个新的 MockFileSearchService 实例
func NewMockFileSearchService() *MockFileSearchService {
	return &MockFileSearchService{}
}

// SetupMockFileSearchServiceSuccess 设置一个"成功"场景的 MockFileSearchService
func SetupMockFileSearchServiceSuccess() *MockFileSearchService {
	mockService := NewMockFileSearchService()

	// 设置成功场景
	mockService.On("SetFileInfo", mock.AnythingOfType("[]service.FileSearchInfo"), mock.AnythingOfType("string")).Return(nil)

	return mockService
}

// SetupMockFileSearchServiceFailed 设置一个"失败"场景的 MockFileSearchService
func SetupMockFileSearchServiceFailed() *MockFileSearchService {
	mockService := NewMockFileSearchService()

	// 设置失败场景
	mockService.On("SetFileInfo", mock.AnythingOfType("[]service.FileSearchInfo"), mock.AnythingOfType("string")).Return(assert.AnError)

	return mockService
}

// SetupMockFileSearchServiceCustom 创建一个可以自定义行为的 MockFileSearchService
func SetupMockFileSearchServiceCustom() *MockFileSearchService {
	return NewMockFileSearchService()
}

// ============ 以下是针对具体实现的单元测试 ============

func TestNewFileSearchService(t *testing.T) {
	t.Run("创建服务_ctx为nil", func(t *testing.T) {
		service := NewFileSearchService(nil)
		assert.NotNil(t, service)

		// 转换为具体实现类型进行验证
		impl, ok := service.(*aiToolFileServiceImpl)
		assert.True(t, ok)
		assert.Nil(t, impl.ctx)
		assert.Nil(t, impl.reqParams)
	})

	t.Run("创建服务_ctx不为nil", func(t *testing.T) {
		ctx := createTestWebContext()

		// 直接调用构造函数，因为GetRequestParams是内部调用
		service := NewFileSearchService(ctx)
		assert.NotNil(t, service)

		impl, ok := service.(*aiToolFileServiceImpl)
		assert.True(t, ok)
		assert.Equal(t, ctx, impl.ctx)
		// 在测试环境中，GetRequestParams可能返回nil，这是正常的
		// 我们只验证服务被正确创建即可
		assert.NotNil(t, impl) // 验证实现不为nil即可
	})
}

func TestAiToolFileServiceImpl_SetFileInfo(t *testing.T) {
	// 保存原始的 DefaultRedisModel
	originalRedisModel := model.DefaultRedisModel
	defer func() {
		model.DefaultRedisModel = originalRedisModel
	}()

	t.Run("设置文件信息_服务未初始化", func(t *testing.T) {
		var service *aiToolFileServiceImpl
		err := service.SetFileInfo([]FileSearchInfo{}, "token")
		assert.Error(t, err)
		assert.Equal(t, "failed to initialize AiToolFileService", err.Error())
	})

	t.Run("设置文件信息_ctx为nil", func(t *testing.T) {
		service := &aiToolFileServiceImpl{ctx: nil}
		err := service.SetFileInfo([]FileSearchInfo{}, "token")
		assert.Error(t, err)
		assert.Equal(t, "failed to initialize AiToolFileService", err.Error())
	})

	t.Run("设置文件信息_reqParams为nil", func(t *testing.T) {
		ctx := createTestWebContext()
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: nil}
		err := service.SetFileInfo([]FileSearchInfo{}, "token")
		assert.Error(t, err)
		assert.Equal(t, "failed to get reqParams", err.Error())
	})

	t.Run("设置文件信息_UID为空", func(t *testing.T) {
		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: ""}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}
		err := service.SetFileInfo([]FileSearchInfo{}, "token")
		assert.Error(t, err)
		assert.Equal(t, "failed to get uid", err.Error())
	})

	t.Run("设置文件信息_UID格式错误", func(t *testing.T) {
		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: ""}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}
		err := service.SetFileInfo([]FileSearchInfo{}, "token")
		assert.Error(t, err)
		assert.Equal(t, "failed to get uid", err.Error())
	})

	t.Run("设置文件信息_Redis操作失败", func(t *testing.T) {
		// Mock Redis
		mockRedis := &MockRedisModel{}
		model.DefaultRedisModel = mockRedis

		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: "user123|other"}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}

		fileInfos := []FileSearchInfo{
			{ID: "file1", Name: "test.txt", Type: "text", URL: "http://example.com", MimeType: "text/plain", Size: 100},
		}

		expectedKey := "rec-ui:user123:token123"
		expectedValues := map[string]interface{}{
			"file1": `{"id":"file1","name":"test.txt","type":"text","url":"http://example.com","mime_type":"text/plain","size":100}`,
		}

		mockRedis.On("SetHashMap", ctx, expectedKey, expectedValues, 30*time.Minute).Return(errors.New("redis error"))

		err := service.SetFileInfo(fileInfos, "token123")
		assert.Error(t, err)
		assert.Equal(t, "redis error", err.Error())
		mockRedis.AssertExpectations(t)
	})

	t.Run("设置文件信息_成功", func(t *testing.T) {
		// Mock Redis
		mockRedis := &MockRedisModel{}
		model.DefaultRedisModel = mockRedis

		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: "user123|other"}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}

		fileInfos := []FileSearchInfo{
			{ID: "file1", Name: "test.txt", Type: "text", URL: "http://example.com", MimeType: "text/plain", Size: 100},
			{ID: "file2", Name: "test.pdf", Type: "document", URL: "http://example.com/doc", MimeType: "application/pdf", Size: 200},
		}

		expectedKey := "rec-ui:user123:token123"
		expectedValues := map[string]interface{}{
			"file1": `{"id":"file1","name":"test.txt","type":"text","url":"http://example.com","mime_type":"text/plain","size":100}`,
			"file2": `{"id":"file2","name":"test.pdf","type":"document","url":"http://example.com/doc","mime_type":"application/pdf","size":200}`,
		}

		mockRedis.On("SetHashMap", ctx, expectedKey, expectedValues, 30*time.Minute).Return(nil)

		err := service.SetFileInfo(fileInfos, "token123")
		assert.NoError(t, err)
		mockRedis.AssertExpectations(t)
	})

	t.Run("设置文件信息_华为鸿蒙系统_成功", func(t *testing.T) {
		// Mock Redis
		mockRedis := &MockRedisModel{}
		model.DefaultRedisModel = mockRedis

		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: "user123|other", RealOSBranch: "h0", HUID: "user123HW|H00"}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}

		fileInfos := []FileSearchInfo{
			{ID: "file1", Name: "test.txt", Type: "text", URL: "http://example.com", MimeType: "text/plain", Size: 100},
			{ID: "file2", Name: "test.pdf", Type: "document", URL: "http://example.com/doc", MimeType: "application/pdf", Size: 200},
		}

		expectedKey := "rec-ui:user123:token123"
		expectedValues := map[string]interface{}{
			"file1": `{"id":"file1","name":"test.txt","type":"text","url":"http://example.com","mime_type":"text/plain","size":100}`,
			"file2": `{"id":"file2","name":"test.pdf","type":"document","url":"http://example.com/doc","mime_type":"application/pdf","size":200}`,
		}

		mockRedis.On("SetHashMap", ctx, expectedKey, expectedValues, 30*time.Minute).Return(nil)
		expectedHWKey := "rec-ui:user123HW:token123"
		expectedHWValues := map[string]interface{}{
			"file1": `{"id":"file1","name":"test.txt","type":"text","url":"http://example.com","mime_type":"text/plain","size":100}`,
			"file2": `{"id":"file2","name":"test.pdf","type":"document","url":"http://example.com/doc","mime_type":"application/pdf","size":200}`,
		}
		mockRedis.On("SetHashMap", ctx, expectedHWKey, expectedHWValues, 30*time.Minute).Return(nil)
		err := service.SetFileInfo(fileInfos, "token123")
		assert.NoError(t, err)
		mockRedis.AssertExpectations(t)
	})
}

func TestAiToolFileServiceImpl_GetFileIDs(t *testing.T) {
	// 保存原始的 DefaultRedisModel
	originalRedisModel := model.DefaultRedisModel
	defer func() {
		model.DefaultRedisModel = originalRedisModel
	}()

	t.Run("获取文件ID_服务未初始化", func(t *testing.T) {
		var service *aiToolFileServiceImpl
		ids, err := service.GetFileIDs("token")
		assert.Nil(t, ids)
		assert.Error(t, err)
		assert.Equal(t, "failed to initialize AiToolFileService", err.Error())
	})

	t.Run("获取文件ID_ctx为nil", func(t *testing.T) {
		service := &aiToolFileServiceImpl{ctx: nil}
		ids, err := service.GetFileIDs("token")
		assert.Nil(t, ids)
		assert.Error(t, err)
		assert.Equal(t, "failed to initialize AiToolFileService", err.Error())
	})

	t.Run("获取文件ID_reqParams为nil", func(t *testing.T) {
		ctx := createTestWebContext()
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: nil}
		ids, err := service.GetFileIDs("token")
		assert.Nil(t, ids)
		assert.Error(t, err)
		assert.Equal(t, "failed to get reqParams", err.Error())
	})

	t.Run("获取文件ID_UID为空", func(t *testing.T) {
		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: ""}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}
		ids, err := service.GetFileIDs("token")
		assert.Nil(t, ids)
		assert.Error(t, err)
		assert.Equal(t, "failed to get uid", err.Error())
	})

	t.Run("获取文件ID_UID格式错误", func(t *testing.T) {
		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: ""}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}
		ids, err := service.GetFileIDs("token")
		assert.Nil(t, ids)
		assert.Error(t, err)
		assert.Equal(t, "failed to get uid", err.Error())
	})

	t.Run("获取文件ID_Redis操作失败", func(t *testing.T) {
		// Mock Redis
		mockRedis := &MockRedisModel{}
		model.DefaultRedisModel = mockRedis

		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: "user123|other"}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}

		expectedKey := "rec-ui:user123:token123"
		mockRedis.On("GetHashMap", ctx, expectedKey).Return(nil, errors.New("redis error"))

		ids, err := service.GetFileIDs("token123")
		assert.Nil(t, ids)
		assert.Error(t, err)
		assert.Equal(t, "redis error", err.Error())
		mockRedis.AssertExpectations(t)
	})

	t.Run("获取文件ID_成功_空结果", func(t *testing.T) {
		// Mock Redis
		mockRedis := &MockRedisModel{}
		model.DefaultRedisModel = mockRedis

		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: "user123|other"}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}

		expectedKey := "rec-ui:user123:token123"
		mockRedis.On("GetHashMap", ctx, expectedKey).Return(map[string]string{}, nil)

		ids, err := service.GetFileIDs("token123")
		assert.NoError(t, err)
		assert.NotNil(t, ids)
		assert.Equal(t, 0, len(ids))
		mockRedis.AssertExpectations(t)
	})

	t.Run("获取文件ID_成功_有数据", func(t *testing.T) {
		// Mock Redis
		mockRedis := &MockRedisModel{}
		model.DefaultRedisModel = mockRedis

		ctx := createTestWebContext()
		reqParams := &entity.RequestParams{UID: "user123|other"}
		service := &aiToolFileServiceImpl{ctx: ctx, reqParams: reqParams}

		expectedKey := "rec-ui:user123:token123"
		redisData := map[string]string{
			"file1": `{"id":"file1","name":"test1.txt","type":"text","url":"http://example.com/1","mime_type":"text/plain","size":100}`,
			"file2": `{"id":"file2","name":"test2.pdf","type":"document","url":"http://example.com/2","mime_type":"application/pdf","size":200}`,
			"file3": `{"id":"file3","name":"test3.jpg","type":"image","url":"http://example.com/3","mime_type":"image/jpeg","size":300}`,
		}
		mockRedis.On("GetHashMap", ctx, expectedKey).Return(redisData, nil)

		ids, err := service.GetFileIDs("token123")
		assert.NoError(t, err)
		assert.NotNil(t, ids)
		assert.Equal(t, 3, len(ids))
		assert.True(t, ids["file1"])
		assert.True(t, ids["file2"])
		assert.True(t, ids["file3"])
		mockRedis.AssertExpectations(t)
	})
}

// 使用示例：在 handler 测试中使用 MockFileSearchService
func TestMockFileSearchService_Usage(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("handler测试-成功场景", func(t *testing.T) {
		mockService := SetupMockFileSearchServiceSuccess()

		// 准备测试数据
		fileInfos := []FileSearchInfo{
			{
				ID:       "file1",
				Name:     "test1.jpg",
				Type:     "image",
				URL:      "https://example.com/file1.jpg",
				MimeType: "image/jpeg",
				Size:     1024,
			},
			{
				ID:       "file2",
				Name:     "test2.pdf",
				Type:     "document",
				URL:      "https://example.com/file2.pdf",
				MimeType: "application/pdf",
				Size:     2048,
			},
		}

		err := mockService.SetFileInfo(fileInfos, "test-session-token")

		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})

	// ========== 示例2：失败场景 ==========
	t.Run("handler测试-失败场景", func(t *testing.T) {
		mockService := SetupMockFileSearchServiceFailed()

		fileInfos := []FileSearchInfo{
			{
				ID:       "file1",
				Name:     "test1.jpg",
				Type:     "image",
				URL:      "https://example.com/file1.jpg",
				MimeType: "image/jpeg",
				Size:     1024,
			},
		}

		err := mockService.SetFileInfo(fileInfos, "invalid-session-token")

		assert.Error(t, err)
		mockService.AssertExpectations(t)
	})

	// ========== 示例3：自定义场景 ==========
	t.Run("handler测试-自定义场景", func(t *testing.T) {
		mockService := SetupMockFileSearchServiceCustom()

		// 自定义特定的行为 - 针对特定文件名返回错误
		fileInfosWithError := []FileSearchInfo{
			{
				ID:       "error-file",
				Name:     "invalid.exe",
				Type:     "executable",
				URL:      "https://example.com/invalid.exe",
				MimeType: "application/octet-stream",
				Size:     1024,
			},
		}

		fileInfosSuccess := []FileSearchInfo{
			{
				ID:       "valid-file",
				Name:     "document.pdf",
				Type:     "document",
				URL:      "https://example.com/document.pdf",
				MimeType: "application/pdf",
				Size:     2048,
			},
		}

		// 设置不同的行为
		mockService.On("SetFileInfo", fileInfosWithError, "test-session").Return(assert.AnError)
		mockService.On("SetFileInfo", fileInfosSuccess, "test-session").Return(nil)

		// 测试错误情况
		err1 := mockService.SetFileInfo(fileInfosWithError, "test-session")
		assert.Error(t, err1)

		// 测试成功情况
		err2 := mockService.SetFileInfo(fileInfosSuccess, "test-session")
		assert.NoError(t, err2)

		mockService.AssertExpectations(t)
	})

	// ========== 示例4：测试GetFileIDs方法 ==========
	t.Run("handler测试-GetFileIDs成功场景", func(t *testing.T) {
		mockService := SetupMockFileSearchServiceCustom()

		expectedIDs := map[string]bool{
			"file1": true,
			"file2": true,
			"file3": true,
		}

		mockService.On("GetFileIDs", "test-session-token").Return(expectedIDs, nil)

		ids, err := mockService.GetFileIDs("test-session-token")

		assert.NoError(t, err)
		assert.Equal(t, expectedIDs, ids)
		mockService.AssertExpectations(t)
	})

	t.Run("handler测试-GetFileIDs失败场景", func(t *testing.T) {
		mockService := SetupMockFileSearchServiceCustom()

		mockService.On("GetFileIDs", "invalid-token").Return(nil, assert.AnError)

		ids, err := mockService.GetFileIDs("invalid-token")

		assert.Error(t, err)
		assert.Nil(t, ids)
		mockService.AssertExpectations(t)
	})
}

/*
// 在实际的 handler 测试文件中的使用示例
// 文件：api/handler/filesearch_handler_test.go

func TestFileSearchHandler_SetFileInfo(t *testing.T) {
	// 创建 MockFileSearchService
	mockService := service.SetupMockFileSearchServiceSuccess()

	// 创建你的 handler，注入 mock
	handler := NewFileSearchHandler(mockService)

	// 准备请求数据
	fileInfos := []service.FileSearchInfo{
		{
			ID:       "file1",
			Name:     "test.jpg",
			Type:     "image",
			URL:      "https://example.com/test.jpg",
			MimeType: "image/jpeg",
			Size:     1024,
		},
	}

	requestBody, _ := json.Marshal(map[string]interface{}{
		"fileInfo":     fileInfos,
		"sessionToken": "test-session-token",
	})

	// 创建测试请求
	req := httptest.NewRequest("POST", "/filesearch/set-info", strings.NewReader(string(requestBody)))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行 handler
	handler.SetFileInfo(c)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证 FileSearchService 的方法被正确调用
	mockService.AssertExpectations(t)
}

// 测试设置文件信息失败的场景
func TestFileSearchHandler_SetFileInfo_Failed(t *testing.T) {
	// 创建失败场景的 MockFileSearchService
	mockService := service.SetupMockFileSearchServiceFailed()

	handler := NewFileSearchHandler(mockService)

	fileInfos := []service.FileSearchInfo{
		{
			ID:       "file1",
			Name:     "test.jpg",
			Type:     "image",
			URL:      "https://example.com/test.jpg",
			MimeType: "image/jpeg",
			Size:     1024,
		},
	}

	requestBody, _ := json.Marshal(map[string]interface{}{
		"fileInfo":     fileInfos,
		"sessionToken": "invalid-session-token",
	})

	req := httptest.NewRequest("POST", "/filesearch/set-info", strings.NewReader(string(requestBody)))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	handler.SetFileInfo(c)

	// 验证返回错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	mockService.AssertExpectations(t)
}
*/
