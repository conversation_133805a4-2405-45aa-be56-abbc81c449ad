package service

import (
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	apimock "icode.baidu.com/baidu/searchbox/go-suggest/api/mock"
)

// ======================== 测试工具函数 ========================

// newGuessServiceWithMock 创建带有Mock RedisModel的GuessService实例
func newGuessServiceWithMock(ctx *gdp.WebContext, requestParams *entity.RequestParams, mockRedisModel *apimock.MockRedisModel) *guessServiceImpl {
	return &guessServiceImpl{
		ctx:           ctx,
		requestParams: requestParams,
		redisModel:    mockRedisModel,
	}
}

// ======================== 单元测试 ========================

func TestNewGuessService(t *testing.T) {
	tests := []struct {
		name          string
		ctx           *gdp.WebContext
		requestParams *entity.RequestParams
		wantNil       bool
	}{
		{
			name:          "正常情况",
			ctx:           apimock.CreateTestWebContext(),
			requestParams: apimock.CreateTestRequestParams(),
			wantNil:       false,
		},
		{
			name:          "requestParams为nil",
			ctx:           apimock.CreateTestWebContext(),
			requestParams: nil,
			wantNil:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewGuessService(tt.ctx, tt.requestParams)
			if tt.wantNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.IsType(t, &guessServiceImpl{}, result)
			}
		})
	}
}

func TestGuessService_GetGuessImage(t *testing.T) {
	tests := []struct {
		name    string
		query   []string
		setup   func() (*apimock.MockRedisModel, *guessServiceImpl)
		want    map[string]string
		wantErr bool
	}{
		{
			name:  "正常情况-单个查询",
			query: []string{"苹果"},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				mockModel := apimock.SetupMockRedisModelCustom()
				mockModel.On("MultiGet", mock.Anything, []string{"guess:query-image:苹果"}).Return(
					[]interface{}{"https://example.com/apple.jpg"}, nil)
				service := newGuessServiceWithMock(
					apimock.CreateTestWebContext(),
					apimock.CreateTestRequestParams(),
					mockModel,
				)
				return mockModel, service
			},
			want: map[string]string{
				"苹果": "https://example.com/apple.jpg",
			},
			wantErr: false,
		},
		{
			name:  "正常情况-多个查询",
			query: []string{"苹果", "香蕉", "橘子"},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				mockModel := apimock.SetupMockRedisModelCustom()
				expectedKeys := []string{
					"guess:query-image:苹果",
					"guess:query-image:香蕉",
					"guess:query-image:橘子",
				}
				mockModel.On("MultiGet", mock.Anything, expectedKeys).Return(
					[]interface{}{
						"https://example.com/apple.jpg",
						"https://example.com/banana.jpg",
						"https://example.com/orange.jpg",
					}, nil)
				service := newGuessServiceWithMock(
					apimock.CreateTestWebContext(),
					apimock.CreateTestRequestParams(),
					mockModel,
				)
				return mockModel, service
			},
			want: map[string]string{
				"苹果": "https://example.com/apple.jpg",
				"香蕉": "https://example.com/banana.jpg",
				"橘子": "https://example.com/orange.jpg",
			},
			wantErr: false,
		},
		{
			name:  "部分查询有结果",
			query: []string{"苹果", "香蕉", "橘子"},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				mockModel := apimock.SetupMockRedisModelCustom()
				expectedKeys := []string{
					"guess:query-image:苹果",
					"guess:query-image:香蕉",
					"guess:query-image:橘子",
				}
				mockModel.On("MultiGet", mock.Anything, expectedKeys).Return(
					[]interface{}{
						"https://example.com/apple.jpg",
						nil, // 香蕉没有结果
						"https://example.com/orange.jpg",
					}, nil)
				service := newGuessServiceWithMock(
					apimock.CreateTestWebContext(),
					apimock.CreateTestRequestParams(),
					mockModel,
				)
				return mockModel, service
			},
			want: map[string]string{
				"苹果": "https://example.com/apple.jpg",
				"橘子": "https://example.com/orange.jpg",
			},
			wantErr: false,
		},
		{
			name:  "空查询数组",
			query: []string{},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				mockModel := apimock.SetupMockRedisModelCustom()
				service := newGuessServiceWithMock(
					apimock.CreateTestWebContext(),
					apimock.CreateTestRequestParams(),
					mockModel,
				)
				return mockModel, service
			},
			want:    map[string]string{},
			wantErr: false,
		},
		{
			name:  "Redis错误",
			query: []string{"苹果"},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				mockModel := apimock.SetupMockRedisModelCustom()
				mockModel.On("MultiGet", mock.Anything, []string{"guess:query-image:苹果"}).Return(
					nil, errors.New("redis connection failed"))
				service := newGuessServiceWithMock(
					apimock.CreateTestWebContext(),
					apimock.CreateTestRequestParams(),
					mockModel,
				)
				return mockModel, service
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:  "service为nil",
			query: []string{"苹果"},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				return apimock.SetupMockRedisModelCustom(), nil
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:  "requestParams为nil",
			query: []string{"苹果"},
			setup: func() (*apimock.MockRedisModel, *guessServiceImpl) {
				mockModel := apimock.SetupMockRedisModelCustom()
				service := newGuessServiceWithMock(
					apimock.CreateTestWebContext(),
					nil,
					mockModel,
				)
				return mockModel, service
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockModel, service := tt.setup()

			var result map[string]string
			var err error
			if service != nil {
				result, err = service.GetGuessImage(tt.query)
			} else {
				var nilService *guessServiceImpl
				result, err = nilService.GetGuessImage(tt.query)
			}

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, result)
			}

			if mockModel != nil {
				mockModel.AssertExpectations(t)
			}
		})
	}
}

// TestGuessService_GetGuessImage_EdgeCases 测试边界情况
func TestGuessService_GetGuessImage_EdgeCases(t *testing.T) {
	t.Run("Redis返回空字符串", func(t *testing.T) {
		mockModel := apimock.SetupMockRedisModelCustom()
		mockModel.On("MultiGet", mock.Anything, []string{"guess:query-image:苹果"}).Return(
			[]interface{}{""}, nil) // 返回空字符串
		service := newGuessServiceWithMock(
			apimock.CreateTestWebContext(),
			apimock.CreateTestRequestParams(),
			mockModel,
		)

		result, err := service.GetGuessImage([]string{"苹果"})

		assert.NoError(t, err)
		assert.Empty(t, result) // 空字符串应该被过滤掉
		mockModel.AssertExpectations(t)
	})

	t.Run("Redis返回非字符串类型", func(t *testing.T) {
		mockModel := apimock.SetupMockRedisModelCustom()
		mockModel.On("MultiGet", mock.Anything, []string{"guess:query-image:苹果"}).Return(
			[]interface{}{123}, nil) // 返回数字而不是字符串
		service := newGuessServiceWithMock(
			apimock.CreateTestWebContext(),
			apimock.CreateTestRequestParams(),
			mockModel,
		)

		result, err := service.GetGuessImage([]string{"苹果"})

		assert.NoError(t, err)
		assert.Empty(t, result) // 非字符串类型应该被过滤掉
		mockModel.AssertExpectations(t)
	})

	t.Run("查询词包含特殊字符", func(t *testing.T) {
		mockModel := apimock.SetupMockRedisModelCustom()
		specialQuery := "苹果:特殊字符"
		expectedKey := "guess:query-image:" + specialQuery
		mockModel.On("MultiGet", mock.Anything, []string{expectedKey}).Return(
			[]interface{}{"https://example.com/special.jpg"}, nil)
		service := newGuessServiceWithMock(
			apimock.CreateTestWebContext(),
			apimock.CreateTestRequestParams(),
			mockModel,
		)

		result, err := service.GetGuessImage([]string{specialQuery})

		assert.NoError(t, err)
		assert.Equal(t, map[string]string{specialQuery: "https://example.com/special.jpg"}, result)
		mockModel.AssertExpectations(t)
	})

	t.Run("大量查询词", func(t *testing.T) {
		mockModel := apimock.SetupMockRedisModelCustom()

		// 创建10个查询词进行测试
		queries := make([]string, 10)
		expectedKeys := make([]string, 10)
		expectedValues := make([]interface{}, 10)
		expectedResult := make(map[string]string)

		for i := 0; i < 10; i++ {
			query := fmt.Sprintf("查询词%d", i)
			queries[i] = query
			expectedKeys[i] = "guess:query-image:" + query
			expectedValues[i] = fmt.Sprintf("https://example.com/image%d.jpg", i)
			expectedResult[query] = expectedValues[i].(string)
		}

		mockModel.On("MultiGet", mock.Anything, expectedKeys).Return(expectedValues, nil)
		service := newGuessServiceWithMock(
			apimock.CreateTestWebContext(),
			apimock.CreateTestRequestParams(),
			mockModel,
		)

		result, err := service.GetGuessImage(queries)

		assert.NoError(t, err)
		assert.Equal(t, expectedResult, result)
		assert.Len(t, result, 10)
		mockModel.AssertExpectations(t)
	})
}

// TestMockGuessService_Usage 使用示例：在 handler 测试中使用 MockGuessService
func TestMockGuessService_Usage(t *testing.T) {
	// ========== 示例1：成功场景 ==========
	t.Run("handler测试-成功场景", func(t *testing.T) {
		mockService := apimock.SetupMockGuessServiceSuccess()

		// 测试获取图片
		queries := []string{"苹果", "香蕉", "橘子"}
		result, err := mockService.GetGuessImage(queries)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 3)
		assert.Equal(t, "https://example.com/apple.jpg", result["苹果"])
		assert.Equal(t, "https://example.com/banana.jpg", result["香蕉"])
		assert.Equal(t, "https://example.com/orange.jpg", result["橘子"])

		mockService.AssertExpectations(t)
	})

	// ========== 示例2：失败场景 ==========
	t.Run("handler测试-失败场景", func(t *testing.T) {
		mockService := apimock.SetupMockGuessServiceFailed()

		// 测试Redis连接失败
		queries := []string{"苹果"}
		result, err := mockService.GetGuessImage(queries)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "redis connection failed")

		mockService.AssertExpectations(t)
	})

	// ========== 示例3：自定义场景 ==========
	t.Run("handler测试-自定义场景", func(t *testing.T) {
		mockService := apimock.SetupMockGuessServiceCustom()

		// 自定义特定的行为
		mockService.On("GetGuessImage", []string{"存在的查询"}).Return(
			map[string]string{"存在的查询": "https://example.com/exists.jpg"}, nil)
		mockService.On("GetGuessImage", []string{"不存在的查询"}).Return(
			map[string]string{}, nil)

		// 测试存在的查询
		result1, err1 := mockService.GetGuessImage([]string{"存在的查询"})
		assert.NoError(t, err1)
		assert.Len(t, result1, 1)
		assert.Equal(t, "https://example.com/exists.jpg", result1["存在的查询"])

		// 测试不存在的查询
		result2, err2 := mockService.GetGuessImage([]string{"不存在的查询"})
		assert.NoError(t, err2)
		assert.Empty(t, result2)

		mockService.AssertExpectations(t)
	})
}

// TestGuessService_Integration 集成测试示例
func TestGuessService_Integration(t *testing.T) {
	t.Run("完整流程测试", func(t *testing.T) {
		// 创建真实的WebContext和RequestParams
		ctx := apimock.CreateTestWebContext()
		requestParams := apimock.CreateTestRequestParams()

		// 创建Mock RedisModel
		mockRedisModel := apimock.SetupMockRedisModelCustom()

		// 设置期望的调用
		queries := []string{"苹果", "香蕉"}
		expectedKeys := []string{"guess:query-image:苹果", "guess:query-image:香蕉"}
		expectedValues := []interface{}{"https://example.com/apple.jpg", "https://example.com/banana.jpg"}

		mockRedisModel.On("MultiGet", mock.Anything, expectedKeys).Return(expectedValues, nil)

		// 创建service实例
		service := newGuessServiceWithMock(ctx, requestParams, mockRedisModel)

		// 执行测试
		result, err := service.GetGuessImage(queries)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)
		assert.Equal(t, "https://example.com/apple.jpg", result["苹果"])
		assert.Equal(t, "https://example.com/banana.jpg", result["香蕉"])

		// 验证Mock调用
		mockRedisModel.AssertExpectations(t)
	})
}
