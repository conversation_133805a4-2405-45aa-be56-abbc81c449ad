package service

import (
	"fmt"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

// RiskResult 风控检查结果
type RiskResultItem struct {
	ID      string          `json:"id"`
	Code    entity.RiskCode `json:"code"`
	Message string          `json:"message"`
}

type RiskResult map[FileID]RiskResultItem

type RiskRequest []RiskRequestFile

type FileID string

type RiskRequestFile struct {
	ID          string
	Name        string
	URL         string
	Type        string
	Size        int64
	MimeType    string
	ImageWidth  int64
	ImageHeight int64
}

// RiskService 风控服务接口
type RiskService interface {
	// CheckRisk 检查风控结果
	CheckRisk(files []RiskRequestFile) (RiskResult, error)
}

// DefaultRiskService 默认风控服务实现
type DefaultRiskService struct {
	ctx       *gdp.WebContext
	riskModel model.RiskModel
}

// NewRiskService 创建风控服务实例
func NewRiskService(ctx *gdp.WebContext) RiskService {
	return &DefaultRiskService{
		ctx:       ctx,
		riskModel: model.NewDefaultRiskModel(ctx),
	}
}

// CheckRisk 检查风控结果
func (s *DefaultRiskService) CheckRisk(files []RiskRequestFile) (RiskResult, error) {
	req := &model.RiskRequest{
		File: make([]model.RiskRequestFile, 0, len(files)),
	}
	for _, file := range files {
		req.File = append(req.File, model.RiskRequestFile{
			ID:          file.ID,
			Name:        file.Name,
			URL:         file.URL,
			Type:        file.Type,
			Size:        file.Size,
			MimeTypes:   file.MimeType,
			ImageWidth:  file.ImageWidth,
			ImageHeight: file.ImageHeight,
		})
	}
	riskModelResult, err := s.riskModel.GetRiskResult(req)
	if err != nil {
		return nil, err
	}
	if riskModelResult.Code != 0 {
		return nil, fmt.Errorf("APP480 risk check failed: %d", riskModelResult.Code)
	}
	riskResult := make(RiskResult)
	for _, file := range files {

		// 如果没有命中抽样配置，则使用真实的风控结果
		item, ok := riskModelResult.DataMap[file.ID]
		if ok {
			riskResult[FileID(file.ID)] = RiskResultItem{
				ID:      file.ID,
				Code:    RiskModelCodeConvertToRiskCode(item.Code),
				Message: item.Message,
			}
		} else {
			riskResult[FileID(file.ID)] = RiskResultItem{
				ID:      file.ID,
				Code:    entity.RiskCodeFailed,
				Message: "file not found",
			}
		}
	}

	return riskResult, nil
}

// RiskModelCodeConvertToRiskCode 将model层的风控结果码转换为服务层的风控结果码
func RiskModelCodeConvertToRiskCode(code model.RiskModelResponseCode) entity.RiskCode {
	switch code {
	case model.RiskModelResponseCodeSuccess:
		return entity.RiskCodeSuccess
	case model.RiskModelResponseCodeFailed, model.RiskModelResponseCodeSizeTooLarge,
		model.RiskModelResponseCodeFileNameRisk, model.RiskModelResponseCodeContentRisk:
		return entity.RiskCodeFailed
	default:
		return entity.RiskCodeFailed
	}
}
