package service

import (
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

const (
	baseTokenTimeout       = 30 * time.Minute
	clientTokenDiffTimeout = 30 * time.Minute
)

type TokenService interface {
	GetBaseToken() string
	GetSessionToken() string
	GetClientToken(baseToken string) string
	ValidateBaseToken(baseToken string) bool
	ValidateSessionToken(sessionToken string) bool
	ValidateClientToken(clientToken string) bool
}

type tokenServiceImpl struct {
	ctx           *gdp.WebContext
	requestParams *entity.RequestParams
	tokenModel    model.TokenModel
}

func NewTokenService(ctx *gdp.WebContext, requestParams *entity.RequestParams) TokenService {
	if requestParams == nil {
		return (*tokenServiceImpl)(nil)
	}
	return &tokenServiceImpl{ctx: ctx, requestParams: requestParams, tokenModel: model.NewTokenDefaultModel(ctx, requestParams)}
}

func (s *tokenServiceImpl) GetBaseToken() string {
	if s == nil || s.requestParams == nil {
		return ""
	}
	if s.requestParams.RealOSBranch == "h0" && s.requestParams.HUID != "" {
		return s.tokenModel.GenerateBaseToken(s.requestParams.HUID)
	}
	return s.tokenModel.GenerateBaseToken(s.requestParams.UID)

}

func (s *tokenServiceImpl) GetSessionToken() string {
	if s == nil || s.requestParams == nil {
		return ""
	}
	return s.tokenModel.GenerateSessionToken(s.ctx.GetLogID())
}

func (s *tokenServiceImpl) GetClientToken(baseToken string) string {
	if s == nil || s.requestParams == nil {
		return ""
	}
	return s.tokenModel.GenerateClientToken(baseToken, s.requestParams.UID)
}

func (s *tokenServiceImpl) ValidateBaseToken(baseToken string) bool {
	if s == nil || s.requestParams == nil {
		return false
	}
	tokenTs, ok := s.tokenModel.ValidateBaseToken(baseToken, s.requestParams.UID)
	if !ok {
		return false
	}
	if time.Since(time.Unix(tokenTs, 0)) > baseTokenTimeout {
		return false
	}
	return true
}

func (s *tokenServiceImpl) ValidateSessionToken(sessionToken string) bool {
	if s == nil || s.requestParams == nil {
		return false
	}
	return s.tokenModel.ValidateSessionToken(sessionToken)
}

func (s *tokenServiceImpl) ValidateClientToken(clientToken string) bool {
	if s == nil || s.requestParams == nil {
		return false
	}
	cuid, baseTokenTs, diff, ok := s.tokenModel.ValidateClientToken(clientToken)
	if !ok {
		return false
	}
	if diff > clientTokenDiffTimeout {
		return false
	}
	deviceID := s.requestParams.UID
	parts := strings.Split(deviceID, "|")

	if len(parts) == 2 {
		deviceID = parts[0]
	}
	if cuid != deviceID {
		if s.requestParams.RealOSBranch == "h0" {
			parts := strings.Split(s.requestParams.HUID, "|")
			if len(parts) == 2 {
				deviceID = parts[0]
			}
			if cuid != deviceID {
				return false
			}
		} else {
			return false
		}
	}
	if time.Since(time.Unix(baseTokenTs, 0)) > baseTokenTimeout {
		return false
	}
	return true
}
