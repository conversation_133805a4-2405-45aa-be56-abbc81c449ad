package service

import (
	"fmt"
	"math/rand"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/gdp"
	constants "icode.baidu.com/baidu/searchbox/go-suggest/api/const"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

// FileSearchBusinessService 定义文件搜索业务服务接口
type FileSearchBusinessService interface {
	// ValidateFiles 文件验证业务逻辑
	ValidateFiles(req *entity.FileSearchValidateRequest, sessionToken string, uid string) (*entity.FileSearchValidateResponse, error)

	// GenerateFileSuggestions 文件搜索建议业务逻辑
	GenerateFileSuggestions(req *entity.FileSearchSugRequest, uid string) (*entity.FileSearchSugResponse, error)
}

// fileSearchBusinessServiceImpl FileSearchBusinessService的实现
type fileSearchBusinessServiceImpl struct {
	ctx               *gdp.WebContext
	bosService        *BosServiceImpl
	riskService       RiskService
	fileSearchService FileSearchService
	aiToolService     AiToolService
}

// NewFileSearchBusinessService 创建FileSearchBusinessService实例
func NewFileSearchBusinessService(ctx *gdp.WebContext) FileSearchBusinessService {
	return &fileSearchBusinessServiceImpl{
		ctx:               ctx,
		bosService:        NewBosService(),
		riskService:       NewRiskService(ctx),
		fileSearchService: NewFileSearchService(ctx),
		aiToolService:     NewAiToolService(ctx),
	}
}

// ValidateFiles 实现文件验证业务逻辑
func (s *fileSearchBusinessServiceImpl) ValidateFiles(req *entity.FileSearchValidateRequest, sessionToken string, uid string) (*entity.FileSearchValidateResponse, error) {
	// 1. 文件路径验证和元信息获取
	validatedFiles, riskRequestFiles := s.validateFilePaths(req.File, uid)

	// 2. 风控检查
	riskResult, err := s.riskService.CheckRisk(riskRequestFiles)
	if err != nil {
		return &entity.FileSearchValidateResponse{
			Code: 503,
			Data: entity.ResponseData{
				Files: nil,
			},
			Msg:   "failed",
			LogID: s.ctx.GetLogID(),
		}, nil
	}

	// 3. 处理风控结果
	fileInfos := s.processRiskResults(riskRequestFiles, riskResult, &validatedFiles)

	// 4. 存储文件信息到Redis
	err = s.fileSearchService.SetFileInfo(fileInfos, sessionToken)
	if err != nil {
		s.ctx.WarningF("set file info failed: %v", err)
		s.handleRedisError(&validatedFiles)
	}

	// 5. 构造响应
	return s.buildValidateResponse(validatedFiles), nil
}

// validateFilePaths 验证文件路径并获取元信息
func (s *fileSearchBusinessServiceImpl) validateFilePaths(files []entity.FileItem, uid string) ([]entity.FileValidationResult, []RiskRequestFile) {
	var validatedFiles []entity.FileValidationResult
	var riskRequestFiles []RiskRequestFile
	metaInfoErrCount := 0
	authURLErrCount := 0

	userPath := s.bosService.GetBosPath(constants.PathPerfix, uid)

	for _, fileItem := range files {
		// 仅允许访问该 CUID 对应目录下的文件
		if !strings.Contains(fileItem.Path, userPath) {
			validatedFiles = append(validatedFiles, entity.FileValidationResult{
				ID:     fileItem.ID,
				Code:   entity.RiskCodeReupload,
				Msg:    "need re-upload",
				Status: "failed",
			})
			continue
		}

		switch fileItem.Type {
		case entity.FileTypeImage:
			info, err := s.bosService.GetImageMetaInfo(fileItem.Path)
			if err != nil {
				metaInfoErrCount++
				continue
			}
			authURL, err := s.bosService.GetAuthURL(constants.PathPerfix, constants.AuthURLExpirationTime, uid, []string{fileItem.Path})
			if err != nil || len(authURL) == 0 {
				authURLErrCount++
				continue
			}
			riskRequestFiles = append(riskRequestFiles, RiskRequestFile{
				ID:          fileItem.ID,
				Name:        fileItem.Name,
				URL:         authURL[0],
				Type:        string(fileItem.Type),
				Size:        info.Size,
				MimeType:    info.MimeType,
				ImageWidth:  info.Width,
				ImageHeight: info.Height,
			})
		case entity.FileTypeFile:
			info, err := s.bosService.GetObjectMetaInfo(fileItem.Path)
			if err != nil {
				metaInfoErrCount++
				continue
			}
			authURL, err := s.bosService.GetAuthURL(constants.PathPerfix, constants.AuthURLExpirationTime, uid, []string{fileItem.Path})
			if err != nil || len(authURL) == 0 {
				authURLErrCount++
				continue
			}
			riskRequestFiles = append(riskRequestFiles, RiskRequestFile{
				ID:       fileItem.ID,
				Name:     fileItem.Name,
				URL:      authURL[0],
				Type:     string(fileItem.Type),
				Size:     info.Size,
				MimeType: strings.ToUpper(strings.TrimPrefix(filepath.Ext(fileItem.Name), ".")),
			})
		}
	}

	return validatedFiles, riskRequestFiles
}

// processRiskResults 处理风控结果
func (s *fileSearchBusinessServiceImpl) processRiskResults(riskFiles []RiskRequestFile, riskResult RiskResult, validatedFiles *[]entity.FileValidationResult) []FileSearchInfo {
	fileInfos := make([]FileSearchInfo, 0, len(riskFiles))

	for _, fileItem := range riskFiles {
		result, ok := riskResult[FileID(fileItem.ID)]
		if !ok {
			*validatedFiles = append(*validatedFiles, entity.FileValidationResult{
				ID:     fileItem.ID,
				Code:   entity.RiskCodeReupload,
				Msg:    "need re-upload",
				Status: "failed",
			})
			continue
		}

		fileInfos = append(fileInfos, FileSearchInfo{
			ID:       result.ID,
			Name:     fileItem.Name,
			Type:     string(fileItem.Type),
			MimeType: fileItem.MimeType,
			Size:     fileItem.Size,
			URL:      fileItem.URL,
		})

		validatedFile := entity.FileValidationResult{
			ID:   result.ID,
			Code: result.Code,
			Msg:  result.Message,
		}
		if result.Code == entity.RiskCodeSuccess {
			validatedFile.Status = "success"
		} else {
			validatedFile.Status = "failed"
		}
		*validatedFiles = append(*validatedFiles, validatedFile)
	}

	return fileInfos
}

// handleRedisError 处理Redis错误
func (s *fileSearchBusinessServiceImpl) handleRedisError(validatedFiles *[]entity.FileValidationResult) {
	for i := range *validatedFiles {
		(*validatedFiles)[i].Status = "failed"
		(*validatedFiles)[i].Code = entity.RiskCodeReupload
		(*validatedFiles)[i].Msg = "need re-upload"
	}
}

// buildValidateResponse 构造验证响应
func (s *fileSearchBusinessServiceImpl) buildValidateResponse(validatedFiles []entity.FileValidationResult) *entity.FileSearchValidateResponse {
	// 按ID排序
	sort.Slice(validatedFiles, func(i, j int) bool {
		idI, _ := strconv.Atoi(validatedFiles[i].ID)
		idJ, _ := strconv.Atoi(validatedFiles[j].ID)
		return idI < idJ
	})

	return &entity.FileSearchValidateResponse{
		Code: 0,
		Data: entity.ResponseData{
			Files: validatedFiles,
		},
		Msg:   "success",
		LogID: s.ctx.GetLogID(),
	}
}

// GenerateFileSuggestions 实现文件搜索建议业务逻辑
func (s *fileSearchBusinessServiceImpl) GenerateFileSuggestions(req *entity.FileSearchSugRequest, uid string) (*entity.FileSearchSugResponse, error) {
	// 1. 确定建议词条和默认框
	sugWords, defaultBox := s.determineSuggestionWords(req.File)

	// 2. 生成AI工具信息（如果需要）
	fileTool := s.generateFileSearchTool(req.File, uid)

	// 3. 随机选择词条
	selectedWords := s.selectRandomWords(sugWords, 10)

	// 4. 构造建议项
	sugItems := s.buildSuggestionItems(selectedWords)

	// 5. 构造响应
	return s.buildSugResponse(req.Word, defaultBox, fileTool, sugItems), nil
}

// determineSuggestionWords 确定建议词条和默认框
func (s *fileSearchBusinessServiceImpl) determineSuggestionWords(files []entity.FileItem) ([]string, string) {
	var sugWords []string
	var defaultBox string

	if len(files) > 0 {
		switch files[0].Type {
		case "file":
			sugWords = entity.FileSugWords
			defaultBox = entity.FileSugDefaultBox
		case "image":
			sugWords = entity.ImageSugWords
			defaultBox = entity.ImageSugDefaultBox
		default:
			sugWords = entity.FileSugWords
			defaultBox = entity.FileSugDefaultBox
		}
	} else {
		sugWords = entity.FileSugWords
		defaultBox = entity.FileSugDefaultBox
	}

	return sugWords, defaultBox
}

// generateFileSearchTool 生成AI工具信息
func (s *fileSearchBusinessServiceImpl) generateFileSearchTool(files []entity.FileItem, uid string) *FileSearchToolResponse {
	if len(files) > 0 && files[0].Type == "image" {
		fileTool, _ := s.aiToolService.GetFileSearchTool(uid, files[0].Path, "pic")
		return fileTool
	}
	return nil
}

// selectRandomWords 随机选择词条
func (s *fileSearchBusinessServiceImpl) selectRandomWords(sugWords []string, maxCount int) []string {
	selectedWords := make([]string, 0, maxCount)
	if len(sugWords) <= maxCount {
		selectedWords = sugWords
	} else {
		perm := rand.Perm(len(sugWords))
		for i := 0; i < maxCount; i++ {
			selectedWords = append(selectedWords, sugWords[perm[i]])
		}
	}
	return selectedWords
}

// buildSuggestionItems 构造建议项
func (s *fileSearchBusinessServiceImpl) buildSuggestionItems(selectedWords []string) []entity.FileSearchItem {
	var sugItems []entity.FileSearchItem
	for i, word := range selectedWords {
		sugItems = append(sugItems, entity.FileSearchItem{
			Word:   word,
			Type:   "0",
			WapURL: "",
			WwwURL: "",
			SA:     fmt.Sprintf("aib_aifile_s_%d", i+1),
		})
	}
	return sugItems
}

// buildSugResponse 构造建议响应
func (s *fileSearchBusinessServiceImpl) buildSugResponse(word string, defaultBox string, fileTool *FileSearchToolResponse, sugItems []entity.FileSearchItem) *entity.FileSearchSugResponse {
	return &entity.FileSearchSugResponse{
		Errno: 0,
		Msg:   "success",
		Type:  "sug",
		Slid:  "3984000149",
		Extend: entity.Extend{
			Query: entity.Query{
				Word: word,
				Type: "0",
			},
			Prefetch: "off",
		},
		BoxData: entity.BoxData{
			ShowText: defaultBox,
		},
		FileSearchTool: fileTool,
		Data:           sugItems,
		GMot:           []interface{}{},
		SwitchControl: entity.SwitchControl{
			RequestLocation: 1,
		},
		GMotStyle: 0,
		GMotTitle: "",
		LogID:     s.ctx.GetLogID(),
	}
}
