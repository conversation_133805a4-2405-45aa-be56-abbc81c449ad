// Package service  新接口 service 层
package service

import (
	"errors"
	constent "icode.baidu.com/baidu/searchbox/go-suggest/api/const"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
	"strings"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

// AiToolResponse 定义了 AI 工具接口的响应结构
type AiToolResponse struct {
	Data interface{} `json:"data"`
}

// FileSearchToolResponse 定义了 PlusSignFunction 的响应结构
type FileSearchToolResponse struct {
	Data interface{} `json:"data"`
}

// PlusSignFunctionResponse 定义了 PlusSignFunction 的响应结构
type PlusSignFunctionResponse struct {
	Data interface{} `json:"data"`
}

// AiToolService 定义了 AI 工具服务的接口
type AiToolService interface {
	GetAiTool(types []string) (*AiToolResponse, error)
	GetPlusSignFunction() (*PlusSignFunctionResponse, error)
	GetAiToolTypes() []string
	GetAiToolIcons() map[string]map[string]string
	GetFileSearchTool(uid string, filePath string, fileType string) (*FileSearchToolResponse, error)
}

// aiToolServiceImpl 是 AiToolService 的具体实现
type aiToolServiceImpl struct {
	ctx         *gdp.WebContext
	bosService  *BosServiceImpl
	aiToolModel *model.DefaultAiToolModel
}

// NewAiToolService 创建一个新的 AiToolService 实例
func NewAiToolService(ctx *gdp.WebContext) AiToolService {
	if ctx == nil {
		return (*aiToolServiceImpl)(nil)
	}
	return &aiToolServiceImpl{
		ctx:         ctx,
		bosService:  NewBosService(),
		aiToolModel: model.NewDefaultAiToolModel(ctx),
	}
}

// GetAiTool 获取 AI 工具配置
func (s *aiToolServiceImpl) GetAiTool(types []string) (*AiToolResponse, error) {
	if s == nil || s.ctx == nil || s.ctx.Request == nil {
		return nil, errors.New("failed to initialize AiToolService")
	}
	// 旧兼容逻辑
	if s.ctx.Request.FormValue("compatible") != "1" {
		aiTool := model.GetCompatibleAiToolConfig()
		return &AiToolResponse{Data: aiTool}, nil
	}

	data := s.aiToolModel.GetAiTool()

	return &AiToolResponse{Data: data.SortAiToolsByTypes(types)}, nil
}

// GetPlusSignFunction 获取 PlusSignFunction 的响应
func (s *aiToolServiceImpl) GetPlusSignFunction() (*PlusSignFunctionResponse, error) {
	if s == nil || s.ctx == nil || s.ctx.Request == nil {
		return nil, errors.New("failed to initialize AiToolService")
	}
	data := s.aiToolModel.GetPlusSignFunction()
	if data == nil {
		return nil, errors.New("failed to get PlusSignFunction data")
	}
	return &PlusSignFunctionResponse{Data: data}, nil
}

func (s *aiToolServiceImpl) GetAiToolTypes() []string {
	if s == nil || s.ctx == nil || s.ctx.Request == nil {
		return nil
	}
	data := s.aiToolModel.GetAiTool()
	return data.GetAiToolsTypes()
}

func (s *aiToolServiceImpl) GetAiToolIcons() map[string]map[string]string {
	if s == nil || s.ctx == nil || s.ctx.Request == nil {
		return nil
	}
	data := s.aiToolModel.GetAiTool()
	return data.GetAiToolsIcons()
}

func (s *aiToolServiceImpl) GetFileSearchTool(uid string, filePath string, fileType string) (*FileSearchToolResponse, error) {
	if s == nil || s.ctx == nil || s.ctx.Request == nil {
		return nil, errors.New("failed to initialize AiToolService")
	}
	userPath := s.bosService.GetBosPath(constent.PathPerfix, uid)
	if !strings.Contains(filePath, userPath) {
		return nil, errors.New("file path does not contain user path")
	}
	authURL, err := s.bosService.GetAuthURL(constent.PathPerfix, constent.AuthURLExpirationTime, uid, []string{filePath})
	if err != nil || len(authURL) == 0 || authURL[0] == "" {
		return nil, errors.New("failed to get auth url")
	}
	data := s.aiToolModel.GetFileSearchTool(authURL[0], entity.FileType(fileType))
	if data == nil {
		return nil, errors.New("failed to get FileSearchTool data")
	}
	return &FileSearchToolResponse{Data: data}, nil
}
