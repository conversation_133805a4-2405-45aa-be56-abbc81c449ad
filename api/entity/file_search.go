package entity

// 风控状态常量
const (
	RiskStatusSuccess  = "success"        // 风控通过
	RiskStatusReupload = "need re-upload" // 需要重传文件
	RiskStatusFailed   = "failed"         // 风控失败不可重试
	RiskStatusRetry    = "retry"          // 风控可重试
)

// 风控错误码
const (
	RiskCodeSuccess  RiskCode = 0 // 风控通过
	RiskCodeReupload RiskCode = 1 // 需要重传文件
	RiskCodeFailed   RiskCode = 2 // 风控失败不可重试
	RiskCodeRetry    RiskCode = 3 // 风控可重试
)

type RiskCode int
