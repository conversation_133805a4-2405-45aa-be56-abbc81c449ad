package entity

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"regexp"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	"icode.baidu.com/baidu/searchbox/go-suggest/middlewares"
)

// CurlData 存储从 curl 文件解析出的数据
type CurlData struct {
	URL     string
	Method  string
	Headers map[string]string
	Body    string
	Cookies map[string]string
}

// parseCurlFile 解析 curl 文件，提取完整的请求信息
func parseCurlFile(filePath string) (*CurlData, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取 curl 文件失败: %w", err)
	}

	curlContent := string(content)
	curlData := &CurlData{
		Headers: make(map[string]string),
		Cookies: make(map[string]string),
		Method:  "GET", // 默认方法
	}

	// 解析 URL
	urlRegex := regexp.MustCompile(`curl\s+'([^']+)'`)
	if matches := urlRegex.FindStringSubmatch(curlContent); len(matches) > 1 {
		curlData.URL = matches[1]
	}

	// 解析方法
	if strings.Contains(curlContent, "-X POST") {
		curlData.Method = "POST"
	}

	// 解析 Headers
	headerRegex := regexp.MustCompile(`-H\s+'([^:]+):\s*([^']+)'`)
	headerMatches := headerRegex.FindAllStringSubmatch(curlContent, -1)
	for _, match := range headerMatches {
		if len(match) >= 3 {
			curlData.Headers[match[1]] = match[2]
		}
	}

	// 解析 Cookies
	cookieRegex := regexp.MustCompile(`--cookie\s+'([^']+)'`)
	if matches := cookieRegex.FindStringSubmatch(curlContent); len(matches) > 1 {
		cookieStr := matches[1]
		cookies := strings.Split(cookieStr, "; ")
		for _, cookie := range cookies {
			parts := strings.SplitN(cookie, "=", 2)
			if len(parts) == 2 {
				curlData.Cookies[parts[0]] = parts[1]
			}
		}
	}

	// 解析 Body
	bodyRegex := regexp.MustCompile(`--data-raw\s+'([^']+)'`)
	if matches := bodyRegex.FindStringSubmatch(curlContent); len(matches) > 1 {
		curlData.Body = matches[1]
	}

	return curlData, nil
}

// createWebContextFromCurl 根据 curl 数据创建 WebContext
func createWebContextFromCurl(curlData *CurlData) (*gdp.WebContext, error) {
	// 解析 URL 来验证其有效性
	_, err := url.Parse(curlData.URL)
	if err != nil {
		return nil, fmt.Errorf("解析 URL 失败: %w", err)
	}

	// 创建请求体
	var body io.Reader
	if curlData.Body != "" {
		body = strings.NewReader(curlData.Body)
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest(curlData.Method, curlData.URL, body)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置 Headers
	for key, value := range curlData.Headers {
		req.Header.Set(key, value)
	}

	// 设置 Cookies
	for name, value := range curlData.Cookies {
		req.AddCookie(&http.Cookie{
			Name:  name,
			Value: value,
		})
	}

	// 解析表单数据
	if curlData.Method == "POST" && curlData.Body != "" {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}
	_ = req.ParseForm()

	// 创建 Gin Context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 创建 WebContext
	return gdp.NewWebContext(c), nil
}

// TestRequestParams_FromCurlFile 从 curl 文件构建完整的请求参数测试
func TestRequestParams_FromCurlFile(t *testing.T) {
	// 解析 curl 文件
	curlData, err := parseCurlFile("../test/res/hisUniversal.curl")
	assert.NoError(t, err, "应该能够成功解析 curl 文件")
	assert.NotNil(t, curlData, "curl 数据不应为空")

	// 创建 WebContext
	ctx, err := createWebContextFromCurl(curlData)
	assert.NoError(t, err, "应该能够成功创建 WebContext")

	// 调用中间件处理请求
	middlewares.RequestWare(ctx)

	// 获取处理后的原始参数 map（中间件存储的是 map[string]string）
	if val, exists := ctx.Get("____request"); exists && val != nil {
		requestParamsMap, ok := val.(map[string]string)
		assert.True(t, ok, "应该能够转换为 map[string]string")
		assert.NotEmpty(t, requestParamsMap, "请求参数 map 不应为空")

		// 验证基本参数
		assert.Equal(t, "his", requestParamsMap["ctl"], "ctl 参数应该正确")
		assert.Equal(t, "universal", requestParamsMap["action"], "action 参数应该正确")
		assert.Equal(t, "baiduboxapp", requestParamsMap["osname"], "osname 参数应该正确")
		assert.Equal(t, "1099a", requestParamsMap["from"], "from 参数应该正确")
		assert.Equal(t, "1099a", requestParamsMap["cfrom"], "cfrom 参数应该正确")
		assert.Equal(t, "1_0", requestParamsMap["network"], "network 参数应该正确")
		assert.Equal(t, "i0", requestParamsMap["osbranch"], "osbranch 参数应该正确")
		assert.Equal(t, "bdbox", requestParamsMap["service"], "service 参数应该正确")
		assert.Equal(t, "0", requestParamsMap["st"], "st 参数应该正确")
		assert.Equal(t, "1320_2868_iphone_15.14.0.11_0", requestParamsMap["ua"], "ua 参数应该正确")
		assert.Equal(t, "E6BA0237B124F4749397C7A2C4986FE4E4D97648EOLQTRRMONH", requestParamsMap["uid"], "uid 参数应该正确")
		assert.Equal(t, "_uv5i_u3v8QlA", requestParamsMap["puid"], "puid 参数应该正确")
		assert.Equal(t, "20", requestParamsMap["max_return_num"], "max_return_num 参数应该正确")
		assert.Equal(t, "17494507710089544437", requestParamsMap["lid"], "lid 参数应该正确")

		// 验证 Cookie 相关参数
		assert.NotEmpty(t, requestParamsMap["sst"], "SST 参数应该被设置")
		if cookieUID, exists := requestParamsMap["_uid"]; exists {
			assert.NotEmpty(t, cookieUID, "Cookie UID 应该被设置")
		}

		t.Logf("RequestParams 验证通过，包含 %d 个参数", len(requestParamsMap))

		// 打印所有参数以便调试
		t.Logf("所有请求参数: %+v", requestParamsMap)
	} else {
		t.Error("未找到请求参数")
	}
}

// TestAdaptionParams_FromCurlFile 测试适配参数的解析
func TestAdaptionParams_FromCurlFile(t *testing.T) {
	// 解析 curl 文件
	curlData, err := parseCurlFile("../test/res/hisUniversal.curl")
	assert.NoError(t, err, "应该能够成功解析 curl 文件")

	// 创建 WebContext
	ctx, err := createWebContextFromCurl(curlData)
	assert.NoError(t, err, "应该能够成功创建 WebContext")

	// 调用中间件处理请求
	middlewares.RequestWare(ctx)

	// 获取处理后的 AdaptionParams
	adaptParams := GetAdaptionParams(ctx)
	// 注意：AdaptionParams 可能为空，因为它们通常在其他中间件中设置
	// 这里主要测试获取功能是否正常工作
	if adaptParams != nil {
		t.Logf("AdaptionParams 不为空，包含平台信息: %s", adaptParams.Platform)
	} else {
		t.Log("AdaptionParams 为空，这是正常的，因为它们在其他中间件中设置")
	}
}

// TestFormData_FromCurlFile 测试表单数据的解析
func TestFormData_FromCurlFile(t *testing.T) {
	// 解析 curl 文件
	curlData, err := parseCurlFile("../test/res/hisUniversal.curl")
	assert.NoError(t, err, "应该能够成功解析 curl 文件")

	// 创建 WebContext
	ctx, err := createWebContextFromCurl(curlData)
	assert.NoError(t, err, "应该能够成功创建 WebContext")

	// 从 POST body 中解析表单数据
	if curlData.Body != "" && strings.HasPrefix(curlData.Body, "data=") {
		// URL 解码
		encodedData := curlData.Body[5:] // 去掉 "data=" 前缀
		decodedJSON, err := url.QueryUnescape(encodedData)
		assert.NoError(t, err, "URL 解码应该成功")

		// JSON 解析
		formData := &FormData{}
		err = json.Unmarshal([]byte(decodedJSON), formData)
		assert.NoError(t, err, "JSON 解析应该成功")

		// 将 FormData 存储到 Context 中
		ctx.Set(FormDataKey, formData)

		// 验证获取功能
		retrievedFormData := GetFormData(ctx)
		assert.NotNil(t, retrievedFormData, "应该能够获取到 FormData")
		assert.Equal(t, formData.Eventtype, retrievedFormData.Eventtype)
		assert.Equal(t, formData.BoxShowQuery, retrievedFormData.BoxShowQuery)

		t.Logf("FormData 验证通过，事件类型: %s", formData.Eventtype)
	}
}

// TestConstants 测试常量定义
func TestConstants(t *testing.T) {
	assert.Equal(t, "structured_request_params", ReqParamsKey)
	assert.Equal(t, "structured_adaption_params", AdapParamsKey)
	assert.Equal(t, "structured_form_data", FormDataKey)
	assert.Equal(t, "userPrivacyInfo", PrivacyDataKey)

	t.Log("常量定义测试通过")
}

// extractDataFromCurlFile 从 curl 文件中提取 data 参数的值
func extractDataFromCurlFile(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	// 查找 --data-raw 后面的内容
	dataRawRegex := regexp.MustCompile(`--data-raw\s+'([^']+)'`)
	matches := dataRawRegex.FindStringSubmatch(string(content))
	if len(matches) < 2 {
		return "", nil
	}

	dataRawContent := matches[1]

	// 提取 data= 后面的部分
	if strings.HasPrefix(dataRawContent, "data=") {
		return dataRawContent[5:], nil // 去掉 "data=" 前缀
	}

	return "", nil
}

// createTestWebContext 创建测试用的 WebContext
func createTestWebContext() *gdp.WebContext {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	return gdp.NewWebContext(c)
}

// TestFormData_UnmarshalJSON_RealData 使用真实的 curl 数据测试 JSON 解析
func TestFormData_UnmarshalJSON_RealData(t *testing.T) {
	// 从 curl 文件中读取真实的 URL 编码数据
	encodedData, err := extractDataFromCurlFile("../test/res/hisUniversal.curl")
	assert.NoError(t, err, "应该能够成功读取 curl 文件")
	assert.NotEmpty(t, encodedData, "应该能够提取到数据")

	// URL 解码
	decodedJSON, err := url.QueryUnescape(encodedData)
	assert.NoError(t, err, "URL 解码应该成功")

	// JSON 解析
	formData := &FormData{}
	err = json.Unmarshal([]byte(decodedJSON), formData)
	assert.NoError(t, err, "JSON 解析应该成功")

	// 验证基本字段
	assert.Equal(t, "hissug", formData.Eventtype)
	assert.Equal(t, "home", formData.EventHisFrom)
	assert.Equal(t, "美22个民主党州长谴责特朗普", formData.BoxShowQuery)
	assert.Equal(t, 0, formData.PrivateMode)
	assert.Equal(t, 1, formData.ShowGuess)
	assert.Equal(t, float64(0), formData.Guessshowrownum)
	assert.Equal(t, "default", formData.Eventfrom)
	assert.Equal(t, "default", formData.Eventchannel)
	assert.Equal(t, "default", formData.Eventbox)

	// 验证 vid_ctl 字段解析
	assert.NotEmpty(t, formData.VidCtl)
	assert.Equal(t, 4, formData.VidCtl["rec_n"])
	assert.Equal(t, 1, formData.VidCtl["high_n"])
	assert.Equal(t, 1, formData.VidCtl["ins_guess"])

	// 验证 base64 编码字段不为空（解码后应该有数据）
	assert.NotEmpty(t, formData.RawGuessdata)
	assert.NotEmpty(t, formData.RawBoxdata)
	assert.NotEmpty(t, formData.Guessdata) // 解码后的数据
	assert.NotEmpty(t, formData.Boxdata)   // 解码后的数据

	// 验证 target 字段包含预期内容
	assert.Contains(t, formData.Target, "board")
	assert.Contains(t, formData.Target, "rec")
	assert.Contains(t, formData.Target, "list")
}

// TestFormData_JSON_MarshalUnmarshal 测试 JSON 序列化和反序列化
func TestFormData_JSON_MarshalUnmarshal(t *testing.T) {
	// 创建测试数据
	original := &FormData{
		Eventtype:       "test_event",
		ClientName:      "test_client",
		PrivateMode:     1,
		TodayFirst:      1,
		DisplayWidth:    1080,
		TextCount:       20,
		ShowGuess:       1,
		Guessshowrownum: 10.5,
		VidCtl: map[string]int{
			"rec_n":     4,
			"high_n":    1,
			"ins_guess": 1,
		},
		Hisdata:   []string{"搜索词1", "搜索词2"},
		Boxdata:   []map[string]interface{}{{"query": "测试", "type": 1}},
		Guessdata: []map[string]interface{}{{"word": "猜测词", "score": 0.9}},
		ContentInfo: map[string]interface{}{
			"url":   "https://www.baidu.com",
			"title": "测试页面",
		},
	}

	// 序列化
	jsonData, err := json.Marshal(original)
	assert.NoError(t, err)

	// 反序列化
	parsed := &FormData{}
	err = json.Unmarshal(jsonData, parsed)
	assert.NoError(t, err)

	// 验证基本字段
	assert.Equal(t, original.Eventtype, parsed.Eventtype)
	assert.Equal(t, original.ClientName, parsed.ClientName)
	assert.Equal(t, original.PrivateMode, parsed.PrivateMode)
	assert.Equal(t, original.TodayFirst, parsed.TodayFirst)
	assert.Equal(t, original.DisplayWidth, parsed.DisplayWidth)
	assert.Equal(t, original.TextCount, parsed.TextCount)
	assert.Equal(t, original.ShowGuess, parsed.ShowGuess)
	assert.Equal(t, original.Guessshowrownum, parsed.Guessshowrownum)
}

// TestGetRequestParams 测试获取请求参数
func TestGetRequestParams(t *testing.T) {
	ctx := createTestWebContext()

	// 测试参数不存在的情况
	result := GetRequestParams(ctx)
	assert.Nil(t, result)

	// 测试正常情况
	params := &RequestParams{Query: "test", Service: "suggest"}
	ctx.Set(ReqParamsKey, params)
	result = GetRequestParams(ctx)
	assert.Equal(t, params, result)
}

// TestGetAdaptionParams 测试获取适配参数
func TestGetAdaptionParams(t *testing.T) {
	ctx := createTestWebContext()

	// 测试参数不存在的情况
	result := GetAdaptionParams(ctx)
	assert.Nil(t, result)

	// 测试正常情况
	params := &AdaptionParams{Platform: "android", DeviceID: "test"}
	ctx.Set(AdapParamsKey, params)
	result = GetAdaptionParams(ctx)
	assert.Equal(t, params, result)
}

// TestGetFormData 测试获取表单数据
func TestGetFormData(t *testing.T) {
	ctx := createTestWebContext()

	// 测试数据不存在的情况
	result := GetFormData(ctx)
	assert.Nil(t, result)

	// 测试正常情况
	formData := &FormData{Eventtype: "test", ClientName: "baidu"}
	ctx.Set(FormDataKey, formData)
	result = GetFormData(ctx)
	assert.Equal(t, formData, result)
}

// TestGetUserPrivacyInfo 测试获取用户隐私信息
func TestGetUserPrivacyInfo(t *testing.T) {
	ctx := createTestWebContext()

	// 测试隐私信息不存在的情况
	result := GetUserPrivacyInfo(ctx)
	assert.Nil(t, result)

	// 测试正常情况
	privacyInfo := &common.UserPrivacyInfo{}
	ctx.Set(PrivacyDataKey, privacyInfo)
	result = GetUserPrivacyInfo(ctx)
	assert.Equal(t, privacyInfo, result)
}
