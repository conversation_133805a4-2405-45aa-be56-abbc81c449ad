package entity

// 文件搜索相关的文件类型常量
const (
	FileTypeImage FileType = "image"
	FileTypeFile  FileType = "file"
)

// FileItem 定义了文件对象的结构
type FileItem struct {
	ID   string   `json:"id"`
	Name string   `json:"name"`
	Path string   `json:"path"`
	Type FileType `json:"type"`
	Size int64    `json:"size"`
}

// FileSearchValidateRequest 定义了 AiToolValidate 接口的请求结构
type FileSearchValidateRequest struct {
	File []FileItem `json:"file"`
}

// FileSearchSugRequest 定义了文件搜索建议请求结构
type FileSearchSugRequest struct {
	Word string     `json:"word"`
	File []FileItem `json:"file"`
}

// FileValidationResult 定义了文件校验结果的结构
type FileValidationResult struct {
	ID        string   `json:"id"`
	Status    string   `json:"status"`
	Code      RiskCode `json:"code"`
	Msg       string   `json:"errmsg"`
	WordCount int      `json:"word_count"`
}

// ResponseData 定义了响应数据中 data 字段的结构
type ResponseData struct {
	Files []FileValidationResult `json:"files"`
}

// FileSearchValidateResponse 定义了 AiToolValidate 接口的响应结构
type FileSearchValidateResponse struct {
	Code  int          `json:"errno"`
	Data  ResponseData `json:"data"`
	Msg   string       `json:"errmsg"`
	LogID string       `json:"requestid"`
}

// FileSearchItem 定义了搜索建议项
type FileSearchItem struct {
	Word   string `json:"word"`
	Type   string `json:"type"`
	WapURL string `json:"wap_url"`
	WwwURL string `json:"www_url"`
	SA     string `json:"sa"`
}

// Query 定义了查询结构
type Query struct {
	Word string `json:"word"`
	Type string `json:"type"`
}

// Extend 定义了扩展信息
type Extend struct {
	Query    Query  `json:"query"`
	Prefetch string `json:"prefetch"`
}

// BoxData 定义了框数据
type BoxData struct {
	ShowText string `json:"show_text"`
}

// SwitchControl 定义了开关控制
type SwitchControl struct {
	RequestLocation int `json:"request_location"`
}

// FileSearchSugResponse 定义新的 AiToolSug 接口的响应结构
type FileSearchSugResponse struct {
	Errno          int              `json:"errno"`
	Msg            string           `json:"msg"`
	Type           string           `json:"type"`
	Slid           string           `json:"slid"`
	Extend         Extend           `json:"extend"`
	BoxData        BoxData          `json:"boxData"`
	Data           []FileSearchItem `json:"data"`
	GMot           []interface{}    `json:"g_mot"`
	SwitchControl  SwitchControl    `json:"switch_control"`
	GMotStyle      int              `json:"g_mot_style"`
	GMotTitle      string           `json:"g_mot_title"`
	LogID          string           `json:"requestid"`
	FileSearchTool interface{}      `json:"ai_tool"`
}

// FileSearchSTSResponseData 定义了STS响应数据
type FileSearchSTSResponseData struct {
	PreFixPath string `json:"preFixPath"`
	BceURL     string `json:"bceUrl"`
	BucketName string `json:"bucketName"`
	Ak         string `json:"ak"`
	Token      string `json:"token"`
	Sk         string `json:"sk"`
}

// FileSearchSTSResponse 定义了STS响应结构
type FileSearchSTSResponse struct {
	Code  int                       `json:"errno"`
	Data  FileSearchSTSResponseData `json:"data"`
	Msg   string                    `json:"errmsg"`
	LogID string                    `json:"requestid"`
}
