// Package entity 定义了API服务使用的实体结构
package entity

type FileType string

const (
	FileTypeIMG  FileType = "pic"
	FileTypeFILE FileType = "file"
)

// AiToolConfig 定义了AI工具配置的结构
type AiToolConfig struct {
	Tools   map[string]AiToolItem    `koanf:"tools"`
	ToolVer map[string][]ToolVersion `koanf:"toolVer"`
}

// ToolVersion 定义了工具版本配置
type ToolVersion struct {
	AppVer         string   `koanf:"appVer"`
	Tools          []string `koanf:"tools"`
	IncognitoTools []string `koanf:"incognitoTools"`
}

// AiToolItem 定义了单个AI工具项的结构
type AiToolItem struct {
	FileType  FileType    `json:"fileType"`
	Schema    string      `koanf:"schema" json:"schema"`
	Icon      string      `koanf:"icon" json:"icon"`
	IconDark  string      `koanf:"iconDark" json:"iconDark"`
	IconNight string      `koanf:"iconNight" json:"iconNight"`
	Text      string      `koanf:"text" json:"text"`
	Type      string      `koanf:"type" json:"type"`
	Panel     interface{} `koanf:"panel" json:"panel"`
	PanelVer  int         `koanf:"panelVer" json:"version"`
	Login     int         `koanf:"login" json:"login"`
	Back      int         `koanf:"back" json:"back"`
}
