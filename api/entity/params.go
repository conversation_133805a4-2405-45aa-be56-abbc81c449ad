package entity

import (
	"encoding/json"
	"net/url"

	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/library/common"
	base64lib "icode.baidu.com/baidu/searchbox/golang-lib/chprb64"
)

const (
	ReqParamsKey   = "structured_request_params"
	AdapParamsKey  = "structured_adaption_params"
	FormDataKey    = "structured_form_data"
	PrivacyDataKey = "userPrivacyInfo"
)

// RequestParams 存储所有请求相关参数
type RequestParams struct {
	// 通用参数
	V       string            `mapstructure:"v" json:"v"`             // 版本
	Query   string            `mapstructure:"query" json:"query"`     // 查询词
	Service string            `mapstructure:"service" json:"service"` // 服务
	From    string            `mapstructure:"from" json:"from"`       // 来源
	CFrom   string            `mapstructure:"cfrom" json:"cfrom"`     // 客户端来源
	Network string            `mapstructure:"network" json:"network"` // 网络类型
	CIP     string            `mapstructure:"cip" json:"cip"`         // 客户端IP
	IPV4    string            // 客户端IPV4
	IPV6    string            // 客户端IPV6
	Address map[string]string // 地址信息
	Ctl     string            `mapstructure:"ctl" json:"ctl"`         // 控制参数
	Action  string            `mapstructure:"action" json:"action"`   // 动作
	TypeID  string            `mapstructure:"typeid" json:"typeid"`   // 类型ID
	Version string            `mapstructure:"version" json:"version"` // 版本号

	// 设备相关
	UA        string `mapstructure:"ua" json:"ua"`     // 用户代理
	UT        string `mapstructure:"ut" json:"ut"`     // 用户代理类型
	UID       string `mapstructure:"uid" json:"uid"`   // 用户ID
	HUID      string `mapstructure:"huid" json:"huid"` // 鸿蒙用户原始 UID
	PUID      string `mapstructure:"puid" json:"puid"` // Passport用户ID
	CookieUID string `mapstructure:"_uid" json:"_uid"` // Cookie中的UID，对应_uid

	// 客户端信息
	OSBranch     string `mapstructure:"osbranch" json:"osbranch"`         // 操作系统分支
	RealOSBranch string `mapstructure:"realOsbranch" json:"realOsbranch"` // 原始操作系统分支
	OSName       string `mapstructure:"osname" json:"osname"`             // 操作系统名称

	// 网络相关
	NetworkType string `mapstructure:"network_type" json:"network_type"` // 网络类型(wifi/4g/5g等)

	// 加密参数
	Cen string `mapstructure:"cen" json:"cen"` // 加密标识
	Ctv string `mapstructure:"ctv" json:"ctv"` // 加密版本

	// 其他常用参数
	FV        string `mapstructure:"fv" json:"fv"`                 // 前端版本
	LID       string `mapstructure:"lid" json:"lid"`               // 日志ID
	SugID     string `mapstructure:"sugid" json:"sugid"`           // 建议ID
	SugMode   string `mapstructure:"sugmode" json:"sugmode"`       // 建议模式
	Scene     string `mapstructure:"scene" json:"scene"`           // 场景
	HotLaunch string `mapstructure:"hot_launch" json:"hot_launch"` // 热启动标记
	OFrom     string `mapstructure:"ofrom" json:"ofrom"`           // 原始来源

	// 鸿蒙适配参数
	IsHarmony string `mapstructure:"is_harmony" json:"is_harmony"` // 是否鸿蒙系统

	// 荣耀白牌适配
	RongyaoBaipai string `mapstructure:"rongyao_baipai" json:"rongyao_baipai"` // 荣耀白牌标记

	// 会话参数
	SessionUID string `mapstructure:"session_uid" json:"session_uid"` // 会话用户ID

	// 用户行为参数
	ST             string `mapstructure:"st" json:"st"`                           // 搜索类型
	PQ             string `mapstructure:"pq" json:"pq"`                           // 上一次查询
	PT             string `mapstructure:"pt" json:"pt"`                           // 上一次类型
	BDID           string `mapstructure:"bdid" json:"bdid"`                       // 百度ID
	VOI            string `mapstructure:"voi" json:"voi"`                         // 语音输入标识
	OriLid         string `mapstructure:"orilid" json:"orilid"`                   // 原始日志ID
	ChatSearchTag  string `mapstructure:"chat_search_tag" json:"chat_search_tag"` // 聊天搜索标签
	InteractiveTag string `mapstructure:"interactive_tag" json:"interactive_tag"` // 交互标签
	Data           string `mapstructure:"data" json:"data"`                       // 数据
	DeviceTime     string `mapstructure:"device_time" json:"device_time"`         // 设备时间
	SetParams      string `mapstructure:"set_params" json:"set_params"`           // 设置参数
	PWD            string `mapstructure:"pwd" json:"pwd"`                         // 密码

	// 预取参数
	PreRender    string `mapstructure:"prerender" json:"prerender"`         // 预渲染
	ISID         string `mapstructure:"isid" json:"isid"`                   // 页面ID
	PU           string `mapstructure:"pu" json:"pu"`                       // 用户参数
	ANTCT        string `mapstructure:"antct" json:"antct"`                 // 防作弊控制参数
	TN           string `mapstructure:"tn" json:"tn"`                       // 模板号
	WiseCsor     string `mapstructure:"wisecsor" json:"wisecsor"`           // 智能光标
	CList        string `mapstructure:"clist" json:"clist"`                 // 列表类型
	EventType    string `mapstructure:"event_type" json:"event_type"`       // 事件类型
	EventChannel string `mapstructure:"event_channel" json:"event_channel"` // 事件频道
	RsvSug4      string `mapstructure:"rsvsug4" json:"rsvsug4"`             // 备用建议4
	RSGPQ        string `mapstructure:"rsgpq" json:"rsgpq"`                 // 备用查询
	OQ           string `mapstructure:"oq" json:"oq"`                       // 原始查询
	RQ           string `mapstructure:"rq" json:"rq"`                       // 相关查询
	SA           string `mapstructure:"sa" json:"sa"`                       // 来源属性
	TSamp        string `mapstructure:"t_samp" json:"t_samp"`               // 采样时间
	CKSize       string `mapstructure:"cksize" json:"cksize"`               // Cookie大小
	PreMode      string `mapstructure:"premode" json:"premode"`             // 预取模式

	// 协议参数
	CSMixed string `mapstructure:"csmixed" json:"csmixed"` // 混合协议

	// 双清单参数
	PSV        string `mapstructure:"psv" json:"psv"`               // 页面版本
	PNW        string `mapstructure:"pnw" json:"pnw"`               // 网络参数
	MPV        string `mapstructure:"mpv" json:"mpv"`               // 移动页面版本
	AppName    string `mapstructure:"appname" json:"appname"`       // 应用名称
	BranchName string `mapstructure:"branchname" json:"branchname"` // 分支名称

	// 先知搜索用户行为特征
	PreInputNum  string `mapstructure:"preinput_num" json:"preinput_num"`     // 预输入数量
	DeleteNum    string `mapstructure:"delete_num" json:"delete_num"`         // 删除数量
	PreTList     string `mapstructure:"pretlist" json:"pretlist"`             // 预取列表
	IsDelete     string `mapstructure:"isdelete" json:"isdelete"`             // 是否删除
	MaxReturnNum string `mapstructure:"max_return_num" json:"max_return_num"` // 最大返回数量

	// 跨度升级参数
	SpanUpgrade string `mapstructure:"span_upgrade" json:"span_upgrade"` // 跨度升级
	TBundle     string `mapstructure:"tbundle" json:"tbundle"`           // 绑定类型
	TVersion    string `mapstructure:"tversion" json:"tversion"`         // 类型版本

	// Cookie参数
	SST string `mapstructure:"sst" json:"sst"` // SST状态，用于控制屏蔽

	// 特殊处理参数
	Sdel        string `mapstructure:"sdel" json:"sdel"`                 // SDEL请求体内容
	CuidMapping string `mapstructure:"cuid_mapping" json:"cuid_mapping"` // CUID映射标志
}

// AdaptionParams 存储所有适配相关参数
type AdaptionParams struct {
	// Cookie字段
	Sid      string  // Wise sid
	SidInt   []int   // Wise sid int
	CSid     string  // 客户端 sid
	WisePM   string  // 百度pm
	FontSize float64 // 字体大小
	BDEnvMod string  // 百度环境模式
	LogID    uint64  // 日志ID
	// 设备尺寸
	ResolutionWidth  string `mapstructure:"resolution_width" json:"resolution_width"`   // 分辨率宽度
	ResolutionHeight string `mapstructure:"resolution_height" json:"resolution_height"` // 分辨率高度

	// 平台信息
	Platform     string `mapstructure:"platform" json:"platform"`         // 平台(android/iphone等)
	RealPlatform string `mapstructure:"realPlatform" json:"realPlatform"` // 原始平台
	BDVersion    string `mapstructure:"bd_version" json:"bd_version"`     // 百度版本号

	// 设备标识
	DeviceID    string `mapstructure:"device_id" json:"device_id"`       // 设备ID
	DeviceRIMEI string `mapstructure:"device_rimei" json:"device_rimei"` // 设备IMEI

	// 设备规格
	DeviceDDpi   string `mapstructure:"device_dDpi" json:"device_dDpi"`     // 设备DPI
	DeviceDpi    string `mapstructure:"device_dpi" json:"device_dpi"`       // 设备DPI（浮点数）
	DeviceDpis   string `mapstructure:"device_dpis" json:"device_dpis"`     // 设备DPI字符串(ldpi/mdpi/hdpi等)
	DeviceModel  string `mapstructure:"device_model" json:"device_model"`   // 设备型号
	OSVersion    string `mapstructure:"os_version" json:"os_version"`       // 操作系统版本
	SDKVersion   string `mapstructure:"sdk_version" json:"sdk_version"`     // SDK版本
	DeviceVendor string `mapstructure:"device_vendor" json:"device_vendor"` // 设备厂商

	// 平台类型
	IsAndroid string `mapstructure:"isAndroid" json:"isAndroid"` // 是否安卓
	IsIOS     string `mapstructure:"isiOS" json:"isiOS"`         // 是否iOS
	IsUwp     string `mapstructure:"isUwp" json:"isUwp"`         // 是否UWP

	// 屏幕相关
	DeviceHD  string `mapstructure:"device_hd" json:"device_hd"`   // 设备是否高清
	DeviceDip string `mapstructure:"device_dip" json:"device_dip"` // 设备DIP
}

// PrivacyData 用户隐私数据
type PrivacyData struct {
	PrivacyString string `json:"v"`
	HonorOaID     string `json:"h_v"`
}

// FormData 表单数据结构
type FormData struct {
	// 原始字段，用于保存原始JSON字符串
	RawHisdata            string `json:"hisdata"`
	RawBoxdata            string `json:"boxdata"`
	RawGuessdata          string `json:"guessdata"`
	RawShowquerys         string `json:"showquerys"`
	RawGuessfeedbackquery string `json:"guess_feedback_query"`
	RawContentInfo        string `json:"content_info"`
	RawUnderBox           string `json:"under_box"`
	RawVidCtl             string `json:"vid_ctl"`
	RawAiToolVersion      string `json:"aitool_ver"`

	// 事件相关字段
	Eventtype    string `json:"event_type"`
	Eventbox     string `json:"event_box"`
	Eventquery   string `json:"event_query"`
	Eventchannel string `json:"event_channel"`
	Eventfrom    string `json:"event_from"`
	EventHisFrom string `json:"event_his_from"`
	BoxShowQuery string `json:"box_show_query"` // his框下推荐: 表示当前query词

	// 控制参数
	Guessshowrownum    float64 `json:"guess_show_rownum"`
	PrivateMode        int     `json:"private_mode"`
	ClientName         string  `json:"clientname"`
	Target             string  `json:"target"`
	TodayFirst         int     `json:"today_first"`   // 是否今日首次启动
	DisplayWidth       int     `json:"display_width"` // 屏幕宽度
	RecVideoInfo       int     `json:"rec_video_info"`
	ColdLaunchTime     string  `json:"cold_launch_time"` // 用户冷启进入手百的时间（毫秒时间戳）
	ShowGuess          int     `json:"show_guess"`       // 是否返回搜索发现数据
	TopicPageNum       int     `json:"topic_page_num"`
	RecMaxReturn       int     `json:"rec_max_return"`       // 最大返回数量控制
	TextCount          int     `json:"text_count"`           // 设备可展现最大中文字符数
	IntelligentHisType int     `json:"intelligent_his_type"` // 智能模式猜搜
	// 用户隐私数据
	EncodePrivacyData PrivacyData `json:"od"` // 用户隐私数据, 安卓传加密oaid，ios传加密idfa

	// 解析后的字段
	Hisdata            []string                 `json:"-"`
	Boxdata            []map[string]interface{} `json:"-"`
	Guessdata          []map[string]interface{} `json:"-"`
	Showquerys         []string                 `json:"-"`
	Guessfeedbackquery []string                 `json:"-"`
	ContentInfo        map[string]interface{}   `json:"-"`
	UnderBox           []map[string]interface{} `json:"-"`
	VidCtl             map[string]int           `json:"-"`
	AiToolPanelVersion map[string]int           `json:"-"`
}

// UnmarshalJSON 实现json.Unmarshaler接口，用于解析嵌套的JSON字段
func (fd *FormData) UnmarshalJSON(data []byte) error {
	// 定义一个别名类型，避免递归调用UnmarshalJSON
	type formDataAlias FormData

	// 初始化别名结构体
	alias := formDataAlias{}

	// 解析JSON到别名结构体
	if err := json.Unmarshal(data, &alias); err != nil {
		return err
	}

	// 将别名结构体转换回FormData
	*fd = FormData(alias)
	fd.Hisdata = []string{}
	// 解析Hisdata字段
	if fd.RawHisdata != "" {
		decodeRet, err := base64lib.Deocde(fd.RawHisdata, 0)
		if err == nil {
			// 尝试解析为字符串数组
			var hisItems []string
			if err := json.Unmarshal([]byte(decodeRet), &hisItems); err != nil {
				// 尝试解析ios的数据格式
				type Text struct {
					T string `json:"text"`
				}
				var texts []Text
				if err := json.Unmarshal([]byte(decodeRet), &texts); err == nil {
					hisItems = make([]string, len(texts))
					for i, hisItem := range texts {
						hisItems[i] = hisItem.T
					}
				}
			}
			fd.Hisdata = hisItems
		}
	}
	fd.Boxdata = []map[string]interface{}{}
	// 解析Boxdata字段
	if fd.RawBoxdata != "" {
		decodeRet, err := base64lib.Deocde(fd.RawBoxdata, 0)
		if err == nil {
			var boxItems []map[string]interface{}
			if err := json.Unmarshal([]byte(decodeRet), &boxItems); err == nil {
				fd.Boxdata = boxItems
			}
		}
	}
	fd.Guessdata = []map[string]interface{}{}
	// 解析Guessdata字段
	if fd.RawGuessdata != "" {
		decodeRet, err := base64lib.Deocde(fd.RawGuessdata, 0)
		if err == nil {
			var guessItems []map[string]interface{}
			if err := json.Unmarshal([]byte(decodeRet), &guessItems); err == nil {
				fd.Guessdata = guessItems
			}
		}
	}
	fd.Showquerys = []string{}
	// 解析Showquerys字段
	if fd.RawShowquerys != "" {
		decodeRet, err := base64lib.Deocde(fd.RawShowquerys, 0)
		if err == nil {
			var showItems []string
			if err := json.Unmarshal([]byte(decodeRet), &showItems); err == nil {
				fd.Showquerys = showItems
			}
		}
	}
	fd.Guessfeedbackquery = []string{}
	// 解析Guessfeedbackquery字段
	if fd.RawGuessfeedbackquery != "" {
		var feedbackItems []string
		if err := json.Unmarshal([]byte(fd.RawGuessfeedbackquery), &feedbackItems); err == nil {
			fd.Guessfeedbackquery = feedbackItems
		}
	}
	fd.ContentInfo = map[string]interface{}{}
	// 解析ContentInfo字段
	if fd.RawContentInfo != "" {
		var contentInfo map[string]interface{}
		if err := json.Unmarshal([]byte(fd.RawContentInfo), &contentInfo); err == nil {
			// 如果url字段存在且需要解码
			if contentInfo["url"] != nil {
				v, _ := contentInfo["url"].(string)
				urlInfoRes, err := url.QueryUnescape(v)
				if err == nil {
					contentInfo["url"] = urlInfoRes
				}
			}
			fd.ContentInfo = contentInfo
		}
	}
	fd.UnderBox = []map[string]interface{}{}
	// 解析UnderBox字段
	if fd.RawUnderBox != "" {
		var underBoxItems []map[string]interface{}
		if err := json.Unmarshal([]byte(fd.RawUnderBox), &underBoxItems); err == nil {
			fd.UnderBox = underBoxItems
		}
	}
	fd.VidCtl = map[string]int{}
	// 解析VidCtl字段
	if fd.RawVidCtl != "" {
		var vidCtl map[string]int
		if err := json.Unmarshal([]byte(fd.RawVidCtl), &vidCtl); err == nil {
			fd.VidCtl = vidCtl
		}
	}
	fd.AiToolPanelVersion = map[string]int{}
	// 解析AiToolVersion字段
	if fd.RawAiToolVersion != "" {
		var aitoolVersion map[string]int
		if err := json.Unmarshal([]byte(fd.RawAiToolVersion), &aitoolVersion); err == nil {
			fd.AiToolPanelVersion = aitoolVersion
		}
	}
	return nil
}
func GetRequestParams(ctx *gdp.WebContext) *RequestParams {
	if val, exists := ctx.Get(ReqParamsKey); exists && val != nil {
		params, _ := val.(*RequestParams)
		return params
	}
	return nil
}

func GetAdaptionParams(ctx *gdp.WebContext) *AdaptionParams {
	if val, exists := ctx.Get(AdapParamsKey); exists && val != nil {
		params, _ := val.(*AdaptionParams)
		return params
	}
	return nil
}

func GetFormData(ctx *gdp.WebContext) *FormData {
	if val, exists := ctx.Get(FormDataKey); exists && val != nil {
		params, _ := val.(*FormData)
		return params
	}
	return nil
}

func GetUserPrivacyInfo(ctx *gdp.WebContext) *common.UserPrivacyInfo {
	if val, exists := ctx.Get(PrivacyDataKey); exists && val != nil {
		params, _ := val.(*common.UserPrivacyInfo)
		return params
	}
	return nil
}
