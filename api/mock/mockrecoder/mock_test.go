package mockrecorder

import (
	"encoding/json"
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
)

type payload struct {
	A int    `json:"a"`
	B string `json:"b"`
}

func withTempStore(t *testing.T) (*Store, string, func()) {
	t.Helper()
	base, err := ioutil.TempDir("", "mockrecorder_test")
	if err != nil {
		t.Fatalf("TempDir: %v", err)
	}
	cleanup := func() { _ = os.RemoveAll(base) }
	return New(base), base, cleanup
}

// ========== ReplayOrRecordTypedSeq: 录制到上限后循环重放 ==========
func TestReplayOrRecordTypedSeq_RecordThenReplayCycle(t *testing.T) {
	s, base, cleanup := withTempStore(t)
	defer cleanup()

	relDir := "seq"
	maxFiles := 2

	// 第一次：录制 0001.json
	var out1 payload
	fromReplay, usedFile, err := s.ReplayOrRecordTypedSeq(relDir, maxFiles, func() (interface{}, error) {
		// produce 返回 map（任意类型），但 out 是强类型 struct
		return map[string]interface{}{"a": 1, "b": "x"}, nil
	}, &out1)
	if err != nil {
		t.Fatalf("1st call error: %v", err)
	}
	if fromReplay {
		t.Fatalf("1st call should record, got replay")
	}
	if out1.A != 1 || out1.B != "x" {
		t.Fatalf("1st out mismatch: %+v", out1)
	}
	// 文件存在且以换行结尾（美观化）
	b1, err := ioutil.ReadFile(usedFile)
	if err != nil {
		t.Fatalf("read 0001: %v", err)
	}
	if len(b1) == 0 || b1[len(b1)-1] != '\n' {
		t.Fatalf("pretty JSON should end with newline")
	}

	// 第二次：录制 0002.json
	var out2 payload
	fromReplay, usedFile, err = s.ReplayOrRecordTypedSeq(relDir, maxFiles, func() (interface{}, error) {
		return map[string]interface{}{"a": 2, "b": "y"}, nil
	}, &out2)
	if err != nil {
		t.Fatalf("2nd call error: %v", err)
	}
	if fromReplay {
		t.Fatalf("2nd call should record, got replay")
	}
	if out2.A != 2 || out2.B != "y" {
		t.Fatalf("2nd out mismatch: %+v", out2)
	}
	if filepath.Base(usedFile) != "0002.json" {
		t.Fatalf("expected 0002.json, got %s", usedFile)
	}

	// 第三次：已达上限 -> 重放 0001.json
	var out3 payload
	fromReplay, usedFile, err = s.ReplayOrRecordTypedSeq(relDir, maxFiles, func() (interface{}, error) {
		// 即便 produce 提供不同数据，重放时也应读取文件内容覆盖 out
		return map[string]interface{}{"a": 999, "b": "zzz"}, nil
	}, &out3)
	if err != nil {
		t.Fatalf("3rd call error: %v", err)
	}
	if !fromReplay {
		t.Fatalf("3rd call should replay")
	}
	if filepath.Base(usedFile) != "0001.json" {
		t.Fatalf("3rd replay expected 0001.json, got %s", usedFile)
	}
	if out3.A != 1 || out3.B != "x" {
		t.Fatalf("3rd out should be from file #1, got %+v", out3)
	}

	// 第四次：重放 0002.json（循环）
	var out4 payload
	fromReplay, usedFile, err = s.ReplayOrRecordTypedSeq(relDir, maxFiles, func() (interface{}, error) {
		return map[string]interface{}{"a": 123, "b": "abc"}, nil
	}, &out4)
	if err != nil {
		t.Fatalf("4th call error: %v", err)
	}
	if !fromReplay {
		t.Fatalf("4th call should replay")
	}
	if filepath.Base(usedFile) != "0002.json" {
		t.Fatalf("4th replay expected 0002.json, got %s", usedFile)
	}
	if out4.A != 2 || out4.B != "y" {
		t.Fatalf("4th out should be from file #2, got %+v", out4)
	}

	// 第五次：再次回到 0001.json
	var out5 payload
	fromReplay, usedFile, err = s.ReplayOrRecordTypedSeq(relDir, maxFiles, func() (interface{}, error) {
		return map[string]interface{}{"a": 0, "b": ""}, nil
	}, &out5)
	if err != nil {
		t.Fatalf("5th call error: %v", err)
	}
	if !fromReplay {
		t.Fatalf("5th call should replay")
	}
	if filepath.Base(usedFile) != "0001.json" {
		t.Fatalf("5th replay expected 0001.json, got %s", usedFile)
	}
	if out5.A != 1 || out5.B != "x" {
		t.Fatalf("5th out should be from file #1, got %+v", out5)
	}

	// 确认目录下只有 2 个 .json（没有继续录制）
	files, err := s.listJSONFiles(filepath.Join(base, relDir))
	if err != nil {
		t.Fatalf("listJSONFiles error: %v", err)
	}
	if len(files) != 2 {
		t.Fatalf("expected 2 recorded files, got %d (%v)", len(files), files)
	}
}

// ========== maxFiles=0: 永远录制 ==========
func TestReplayOrRecordTypedSeq_UnlimitedRecording(t *testing.T) {
	s, base, cleanup := withTempStore(t)
	defer cleanup()

	relDir := "unlimited"
	maxFiles := 0

	for i := 1; i <= 3; i++ {
		var out payload
		fromReplay, usedFile, err := s.ReplayOrRecordTypedSeq(relDir, maxFiles, func() (interface{}, error) {
			return payload{A: i, B: "v"}, nil
		}, &out)
		if err != nil {
			t.Fatalf("call %d error: %v", i, err)
		}
		if fromReplay {
			t.Fatalf("call %d should record (unlimited)", i)
		}
		if out.A != i || out.B != "v" {
			t.Fatalf("out %d mismatch: %+v", i, out)
		}
		if filepath.Base(usedFile) != (func(n int) string {
			return (([]string{"0001.json", "0002.json", "0003.json"})[n-1])
		})(i) {
			t.Fatalf("unexpected file for call %d: %s", i, usedFile)
		}
	}

	files, err := s.listJSONFiles(filepath.Join(base, relDir))
	if err != nil {
		t.Fatalf("listJSONFiles: %v", err)
	}
	if len(files) != 3 {
		t.Fatalf("expected 3 files, got %d", len(files))
	}
}

// ========== 错误分支 ==========
func TestReplayOrRecordTypedSeq_Errors(t *testing.T) {
	s, _, cleanup := withTempStore(t)
	defer cleanup()

	// out 为空
	_, _, err := s.ReplayOrRecordTypedSeq("dir", 1, func() (interface{}, error) {
		return payload{A: 1, B: "x"}, nil
	}, nil)
	if err == nil {
		t.Fatalf("expected error when out is nil")
	}

	// 目录为空
	var out payload
	_, _, err = s.ReplayOrRecordTypedSeq("", 1, func() (interface{}, error) {
		return payload{A: 1, B: "x"}, nil
	}, &out)
	if err == nil {
		t.Fatalf("expected error when relDir is empty")
	}

	// maxFiles < 0
	_, _, err = s.ReplayOrRecordTypedSeq("dir", -1, func() (interface{}, error) {
		return payload{A: 1, B: "x"}, nil
	}, &out)
	if err == nil {
		t.Fatalf("expected error when maxFiles < 0")
	}
}

// ========== 旧接口：ReplayOrRecordBytes / ReplayOrRecordJSON ==========
func TestReplayOrRecordBytesAndJSON(t *testing.T) {
	s, base, cleanup := withTempStore(t)
	defer cleanup()

	// Bytes 分支
	p := "bytes/a.json"
	data1 := []byte(`{"k":1,"v":"x"}`)
	b, replay, err := s.ReplayOrRecordBytes(p, func() ([]byte, error) { return data1, nil })
	if err != nil {
		t.Fatalf("ReplayOrRecordBytes #1: %v", err)
	}
	if replay {
		t.Fatalf("first bytes call should record")
	}
	// 内容可被解码，且有换行
	var m map[string]interface{}
	if err := json.Unmarshal(b, &m); err != nil {
		t.Fatalf("bytes json decode: %v", err)
	}
	if len(b) == 0 || b[len(b)-1] != '\n' {
		t.Fatalf("bytes pretty should end with newline")
	}
	// 第二次应重放
	b2, replay, err := s.ReplayOrRecordBytes(p, func() ([]byte, error) { return []byte(`{"k":2}`), nil })
	if err != nil {
		t.Fatalf("ReplayOrRecordBytes #2: %v", err)
	}
	if !replay {
		t.Fatalf("second bytes call should replay")
	}
	if string(b2) != string(b) {
		t.Fatalf("replayed content changed")
	}

	// JSON 分支
	p2 := "json/b.json"
	obj := map[string]interface{}{"x": 10, "y": "z"}
	j, replay, err := s.ReplayOrRecordJSON(p2, func() (interface{}, error) { return obj, nil })
	if err != nil {
		t.Fatalf("ReplayOrRecordJSON #1: %v", err)
	}
	if replay {
		t.Fatalf("first json call should record")
	}
	if len(j) == 0 || j[len(j)-1] != '\n' {
		t.Fatalf("json pretty should end with newline")
	}
	// 第二次应重放
	j2, replay, err := s.ReplayOrRecordJSON(p2, func() (interface{}, error) { return map[string]int{"x": 99}, nil })
	if err != nil {
		t.Fatalf("ReplayOrRecordJSON #2: %v", err)
	}
	if !replay {
		t.Fatalf("second json call should replay")
	}
	if string(j2) != string(j) {
		t.Fatalf("replayed json content changed")
	}

	// Save / Load
	p3 := "single/test.json"
	if err := s.Save(p3, map[string]int{"a": 1}); err != nil {
		t.Fatalf("Save: %v", err)
	}
	var got map[string]int
	hit, err := s.Load(p3, &got)
	if err != nil || !hit {
		t.Fatalf("Load: hit=%v err=%v", hit, err)
	}
	if got["a"] != 1 {
		t.Fatalf("Load mismatch: %+v", got)
	}

	// 确认文件都在 BaseDir 下
	if _, err := os.Stat(filepath.Join(base, p)); err != nil {
		t.Fatalf("expected file %s to exist", p)
	}
	if _, err := os.Stat(filepath.Join(base, p2)); err != nil {
		t.Fatalf("expected file %s to exist", p2)
	}
	if _, err := os.Stat(filepath.Join(base, p3)); err != nil {
		t.Fatalf("expected file %s to exist", p3)
	}
}
