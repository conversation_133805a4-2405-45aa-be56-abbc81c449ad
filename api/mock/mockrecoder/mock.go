// Package mockrecorder 用以录制流量
package mockrecorder

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

var Recoder = New("")

type Store struct {
	BaseDir string // 根目录，默认 "./mock"
}

// New 创建 Store；baseDir 为空则默认 "./mock"。
func New(baseDir string) *Store {
	if strings.TrimSpace(baseDir) == "" {
		baseDir = "./mock"
	}
	return &Store{BaseDir: baseDir}
}

// ================== 新增：顺序录制/重放（带上限，强类型） ==================
//
// ReplayOrRecordTypedSeq 会在 BaseDir/relDir 目录下顺序写入 0001.json、0002.json ...，直到数量达到 maxFiles。
// - 若目录下的 .json 数量 < maxFiles：调用 produce() 生产数据 -> 美观化写入下一个序号文件 -> 将其解码到 out 并返回（fromReplay=false）。
// - 若数量 >= maxFiles：不再录制；读取该目录按顺序的下一个文件，解码到 out 并返回（fromReplay=true）。
// - 顺序通过目录内隐藏文件 ".cursor" 维护，采用循环播放（到尾部后回到 1）。
// 约定：relDir 必须是一个“目录路径”（相对 BaseDir），不要带 .json 文件名。
func (s *Store) ReplayOrRecordTypedSeq(relDir string, maxFiles int, produce func() (interface{}, error), out interface{}) (fromReplay bool, usedFile string, err error) {
	if out == nil {
		return false, "", errors.New("out 不能为空，且必须为指向目标类型的指针")
	}
	if strings.TrimSpace(relDir) == "" {
		return false, "", errors.New("relDir 不能为空，且必须是相对的目录路径")
	}
	if maxFiles < 0 {
		return false, "", errors.New("maxFiles 不能为负数（0 表示不限制，始终录制）")
	}

	dir := s.fullPath(relDir)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return false, "", err
	}

	// 枚举目录内的 .json 文件（按文件名排序）
	files, err := s.listJSONFiles(dir)
	if err != nil {
		return false, "", err
	}

	// 当达到上限（且上限>0）时，停止录制，进入重放。
	if maxFiles > 0 && len(files) >= maxFiles {
		if len(files) == 0 {
			return false, "", errors.New("内部错误：maxFiles>0 且认为已满，但目录又是空的")
		}
		// 读取游标，得到下一个要重放的文件序号（1..len(files)），循环播放
		idx := s.readCursor(dir, len(files))
		if idx < 1 || idx > len(files) {
			idx = 1
		}
		next := idx%len(files) + 1

		fp := filepath.Join(dir, files[idx-1])
		b, e := ioutil.ReadFile(fp)
		if e != nil {
			return false, "", e
		}
		if e = json.Unmarshal(b, out); e != nil {
			return false, "", e
		}
		// 更新游标
		_ = s.writeCursor(dir, next)

		return true, fp, nil
	}

	// 尚未达到上限，执行录制流程
	val, e := produce()
	if e != nil {
		return false, "", e
	}

	pretty, e := marshalPretty(val)
	if e != nil {
		return false, "", e
	}

	// 生成文件名：0001.json、0002.json ...
	name := fmt.Sprintf("%04d.json", len(files)+1)
	dst := filepath.Join(dir, name)
	if e = s.writeAtomic(dst, pretty); e != nil {
		return false, "", e
	}

	// 为保证“即使没有记录文件也要按传入的类型重放”的语义，
	// 我们把刚生产的值再按 out 的类型进行一次解码（而不是直接类型断言）。
	if e = json.Unmarshal(pretty, out); e != nil {
		return false, "", e
	}

	// 若这是目录内的第一个文件，初始化游标指向“下一次重放应读第 1 个文件”
	// （当达到上限后开始重放时就会用到；不写也没关系，readCursor 有默认值 1）
	if len(files) == 0 {
		_ = s.writeCursor(dir, 1)
	}

	return false, dst, nil
}

// ================== 原有 API：按“单文件路径”录制/重放（保留） ==================

// 若文件存在，直接读取返回（fromReplay=true）；否则调用 produce() 得到原始 JSON（[]byte），
// 美观化后写入并返回。
func (s *Store) ReplayOrRecordBytes(relPath string, produce func() ([]byte, error)) (data []byte, fromReplay bool, err error) {
	full := s.fullPath(relPath)
	if exists(full) {
		b, e := ioutil.ReadFile(full)
		return b, true, e
	}
	raw, e := produce()
	if e != nil {
		return nil, false, e
	}
	pretty, e := prettyJSONBytes(raw)
	if e != nil {
		return nil, false, e
	}
	if e = s.writeAtomic(full, pretty); e != nil {
		return nil, false, e
	}
	return pretty, false, nil
}

// 若文件存在直接重放；否则调用 produce() 得到任意可 JSON 化对象，
// 美观化写入，最后返回编码后的字节。
func (s *Store) ReplayOrRecordJSON(relPath string, produce func() (interface{}, error)) (data []byte, fromReplay bool, err error) {
	full := s.fullPath(relPath)
	if exists(full) {
		b, e := ioutil.ReadFile(full)
		return b, true, e
	}
	v, e := produce()
	if e != nil {
		return nil, false, e
	}
	b, e := json.MarshalIndent(v, "", "  ")
	if e != nil {
		return nil, false, e
	}
	b = append(b, '\n')
	if e = s.writeAtomic(full, b); e != nil {
		return nil, false, e
	}
	return b, false, nil
}

// Load 将已存在的 JSON 文件解码到 out（指针）；返回是否命中与错误。
func (s *Store) Load(relPath string, out interface{}) (bool, error) {
	full := s.fullPath(relPath)
	if !exists(full) {
		return false, nil
	}
	b, err := ioutil.ReadFile(full)
	if err != nil {
		return false, err
	}
	return true, json.Unmarshal(b, out)
}

// Save 将对象以美观化 JSON 写入到目标路径（会覆盖同名文件）。
func (s *Store) Save(relPath string, v interface{}) error {
	full := s.fullPath(relPath)
	b, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		return err
	}
	b = append(b, '\n')
	return s.writeAtomic(full, b)
}

// SaveBytes 将原始 JSON（[]byte）美观化后写入（会校验是否为合法 JSON）。
func (s *Store) SaveBytes(relPath string, raw []byte) error {
	full := s.fullPath(relPath)
	b, err := prettyJSONBytes(raw)
	if err != nil {
		return err
	}
	return s.writeAtomic(full, b)
}

// ================== 内部工具 ==================

func (s *Store) fullPath(relPath string) string {
	clean := filepath.Clean(relPath)
	// 防止传入绝对路径把文件写到项目外
	if filepath.IsAbs(clean) {
		clean = strings.TrimPrefix(clean, string(os.PathSeparator))
	}
	return filepath.Join(s.BaseDir, clean)
}

func (s *Store) writeAtomic(dst string, data []byte) error {
	dir := filepath.Dir(dst)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}
	tmp := dst + ".tmp-" + time.Now().Format("20060102150405.000000000")
	if err := ioutil.WriteFile(tmp, data, 0644); err != nil {
		return err
	}
	// 原子替换（大多数 *nix）；Windows 下若目标存在可能失败，做一次兜底
	if err := os.Rename(tmp, dst); err != nil {
		if exists(dst) {
			_ = os.Remove(tmp)
			return nil
		}
		return err
	}
	return nil
}

func exists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

func marshalPretty(v interface{}) ([]byte, error) {
	b, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		return nil, err
	}
	return append(b, '\n'), nil
}

func prettyJSONBytes(raw []byte) ([]byte, error) {
	var v interface{}
	if err := json.Unmarshal(raw, &v); err != nil {
		return nil, errors.New("SaveBytes/ReplayOrRecordBytes: 输入不是合法 JSON")
	}
	return marshalPretty(v)
}

func (s *Store) listJSONFiles(dir string) ([]string, error) {
	infos, err := ioutil.ReadDir(dir)
	if err != nil {
		if os.IsNotExist(err) {
			return []string{}, nil
		}
		return nil, err
	}
	out := make([]string, 0, len(infos))
	for _, fi := range infos {
		if fi.IsDir() {
			continue
		}
		name := fi.Name()
		if strings.HasPrefix(name, ".") {
			continue
		}
		if strings.HasSuffix(strings.ToLower(name), ".json") {
			out = append(out, name)
		}
	}
	sort.Strings(out) // 依赖 0001.json、0002.json 的零填充分布
	return out, nil
}

func (s *Store) cursorPath(dir string) string {
	return filepath.Join(dir, ".cursor")
}

func (s *Store) readCursor(dir string, total int) int {
	b, err := ioutil.ReadFile(s.cursorPath(dir))
	if err != nil || len(b) == 0 {
		return 1
	}
	v, err := strconv.Atoi(strings.TrimSpace(string(b)))
	if err != nil || v < 1 || v > total {
		return 1
	}
	return v
}

func (s *Store) writeCursor(dir string, next int) error {
	data := []byte(fmt.Sprintf("%d\n", next))
	return s.writeAtomic(s.cursorPath(dir), data)
}
