package mock

import (
	"net/http/httptest"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/gdp/gdp"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/entity"
)

// CreateTestWebContext 创建测试用的 WebContext
func CreateTestWebContext() *gdp.WebContext {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	return gdp.NewWebContext(c)
}

// CreateTestRequestParams 创建测试用的RequestParams
func CreateTestRequestParams() *entity.RequestParams {
	return &entity.RequestParams{
		UID:     "test-uid",
		Query:   "test-query",
		Service: "suggest",
	}
}

// CreateTestRequestParamsWithUID 创建带指定UID的测试RequestParams
func CreateTestRequestParamsWithUID(uid string) *entity.RequestParams {
	return &entity.RequestParams{
		UID:     uid,
		Query:   "test-query",
		Service: "suggest",
	}
}

// CreateTestRequestParamsWithQuery 创建带指定查询的测试RequestParams
func CreateTestRequestParamsWithQuery(query string) *entity.RequestParams {
	return &entity.RequestParams{
		UID:     "test-uid",
		Query:   query,
		Service: "suggest",
	}
}

// CreateTestRequestParamsCustom 创建自定义的测试RequestParams
func CreateTestRequestParamsCustom(uid, query, service string) *entity.RequestParams {
	return &entity.RequestParams{
		UID:     uid,
		Query:   query,
		Service: service,
	}
}
