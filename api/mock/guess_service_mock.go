package mock

import (
	"errors"

	"github.com/stretchr/testify/mock"
)

// MockGuessService 实现了 service.GuessService 接口，用于测试
type MockGuessService struct {
	mock.Mock
}

// NewMockGuessService 创建一个新的 MockGuessService 实例
func NewMockGuessService() *MockGuessService {
	return &MockGuessService{}
}

func (m *MockGuessService) GetGuessImage(query []string) (map[string]string, error) {
	args := m.Called(query)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]string), args.Error(1)
}

// SetupMockGuessServiceSuccess 设置一个"成功"场景的 MockGuessService
func SetupMockGuessServiceSuccess() *MockGuessService {
	mockService := NewMockGuessService()

	// 设置成功返回的数据
	successResult := map[string]string{
		"苹果": "https://example.com/apple.jpg",
		"香蕉": "https://example.com/banana.jpg",
		"橘子": "https://example.com/orange.jpg",
	}

	mockService.On("GetGuessImage", []string{"苹果", "香蕉", "橘子"}).Return(successResult, nil)
	mockService.On("GetGuessImage", mock.AnythingOfType("[]string")).Return(successResult, nil)

	return mockService
}

// SetupMockGuessServiceFailed 设置一个"失败"场景的 MockGuessService
func SetupMockGuessServiceFailed() *MockGuessService {
	mockService := NewMockGuessService()

	// 设置失败场景 - Redis连接失败
	mockService.On("GetGuessImage", mock.AnythingOfType("[]string")).Return(nil, errors.New("redis connection failed"))

	return mockService
}

// SetupMockGuessServiceEmpty 设置一个"空结果"场景的 MockGuessService
func SetupMockGuessServiceEmpty() *MockGuessService {
	mockService := NewMockGuessService()

	// 设置空结果场景
	mockService.On("GetGuessImage", mock.AnythingOfType("[]string")).Return(map[string]string{}, nil)

	return mockService
}

// SetupMockGuessServiceCustom 创建一个可以自定义行为的 MockGuessService
func SetupMockGuessServiceCustom() *MockGuessService {
	return NewMockGuessService()
}

// MockGuessService 实现了 GuessService 接口，用于测试
// 注意：为了避免循环导入，这里不直接引用 service.GuessService 接口
