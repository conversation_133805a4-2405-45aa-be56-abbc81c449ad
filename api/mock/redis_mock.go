package mock

import (
	"context"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"icode.baidu.com/baidu/searchbox/go-suggest/api/model"
)

// MockRedisModel 实现了 model.RedisModel 接口，用于测试
type MockRedisModel struct {
	mock.Mock
}

// NewMockRedisModel 创建一个新的 MockRedisModel 实例
func NewMockRedisModel() *MockRedisModel {
	return &MockRedisModel{}
}

func (m *MockRedisModel) SetHash(ctx context.Context, key, field string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, field, value, expiration)
	return args.Error(0)
}

func (m *MockRedisModel) SetHashMap(ctx context.Context, key string, values map[string]interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, values, expiration)
	return args.Error(0)
}

func (m *MockRedisModel) GetHash(ctx context.Context, key, field string) (string, error) {
	args := m.Called(ctx, key, field)
	return args.String(0), args.Error(1)
}

func (m *MockRedisModel) GetHashMap(ctx context.Context, key string) (map[string]string, error) {
	args := m.Called(ctx, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]string), args.Error(1)
}

func (m *MockRedisModel) MultiGet(ctx context.Context, keys []string) ([]interface{}, error) {
	args := m.Called(ctx, keys)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]interface{}), args.Error(1)
}

// SetupMockRedisModelSuccess 设置一个"成功"场景的 MockRedisModel
func SetupMockRedisModelSuccess(methods ...string) *MockRedisModel {
	mockModel := NewMockRedisModel()
	for _, m := range methods {
		switch m {
		case "SetHash":
			mockModel.On("SetHash",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
				mock.Anything,
				mock.AnythingOfType("time.Duration"),
			).Return(nil)
		case "SetHashMap":
			mockModel.On("SetHashMap",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("map[string]interface {}"),
				mock.AnythingOfType("time.Duration"),
			).Return(nil)
		case "GetHash":
			mockModel.On("GetHash",
				mock.Anything,
				mock.AnythingOfType("string"),
				mock.AnythingOfType("string"),
			).Return("test-value", nil)
		case "GetHashMap":
			hashMapResult := map[string]string{
				"field1": "value1",
				"field2": "value2",
				"field3": "value3",
			}
			mockModel.On("GetHashMap",
				mock.Anything,
				mock.AnythingOfType("string"),
			).Return(hashMapResult, nil)
		case "MultiGet":
			multiGetResult := []interface{}{
				"value1",
				"value2",
				"value3",
			}
			mockModel.On("MultiGet",
				mock.Anything,
				mock.AnythingOfType("[]string"),
			).Return(multiGetResult, nil)
		}
	}
	return mockModel
}

// SetupMockRedisModelFailed 设置一个"失败"场景的 MockRedisModel
func SetupMockRedisModelFailed() *MockRedisModel {
	mockModel := NewMockRedisModel()

	// 设置失败场景
	mockModel.On("SetHash",
		mock.Anything,
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
		mock.Anything,
		mock.AnythingOfType("time.Duration"),
	).Return(assert.AnError)
	mockModel.On("SetHashMap",
		mock.Anything,
		mock.AnythingOfType("string"),
		mock.AnythingOfType("map[string]interface {}"),
		mock.AnythingOfType("time.Duration"),
	).Return(assert.AnError)
	mockModel.On("GetHash",
		mock.Anything,
		mock.AnythingOfType("string"),
		mock.AnythingOfType("string"),
	).Return("", assert.AnError)
	mockModel.On("GetHashMap",
		mock.Anything,
		mock.AnythingOfType("string"),
	).Return(map[string]string{}, assert.AnError)
	mockModel.On("MultiGet",
		mock.Anything,
		mock.AnythingOfType("[]string"),
	).Return(nil, assert.AnError)

	return mockModel
}

// SetupMockRedisModelCustom 创建一个可以自定义行为的 MockRedisModel
func SetupMockRedisModelCustom() *MockRedisModel {
	return NewMockRedisModel()
}

// 确保 MockRedisModel 实现了 model.RedisModel 接口
var _ model.RedisModel = (*MockRedisModel)(nil)
